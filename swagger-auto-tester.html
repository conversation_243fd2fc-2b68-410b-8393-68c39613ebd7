<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Swagger API Auto Tester</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #2563eb;
            --success-color: #10b981;
            --error-color: #ef4444;
            --warning-color: #f59e0b;
            --bg-color: #f8fafc;
            --card-bg: #ffffff;
            --text-color: #1e293b;
            --border-color: #e2e8f0;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 8px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .card {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 20px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }

        .card h3 {
            margin-bottom: 16px;
            color: var(--primary-color);
            font-size: 1.2rem;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: var(--text-color);
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .progress-container {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: var(--border-color);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--success-color), #34d399);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .progress-text {
            text-align: center;
            font-weight: 500;
            color: var(--text-color);
        }

        .results-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .log-container {
            background: #1e293b;
            color: #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 4px 0;
        }

        .log-success {
            color: var(--success-color);
        }

        .log-error {
            color: var(--error-color);
        }

        .log-warning {
            color: var(--warning-color);
        }

        .log-info {
            color: #60a5fa;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: var(--card-bg);
            padding: 16px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #64748b;
        }

        .success { color: var(--success-color); }
        .error { color: var(--error-color); }
        .warning { color: var(--warning-color); }
        .info { color: var(--primary-color); }

        .spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff40;
            border-radius: 50%;
            border-top-color: #ffffff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .main-grid,
            .results-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🚀 Swagger API Auto Tester</h1>
            <p>自动化测试你的API，告别手动点击的烦恼！</p>
        </div>

        <!-- Configuration and Control -->
        <div class="main-grid">
            <!-- Configuration Panel -->
            <div class="card">
                <h3>⚙️ 测试配置</h3>
                <div class="form-group">
                    <label for="apiUrl">API Base URL</label>
                    <input type="text" id="apiUrl" class="form-control" 
                           value="http://localhost:8080" placeholder="http://localhost:8080">
                </div>
                <div class="form-group">
                    <label for="swaggerUrl">Swagger JSON URL</label>
                    <input type="text" id="swaggerUrl" class="form-control" 
                           value="http://localhost:8080/swagger/doc.json" 
                           placeholder="http://localhost:8080/swagger/doc.json">
                </div>
                <div class="form-group">
                    <label for="authToken">认证Token (可选)</label>
                    <input type="text" id="authToken" class="form-control" 
                           placeholder="Bearer your-jwt-token">
                </div>
                <div class="form-group">
                    <label for="testMode">测试模式</label>
                    <select id="testMode" class="form-control">
                        <option value="all">全部测试</option>
                        <option value="auth">仅认证API</option>
                        <option value="database">仅数据库API</option>
                        <option value="metrics">仅监控API</option>
                        <option value="alerts">仅告警API</option>
                    </select>
                </div>
            </div>

            <!-- Control Panel -->
            <div class="card">
                <h3>🎮 测试控制</h3>
                <div style="margin-bottom: 20px;">
                    <button id="loadSwaggerBtn" class="btn btn-primary">
                        📖 加载Swagger文档
                    </button>
                    <button id="startTestBtn" class="btn btn-success" disabled>
                        ▶️ 开始测试
                    </button>
                </div>
                <div style="margin-bottom: 20px;">
                    <button id="pauseTestBtn" class="btn btn-warning hidden">
                        ⏸️ 暂停测试
                    </button>
                    <button id="stopTestBtn" class="btn btn-danger hidden">
                        ⏹️ 停止测试
                    </button>
                    <button id="resetBtn" class="btn btn-primary">
                        🔄 重置
                    </button>
                </div>
                <div>
                    <button id="exportBtn" class="btn btn-primary">
                        📊 导出报告
                    </button>
                </div>
            </div>
        </div>

        <!-- Progress -->
        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">准备就绪</div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number success" id="successCount">0</div>
                <div class="stat-label">成功</div>
            </div>
            <div class="stat-card">
                <div class="stat-number error" id="failureCount">0</div>
                <div class="stat-label">失败</div>
            </div>
            <div class="stat-card">
                <div class="stat-number info" id="totalCount">0</div>
                <div class="stat-label">总计</div>
            </div>
            <div class="stat-card">
                <div class="stat-number warning" id="avgTime">0ms</div>
                <div class="stat-label">平均响应</div>
            </div>
        </div>

        <!-- Results -->
        <div class="results-grid">
            <!-- Real-time Log -->
            <div class="card">
                <h3>📝 实时日志</h3>
                <div class="log-container" id="logContainer">
                    <div class="log-entry log-info">🚀 Swagger API Auto Tester 已启动</div>
                    <div class="log-entry log-info">📖 请先加载Swagger文档开始测试</div>
                </div>
            </div>

            <!-- Test Results -->
            <div class="card">
                <h3>📊 测试结果</h3>
                <div id="resultsContainer" style="height: 400px; overflow-y: auto;">
                    <p style="text-align: center; color: #64748b; margin-top: 50px;">
                        测试结果将在这里显示
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局状态管理
        const AppState = {
            swaggerDoc: null,
            testResults: [],
            isRunning: false,
            isPaused: false,
            currentTestIndex: 0,
            stats: {
                total: 0,
                success: 0,
                failure: 0,
                totalTime: 0
            }
        };

        // DOM 元素引用
        const elements = {
            apiUrl: document.getElementById('apiUrl'),
            swaggerUrl: document.getElementById('swaggerUrl'),
            authToken: document.getElementById('authToken'),
            testMode: document.getElementById('testMode'),
            loadSwaggerBtn: document.getElementById('loadSwaggerBtn'),
            startTestBtn: document.getElementById('startTestBtn'),
            pauseTestBtn: document.getElementById('pauseTestBtn'),
            stopTestBtn: document.getElementById('stopTestBtn'),
            resetBtn: document.getElementById('resetBtn'),
            exportBtn: document.getElementById('exportBtn'),
            progressFill: document.getElementById('progressFill'),
            progressText: document.getElementById('progressText'),
            logContainer: document.getElementById('logContainer'),
            resultsContainer: document.getElementById('resultsContainer'),
            successCount: document.getElementById('successCount'),
            failureCount: document.getElementById('failureCount'),
            totalCount: document.getElementById('totalCount'),
            avgTime: document.getElementById('avgTime')
        };

        // 日志系统
        class Logger {
            static log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry log-${type}`;
                
                const icon = {
                    info: 'ℹ️',
                    success: '✅',
                    error: '❌',
                    warning: '⚠️'
                }[type] || 'ℹ️';
                
                logEntry.innerHTML = `[${timestamp}] ${icon} ${message}`;
                elements.logContainer.appendChild(logEntry);
                elements.logContainer.scrollTop = elements.logContainer.scrollHeight;
            }
        }

        // 初始化应用
        function initApp() {
            Logger.log('应用初始化完成', 'success');
            
            // 绑定事件监听器
            elements.loadSwaggerBtn.addEventListener('click', loadSwaggerDoc);
            elements.startTestBtn.addEventListener('click', startTesting);
            elements.pauseTestBtn.addEventListener('click', pauseTesting);
            elements.stopTestBtn.addEventListener('click', stopTesting);
            elements.resetBtn.addEventListener('click', resetApp);
            elements.exportBtn.addEventListener('click', exportReport);
        }

        // 加载Swagger文档
        async function loadSwaggerDoc() {
            const swaggerUrl = elements.swaggerUrl.value.trim();
            if (!swaggerUrl) {
                Logger.log('请输入Swagger JSON URL', 'error');
                return;
            }

            Logger.log('正在加载Swagger文档...', 'info');
            elements.loadSwaggerBtn.disabled = true;
            elements.loadSwaggerBtn.innerHTML = '<span class="spinner"></span> 加载中...';

            try {
                const response = await fetch(swaggerUrl);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                AppState.swaggerDoc = await response.json();
                Logger.log(`Swagger文档加载成功! 发现 ${Object.keys(AppState.swaggerDoc.paths || {}).length} 个API端点`, 'success');
                
                elements.startTestBtn.disabled = false;
                generateTestCases();
                
            } catch (error) {
                Logger.log(`加载Swagger文档失败: ${error.message}`, 'error');
            } finally {
                elements.loadSwaggerBtn.disabled = false;
                elements.loadSwaggerBtn.innerHTML = '📖 加载Swagger文档';
            }
        }

        // 生成测试用例
        function generateTestCases() {
            if (!AppState.swaggerDoc || !AppState.swaggerDoc.paths) {
                Logger.log('Swagger文档无效', 'error');
                return;
            }

            Logger.log('正在生成测试用例...', 'info');
            const testCases = [];
            const paths = AppState.swaggerDoc.paths;
            const baseUrl = elements.apiUrl.value.trim();
            const testMode = elements.testMode.value;

            // 遍历所有API端点
            for (const [path, methods] of Object.entries(paths)) {
                for (const [method, spec] of Object.entries(methods)) {
                    if (typeof spec !== 'object' || !spec.summary) continue;

                    // 根据测试模式过滤
                    if (shouldIncludeEndpoint(path, method, testMode)) {
                        const testCase = createTestCase(baseUrl, path, method, spec);
                        testCases.push(testCase);
                    }
                }
            }

            AppState.testCases = testCases;
            AppState.stats.total = testCases.length;
            updateStats();

            Logger.log(`生成了 ${testCases.length} 个测试用例`, 'success');
            elements.progressText.textContent = `准备执行 ${testCases.length} 个测试`;
        }

        // 判断是否包含端点
        function shouldIncludeEndpoint(path, method, testMode) {
            if (testMode === 'all') return true;

            const modeMap = {
                'auth': path.includes('/auth'),
                'database': path.includes('/databases'),
                'metrics': path.includes('/metrics'),
                'alerts': path.includes('/alerts')
            };

            return modeMap[testMode] || false;
        }

        // 创建测试用例
        function createTestCase(baseUrl, path, method, spec) {
            const testCase = {
                id: `${method.toUpperCase()}_${path.replace(/[^a-zA-Z0-9]/g, '_')}`,
                name: spec.summary || `${method.toUpperCase()} ${path}`,
                method: method.toUpperCase(),
                url: baseUrl + path,
                path: path,
                spec: spec,
                status: 'pending',
                response: null,
                duration: 0,
                error: null
            };

            // 生成请求参数
            testCase.params = generateRequestParams(spec, path);

            return testCase;
        }

        // 生成请求参数
        function generateRequestParams(spec, path) {
            const params = {
                headers: {
                    'Content-Type': 'application/json'
                },
                pathParams: {},
                queryParams: {},
                body: null
            };

            // 添加认证头
            const authToken = elements.authToken.value.trim();
            if (authToken) {
                params.headers['Authorization'] = authToken.startsWith('Bearer ')
                    ? authToken
                    : `Bearer ${authToken}`;
            }

            // 处理路径参数
            const pathParamMatches = path.match(/{([^}]+)}/g);
            if (pathParamMatches) {
                pathParamMatches.forEach(match => {
                    const paramName = match.slice(1, -1);
                    params.pathParams[paramName] = generateMockValue(paramName, 'string');
                });
            }

            // 处理请求体
            if (spec.requestBody && spec.requestBody.content) {
                const contentType = Object.keys(spec.requestBody.content)[0];
                if (contentType === 'application/json') {
                    const schema = spec.requestBody.content[contentType].schema;
                    params.body = generateMockData(schema);
                }
            }

            // 处理查询参数
            if (spec.parameters) {
                spec.parameters.forEach(param => {
                    if (param.in === 'query') {
                        params.queryParams[param.name] = generateMockValue(param.name, param.schema?.type || 'string');
                    }
                });
            }

            return params;
        }

        // 生成模拟数据
        function generateMockData(schema) {
            if (!schema) return {};

            if (schema.type === 'object' && schema.properties) {
                const mockData = {};
                for (const [key, prop] of Object.entries(schema.properties)) {
                    mockData[key] = generateMockValue(key, prop.type, prop);
                }
                return mockData;
            }

            return generateMockValue('data', schema.type, schema);
        }

        // 生成模拟值
        function generateMockValue(fieldName, type, schema = {}) {
            const field = fieldName.toLowerCase();

            // 根据字段名生成特定值
            if (field.includes('email')) return '<EMAIL>';
            if (field.includes('password')) return 'Test123456';
            if (field.includes('name')) return 'Test Name';
            if (field.includes('id') && type === 'integer') return 1;
            if (field.includes('port')) return 3306;
            if (field.includes('host')) return 'localhost';
            if (field.includes('type') && field.includes('database')) return 'mysql';

            // 根据类型生成值
            switch (type) {
                case 'string':
                    return schema.enum ? schema.enum[0] : 'test-value';
                case 'integer':
                case 'number':
                    return schema.minimum || 1;
                case 'boolean':
                    return true;
                case 'array':
                    return [];
                default:
                    return 'test-value';
            }
        }

        // 开始测试
        async function startTesting() {
            if (!AppState.testCases || AppState.testCases.length === 0) {
                Logger.log('没有可执行的测试用例', 'error');
                return;
            }

            Logger.log('开始自动化测试...', 'info');
            AppState.isRunning = true;
            AppState.isPaused = false;
            AppState.currentTestIndex = 0;

            // 重置统计
            AppState.stats = { total: AppState.testCases.length, success: 0, failure: 0, totalTime: 0 };
            AppState.testResults = [];

            // 更新UI状态
            elements.startTestBtn.classList.add('hidden');
            elements.pauseTestBtn.classList.remove('hidden');
            elements.stopTestBtn.classList.remove('hidden');
            elements.resultsContainer.innerHTML = '';

            // 执行测试
            await executeTestSuite();
        }

        // 执行测试套件
        async function executeTestSuite() {
            const testCases = AppState.testCases;

            for (let i = 0; i < testCases.length; i++) {
                if (!AppState.isRunning) break;

                // 暂停检查
                while (AppState.isPaused && AppState.isRunning) {
                    await sleep(100);
                }

                if (!AppState.isRunning) break;

                AppState.currentTestIndex = i;
                const testCase = testCases[i];

                Logger.log(`执行测试: ${testCase.name}`, 'info');

                try {
                    await executeTestCase(testCase);
                } catch (error) {
                    Logger.log(`测试执行异常: ${error.message}`, 'error');
                }

                // 更新进度
                updateProgress();
                updateStats();

                // 添加小延迟避免过快请求
                await sleep(200);
            }

            if (AppState.isRunning) {
                Logger.log('所有测试执行完成!', 'success');
                stopTesting();
                showTestSummary();
            }
        }

        // 执行单个测试用例
        async function executeTestCase(testCase) {
            const startTime = Date.now();

            try {
                // 构建请求URL
                let url = testCase.url;

                // 替换路径参数
                for (const [key, value] of Object.entries(testCase.params.pathParams)) {
                    url = url.replace(`{${key}}`, value);
                }

                // 添加查询参数
                const queryParams = new URLSearchParams(testCase.params.queryParams);
                if (queryParams.toString()) {
                    url += '?' + queryParams.toString();
                }

                // 构建请求选项
                const options = {
                    method: testCase.method,
                    headers: testCase.params.headers,
                    mode: 'cors'
                };

                // 添加请求体
                if (testCase.params.body && ['POST', 'PUT', 'PATCH'].includes(testCase.method)) {
                    options.body = JSON.stringify(testCase.params.body);
                }

                // 发送请求
                const response = await fetch(url, options);
                const responseData = await response.text();

                // 计算响应时间
                const duration = Date.now() - startTime;
                testCase.duration = duration;
                AppState.stats.totalTime += duration;

                // 解析响应
                let parsedData;
                try {
                    parsedData = JSON.parse(responseData);
                } catch {
                    parsedData = responseData;
                }

                // 设置测试结果
                testCase.response = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    data: parsedData
                };

                // 判断测试是否成功
                if (response.ok || response.status === 503) { // 503是文档模式的正常响应
                    testCase.status = 'success';
                    AppState.stats.success++;
                    Logger.log(`✅ ${testCase.name} - ${response.status} (${duration}ms)`, 'success');
                } else {
                    testCase.status = 'failure';
                    AppState.stats.failure++;
                    Logger.log(`❌ ${testCase.name} - ${response.status} ${response.statusText} (${duration}ms)`, 'error');
                }

            } catch (error) {
                const duration = Date.now() - startTime;
                testCase.duration = duration;
                testCase.status = 'failure';
                testCase.error = error.message;
                AppState.stats.failure++;
                AppState.stats.totalTime += duration;

                Logger.log(`❌ ${testCase.name} - 网络错误: ${error.message} (${duration}ms)`, 'error');
            }

            // 添加到结果列表
            AppState.testResults.push(testCase);
            addTestResult(testCase);
        }

        // 添加测试结果到UI
        function addTestResult(testCase) {
            const resultDiv = document.createElement('div');
            resultDiv.style.cssText = `
                padding: 12px;
                margin-bottom: 8px;
                border-radius: 8px;
                border-left: 4px solid ${testCase.status === 'success' ? 'var(--success-color)' : 'var(--error-color)'};
                background: ${testCase.status === 'success' ? '#f0fdf4' : '#fef2f2'};
                font-size: 14px;
            `;

            const statusIcon = testCase.status === 'success' ? '✅' : '❌';
            const statusColor = testCase.status === 'success' ? 'var(--success-color)' : 'var(--error-color)';

            resultDiv.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                    <span style="font-weight: 500;">${statusIcon} ${testCase.name}</span>
                    <span style="color: ${statusColor}; font-weight: 500;">${testCase.duration}ms</span>
                </div>
                <div style="font-size: 12px; color: #64748b;">
                    ${testCase.method} ${testCase.path}
                    ${testCase.response ? ` → ${testCase.response.status} ${testCase.response.statusText}` : ''}
                    ${testCase.error ? ` → ${testCase.error}` : ''}
                </div>
            `;

            elements.resultsContainer.appendChild(resultDiv);
            elements.resultsContainer.scrollTop = elements.resultsContainer.scrollHeight;
        }

        // 更新进度
        function updateProgress() {
            const progress = ((AppState.currentTestIndex + 1) / AppState.stats.total) * 100;
            elements.progressFill.style.width = `${progress}%`;
            elements.progressText.textContent =
                `进度: ${AppState.currentTestIndex + 1}/${AppState.stats.total} (${Math.round(progress)}%)`;
        }

        // 显示测试摘要
        function showTestSummary() {
            const { total, success, failure, totalTime } = AppState.stats;
            const successRate = total > 0 ? ((success / total) * 100).toFixed(1) : 0;
            const avgTime = total > 0 ? Math.round(totalTime / total) : 0;

            Logger.log(`📊 测试摘要: 总计 ${total}, 成功 ${success}, 失败 ${failure}`, 'info');
            Logger.log(`📈 成功率: ${successRate}%, 平均响应时间: ${avgTime}ms`, 'info');

            elements.progressText.textContent =
                `测试完成! 成功率: ${successRate}% (${success}/${total})`;
        }

        // 工具函数
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // 暂停测试
        function pauseTesting() {
            AppState.isPaused = !AppState.isPaused;
            const btn = elements.pauseTestBtn;
            
            if (AppState.isPaused) {
                btn.innerHTML = '▶️ 继续测试';
                Logger.log('测试已暂停', 'warning');
            } else {
                btn.innerHTML = '⏸️ 暂停测试';
                Logger.log('测试已继续', 'info');
            }
        }

        // 停止测试
        function stopTesting() {
            AppState.isRunning = false;
            AppState.isPaused = false;
            
            // 更新UI状态
            elements.startTestBtn.classList.remove('hidden');
            elements.pauseTestBtn.classList.add('hidden');
            elements.stopTestBtn.classList.add('hidden');
            
            Logger.log('测试已停止', 'warning');
        }

        // 重置应用
        function resetApp() {
            AppState.swaggerDoc = null;
            AppState.testResults = [];
            AppState.isRunning = false;
            AppState.isPaused = false;
            AppState.currentTestIndex = 0;
            AppState.stats = { total: 0, success: 0, failure: 0, totalTime: 0 };
            
            // 重置UI
            elements.startTestBtn.disabled = true;
            elements.progressFill.style.width = '0%';
            elements.progressText.textContent = '准备就绪';
            elements.logContainer.innerHTML = '<div class="log-entry log-info">🚀 应用已重置</div>';
            elements.resultsContainer.innerHTML = '<p style="text-align: center; color: #64748b; margin-top: 50px;">测试结果将在这里显示</p>';
            
            updateStats();
            Logger.log('应用已重置', 'info');
        }

        // 导出报告
        function exportReport() {
            if (!AppState.testResults || AppState.testResults.length === 0) {
                Logger.log('没有测试结果可导出', 'warning');
                return;
            }

            Logger.log('正在生成测试报告...', 'info');

            const report = generateTestReport();
            const blob = new Blob([report], { type: 'text/html' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `swagger-test-report-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.html`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            Logger.log('测试报告已导出', 'success');
        }

        // 生成测试报告
        function generateTestReport() {
            const { total, success, failure, totalTime } = AppState.stats;
            const successRate = total > 0 ? ((success / total) * 100).toFixed(1) : 0;
            const avgTime = total > 0 ? Math.round(totalTime / total) : 0;
            const timestamp = new Date().toLocaleString();

            let reportHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Swagger API 测试报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; }
        .header { background: #2563eb; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 20px; }
        .stat-card { background: #f8fafc; padding: 16px; border-radius: 8px; text-align: center; border: 1px solid #e2e8f0; }
        .stat-number { font-size: 2rem; font-weight: bold; margin-bottom: 4px; }
        .success { color: #10b981; }
        .error { color: #ef4444; }
        .info { color: #2563eb; }
        .warning { color: #f59e0b; }
        .test-result { padding: 12px; margin-bottom: 8px; border-radius: 8px; border-left: 4px solid; }
        .test-success { border-left-color: #10b981; background: #f0fdf4; }
        .test-failure { border-left-color: #ef4444; background: #fef2f2; }
        .test-details { font-size: 12px; color: #64748b; margin-top: 4px; }
        pre { background: #f1f5f9; padding: 12px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Swagger API 测试报告</h1>
        <p>生成时间: ${timestamp}</p>
        <p>API Base URL: ${elements.apiUrl.value}</p>
    </div>

    <div class="stats">
        <div class="stat-card">
            <div class="stat-number info">${total}</div>
            <div>总测试数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number success">${success}</div>
            <div>成功</div>
        </div>
        <div class="stat-card">
            <div class="stat-number error">${failure}</div>
            <div>失败</div>
        </div>
        <div class="stat-card">
            <div class="stat-number warning">${successRate}%</div>
            <div>成功率</div>
        </div>
        <div class="stat-card">
            <div class="stat-number info">${avgTime}ms</div>
            <div>平均响应时间</div>
        </div>
    </div>

    <h2>测试结果详情</h2>
`;

            // 添加每个测试结果
            AppState.testResults.forEach(testCase => {
                const statusClass = testCase.status === 'success' ? 'test-success' : 'test-failure';
                const statusIcon = testCase.status === 'success' ? '✅' : '❌';

                reportHtml += `
    <div class="test-result ${statusClass}">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <strong>${statusIcon} ${testCase.name}</strong>
            <span>${testCase.duration}ms</span>
        </div>
        <div class="test-details">
            ${testCase.method} ${testCase.path}
            ${testCase.response ? ` → ${testCase.response.status} ${testCase.response.statusText}` : ''}
            ${testCase.error ? ` → 错误: ${testCase.error}` : ''}
        </div>
`;

                // 添加请求详情
                if (testCase.params.body) {
                    reportHtml += `
        <details style="margin-top: 8px;">
            <summary style="cursor: pointer; color: #64748b;">请求详情</summary>
            <pre>${JSON.stringify(testCase.params.body, null, 2)}</pre>
        </details>
`;
                }

                // 添加响应详情
                if (testCase.response && testCase.response.data) {
                    reportHtml += `
        <details style="margin-top: 8px;">
            <summary style="cursor: pointer; color: #64748b;">响应详情</summary>
            <pre>${JSON.stringify(testCase.response.data, null, 2)}</pre>
        </details>
`;
                }

                reportHtml += `    </div>\n`;
            });

            reportHtml += `
</body>
</html>`;

            return reportHtml;
        }

        // 更新统计信息
        function updateStats() {
            elements.successCount.textContent = AppState.stats.success;
            elements.failureCount.textContent = AppState.stats.failure;
            elements.totalCount.textContent = AppState.stats.total;
            
            const avgTime = AppState.stats.total > 0 
                ? Math.round(AppState.stats.totalTime / AppState.stats.total)
                : 0;
            elements.avgTime.textContent = `${avgTime}ms`;
        }

        // 启动应用
        document.addEventListener('DOMContentLoaded', initApp);
    </script>
</body>
</html>
