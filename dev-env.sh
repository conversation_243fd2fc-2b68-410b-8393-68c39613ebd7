#!/bin/bash

# 开发环境配置脚本
# 用于清除代理设置，确保本地开发环境正常工作

echo "🔧 配置开发环境..."

# 清除所有代理设置
unset http_proxy
unset https_proxy
unset HTTP_PROXY
unset HTTPS_PROXY
unset all_proxy
unset ALL_PROXY

# 设置本地绕过规则（作为备用）
export no_proxy="localhost,127.0.0.1,::1,**********/12,***********/16,10.0.0.0/8"
export NO_PROXY="localhost,127.0.0.1,::1,**********/12,***********/16,10.0.0.0/8"

echo "✅ 开发环境配置完成"
echo "📍 代理已清除，本地服务可正常访问"

# 测试后端连接
echo "🔍 测试后端连接..."
if curl -s http://localhost:8080/health > /dev/null; then
    echo "✅ 后端服务正常 (http://localhost:8080)"
else
    echo "❌ 后端服务不可用"
fi

# 显示当前代理状态
echo "📊 当前代理状态:"
echo "  http_proxy: ${http_proxy:-未设置}"
echo "  https_proxy: ${https_proxy:-未设置}"
echo "  all_proxy: ${all_proxy:-未设置}"
echo "  no_proxy: ${no_proxy:-未设置}"
