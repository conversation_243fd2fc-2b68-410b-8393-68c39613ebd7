#!/bin/bash

# 快速停止开发环境脚本

echo "🛑 停止数据库监控平台开发环境..."

# 1. 停止前端服务
if [ -f frontend.pid ]; then
    echo "🎨 停止前端服务..."
    kill $(cat frontend.pid) 2>/dev/null || echo "前端服务已停止"
    rm -f frontend.pid
fi

# 2. 停止后端服务
if [ -f backend.pid ]; then
    echo "🔧 停止后端服务..."
    kill $(cat backend.pid) 2>/dev/null || echo "后端服务已停止"
    rm -f backend.pid
fi

# 3. 停止Docker服务
echo "📦 停止Docker服务..."
docker-compose down

echo ""
echo "✅ 所有服务已停止！"
echo ""
echo "📝 日志文件保留："
echo "   后端日志: backend.log"
echo "   前端日志: frontend.log"
