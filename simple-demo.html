<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库监控平台 - MVP演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .logo h1 {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }
        
        .logo p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .dashboard {
            display: none;
            background: #f8f9fa;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .header {
            background: white;
            padding: 1rem 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-card h3 {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        
        .stat-card .value {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
        }
        
        .database-list {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .db-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #eee;
        }
        
        .db-item:last-child {
            border-bottom: none;
        }
        
        .db-info h4 {
            color: #333;
            margin-bottom: 0.25rem;
        }
        
        .db-info p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status.healthy {
            background: #d4edda;
            color: #155724;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <!-- 登录界面 -->
    <div id="loginForm" class="container">
        <div class="logo">
            <h1>🗄️ 数据库监控平台</h1>
            <p>企业级数据库性能监控解决方案</p>
        </div>
        
        <form onsubmit="login(event)">
            <div class="form-group">
                <label for="email">邮箱地址</label>
                <input type="email" id="email" placeholder="请输入邮箱" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" placeholder="请输入密码" required>
            </div>
            
            <button type="submit" class="btn">登录</button>
        </form>
    </div>
    
    <!-- Dashboard界面 -->
    <div id="dashboard" class="dashboard">
        <div class="header">
            <h1>数据库监控平台</h1>
            <p>欢迎回来，<span id="userName"></span></p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <h3>数据库实例</h3>
                <div class="value">3</div>
            </div>
            <div class="stat-card">
                <h3>活跃连接</h3>
                <div class="value">297</div>
            </div>
            <div class="stat-card">
                <h3>告警数量</h3>
                <div class="value">1</div>
            </div>
            <div class="stat-card">
                <h3>健康状态</h3>
                <div class="value">良好</div>
            </div>
        </div>
        
        <div class="database-list">
            <h2 style="margin-bottom: 1rem;">数据库实例</h2>
            
            <div class="db-item">
                <div class="db-info">
                    <h4>MySQL-prod</h4>
                    <p>MySQL • 192.168.1.100:3306</p>
                    <p>CPU: 75.2% | 内存: 68.5% | 连接: 85/100</p>
                </div>
                <span class="status warning">警告</span>
            </div>
            
            <div class="db-item">
                <div class="db-info">
                    <h4>PostgreSQL-main</h4>
                    <p>PostgreSQL • 192.168.1.101:5432</p>
                    <p>CPU: 45.8% | 内存: 82.1% | 连接: 200/200</p>
                </div>
                <span class="status warning">警告</span>
            </div>
            
            <div class="db-item">
                <div class="db-info">
                    <h4>MySQL-test</h4>
                    <p>MySQL • *************:3306</p>
                    <p>CPU: 25.3% | 内存: 35.7% | 连接: 12/50</p>
                </div>
                <span class="status healthy">健康</span>
            </div>
        </div>
    </div>
    
    <script>
        function login(event) {
            event.preventDefault();
            
            const email = document.getElementById('email').value;
            const userName = email.split('@')[0];
            
            // 隐藏登录表单，显示Dashboard
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('dashboard').style.display = 'block';
            document.getElementById('userName').textContent = userName;
            
            // 模拟加载效果
            document.body.style.background = '#f8f9fa';
        }
    </script>
</body>
</html>
