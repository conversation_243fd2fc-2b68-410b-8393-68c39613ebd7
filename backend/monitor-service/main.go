package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/dbmonitor/monitor-service/internal/api"
	"github.com/dbmonitor/monitor-service/internal/config"
	"github.com/dbmonitor/monitor-service/internal/monitor"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 设置日志
	logrus.SetLevel(logrus.InfoLevel)
	if cfg.Debug {
		logrus.SetLevel(logrus.DebugLevel)
	}

	// 创建监控服务
	monitorService := monitor.NewService(cfg)

	// 启动监控服务
	go func() {
		if err := monitorService.Start(); err != nil {
			logrus.Fatalf("Failed to start monitor service: %v", err)
		}
	}()

	// 设置Gin模式
	if !cfg.Debug {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建HTTP服务器
	router := api.SetupRouter(monitorService)
	
	srv := &http.Server{
		Addr:    cfg.Server.Address,
		Handler: router,
	}

	// 启动HTTP服务器
	go func() {
		logrus.Infof("Starting HTTP server on %s", cfg.Server.Address)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logrus.Fatalf("Failed to start HTTP server: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logrus.Info("Shutting down server...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logrus.Fatalf("Server forced to shutdown: %v", err)
	}

	monitorService.Stop()
	logrus.Info("Server exited")
}
