package config

import (
	"os"
	"strconv"
)

type Config struct {
	Debug  bool   `yaml:"debug"`
	Server Server `yaml:"server"`
	Redis  Redis  `yaml:"redis"`
}

type Server struct {
	Address string `yaml:"address"`
}

type Redis struct {
	Address  string `yaml:"address"`
	Password string `yaml:"password"`
	DB       int    `yaml:"db"`
}

func Load() (*Config, error) {
	cfg := &Config{
		Debug: getEnvBool("DEBUG", true),
		Server: Server{
			Address: getEnv("SERVER_ADDRESS", ":8081"),
		},
		Redis: Redis{
			Address:  getEnv("REDIS_ADDRESS", "localhost:6379"),
			Password: getEnv("REDIS_PASSWORD", ""),
			DB:       getEnvInt("REDIS_DB", 0),
		},
	}

	return cfg, nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if b, err := strconv.ParseBool(value); err == nil {
			return b
		}
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if i, err := strconv.Atoi(value); err == nil {
			return i
		}
	}
	return defaultValue
}
