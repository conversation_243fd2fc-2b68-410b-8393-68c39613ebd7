package api

import (
	"net/http"
	"time"

	"github.com/dbmonitor/monitor-service/internal/monitor"
	"github.com/gin-gonic/gin"
)

type Handler struct {
	monitorService *monitor.Service
}

func SetupRouter(monitorService *monitor.Service) *gin.Engine {
	router := gin.Default()

	// CORS中间件
	router.Use(func(c *gin.Context) {
		c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	handler := &Handler{
		monitorService: monitorService,
	}

	// API路由
	api := router.Group("/api")
	{
		api.GET("/health", handler.Health)
		api.GET("/databases", handler.GetDatabases)
		api.GET("/metrics", handler.GetMetrics)
		api.GET("/metrics/:instanceId", handler.GetMetricsByInstance)
		api.GET("/metrics/:instanceId/history", handler.GetMetricsHistory)
	}

	return router
}

func (h *Handler) Health(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "ok",
		"timestamp": time.Now(),
		"service":   "monitor-service",
	})
}

func (h *Handler) GetDatabases(c *gin.Context) {
	databases := h.monitorService.GetDatabases()
	
	// 转换为数组格式
	var result []interface{}
	for _, db := range databases {
		result = append(result, db)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

func (h *Handler) GetMetrics(c *gin.Context) {
	metrics := h.monitorService.GetMetrics()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    metrics,
	})
}

func (h *Handler) GetMetricsByInstance(c *gin.Context) {
	instanceID := c.Param("instanceId")
	metrics := h.monitorService.GetMetricsByInstance(instanceID)

	if metrics == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Instance not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    metrics,
	})
}

func (h *Handler) GetMetricsHistory(c *gin.Context) {
	instanceID := c.Param("instanceId")
	
	// 生成模拟历史数据
	var history []map[string]interface{}
	now := time.Now()
	
	for i := 23; i >= 0; i-- {
		timestamp := now.Add(time.Duration(-i) * time.Hour)
		
		// 生成模拟数据
		history = append(history, map[string]interface{}{
			"timestamp":   timestamp,
			"cpu":         20 + float64(i%60),
			"memory":      30 + float64(i%50),
			"connections": 50 + i*2,
			"qps":         100 + i*10,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"instance_id": instanceID,
			"metrics":     history,
		},
	})
}
