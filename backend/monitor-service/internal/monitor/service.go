package monitor

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"github.com/dbmonitor/monitor-service/internal/config"
	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
)

type Service struct {
	config      *config.Config
	redisClient *redis.Client
	databases   map[string]*DatabaseInstance
	metrics     map[string]*MetricData
	mutex       sync.RWMutex
	stopChan    chan struct{}
}

type DatabaseInstance struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	Type     string `json:"type"`
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Database string `json:"database"`
	Username string `json:"username"`
	Password string `json:"password"`
	Status   string `json:"status"`
}

type MetricData struct {
	InstanceID  string                 `json:"instance_id"`
	Timestamp   time.Time              `json:"timestamp"`
	CPU         float64                `json:"cpu"`
	Memory      float64                `json:"memory"`
	Connections int                    `json:"connections"`
	QPS         int                    `json:"qps"`
	SlowQueries int                    `json:"slow_queries"`
	Custom      map[string]interface{} `json:"custom"`
}

func NewService(cfg *config.Config) *Service {
	rdb := redis.NewClient(&redis.Options{
		Addr:     cfg.Redis.Address,
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})

	return &Service{
		config:      cfg,
		redisClient: rdb,
		databases:   make(map[string]*DatabaseInstance),
		metrics:     make(map[string]*MetricData),
		stopChan:    make(chan struct{}),
	}
}

func (s *Service) Start() error {
	logrus.Info("Starting monitor service...")

	// 添加一些模拟数据库实例用于演示
	s.addMockDatabases()

	// 启动监控循环 - 降低频率减少内存压力
	ticker := time.NewTicker(5 * time.Minute) // 从30秒改为5分钟
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.collectMetrics()
		case <-s.stopChan:
			logrus.Info("Monitor service stopped")
			return nil
		}
	}
}

func (s *Service) Stop() {
	close(s.stopChan)
	if s.redisClient != nil {
		s.redisClient.Close()
	}
}

func (s *Service) addMockDatabases() {
	mockDBs := []*DatabaseInstance{
		{
			ID:       "mysql-prod-001",
			Name:     "MySQL-prod",
			Type:     "mysql",
			Host:     "*************",
			Port:     3306,
			Database: "production",
			Username: "monitor",
			Password: "password",
			Status:   "active",
		},
		{
			ID:       "postgres-main-001",
			Name:     "PostgreSQL-main",
			Type:     "postgresql",
			Host:     "*************",
			Port:     5432,
			Database: "maindb",
			Username: "monitor",
			Password: "password",
			Status:   "active",
		},
		{
			ID:       "mysql-test-001",
			Name:     "MySQL-test",
			Type:     "mysql",
			Host:     "*************",
			Port:     3306,
			Database: "testdb",
			Username: "monitor",
			Password: "password",
			Status:   "active",
		},
	}

	s.mutex.Lock()
	defer s.mutex.Unlock()

	for _, db := range mockDBs {
		s.databases[db.ID] = db
		logrus.Infof("Added mock database: %s (%s)", db.Name, db.Type)
	}
}

func (s *Service) collectMetrics() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for id, db := range s.databases {
		metrics := s.generateMockMetrics(id, db)
		s.metrics[id] = metrics

		// 存储到Redis (在实际应用中可能存储到ClickHouse)
		ctx := context.Background()
		key := fmt.Sprintf("metrics:%s:%d", id, time.Now().Unix())

		if err := s.redisClient.HSet(ctx, key, map[string]interface{}{
			"cpu":          metrics.CPU,
			"memory":       metrics.Memory,
			"connections":  metrics.Connections,
			"qps":          metrics.QPS,
			"slow_queries": metrics.SlowQueries,
		}).Err(); err != nil {
			logrus.Errorf("Failed to store metrics for %s: %v", id, err)
		}

		// 设置过期时间 (7天)
		s.redisClient.Expire(ctx, key, 7*24*time.Hour)

		logrus.Debugf("Collected metrics for %s: CPU=%.1f%%, Memory=%.1f%%, Connections=%d",
			db.Name, metrics.CPU, metrics.Memory, metrics.Connections)
	}
}

func (s *Service) generateMockMetrics(instanceID string, db *DatabaseInstance) *MetricData {
	// 生成模拟指标数据
	now := time.Now()

	// 基于时间和实例ID生成相对稳定但有变化的数据
	seed := now.Unix() + int64(len(instanceID))
	r := rand.New(rand.NewSource(seed))

	var cpu, memory float64
	var connections, maxConn, qps int

	switch db.Type {
	case "mysql":
		cpu = 20 + r.Float64()*60    // 20-80%
		memory = 30 + r.Float64()*50 // 30-80%
		maxConn = 100
		connections = 10 + r.Intn(90) // 10-100
		qps = 100 + r.Intn(900)       // 100-1000
	case "postgresql":
		cpu = 15 + r.Float64()*70    // 15-85%
		memory = 25 + r.Float64()*60 // 25-85%
		maxConn = 200
		connections = 20 + r.Intn(180) // 20-200
		qps = 50 + r.Intn(450)         // 50-500
	default:
		cpu = 10 + r.Float64()*50
		memory = 20 + r.Float64()*60
		maxConn = 50
		connections = 5 + r.Intn(45)
		qps = 20 + r.Intn(180)
	}

	return &MetricData{
		InstanceID:  instanceID,
		Timestamp:   now,
		CPU:         cpu,
		Memory:      memory,
		Connections: connections,
		QPS:         qps,
		SlowQueries: r.Intn(5), // 0-5 slow queries
		Custom: map[string]interface{}{
			"max_connections": maxConn,
			"db_type":         db.Type,
		},
	}
}

func (s *Service) GetDatabases() map[string]*DatabaseInstance {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	result := make(map[string]*DatabaseInstance)
	for k, v := range s.databases {
		result[k] = v
	}
	return result
}

func (s *Service) GetMetrics() map[string]*MetricData {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	result := make(map[string]*MetricData)
	for k, v := range s.metrics {
		result[k] = v
	}
	return result
}

func (s *Service) GetMetricsByInstance(instanceID string) *MetricData {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	return s.metrics[instanceID]
}
