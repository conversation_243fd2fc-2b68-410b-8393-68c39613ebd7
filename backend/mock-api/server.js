const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 8081;

// 中间件
app.use(cors());
app.use(express.json());

// 模拟数据
const mockDatabases = [
  {
    id: 'mysql-prod-001',
    name: 'MySQL-prod',
    type: 'MySQL',
    host: '*************',
    port: 3306,
    database: 'production',
    status: 'active'
  },
  {
    id: 'postgres-main-001',
    name: 'PostgreSQL-main',
    type: 'PostgreSQL',
    host: '*************',
    port: 5432,
    database: 'maindb',
    status: 'active'
  },
  {
    id: 'mysql-test-001',
    name: 'MySQL-test',
    type: 'MySQL',
    host: '*************',
    port: 3306,
    database: 'testdb',
    status: 'active'
  }
];

// 生成模拟指标数据
function generateMockMetrics(instanceId, dbType) {
  const now = new Date();
  const seed = now.getTime() + instanceId.length;
  const random = () => Math.sin(seed + Date.now() / 10000) * 0.5 + 0.5;
  
  let cpu, memory, connections, maxConn, qps;
  
  switch (dbType) {
    case 'MySQL':
      cpu = 20 + random() * 60;
      memory = 30 + random() * 50;
      maxConn = 100;
      connections = 10 + Math.floor(random() * 90);
      qps = 100 + Math.floor(random() * 900);
      break;
    case 'PostgreSQL':
      cpu = 15 + random() * 70;
      memory = 25 + random() * 60;
      maxConn = 200;
      connections = 20 + Math.floor(random() * 180);
      qps = 50 + Math.floor(random() * 450);
      break;
    default:
      cpu = 10 + random() * 50;
      memory = 20 + random() * 60;
      maxConn = 50;
      connections = 5 + Math.floor(random() * 45);
      qps = 20 + Math.floor(random() * 180);
  }

  return {
    instance_id: instanceId,
    timestamp: now.toISOString(),
    cpu: Math.round(cpu * 10) / 10,
    memory: Math.round(memory * 10) / 10,
    connections: connections,
    qps: qps,
    slow_queries: Math.floor(random() * 5),
    custom: {
      max_connections: maxConn,
      db_type: dbType
    }
  };
}

// API路由
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date(),
    service: 'mock-api'
  });
});

app.get('/api/databases', (req, res) => {
  res.json({
    success: true,
    data: mockDatabases
  });
});

app.get('/api/metrics', (req, res) => {
  const metrics = {};
  mockDatabases.forEach(db => {
    metrics[db.id] = generateMockMetrics(db.id, db.type);
  });
  
  res.json({
    success: true,
    data: metrics
  });
});

app.get('/api/metrics/:instanceId', (req, res) => {
  const { instanceId } = req.params;
  const db = mockDatabases.find(d => d.id === instanceId);
  
  if (!db) {
    return res.status(404).json({
      success: false,
      message: 'Instance not found'
    });
  }
  
  const metrics = generateMockMetrics(instanceId, db.type);
  res.json({
    success: true,
    data: metrics
  });
});

app.get('/api/metrics/:instanceId/history', (req, res) => {
  const { instanceId } = req.params;
  const db = mockDatabases.find(d => d.id === instanceId);
  
  if (!db) {
    return res.status(404).json({
      success: false,
      message: 'Instance not found'
    });
  }
  
  // 生成24小时历史数据
  const history = [];
  const now = new Date();
  
  for (let i = 23; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);
    const baseValue = Math.sin((24 - i) * Math.PI / 12) * 30 + 50;
    
    history.push({
      timestamp: timestamp.toISOString(),
      cpu: Math.max(10, Math.min(90, baseValue + (Math.random() - 0.5) * 20)),
      memory: Math.max(20, Math.min(85, baseValue + (Math.random() - 0.5) * 15)),
      connections: Math.floor(Math.max(10, Math.min(180, baseValue * 2 + (Math.random() - 0.5) * 40))),
      qps: Math.floor(Math.max(50, Math.min(1000, baseValue * 10 + (Math.random() - 0.5) * 200)))
    });
  }
  
  res.json({
    success: true,
    data: {
      instance_id: instanceId,
      metrics: history
    }
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 Mock API server running on http://localhost:${PORT}`);
  console.log(`📊 Available endpoints:`);
  console.log(`   GET /api/health`);
  console.log(`   GET /api/databases`);
  console.log(`   GET /api/metrics`);
  console.log(`   GET /api/metrics/:instanceId`);
  console.log(`   GET /api/metrics/:instanceId/history`);
});
