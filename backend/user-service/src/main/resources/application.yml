server:
  port: 8080

spring:
  application:
    name: user-service
  
  datasource:
    url: *******************************************
    username: dbmonitor
    password: dbmonitor123
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
  
  data:
    redis:
      host: localhost
      port: 6379
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

jwt:
  secret: myVerySecretKeyForDatabaseMonitorPlatform2024
  expiration: 86400000 # 24 hours

logging:
  level:
    com.dbmonitor: DEBUG
    org.springframework.security: DEBUG
