package com.dbmonitor.userservice.repository;

import com.dbmonitor.userservice.entity.DatabaseInstance;
import com.dbmonitor.userservice.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface DatabaseInstanceRepository extends JpaRepository<DatabaseInstance, UUID> {
    
    List<DatabaseInstance> findByUser(User user);
    
    List<DatabaseInstance> findByUserAndStatus(User user, DatabaseInstance.Status status);
}
