#!/bin/bash

# 测试新的API端点
BASE_URL="http://localhost:8080"

echo "🚀 Testing Phase 4 New APIs - Reports and Settings"
echo "=================================================="

# 1. 登录获取token
echo "1. 登录获取认证token..."
LOGIN_RESPONSE=$(curl -s -X POST $BASE_URL/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }')

# 从响应中提取token (简单的字符串处理)
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
if [ -z "$TOKEN" ]; then
  echo "❌ 登录失败，无法获取token"
  echo "Response: $LOGIN_RESPONSE"
  exit 1
fi

echo "✅ 登录成功，获取到token: ${TOKEN:0:20}..."

# 2. 测试设置API
echo ""
echo "2. 测试设置API..."

# 2.1 获取设置分类
echo "2.1 获取设置分类..."
RESPONSE=$(curl -s -X GET $BASE_URL/api/v1/settings/categories \
  -H "Authorization: Bearer $TOKEN")
echo "Status: $(echo $RESPONSE | grep -o '"code":[0-9]*' | cut -d':' -f2)"
echo "Response: $RESPONSE"

# 2.2 获取验证规则
echo ""
echo "2.2 获取验证规则..."
RESPONSE=$(curl -s -X GET $BASE_URL/api/v1/settings/validation-rules \
  -H "Authorization: Bearer $TOKEN")
echo "Status: $(echo $RESPONSE | grep -o '"code":[0-9]*' | cut -d':' -f2)"
echo "Response: $RESPONSE"

# 2.3 获取系统设置
echo ""
echo "2.3 获取系统设置..."
RESPONSE=$(curl -s -X GET $BASE_URL/api/v1/settings/system \
  -H "Authorization: Bearer $TOKEN")
echo "Status: $(echo $RESPONSE | grep -o '"code":[0-9]*' | cut -d':' -f2)"
echo "Response: $RESPONSE"

# 2.4 获取用户偏好
echo ""
echo "2.4 获取用户偏好..."
RESPONSE=$(curl -s -X GET $BASE_URL/api/v1/settings/preferences \
  -H "Authorization: Bearer $TOKEN")
echo "Status: $(echo $RESPONSE | grep -o '"code":[0-9]*' | cut -d':' -f2)"
echo "Response: $RESPONSE"

# 2.5 初始化用户默认设置
echo ""
echo "2.5 初始化用户默认设置..."
RESPONSE=$(curl -s -X POST $BASE_URL/api/v1/settings/preferences/initialize \
  -H "Authorization: Bearer $TOKEN")
echo "Status: $(echo $RESPONSE | grep -o '"code":[0-9]*' | cut -d':' -f2)"
echo "Response: $RESPONSE"

# 3. 测试报表API
echo ""
echo "3. 测试报表API..."

# 3.1 获取报表模板列表
echo "3.1 获取报表模板列表..."
RESPONSE=$(curl -s -X GET $BASE_URL/api/v1/reports/templates \
  -H "Authorization: Bearer $TOKEN")
echo "Status: $(echo $RESPONSE | grep -o '"code":[0-9]*' | cut -d':' -f2)"
echo "Response: $RESPONSE"

# 3.2 创建报表模板
echo ""
echo "3.2 创建报表模板..."
CREATE_TEMPLATE_RESPONSE=$(curl -s -X POST $BASE_URL/api/v1/reports/templates \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "CPU性能报表",
    "description": "监控CPU使用率的性能报表",
    "type": "performance",
    "config": {
      "metrics": ["cpu_usage", "cpu_load"],
      "time_range": "1h",
      "chart_type": "line"
    }
  }')

echo "Status: $(echo $CREATE_TEMPLATE_RESPONSE | grep -o '"code":[0-9]*' | cut -d':' -f2)"
echo "Response: $CREATE_TEMPLATE_RESPONSE"

TEMPLATE_ID=$(echo $CREATE_TEMPLATE_RESPONSE | grep -o '"id":[0-9]*' | cut -d':' -f2)

# 3.3 获取报表模板详情
if [ -n "$TEMPLATE_ID" ]; then
  echo ""
  echo "3.3 获取报表模板详情 (ID: $TEMPLATE_ID)..."
  RESPONSE=$(curl -s -X GET $BASE_URL/api/v1/reports/templates/$TEMPLATE_ID \
    -H "Authorization: Bearer $TOKEN")
  echo "Status: $(echo $RESPONSE | grep -o '"code":[0-9]*' | cut -d':' -f2)"
  echo "Response: $RESPONSE"

  # 3.4 执行报表生成
  echo ""
  echo "3.4 执行报表生成..."
  EXECUTE_RESPONSE=$(curl -s -X POST $BASE_URL/api/v1/reports/execute \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
      "template_id": '$TEMPLATE_ID',
      "format": "pdf",
      "time_range": {
        "start_time": "2025-07-20T00:00:00Z",
        "end_time": "2025-07-20T23:59:59Z"
      },
      "parameters": {
        "database_ids": [1, 2]
      }
    }')

  echo "Status: $(echo $EXECUTE_RESPONSE | grep -o '"code":[0-9]*' | cut -d':' -f2)"
  echo "Response: $EXECUTE_RESPONSE"

  EXECUTION_ID=$(echo $EXECUTE_RESPONSE | grep -o '"id":[0-9]*' | cut -d':' -f2)

  # 3.5 获取执行状态
  if [ -n "$EXECUTION_ID" ]; then
    echo ""
    echo "3.5 获取执行状态 (ID: $EXECUTION_ID)..."
    RESPONSE=$(curl -s -X GET $BASE_URL/api/v1/reports/executions/$EXECUTION_ID \
      -H "Authorization: Bearer $TOKEN")
    echo "Status: $(echo $RESPONSE | grep -o '"code":[0-9]*' | cut -d':' -f2)"
    echo "Response: $RESPONSE"
  fi
fi

# 3.6 获取执行记录列表
echo ""
echo "3.6 获取执行记录列表..."
RESPONSE=$(curl -s -X GET $BASE_URL/api/v1/reports/executions \
  -H "Authorization: Bearer $TOKEN")
echo "Status: $(echo $RESPONSE | grep -o '"code":[0-9]*' | cut -d':' -f2)"
echo "Response: $RESPONSE"

echo ""
echo "🎉 API测试完成！"
echo "=================================================="
