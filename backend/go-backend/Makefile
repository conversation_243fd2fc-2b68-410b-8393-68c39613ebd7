# 数据库监控平台 Makefile

# 变量定义
APP_NAME=db-monitor-platform
BUILD_DIR=build
CMD_DIR=cmd/server
MAIN_FILE=$(CMD_DIR)/main.go

# Go相关变量
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# 构建标志
LDFLAGS=-ldflags "-X main.Version=$(shell git describe --tags --always --dirty) -X main.BuildTime=$(shell date -u '+%Y-%m-%d_%H:%M:%S')"

# 默认目标
.PHONY: all
all: clean build

# 构建应用
.PHONY: build
build:
	@echo "Building $(APP_NAME)..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME) $(MAIN_FILE)
	@echo "Build completed: $(BUILD_DIR)/$(APP_NAME)"

# 运行应用
.PHONY: run
run:
	@echo "Running $(APP_NAME)..."
	$(GOCMD) run $(MAIN_FILE)

# 开发模式运行（带热重载）
.PHONY: dev
dev:
	@echo "Starting development server with hot reload..."
	@if command -v air > /dev/null; then \
		air; \
	else \
		echo "Air not found. Installing..."; \
		$(GOGET) -u github.com/cosmtrek/air; \
		air; \
	fi

# 清理构建文件
.PHONY: clean
clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	@rm -rf $(BUILD_DIR)
	@echo "Clean completed"

# 运行测试
.PHONY: test
test:
	@echo "Running tests..."
	$(GOTEST) -v ./...

# 运行测试并生成覆盖率报告
.PHONY: test-coverage
test-coverage:
	@echo "Running tests with coverage..."
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# 格式化代码
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	$(GOCMD) fmt ./...

# 代码检查
.PHONY: vet
vet:
	@echo "Running go vet..."
	$(GOCMD) vet ./...

# 安装依赖
.PHONY: deps
deps:
	@echo "Downloading dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

# 更新依赖
.PHONY: deps-update
deps-update:
	@echo "Updating dependencies..."
	$(GOGET) -u ./...
	$(GOMOD) tidy

# 生成API文档
.PHONY: docs
docs:
	@echo "Generating API documentation..."
	@if command -v swag > /dev/null; then \
		swag init -g $(MAIN_FILE) -o ./api/swagger; \
	else \
		echo "Swag not found. Installing..."; \
		$(GOGET) -u github.com/swaggo/swag/cmd/swag; \
		swag init -g $(MAIN_FILE) -o ./api/swagger; \
	fi

# 数据库迁移
.PHONY: migrate
migrate:
	@echo "Running database migrations..."
	$(GOCMD) run scripts/migrate.go

# 创建种子数据
.PHONY: seed
seed:
	@echo "Creating seed data..."
	$(GOCMD) run scripts/seed.go

# Docker构建
.PHONY: docker-build
docker-build:
	@echo "Building Docker image..."
	docker build -t $(APP_NAME):latest .

# Docker运行
.PHONY: docker-run
docker-run:
	@echo "Running Docker container..."
	docker run -p 8080:8080 $(APP_NAME):latest

# Docker Compose启动
.PHONY: docker-up
docker-up:
	@echo "Starting services with Docker Compose..."
	docker-compose up -d

# Docker Compose停止
.PHONY: docker-down
docker-down:
	@echo "Stopping services with Docker Compose..."
	docker-compose down

# 代码质量检查
.PHONY: lint
lint:
	@echo "Running linter..."
	@if command -v golangci-lint > /dev/null; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not found. Please install it first."; \
		echo "curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b \$$(go env GOPATH)/bin v1.54.2"; \
	fi

# 安全检查
.PHONY: security
security:
	@echo "Running security check..."
	@if command -v gosec > /dev/null; then \
		gosec ./...; \
	else \
		echo "gosec not found. Installing..."; \
		$(GOGET) github.com/securecodewarrior/gosec/v2/cmd/gosec; \
		gosec ./...; \
	fi

# 性能测试
.PHONY: bench
bench:
	@echo "Running benchmarks..."
	$(GOTEST) -bench=. -benchmem ./...

# 完整的CI检查
.PHONY: ci
ci: fmt vet test lint security
	@echo "All CI checks passed!"

# 帮助信息
.PHONY: help
help:
	@echo "Available commands:"
	@echo "  build         - Build the application"
	@echo "  run           - Run the application"
	@echo "  dev           - Run in development mode with hot reload"
	@echo "  clean         - Clean build files"
	@echo "  test          - Run tests"
	@echo "  test-coverage - Run tests with coverage report"
	@echo "  fmt           - Format code"
	@echo "  vet           - Run go vet"
	@echo "  deps          - Download dependencies"
	@echo "  deps-update   - Update dependencies"
	@echo "  docs          - Generate API documentation"
	@echo "  migrate       - Run database migrations"
	@echo "  seed          - Create seed data"
	@echo "  docker-build  - Build Docker image"
	@echo "  docker-run    - Run Docker container"
	@echo "  docker-up     - Start with Docker Compose"
	@echo "  docker-down   - Stop Docker Compose"
	@echo "  lint          - Run linter"
	@echo "  security      - Run security check"
	@echo "  bench         - Run benchmarks"
	@echo "  ci            - Run all CI checks"
	@echo "  help          - Show this help message"
