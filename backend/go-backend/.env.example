# Server Configuration
SERVER_HOST=localhost
SERVER_PORT=8080
SERVER_MODE=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=dbmonitor
DB_SSLMODE=disable

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRE_HOURS=24
JWT_ISSUER=db-monitor-platform

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# API Keys for monitoring agents
API_KEYS=agent-key-1,agent-key-2

# Metrics retention (days)
METRICS_RETENTION_DAYS=30

# Alerts retention (days)
ALERTS_RETENTION_DAYS=90
