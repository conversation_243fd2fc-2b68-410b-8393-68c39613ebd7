# 数据库监控平台 - Go后端

这是数据库监控平台的Go后端服务，提供RESTful API和WebSocket实时数据推送功能。

## 🚀 快速开始

### 环境要求

- Go 1.21+
- PostgreSQL 12+
- Redis 6+

### 安装依赖

```bash
make deps
```

### 配置

1. 复制配置文件：
```bash
cp configs/config.example.yaml configs/config.yaml
```

2. 修改配置文件中的数据库连接信息：
```yaml
database:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "your-password"
  dbname: "dbmonitor"
```

### 运行

#### 开发模式（推荐）
```bash
make dev
```

#### 普通运行
```bash
make run
```

#### 构建并运行
```bash
make build
./build/db-monitor-platform
```

## 📁 项目结构

```
backend/go-backend/
├── cmd/
│   └── server/
│       └── main.go              # 应用入口
├── internal/
│   ├── config/
│   │   └── config.go            # 配置管理
│   ├── models/                  # 数据模型
│   ├── handlers/                # HTTP处理器
│   ├── middleware/              # 中间件
│   ├── services/                # 业务逻辑
│   ├── repository/              # 数据访问层
│   └── utils/                   # 工具函数
├── pkg/
│   ├── database/                # 数据库连接
│   ├── redis/                   # Redis客户端
│   └── logger/                  # 日志系统
├── api/
│   └── swagger/                 # API文档
├── configs/
│   ├── config.yaml              # 配置文件
│   └── config.example.yaml      # 配置示例
├── scripts/                     # 脚本文件
├── tests/                       # 测试文件
├── docker/                      # Docker相关
├── Makefile                     # 构建脚本
└── README.md                    # 项目说明
```

## 🔧 开发命令

### 基础命令
- `make build` - 构建应用
- `make run` - 运行应用
- `make dev` - 开发模式运行（热重载）
- `make clean` - 清理构建文件

### 测试命令
- `make test` - 运行测试
- `make test-coverage` - 运行测试并生成覆盖率报告
- `make bench` - 运行性能测试

### 代码质量
- `make fmt` - 格式化代码
- `make vet` - 代码检查
- `make lint` - 代码规范检查
- `make security` - 安全检查
- `make ci` - 完整CI检查

### 依赖管理
- `make deps` - 下载依赖
- `make deps-update` - 更新依赖

### 数据库
- `make migrate` - 运行数据库迁移
- `make seed` - 创建种子数据

### Docker
- `make docker-build` - 构建Docker镜像
- `make docker-run` - 运行Docker容器
- `make docker-up` - 启动Docker Compose
- `make docker-down` - 停止Docker Compose

### 文档
- `make docs` - 生成API文档

## 🌐 API端点

### 健康检查
- `GET /health` - 服务健康状态

### 认证
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/logout` - 用户登出

### 用户管理
- `GET /api/v1/profile` - 获取用户信息
- `PUT /api/v1/profile` - 更新用户信息

### 数据库管理
- `GET /api/v1/databases` - 获取数据库列表
- `POST /api/v1/databases` - 创建数据库实例
- `GET /api/v1/databases/:id` - 获取数据库详情
- `PUT /api/v1/databases/:id` - 更新数据库实例
- `DELETE /api/v1/databases/:id` - 删除数据库实例
- `POST /api/v1/databases/:id/test` - 测试数据库连接

### 监控数据
- `GET /api/v1/metrics/:db_id` - 获取监控指标
- `GET /api/v1/metrics/:db_id/realtime` - 获取实时监控数据
- `GET /api/v1/metrics/:db_id/history` - 获取历史监控数据

### 告警管理
- `GET /api/v1/alerts` - 获取告警列表
- `POST /api/v1/alerts` - 创建告警规则
- `PUT /api/v1/alerts/:id` - 更新告警规则
- `DELETE /api/v1/alerts/:id` - 删除告警规则
- `GET /api/v1/alerts/history` - 获取告警历史
- `POST /api/v1/alerts/:id/resolve` - 解决告警

### WebSocket
- `GET /ws` - WebSocket连接

## 🔒 安全特性

- JWT认证
- 密码加密（bcrypt）
- CORS跨域保护
- 请求日志记录
- 错误恢复机制

## 📊 监控和日志

- 结构化日志（JSON格式）
- 请求追踪
- 性能监控
- 错误报告

## 🚀 部署

### Docker部署
```bash
make docker-build
make docker-run
```

### Docker Compose部署
```bash
make docker-up
```

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License

## 📞 联系方式

如有问题，请创建Issue或联系开发团队。
