# 数据库监控平台配置文件

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  mode: "debug" # debug, release, test
  read_timeout: 60s
  write_timeout: 60s

# 数据库配置
database:
  host: "localhost"
  port: 5432
  user: "dbmonitor"
  password: "dbmonitor123"
  dbname: "db_monitor"
  sslmode: "disable"
  timezone: "Asia/Shanghai"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600s

# Redis配置
redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  pool_size: 10
  min_idle_conns: 5

# JWT配置
jwt:
  secret: "your-secret-key-change-in-production"
  expire_hours: 24
  issuer: "db-monitor-platform"

# 日志配置
log:
  level: "info" # debug, info, warn, error
  format: "json" # json, text
  output: "stdout" # stdout, file
  file_path: "logs/app.log"
  max_size: 100 # MB
  max_backups: 3
  max_age: 28 # days

# CORS配置
cors:
  allow_origins: ["http://localhost:3000", "http://localhost:5173"]
  allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  allow_headers: ["Origin", "Content-Type", "Authorization"]
  expose_headers: ["Content-Length"]
  allow_credentials: true
  max_age: 12h

# 监控配置
monitoring:
  metrics_interval: 30s # 监控数据收集间隔
  alert_check_interval: 60s # 告警检查间隔
  data_retention_days: 30 # 数据保留天数

# WebSocket配置
websocket:
  read_buffer_size: 1024
  write_buffer_size: 1024
  check_origin: true
  ping_period: 54s
  pong_wait: 60s
  write_wait: 10s

# 告警配置
alert:
  email:
    smtp_host: "smtp.gmail.com"
    smtp_port: 587
    username: ""
    password: ""
    from: "<EMAIL>"
  webhook:
    timeout: 30s
    retry_times: 3
