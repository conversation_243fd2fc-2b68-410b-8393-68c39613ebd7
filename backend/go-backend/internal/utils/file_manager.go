package utils

import (
	"fmt"
	"os"
	"path/filepath"
	"time"
)

// FileManager 文件管理器接口
type FileManager interface {
	GenerateFilePath(executionID uint, format string) string
	EnsureDirectory(path string) error
	CleanupOldFiles(olderThan time.Duration) error
	GetFileInfo(filePath string) (*FileInfo, error)
	GetFileSize(filePath string) (int64, error)
	FileExists(filePath string) bool
}

// FileInfo 文件信息结构
type FileInfo struct {
	Path         string    `json:"path"`
	Size         int64     `json:"size"`
	CreatedAt    time.Time `json:"created_at"`
	IsAccessible bool      `json:"is_accessible"`
}

// fileManagerImpl 文件管理器实现
type fileManagerImpl struct {
	basePath string
}

// NewFileManager 创建文件管理器实例
func NewFileManager(basePath string) FileManager {
	return &fileManagerImpl{
		basePath: basePath,
	}
}

// GenerateFilePath 生成文件路径
func (fm *fileManagerImpl) GenerateFilePath(executionID uint, format string) string {
	now := time.Now()
	year := now.Format("2006")
	month := now.Format("01")
	timestamp := now.Format("20060102_150405")
	
	filename := fmt.Sprintf("report_%d_%s.%s", executionID, timestamp, format)
	return filepath.Join(fm.basePath, year, month, filename)
}

// EnsureDirectory 确保目录存在
func (fm *fileManagerImpl) EnsureDirectory(path string) error {
	dir := filepath.Dir(path)
	return os.MkdirAll(dir, 0755)
}

// CleanupOldFiles 清理过期文件
func (fm *fileManagerImpl) CleanupOldFiles(olderThan time.Duration) error {
	cutoffTime := time.Now().Add(-olderThan)
	
	return filepath.Walk(fm.basePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		// 跳过目录
		if info.IsDir() {
			return nil
		}
		
		// 检查文件是否过期
		if info.ModTime().Before(cutoffTime) {
			if err := os.Remove(path); err != nil {
				// 记录错误但继续清理其他文件
				fmt.Printf("Failed to remove old file %s: %v\n", path, err)
			}
		}
		
		return nil
	})
}

// GetFileInfo 获取文件信息
func (fm *fileManagerImpl) GetFileInfo(filePath string) (*FileInfo, error) {
	info, err := os.Stat(filePath)
	if err != nil {
		return nil, err
	}
	
	return &FileInfo{
		Path:         filePath,
		Size:         info.Size(),
		CreatedAt:    info.ModTime(),
		IsAccessible: true,
	}, nil
}

// GetFileSize 获取文件大小
func (fm *fileManagerImpl) GetFileSize(filePath string) (int64, error) {
	info, err := os.Stat(filePath)
	if err != nil {
		return 0, err
	}
	return info.Size(), nil
}

// FileExists 检查文件是否存在
func (fm *fileManagerImpl) FileExists(filePath string) bool {
	_, err := os.Stat(filePath)
	return !os.IsNotExist(err)
}

// ValidateFilePath 验证文件路径安全性
func ValidateFilePath(filePath, basePath string) error {
	// 获取绝对路径
	absFilePath, err := filepath.Abs(filePath)
	if err != nil {
		return fmt.Errorf("invalid file path: %w", err)
	}
	
	absBasePath, err := filepath.Abs(basePath)
	if err != nil {
		return fmt.Errorf("invalid base path: %w", err)
	}
	
	// 检查路径遍历攻击
	if !filepath.HasPrefix(absFilePath, absBasePath) {
		return fmt.Errorf("file path outside of allowed directory")
	}
	
	return nil
}

// GetReportStoragePath 获取报表存储路径
func GetReportStoragePath() string {
	// 从环境变量获取，如果没有则使用默认值
	if path := os.Getenv("REPORT_STORAGE_PATH"); path != "" {
		return path
	}
	return "./reports"
}

// InitializeReportStorage 初始化报表存储目录
func InitializeReportStorage() error {
	storagePath := GetReportStoragePath()
	
	// 创建主目录
	if err := os.MkdirAll(storagePath, 0755); err != nil {
		return fmt.Errorf("failed to create report storage directory: %w", err)
	}
	
	// 创建临时目录
	tempPath := filepath.Join(storagePath, "temp")
	if err := os.MkdirAll(tempPath, 0755); err != nil {
		return fmt.Errorf("failed to create temp directory: %w", err)
	}
	
	// 创建归档目录
	archivePath := filepath.Join(storagePath, "archive")
	if err := os.MkdirAll(archivePath, 0755); err != nil {
		return fmt.Errorf("failed to create archive directory: %w", err)
	}
	
	return nil
}
