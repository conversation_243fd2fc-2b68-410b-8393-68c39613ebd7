package utils

import (
	"errors"
	"fmt"
	"reflect"
	"strings"

	"github.com/go-playground/validator/v10"
)

// Validator 验证器实例
var Validator *validator.Validate

// init 初始化验证器
func init() {
	Validator = validator.New()
	
	// 注册自定义验证器
	registerCustomValidators()
	
	// 注册字段名标签函数
	Validator.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})
}

// registerCustomValidators 注册自定义验证器
func registerCustomValidators() {
	// 注册密码强度验证器
	Validator.RegisterValidation("password", validatePassword)
	
	// 注册用户角色验证器
	Validator.RegisterValidation("user_role", validateUserRole)
	
	// 注册数据库类型验证器
	Validator.RegisterValidation("db_type", validateDatabaseType)
}

// validatePassword 验证密码强度
func validatePassword(fl validator.FieldLevel) bool {
	password := fl.Field().String()
	return ValidatePasswordStrength(password) == nil
}

// validateUserRole 验证用户角色
func validateUserRole(fl validator.FieldLevel) bool {
	role := fl.Field().String()
	validRoles := []string{"admin", "user", "viewer"}
	
	for _, validRole := range validRoles {
		if role == validRole {
			return true
		}
	}
	return false
}

// validateDatabaseType 验证数据库类型
func validateDatabaseType(fl validator.FieldLevel) bool {
	dbType := fl.Field().String()
	validTypes := []string{"mysql", "postgresql", "mongodb", "redis"}
	
	for _, validType := range validTypes {
		if dbType == validType {
			return true
		}
	}
	return false
}

// ValidateStruct 验证结构体
func ValidateStruct(s interface{}) error {
	return Validator.Struct(s)
}

// ValidationError 验证错误结构
type ValidationError struct {
	Field   string `json:"field"`
	Tag     string `json:"tag"`
	Value   string `json:"value"`
	Message string `json:"message"`
}

// FormatValidationErrors 格式化验证错误
func FormatValidationErrors(err error) []ValidationError {
	var validationErrors []ValidationError
	
	if validationErr, ok := err.(validator.ValidationErrors); ok {
		for _, fieldError := range validationErr {
			validationError := ValidationError{
				Field: fieldError.Field(),
				Tag:   fieldError.Tag(),
				Value: fmt.Sprintf("%v", fieldError.Value()),
				Message: getErrorMessage(fieldError),
			}
			validationErrors = append(validationErrors, validationError)
		}
	}
	
	return validationErrors
}

// getErrorMessage 获取错误消息
func getErrorMessage(fe validator.FieldError) string {
	switch fe.Tag() {
	case "required":
		return fmt.Sprintf("%s is required", fe.Field())
	case "email":
		return fmt.Sprintf("%s must be a valid email address", fe.Field())
	case "min":
		return fmt.Sprintf("%s must be at least %s characters long", fe.Field(), fe.Param())
	case "max":
		return fmt.Sprintf("%s must be at most %s characters long", fe.Field(), fe.Param())
	case "oneof":
		return fmt.Sprintf("%s must be one of: %s", fe.Field(), fe.Param())
	case "password":
		return fmt.Sprintf("%s must meet password requirements", fe.Field())
	case "user_role":
		return fmt.Sprintf("%s must be one of: admin, user, viewer", fe.Field())
	case "db_type":
		return fmt.Sprintf("%s must be one of: mysql, postgresql, mongodb, redis", fe.Field())
	case "gte":
		return fmt.Sprintf("%s must be greater than or equal to %s", fe.Field(), fe.Param())
	case "lte":
		return fmt.Sprintf("%s must be less than or equal to %s", fe.Field(), fe.Param())
	case "url":
		return fmt.Sprintf("%s must be a valid URL", fe.Field())
	case "hostname_rfc1123":
		return fmt.Sprintf("%s must be a valid hostname", fe.Field())
	case "ip":
		return fmt.Sprintf("%s must be a valid IP address", fe.Field())
	default:
		return fmt.Sprintf("%s is invalid", fe.Field())
	}
}

// ValidateEmail 验证邮箱格式
func ValidateEmail(email string) error {
	if email == "" {
		return errors.New("email is required")
	}
	
	if err := Validator.Var(email, "email"); err != nil {
		return errors.New("invalid email format")
	}
	
	return nil
}

// ValidatePassword 验证密码
func ValidatePassword(password string) error {
	if password == "" {
		return errors.New("password is required")
	}
	
	return ValidatePasswordStrength(password)
}

// ValidateUserRole 验证用户角色
func ValidateUserRole(role string) error {
	if role == "" {
		return errors.New("role is required")
	}
	
	if err := Validator.Var(role, "user_role"); err != nil {
		return errors.New("invalid user role")
	}
	
	return nil
}

// SanitizeInput 清理输入数据
func SanitizeInput(input string) string {
	// 移除前后空格
	input = strings.TrimSpace(input)
	
	// 可以添加更多清理逻辑，如HTML转义等
	
	return input
}

// ValidateAndSanitize 验证并清理结构体字段
func ValidateAndSanitize(s interface{}) error {
	// 首先清理字符串字段
	sanitizeStructFields(s)
	
	// 然后验证
	return ValidateStruct(s)
}

// sanitizeStructFields 清理结构体字符串字段
func sanitizeStructFields(s interface{}) {
	v := reflect.ValueOf(s)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	
	if v.Kind() != reflect.Struct {
		return
	}
	
	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		if field.Kind() == reflect.String && field.CanSet() {
			sanitized := SanitizeInput(field.String())
			field.SetString(sanitized)
		}
	}
}
