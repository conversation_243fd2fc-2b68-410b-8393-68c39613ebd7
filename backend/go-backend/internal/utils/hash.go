package utils

import (
	"crypto/rand"
	"crypto/subtle"
	"encoding/base64"
	"errors"
	"fmt"
	"strings"

	"golang.org/x/crypto/argon2"
	"golang.org/x/crypto/bcrypt"
)

// PasswordHasher 密码哈希接口
type PasswordHasher interface {
	HashPassword(password string) (string, error)
	CheckPassword(password, hash string) bool
}

// BcryptHasher bcrypt密码哈希器
type BcryptHasher struct {
	cost int
}

// NewBcryptHasher 创建bcrypt哈希器
func NewBcryptHasher(cost int) *BcryptHasher {
	if cost < bcrypt.MinCost || cost > bcrypt.MaxCost {
		cost = bcrypt.DefaultCost
	}
	return &BcryptHasher{cost: cost}
}

// HashPassword 使用bcrypt加密密码
func (b *BcryptHasher) HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), b.cost)
	return string(bytes), err
}

// CheckPassword 使用bcrypt验证密码
func (b *BcryptHasher) CheckPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// Argon2Hasher Argon2密码哈希器（更安全，推荐用于新项目）
type Argon2Hasher struct {
	memory      uint32
	iterations  uint32
	parallelism uint8
	saltLength  uint32
	keyLength   uint32
}

// NewArgon2Hasher 创建Argon2哈希器
func NewArgon2Hasher() *Argon2Hasher {
	return &Argon2Hasher{
		memory:      64 * 1024, // 64 MB
		iterations:  3,
		parallelism: 2,
		saltLength:  16,
		keyLength:   32,
	}
}

// HashPassword 使用Argon2加密密码
func (a *Argon2Hasher) HashPassword(password string) (string, error) {
	// 生成随机盐
	salt, err := generateRandomBytes(a.saltLength)
	if err != nil {
		return "", err
	}

	// 生成哈希
	hash := argon2.IDKey([]byte(password), salt, a.iterations, a.memory, a.parallelism, a.keyLength)

	// 编码为base64
	b64Salt := base64.RawStdEncoding.EncodeToString(salt)
	b64Hash := base64.RawStdEncoding.EncodeToString(hash)

	// 格式: $argon2id$v=19$m=65536,t=3,p=2$salt$hash
	encodedHash := fmt.Sprintf("$argon2id$v=%d$m=%d,t=%d,p=%d$%s$%s",
		argon2.Version, a.memory, a.iterations, a.parallelism, b64Salt, b64Hash)

	return encodedHash, nil
}

// CheckPassword 使用Argon2验证密码
func (a *Argon2Hasher) CheckPassword(password, encodedHash string) bool {
	// 解析哈希
	memory, iterations, parallelism, salt, hash, err := decodeArgon2Hash(encodedHash)
	if err != nil {
		return false
	}

	// 使用相同参数计算哈希
	otherHash := argon2.IDKey([]byte(password), salt, iterations, memory, parallelism, a.keyLength)

	// 使用constant time比较防止时序攻击
	return subtle.ConstantTimeCompare(hash, otherHash) == 1
}

// generateRandomBytes 生成随机字节
func generateRandomBytes(n uint32) ([]byte, error) {
	b := make([]byte, n)
	_, err := rand.Read(b)
	if err != nil {
		return nil, err
	}
	return b, nil
}

// decodeArgon2Hash 解码Argon2哈希
func decodeArgon2Hash(encodedHash string) (memory uint32, iterations uint32, parallelism uint8, salt, hash []byte, err error) {
	vals := strings.Split(encodedHash, "$")
	if len(vals) != 6 {
		return 0, 0, 0, nil, nil, errors.New("invalid hash format")
	}

	var version int
	_, err = fmt.Sscanf(vals[2], "v=%d", &version)
	if err != nil {
		return 0, 0, 0, nil, nil, err
	}
	if version != argon2.Version {
		return 0, 0, 0, nil, nil, errors.New("incompatible version")
	}

	_, err = fmt.Sscanf(vals[3], "m=%d,t=%d,p=%d", &memory, &iterations, &parallelism)
	if err != nil {
		return 0, 0, 0, nil, nil, err
	}

	salt, err = base64.RawStdEncoding.DecodeString(vals[4])
	if err != nil {
		return 0, 0, 0, nil, nil, err
	}

	hash, err = base64.RawStdEncoding.DecodeString(vals[5])
	if err != nil {
		return 0, 0, 0, nil, nil, err
	}

	return memory, iterations, parallelism, salt, hash, nil
}

// DefaultPasswordHasher 默认密码哈希器（使用bcrypt）
var DefaultPasswordHasher = NewBcryptHasher(bcrypt.DefaultCost)

// HashPassword 使用默认哈希器加密密码
func HashPassword(password string) (string, error) {
	return DefaultPasswordHasher.HashPassword(password)
}

// CheckPassword 使用默认哈希器验证密码
func CheckPassword(password, hash string) bool {
	return DefaultPasswordHasher.CheckPassword(password, hash)
}

// ValidatePasswordStrength 验证密码强度
func ValidatePasswordStrength(password string) error {
	if len(password) < 6 {
		return errors.New("password must be at least 6 characters long")
	}

	if len(password) > 128 {
		return errors.New("password must be less than 128 characters long")
	}

	// 可以添加更多密码强度检查
	// 例如：必须包含大小写字母、数字、特殊字符等

	return nil
}
