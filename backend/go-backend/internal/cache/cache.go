package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"db-monitor-platform/internal/models"
	"db-monitor-platform/pkg/logger"

	"github.com/go-redis/redis/v8"
)

// CacheService 缓存服务
type CacheService struct {
	client *redis.Client
}

// NewCacheService 创建缓存服务实例
func NewCacheService(client *redis.Client) *CacheService {
	return &CacheService{
		client: client,
	}
}

// 缓存键前缀
const (
	MetricsCachePrefix     = "metrics:"
	DatabaseCachePrefix    = "database:"
	AlertCachePrefix       = "alert:"
	UserCachePrefix        = "user:"
	StatsCachePrefix       = "stats:"
	RealtimeCachePrefix    = "realtime:"
	AggregatedCachePrefix  = "aggregated:"
)

// 缓存过期时间
const (
	ShortCacheTTL    = 1 * time.Minute     // 短期缓存：1分钟
	MediumCacheTTL   = 5 * time.Minute     // 中期缓存：5分钟
	LongCacheTTL     = 30 * time.Minute    // 长期缓存：30分钟
	StaticCacheTTL   = 2 * time.Hour       // 静态缓存：2小时
	RealtimeCacheTTL = 30 * time.Second    // 实时缓存：30秒
)

// ===== 指标缓存 =====

// SetMetrics 缓存指标数据
func (c *CacheService) SetMetrics(dbID uint, metrics []models.Metric, ttl time.Duration) error {
	key := fmt.Sprintf("%s%d", MetricsCachePrefix, dbID)
	data, err := json.Marshal(metrics)
	if err != nil {
		return fmt.Errorf("failed to marshal metrics: %w", err)
	}

	ctx := context.Background()
	return c.client.Set(ctx, key, data, ttl).Err()
}

// GetMetrics 获取缓存的指标数据
func (c *CacheService) GetMetrics(dbID uint) ([]models.Metric, error) {
	key := fmt.Sprintf("%s%d", MetricsCachePrefix, dbID)
	ctx := context.Background()
	
	data, err := c.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存未命中
		}
		return nil, fmt.Errorf("failed to get metrics from cache: %w", err)
	}

	var metrics []models.Metric
	err = json.Unmarshal([]byte(data), &metrics)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal metrics: %w", err)
	}

	return metrics, nil
}

// SetLatestMetrics 缓存最新指标数据
func (c *CacheService) SetLatestMetrics(dbID uint, metrics []models.Metric) error {
	key := fmt.Sprintf("%slatest:%d", MetricsCachePrefix, dbID)
	data, err := json.Marshal(metrics)
	if err != nil {
		return fmt.Errorf("failed to marshal latest metrics: %w", err)
	}

	ctx := context.Background()
	return c.client.Set(ctx, key, data, RealtimeCacheTTL).Err()
}

// GetLatestMetrics 获取缓存的最新指标数据
func (c *CacheService) GetLatestMetrics(dbID uint) ([]models.Metric, error) {
	key := fmt.Sprintf("%slatest:%d", MetricsCachePrefix, dbID)
	ctx := context.Background()
	
	data, err := c.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get latest metrics from cache: %w", err)
	}

	var metrics []models.Metric
	err = json.Unmarshal([]byte(data), &metrics)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal latest metrics: %w", err)
	}

	return metrics, nil
}

// ===== 数据库实例缓存 =====

// SetDatabase 缓存数据库实例
func (c *CacheService) SetDatabase(database *models.DatabaseInstance) error {
	key := fmt.Sprintf("%s%d", DatabaseCachePrefix, database.ID)
	data, err := json.Marshal(database)
	if err != nil {
		return fmt.Errorf("failed to marshal database: %w", err)
	}

	ctx := context.Background()
	return c.client.Set(ctx, key, data, MediumCacheTTL).Err()
}

// GetDatabase 获取缓存的数据库实例
func (c *CacheService) GetDatabase(id uint) (*models.DatabaseInstance, error) {
	key := fmt.Sprintf("%s%d", DatabaseCachePrefix, id)
	ctx := context.Background()
	
	data, err := c.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get database from cache: %w", err)
	}

	var database models.DatabaseInstance
	err = json.Unmarshal([]byte(data), &database)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal database: %w", err)
	}

	return &database, nil
}

// InvalidateDatabase 使数据库缓存失效
func (c *CacheService) InvalidateDatabase(id uint) error {
	key := fmt.Sprintf("%s%d", DatabaseCachePrefix, id)
	ctx := context.Background()
	return c.client.Del(ctx, key).Err()
}

// ===== 统计数据缓存 =====

// SetDatabaseStats 缓存数据库统计
func (c *CacheService) SetDatabaseStats(stats interface{}) error {
	key := fmt.Sprintf("%sdatabase", StatsCachePrefix)
	data, err := json.Marshal(stats)
	if err != nil {
		return fmt.Errorf("failed to marshal database stats: %w", err)
	}

	ctx := context.Background()
	return c.client.Set(ctx, key, data, MediumCacheTTL).Err()
}

// GetDatabaseStats 获取缓存的数据库统计
func (c *CacheService) GetDatabaseStats() (interface{}, error) {
	key := fmt.Sprintf("%sdatabase", StatsCachePrefix)
	ctx := context.Background()
	
	data, err := c.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get database stats from cache: %w", err)
	}

	var stats interface{}
	err = json.Unmarshal([]byte(data), &stats)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal database stats: %w", err)
	}

	return stats, nil
}

// SetAlertStats 缓存告警统计
func (c *CacheService) SetAlertStats(stats interface{}) error {
	key := fmt.Sprintf("%salert", StatsCachePrefix)
	data, err := json.Marshal(stats)
	if err != nil {
		return fmt.Errorf("failed to marshal alert stats: %w", err)
	}

	ctx := context.Background()
	return c.client.Set(ctx, key, data, MediumCacheTTL).Err()
}

// GetAlertStats 获取缓存的告警统计
func (c *CacheService) GetAlertStats() (interface{}, error) {
	key := fmt.Sprintf("%salert", StatsCachePrefix)
	ctx := context.Background()
	
	data, err := c.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get alert stats from cache: %w", err)
	}

	var stats interface{}
	err = json.Unmarshal([]byte(data), &stats)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal alert stats: %w", err)
	}

	return stats, nil
}

// ===== 实时数据缓存 =====

// SetRealtimeMetrics 缓存实时指标数据
func (c *CacheService) SetRealtimeMetrics(metrics map[string]interface{}) error {
	key := fmt.Sprintf("%smetrics", RealtimeCachePrefix)
	data, err := json.Marshal(metrics)
	if err != nil {
		return fmt.Errorf("failed to marshal realtime metrics: %w", err)
	}

	ctx := context.Background()
	return c.client.Set(ctx, key, data, RealtimeCacheTTL).Err()
}

// GetRealtimeMetrics 获取缓存的实时指标数据
func (c *CacheService) GetRealtimeMetrics() (map[string]interface{}, error) {
	key := fmt.Sprintf("%smetrics", RealtimeCachePrefix)
	ctx := context.Background()
	
	data, err := c.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get realtime metrics from cache: %w", err)
	}

	var metrics map[string]interface{}
	err = json.Unmarshal([]byte(data), &metrics)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal realtime metrics: %w", err)
	}

	return metrics, nil
}

// ===== 聚合数据缓存 =====

// SetAggregatedMetrics 缓存聚合指标数据
func (c *CacheService) SetAggregatedMetrics(key string, data interface{}, ttl time.Duration) error {
	cacheKey := fmt.Sprintf("%s%s", AggregatedCachePrefix, key)
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal aggregated metrics: %w", err)
	}

	ctx := context.Background()
	return c.client.Set(ctx, cacheKey, jsonData, ttl).Err()
}

// GetAggregatedMetrics 获取缓存的聚合指标数据
func (c *CacheService) GetAggregatedMetrics(key string) (interface{}, error) {
	cacheKey := fmt.Sprintf("%s%s", AggregatedCachePrefix, key)
	ctx := context.Background()
	
	data, err := c.client.Get(ctx, cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get aggregated metrics from cache: %w", err)
	}

	var result interface{}
	err = json.Unmarshal([]byte(data), &result)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal aggregated metrics: %w", err)
	}

	return result, nil
}

// ===== 缓存管理 =====

// InvalidatePattern 使匹配模式的缓存失效
func (c *CacheService) InvalidatePattern(pattern string) error {
	ctx := context.Background()
	keys, err := c.client.Keys(ctx, pattern).Result()
	if err != nil {
		return fmt.Errorf("failed to get keys for pattern %s: %w", pattern, err)
	}

	if len(keys) > 0 {
		err = c.client.Del(ctx, keys...).Err()
		if err != nil {
			return fmt.Errorf("failed to delete keys: %w", err)
		}
		logger.GetLogger().Infof("Invalidated %d cache keys matching pattern: %s", len(keys), pattern)
	}

	return nil
}

// ClearAll 清除所有缓存
func (c *CacheService) ClearAll() error {
	ctx := context.Background()
	return c.client.FlushDB(ctx).Err()
}

// GetCacheInfo 获取缓存信息
func (c *CacheService) GetCacheInfo() (map[string]interface{}, error) {
	ctx := context.Background()
	info, err := c.client.Info(ctx, "memory").Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get cache info: %w", err)
	}

	dbSize, err := c.client.DBSize(ctx).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get db size: %w", err)
	}

	return map[string]interface{}{
		"info":    info,
		"db_size": dbSize,
	}, nil
}

// Ping 检查缓存连接
func (c *CacheService) Ping() error {
	ctx := context.Background()
	return c.client.Ping(ctx).Err()
}
