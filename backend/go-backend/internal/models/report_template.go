package models

import (
	"time"
	"gorm.io/gorm"
)

// ReportTemplate 报表模板模型
type ReportTemplate struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"size:100;not null;comment:模板名称"`
	Description string    `json:"description" gorm:"size:500;comment:模板描述"`
	Type        string    `json:"type" gorm:"size:50;not null;comment:报表类型(performance,usage,alert)"`
	Config      string    `json:"config" gorm:"type:text;comment:模板配置JSON"`
	CreatedBy   uint      `json:"created_by" gorm:"not null;comment:创建者ID"`
	IsActive    bool      `json:"is_active" gorm:"default:true;comment:是否启用"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Creator     User      `json:"creator" gorm:"foreignKey:CreatedBy"`
}

// TableName 指定表名
func (ReportTemplate) TableName() string {
	return "report_templates"
}

// ReportExecution 报表执行记录模型
type ReportExecution struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	TemplateID   uint      `json:"template_id" gorm:"not null;comment:模板ID"`
	ExecutedBy   uint      `json:"executed_by" gorm:"not null;comment:执行者ID"`
	Status       string    `json:"status" gorm:"size:20;default:'pending';comment:执行状态(pending,running,completed,failed)"`
	Parameters   string    `json:"parameters" gorm:"type:text;comment:执行参数JSON"`
	FilePath     string    `json:"file_path" gorm:"size:500;comment:生成文件路径"`
	FileSize     int64     `json:"file_size" gorm:"default:0;comment:文件大小(字节)"`
	ErrorMessage string    `json:"error_message" gorm:"type:text;comment:错误信息"`
	StartTime    time.Time `json:"start_time" gorm:"comment:开始时间"`
	EndTime      *time.Time `json:"end_time" gorm:"comment:结束时间"`
	Duration     int64     `json:"duration" gorm:"default:0;comment:执行时长(毫秒)"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Template     ReportTemplate `json:"template" gorm:"foreignKey:TemplateID"`
	Executor     User          `json:"executor" gorm:"foreignKey:ExecutedBy"`
}

// TableName 指定表名
func (ReportExecution) TableName() string {
	return "report_executions"
}

// IsCompleted 检查是否已完成
func (re *ReportExecution) IsCompleted() bool {
	return re.Status == "completed" || re.Status == "failed"
}

// IsSuccessful 检查是否成功完成
func (re *ReportExecution) IsSuccessful() bool {
	return re.Status == "completed"
}

// GetDurationSeconds 获取执行时长(秒)
func (re *ReportExecution) GetDurationSeconds() float64 {
	return float64(re.Duration) / 1000.0
}

// 报表类型常量
const (
	ReportTypePerformance = "performance" // 性能报表
	ReportTypeUsage       = "usage"       // 使用情况报表
	ReportTypeAlert       = "alert"       // 告警报表
)

// 执行状态常量
const (
	ExecutionStatusPending   = "pending"   // 等待执行
	ExecutionStatusRunning   = "running"   // 执行中
	ExecutionStatusCompleted = "completed" // 已完成
	ExecutionStatusFailed    = "failed"    // 执行失败
)

// 报表格式常量
const (
	ReportFormatCSV   = "csv"   // CSV格式
	ReportFormatExcel = "excel" // Excel格式
	ReportFormatJSON  = "json"  // JSON格式
	ReportFormatPDF   = "pdf"   // PDF格式
)

// ValidateReportType 验证报表类型
func ValidateReportType(reportType string) bool {
	validTypes := []string{ReportTypePerformance, ReportTypeUsage, ReportTypeAlert}
	for _, validType := range validTypes {
		if reportType == validType {
			return true
		}
	}
	return false
}

// ValidateExecutionStatus 验证执行状态
func ValidateExecutionStatus(status string) bool {
	validStatuses := []string{ExecutionStatusPending, ExecutionStatusRunning, ExecutionStatusCompleted, ExecutionStatusFailed}
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// ValidateReportFormat 验证报表格式
func ValidateReportFormat(format string) bool {
	validFormats := []string{ReportFormatCSV, ReportFormatExcel, ReportFormatJSON, ReportFormatPDF}
	for _, validFormat := range validFormats {
		if format == validFormat {
			return true
		}
	}
	return false
}
