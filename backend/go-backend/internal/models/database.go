package models

import (
	"time"

	"gorm.io/gorm"
)

// DatabaseInstance 数据库实例模型
type DatabaseInstance struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	Name         string         `json:"name" gorm:"not null;size:100" validate:"required,min=2,max=50"`
	Type         string         `json:"type" gorm:"not null;size:20" validate:"required,oneof=mysql postgresql mongodb redis"`
	Host         string         `json:"host" gorm:"not null;size:255" validate:"required,hostname_rfc1123|ip"`
	Port         int            `json:"port" gorm:"not null" validate:"required,min=1,max=65535"`
	DatabaseName string         `json:"database_name" gorm:"size:100"`
	Username     string         `json:"username" gorm:"size:100"`
	Password     string         `json:"-" gorm:"size:255"` // 加密存储，不返回给前端
	Status       string         `json:"status" gorm:"default:active;size:20" validate:"oneof=active inactive error"`
	Description  string         `json:"description" gorm:"size:500"`
	Tags         string         `json:"tags" gorm:"size:500"` // JSON格式存储标签
	IsMonitored  bool           `json:"is_monitored" gorm:"default:true"`
	CreatedBy    uint           `json:"created_by" gorm:"not null"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User        User         `json:"user,omitempty" gorm:"foreignKey:CreatedBy"`
	Metrics     []Metric     `json:"metrics,omitempty" gorm:"foreignKey:DatabaseID"`
	AlertRules  []AlertRule  `json:"alert_rules,omitempty" gorm:"foreignKey:DatabaseID"`
	AlertEvents []AlertEvent `json:"alert_events,omitempty" gorm:"foreignKey:DatabaseID"`
}

// TableName 指定表名
func (DatabaseInstance) TableName() string {
	return "database_instances"
}

// BeforeCreate 创建前钩子
func (d *DatabaseInstance) BeforeCreate(tx *gorm.DB) error {
	// 可以在这里添加密码加密等逻辑
	return nil
}

// DatabaseCreateRequest 数据库实例创建请求
type DatabaseCreateRequest struct {
	Name         string `json:"name" validate:"required,min=2,max=50"`
	Type         string `json:"type" validate:"required,oneof=mysql postgresql mongodb redis"`
	Host         string `json:"host" validate:"required"`
	Port         int    `json:"port" validate:"required,min=1,max=65535"`
	DatabaseName string `json:"database_name,omitempty"`
	Username     string `json:"username,omitempty"`
	Password     string `json:"password,omitempty"`
	Description  string `json:"description,omitempty" validate:"max=500"`
	Tags         string `json:"tags,omitempty"`
	IsMonitored  *bool  `json:"is_monitored,omitempty"`
}

// DatabaseUpdateRequest 数据库实例更新请求
type DatabaseUpdateRequest struct {
	Name         string `json:"name,omitempty" validate:"omitempty,min=2,max=50"`
	Host         string `json:"host,omitempty"`
	Port         *int   `json:"port,omitempty" validate:"omitempty,min=1,max=65535"`
	DatabaseName string `json:"database_name,omitempty"`
	Username     string `json:"username,omitempty"`
	Password     string `json:"password,omitempty"`
	Description  string `json:"description,omitempty" validate:"max=500"`
	Tags         string `json:"tags,omitempty"`
	IsMonitored  *bool  `json:"is_monitored,omitempty"`
}

// DatabaseResponse 数据库实例响应
type DatabaseResponse struct {
	ID           uint      `json:"id"`
	Name         string    `json:"name"`
	Type         string    `json:"type"`
	Host         string    `json:"host"`
	Port         int       `json:"port"`
	DatabaseName string    `json:"database_name"`
	Username     string    `json:"username"`
	Status       string    `json:"status"`
	Description  string    `json:"description"`
	Tags         string    `json:"tags"`
	IsMonitored  bool      `json:"is_monitored"`
	CreatedBy    uint      `json:"created_by"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	User         *User     `json:"user,omitempty"`
}

// ToResponse 转换为响应格式
func (d *DatabaseInstance) ToResponse() *DatabaseResponse {
	return &DatabaseResponse{
		ID:           d.ID,
		Name:         d.Name,
		Type:         d.Type,
		Host:         d.Host,
		Port:         d.Port,
		DatabaseName: d.DatabaseName,
		Username:     d.Username,
		Status:       d.Status,
		Description:  d.Description,
		Tags:         d.Tags,
		IsMonitored:  d.IsMonitored,
		CreatedBy:    d.CreatedBy,
		CreatedAt:    d.CreatedAt,
		UpdatedAt:    d.UpdatedAt,
	}
}

// GetConnectionString 获取连接字符串（不包含密码）
func (d *DatabaseInstance) GetConnectionString() string {
	switch d.Type {
	case "postgresql":
		return "postgresql://" + d.Username + "@" + d.Host + ":" + string(rune(d.Port)) + "/" + d.DatabaseName
	case "mysql":
		return d.Username + "@tcp(" + d.Host + ":" + string(rune(d.Port)) + ")/" + d.DatabaseName
	case "mongodb":
		return "mongodb://" + d.Host + ":" + string(rune(d.Port)) + "/" + d.DatabaseName
	case "redis":
		return "redis://" + d.Host + ":" + string(rune(d.Port))
	default:
		return ""
	}
}

// IsActive 检查数据库实例是否活跃
func (d *DatabaseInstance) IsActive() bool {
	return d.Status == "active"
}

// CanConnect 检查是否可以连接
func (d *DatabaseInstance) CanConnect() bool {
	return d.Status != "error" && d.Host != "" && d.Port > 0
}
