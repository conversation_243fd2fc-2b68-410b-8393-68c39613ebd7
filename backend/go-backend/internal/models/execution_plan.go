package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"gorm.io/gorm"
)

// ExecutionPlan 执行计划模型
type ExecutionPlan struct {
	ID              uint           `json:"id" gorm:"primaryKey"`
	QueryAnalysisID uint           `json:"query_analysis_id" gorm:"index;not null"`
	PlanID          string         `json:"plan_id" gorm:"size:50;not null"`
	TotalCost       float64        `json:"total_cost" gorm:"default:0"`
	ExecutionTime   float64        `json:"execution_time" gorm:"default:0"` // 毫秒
	PlanNodes       PlanNodeJSON   `json:"nodes" gorm:"type:jsonb"`
	PlanStatistics  StatisticsJSON `json:"statistics" gorm:"type:jsonb"`
	RawPlan         string         `json:"raw_plan" gorm:"type:text"`
	PlanFormat      string         `json:"plan_format" gorm:"size:20;default:json" validate:"oneof=json text xml"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	QueryAnalysis QueryAnalysis `json:"query_analysis,omitempty" gorm:"foreignKey:QueryAnalysisID"`
}

// TableName 指定表名
func (ExecutionPlan) TableName() string {
	return "execution_plans"
}

// PlanNode 执行计划节点
type PlanNode struct {
	ID         string     `json:"id"`
	NodeType   string     `json:"node_type"`
	Operation  string     `json:"operation"`
	TableName  string     `json:"table_name,omitempty"`
	IndexName  string     `json:"index_name,omitempty"`
	SchemaName string     `json:"schema_name,omitempty"`
	Alias      string     `json:"alias,omitempty"`

	// === 成本和行数统计 ===
	StartupCost    float64 `json:"startup_cost"`     // 启动成本
	TotalCost      float64 `json:"total_cost"`       // 总成本
	Cost           float64 `json:"cost"`             // 兼容字段
	PlanRows       int     `json:"plan_rows"`        // 计划行数
	ActualRows     int     `json:"actual_rows"`      // 实际行数
	Rows           int     `json:"rows"`             // 兼容字段
	PlanWidth      int     `json:"plan_width"`       // 计划行宽度
	Width          int     `json:"width"`            // 兼容字段

	// === 时间统计 ===
	ActualStartupTime float64 `json:"actual_startup_time"` // 实际启动时间
	ActualTotalTime   float64 `json:"actual_total_time"`   // 实际总时间
	ActualTime        float64 `json:"actual_time"`         // 兼容字段
	ActualLoops       int     `json:"actual_loops"`        // 实际循环次数

	// === 条件和过滤 ===
	Condition      string `json:"condition,omitempty"`       // 连接条件
	Filter         string `json:"filter,omitempty"`          // 过滤条件
	IndexCondition string `json:"index_condition,omitempty"` // 索引条件

	// === 缓冲区统计 ===
	SharedHitBlocks    int `json:"shared_hit_blocks,omitempty"`    // 节点级缓冲区命中
	SharedReadBlocks   int `json:"shared_read_blocks,omitempty"`   // 节点级缓冲区读取
	SharedDirtiedBlocks int `json:"shared_dirtied_blocks,omitempty"` // 节点级缓冲区脏页
	SharedWrittenBlocks int `json:"shared_written_blocks,omitempty"` // 节点级缓冲区写入

	// === 其他信息 ===
	SortMethod     string `json:"sort_method,omitempty"`     // 排序方法
	SortSpaceUsed  int    `json:"sort_space_used,omitempty"` // 排序空间使用
	SortSpaceType  string `json:"sort_space_type,omitempty"` // 排序空间类型
	HashBuckets    int    `json:"hash_buckets,omitempty"`    // 哈希桶数
	HashBatches    int    `json:"hash_batches,omitempty"`    // 哈希批次数
	OriginalHashBatches int `json:"original_hash_batches,omitempty"` // 原始哈希批次数
	PeakMemoryUsage int    `json:"peak_memory_usage,omitempty"` // 峰值内存使用

	Children   []PlanNode `json:"children"`
}

// PlanStatistics 执行计划统计信息
type PlanStatistics struct {
	// === 时间统计 ===
	PlanningTime       float64 `json:"planning_time"`        // 规划时间(毫秒)
	ExecutionTime      float64 `json:"execution_time"`       // 执行时间(毫秒)
	TotalTime          float64 `json:"total_time"`           // 总时间(毫秒)

	// === 行数统计 ===
	EstimatedRows      int     `json:"estimated_rows"`       // 估算行数
	ActualRows         int     `json:"actual_rows"`          // 实际行数
	TotalRowsProcessed int     `json:"total_rows_processed"` // 兼容字段
	RowAccuracy        float64 `json:"row_accuracy"`         // 估算准确性%

	// === 缓冲区统计 ===
	SharedHitBlocks    int     `json:"shared_hit_blocks"`    // 共享缓冲区命中
	SharedReadBlocks   int     `json:"shared_read_blocks"`   // 共享缓冲区读取
	SharedDirtiedBlocks int    `json:"shared_dirtied_blocks"` // 共享缓冲区脏页
	SharedWrittenBlocks int    `json:"shared_written_blocks"` // 共享缓冲区写入
	BufferHitRatio     float64 `json:"buffer_hit_ratio"`     // 缓冲区命中率%

	// === I/O统计 ===
	LocalHitBlocks     int     `json:"local_hit_blocks"`     // 本地缓冲区命中
	LocalReadBlocks    int     `json:"local_read_blocks"`    // 本地缓冲区读取
	TempReadBlocks     int     `json:"temp_read_blocks"`     // 临时文件读取
	TempWrittenBlocks  int     `json:"temp_written_blocks"`  // 临时文件写入

	// === 兼容字段 ===
	BufferHits         int     `json:"buffer_hits"`          // 兼容旧版本
	BufferReads        int     `json:"buffer_reads"`         // 兼容旧版本
	TempFiles          int     `json:"temp_files"`           // 临时文件数
	TempSize           int     `json:"temp_size"`            // 临时文件大小(字节)

	// === 衍生指标 ===
	IOEfficiency       float64 `json:"io_efficiency"`        // I/O效率评分(0-100)
	MemoryPressure     float64 `json:"memory_pressure"`      // 内存压力指标(0-100)
	CostAccuracy       float64 `json:"cost_accuracy"`        // 成本估算准确性%
}

// PlanNodeJSON 自定义JSON类型用于存储执行计划节点
type PlanNodeJSON []PlanNode

// Value 实现driver.Valuer接口
func (p PlanNodeJSON) Value() (driver.Value, error) {
	return json.Marshal(p)
}

// Scan 实现sql.Scanner接口
func (p *PlanNodeJSON) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, &p)
}

// StatisticsJSON 自定义JSON类型用于存储统计信息
type StatisticsJSON PlanStatistics

// Value 实现driver.Valuer接口
func (s StatisticsJSON) Value() (driver.Value, error) {
	return json.Marshal(s)
}

// Scan 实现sql.Scanner接口
func (s *StatisticsJSON) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, &s)
}

// ExecutionPlanRequest 执行计划请求
type ExecutionPlanRequest struct {
	DatabaseID     uint   `json:"database_id" validate:"required"`
	SQLQuery       string `json:"sql_query" validate:"required"`
	ExplainOptions ExplainOptions `json:"explain_options"`
}

// ExplainOptions 执行计划选项
type ExplainOptions struct {
	Analyze bool   `json:"analyze" default:"false"`
	Buffers bool   `json:"buffers" default:"false"`
	Format  string `json:"format" default:"json" validate:"oneof=json text xml"`
}

// ExecutionPlanResponse 执行计划响应
type ExecutionPlanResponse struct {
	ID            uint                `json:"id"`
	PlanID        string              `json:"plan_id"`
	TotalCost     float64             `json:"total_cost"`
	ExecutionTime float64             `json:"execution_time"`
	Nodes         []PlanNode          `json:"nodes"`
	Statistics    PlanStatistics      `json:"statistics"`
	PlanFormat    string              `json:"plan_format"`
}

// ToResponse 转换为响应格式
func (ep *ExecutionPlan) ToResponse() *ExecutionPlanResponse {
	return &ExecutionPlanResponse{
		ID:            ep.ID,
		PlanID:        ep.PlanID,
		TotalCost:     ep.TotalCost,
		ExecutionTime: ep.ExecutionTime,
		Nodes:         ep.PlanNodes,
		Statistics:    PlanStatistics(ep.PlanStatistics),
		PlanFormat:    ep.PlanFormat,
	}
}
