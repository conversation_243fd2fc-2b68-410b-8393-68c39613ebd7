package models

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// BackupHistory 备份历史记录模型
type BackupHistory struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	TaskID       uint           `json:"task_id" gorm:"not null;index" validate:"required"`
	Status       string         `json:"status" gorm:"size:20;not null" validate:"required,oneof=success failed running"`
	StartTime    time.Time      `json:"start_time" gorm:"not null"`
	EndTime      *time.Time     `json:"end_time"`
	BackupSize   int64          `json:"backup_size" gorm:"default:0"` // bytes
	FilePath     string         `json:"file_path" gorm:"size:500"`
	ErrorMessage string         `json:"error_message" gorm:"type:text"`
	Duration     int            `json:"duration" gorm:"default:0"` // seconds
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Task BackupTask `json:"task,omitempty" gorm:"foreignKey:TaskID"`
}

// TableName 指定表名
func (BackupHistory) TableName() string {
	return "backup_histories"
}

// BeforeCreate 创建前钩子
func (bh *BackupHistory) BeforeCreate(tx *gorm.DB) error {
	// 如果没有设置开始时间，设置为当前时间
	if bh.StartTime.IsZero() {
		bh.StartTime = time.Now()
	}
	return nil
}

// BeforeUpdate 更新前钩子
func (bh *BackupHistory) BeforeUpdate(tx *gorm.DB) error {
	// 更新时间戳
	bh.UpdatedAt = time.Now()
	
	// 如果状态变为成功或失败，且没有结束时间，设置结束时间
	if (bh.Status == "success" || bh.Status == "failed") && bh.EndTime == nil {
		now := time.Now()
		bh.EndTime = &now
		
		// 计算持续时间
		if !bh.StartTime.IsZero() {
			bh.Duration = int(now.Sub(bh.StartTime).Seconds())
		}
	}
	
	return nil
}

// IsCompleted 检查备份是否已完成
func (bh *BackupHistory) IsCompleted() bool {
	return bh.Status == "success" || bh.Status == "failed"
}

// IsSuccess 检查备份是否成功
func (bh *BackupHistory) IsSuccess() bool {
	return bh.Status == "success"
}

// GetDurationFormatted 获取格式化的持续时间
func (bh *BackupHistory) GetDurationFormatted() string {
	if bh.Duration == 0 {
		return "0s"
	}
	
	duration := time.Duration(bh.Duration) * time.Second
	
	if duration < time.Minute {
		return fmt.Sprintf("%.0fs", duration.Seconds())
	} else if duration < time.Hour {
		return fmt.Sprintf("%.1fm", duration.Minutes())
	} else {
		return fmt.Sprintf("%.1fh", duration.Hours())
	}
}

// GetBackupSizeFormatted 获取格式化的备份大小
func (bh *BackupHistory) GetBackupSizeFormatted() string {
	if bh.BackupSize == 0 {
		return "0 B"
	}
	
	const unit = 1024
	if bh.BackupSize < unit {
		return fmt.Sprintf("%d B", bh.BackupSize)
	}
	
	div, exp := int64(unit), 0
	for n := bh.BackupSize / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	
	return fmt.Sprintf("%.1f %cB", float64(bh.BackupSize)/float64(div), "KMGTPE"[exp])
}

// MarkAsCompleted 标记备份完成
func (bh *BackupHistory) MarkAsCompleted(status string, size int64, errorMsg string) {
	bh.Status = status
	bh.BackupSize = size
	bh.ErrorMessage = errorMsg
	
	now := time.Now()
	bh.EndTime = &now
	
	if !bh.StartTime.IsZero() {
		bh.Duration = int(now.Sub(bh.StartTime).Seconds())
	}
}

// ToResponse 转换为响应格式
func (bh *BackupHistory) ToResponse() *BackupHistoryResponse {
	response := &BackupHistoryResponse{
		ID:                  bh.ID,
		TaskID:              bh.TaskID,
		Status:              bh.Status,
		StartTime:           bh.StartTime,
		EndTime:             bh.EndTime,
		BackupSize:          bh.BackupSize,
		BackupSizeFormatted: bh.GetBackupSizeFormatted(),
		FilePath:            bh.FilePath,
		ErrorMessage:        bh.ErrorMessage,
		Duration:            bh.Duration,
		DurationFormatted:   bh.GetDurationFormatted(),
		CreatedAt:           bh.CreatedAt,
		UpdatedAt:           bh.UpdatedAt,
	}
	
	// 添加关联数据
	if bh.Task.ID > 0 {
		response.TaskName = bh.Task.TaskName
		response.BackupType = bh.Task.BackupType
	}
	
	return response
}

// BackupHistoryResponse 备份历史响应结构
type BackupHistoryResponse struct {
	ID                  uint       `json:"id"`
	TaskID              uint       `json:"task_id"`
	TaskName            string     `json:"task_name,omitempty"`
	BackupType          string     `json:"backup_type,omitempty"`
	Status              string     `json:"status"`
	StartTime           time.Time  `json:"start_time"`
	EndTime             *time.Time `json:"end_time"`
	BackupSize          int64      `json:"backup_size"`
	BackupSizeFormatted string     `json:"backup_size_formatted"`
	FilePath            string     `json:"file_path"`
	ErrorMessage        string     `json:"error_message"`
	Duration            int        `json:"duration"`
	DurationFormatted   string     `json:"duration_formatted"`
	CreatedAt           time.Time  `json:"created_at"`
	UpdatedAt           time.Time  `json:"updated_at"`
}

// BackupStatsResponse 备份统计响应结构
type BackupStatsResponse struct {
	TotalTasks       int     `json:"total_tasks"`
	ActiveTasks      int     `json:"active_tasks"`
	TotalBackups     int     `json:"total_backups"`
	SuccessfulBackups int    `json:"successful_backups"`
	FailedBackups    int     `json:"failed_backups"`
	SuccessRate      float64 `json:"success_rate"`
	TotalSize        int64   `json:"total_size"`
	TotalSizeFormatted string `json:"total_size_formatted"`
	LastBackupTime   *time.Time `json:"last_backup_time"`
	NextBackupTime   *time.Time `json:"next_backup_time"`
}
