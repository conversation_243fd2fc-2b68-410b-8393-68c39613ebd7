package models

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// BackupTask 备份任务模型
type BackupTask struct {
	ID            uint           `json:"id" gorm:"primaryKey"`
	DatabaseID    uint           `json:"database_id" gorm:"not null;index" validate:"required"`
	TaskName      string         `json:"task_name" gorm:"size:100;not null" validate:"required,min=2,max=100"`
	BackupType    string         `json:"backup_type" gorm:"size:20;not null" validate:"required,oneof=full incremental differential"`
	Schedule      string         `json:"schedule" gorm:"size:100" validate:"max=100"`
	RetentionDays int            `json:"retention_days" gorm:"default:30" validate:"min=1,max=365"`
	Compression   bool           `json:"compression" gorm:"default:true"`
	Encryption    bool           `json:"encryption" gorm:"default:false"`
	Status        string         `json:"status" gorm:"size:20;default:active" validate:"oneof=active paused failed"`
	LastBackup    *time.Time     `json:"last_backup"`
	NextBackup    *time.Time     `json:"next_backup"`
	BackupSize    int64          `json:"backup_size" gorm:"default:0"` // bytes
	CreatedBy     uint           `json:"created_by" gorm:"not null;index"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Database       DatabaseInstance `json:"database,omitempty" gorm:"foreignKey:DatabaseID"`
	User           User             `json:"user,omitempty" gorm:"foreignKey:CreatedBy"`
	BackupHistories []BackupHistory `json:"backup_histories,omitempty" gorm:"foreignKey:TaskID"`
}

// TableName 指定表名
func (BackupTask) TableName() string {
	return "backup_tasks"
}

// BeforeCreate 创建前钩子
func (bt *BackupTask) BeforeCreate(tx *gorm.DB) error {
	// 设置下次备份时间（如果有调度）
	if bt.Schedule != "" && bt.NextBackup == nil {
		// 这里可以根据schedule计算下次备份时间
		// 暂时设置为1小时后
		nextTime := time.Now().Add(time.Hour)
		bt.NextBackup = &nextTime
	}
	return nil
}

// BeforeUpdate 更新前钩子
func (bt *BackupTask) BeforeUpdate(tx *gorm.DB) error {
	// 更新时间戳
	bt.UpdatedAt = time.Now()
	return nil
}

// IsActive 检查任务是否激活
func (bt *BackupTask) IsActive() bool {
	return bt.Status == "active"
}

// CanExecute 检查是否可以执行备份
func (bt *BackupTask) CanExecute() bool {
	return bt.IsActive() && bt.DatabaseID > 0
}

// GetBackupSizeFormatted 获取格式化的备份大小
func (bt *BackupTask) GetBackupSizeFormatted() string {
	if bt.BackupSize == 0 {
		return "0 B"
	}
	
	const unit = 1024
	if bt.BackupSize < unit {
		return fmt.Sprintf("%d B", bt.BackupSize)
	}
	
	div, exp := int64(unit), 0
	for n := bt.BackupSize / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	
	return fmt.Sprintf("%.1f %cB", float64(bt.BackupSize)/float64(div), "KMGTPE"[exp])
}

// UpdateLastBackup 更新最后备份时间
func (bt *BackupTask) UpdateLastBackup(size int64) {
	now := time.Now()
	bt.LastBackup = &now
	bt.BackupSize = size
	
	// 计算下次备份时间（简化版本）
	if bt.Schedule != "" {
		nextTime := now.Add(24 * time.Hour) // 默认24小时后
		bt.NextBackup = &nextTime
	}
}

// ToResponse 转换为响应格式
func (bt *BackupTask) ToResponse() *BackupTaskResponse {
	response := &BackupTaskResponse{
		ID:            bt.ID,
		DatabaseID:    bt.DatabaseID,
		TaskName:      bt.TaskName,
		BackupType:    bt.BackupType,
		Schedule:      bt.Schedule,
		RetentionDays: bt.RetentionDays,
		Compression:   bt.Compression,
		Encryption:    bt.Encryption,
		Status:        bt.Status,
		LastBackup:    bt.LastBackup,
		NextBackup:    bt.NextBackup,
		BackupSize:    bt.BackupSize,
		CreatedBy:     bt.CreatedBy,
		CreatedAt:     bt.CreatedAt,
		UpdatedAt:     bt.UpdatedAt,
	}
	
	// 添加关联数据
	if bt.Database.ID > 0 {
		response.DatabaseName = bt.Database.Name
		response.DatabaseType = bt.Database.Type
	}
	
	if bt.User.ID > 0 {
		response.CreatedByName = bt.User.Name
	}
	
	return response
}

// BackupTaskResponse 备份任务响应结构
type BackupTaskResponse struct {
	ID             uint       `json:"id"`
	DatabaseID     uint       `json:"database_id"`
	DatabaseName   string     `json:"database_name,omitempty"`
	DatabaseType   string     `json:"database_type,omitempty"`
	TaskName       string     `json:"task_name"`
	BackupType     string     `json:"backup_type"`
	Schedule       string     `json:"schedule"`
	RetentionDays  int        `json:"retention_days"`
	Compression    bool       `json:"compression"`
	Encryption     bool       `json:"encryption"`
	Status         string     `json:"status"`
	LastBackup     *time.Time `json:"last_backup"`
	NextBackup     *time.Time `json:"next_backup"`
	BackupSize     int64      `json:"backup_size"`
	BackupSizeFormatted string `json:"backup_size_formatted,omitempty"`
	CreatedBy      uint       `json:"created_by"`
	CreatedByName  string     `json:"created_by_name,omitempty"`
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`
}
