package models

import (
	"time"
	"gorm.io/gorm"
)

// SystemSetting 系统设置模型
type SystemSetting struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Category    string    `json:"category" gorm:"size:50;not null;index;comment:设置分类"`
	Key         string    `json:"key" gorm:"size:100;not null;comment:设置键"`
	Value       string    `json:"value" gorm:"type:text;comment:设置值"`
	ValueType   string    `json:"value_type" gorm:"size:20;default:'string';comment:值类型(string,number,boolean,json)"`
	Description string    `json:"description" gorm:"size:500;comment:设置描述"`
	IsPublic    bool      `json:"is_public" gorm:"default:false;comment:是否对普通用户可见"`
	UpdatedBy   uint      `json:"updated_by" gorm:"comment:最后更新者ID"`
	UpdatedAt   time.Time `json:"updated_at"`
	CreatedAt   time.Time `json:"created_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Updater     User      `json:"updater" gorm:"foreignKey:UpdatedBy"`
}

// TableName 指定表名
func (SystemSetting) TableName() string {
	return "system_settings"
}

// BeforeCreate GORM钩子 - 创建前
func (s *SystemSetting) BeforeCreate(tx *gorm.DB) error {
	s.CreatedAt = time.Now()
	s.UpdatedAt = time.Now()
	return nil
}

// BeforeUpdate GORM钩子 - 更新前
func (s *SystemSetting) BeforeUpdate(tx *gorm.DB) error {
	s.UpdatedAt = time.Now()
	return nil
}

// UserPreference 用户偏好设置模型
type UserPreference struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"user_id" gorm:"not null;index;comment:用户ID"`
	Category  string    `json:"category" gorm:"size:50;not null;comment:偏好分类"`
	Key       string    `json:"key" gorm:"size:100;not null;comment:偏好键"`
	Value     string    `json:"value" gorm:"type:text;comment:偏好值"`
	ValueType string    `json:"value_type" gorm:"size:20;default:'string';comment:值类型"`
	UpdatedAt time.Time `json:"updated_at"`
	CreatedAt time.Time `json:"created_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	User      User      `json:"user" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (UserPreference) TableName() string {
	return "user_preferences"
}

// BeforeCreate GORM钩子 - 创建前
func (up *UserPreference) BeforeCreate(tx *gorm.DB) error {
	up.CreatedAt = time.Now()
	up.UpdatedAt = time.Now()
	return nil
}

// BeforeUpdate GORM钩子 - 更新前
func (up *UserPreference) BeforeUpdate(tx *gorm.DB) error {
	up.UpdatedAt = time.Now()
	return nil
}

// 系统设置分类常量
const (
	SettingCategorySystem     = "system"     // 系统设置
	SettingCategoryMonitoring = "monitoring" // 监控设置
	SettingCategoryAlert      = "alert"      // 告警设置
	SettingCategoryBackup     = "backup"     // 备份设置
	SettingCategorySecurity   = "security"   // 安全设置
)

// 用户偏好分类常量
const (
	PreferenceCategoryUI           = "ui"           // 界面偏好
	PreferenceCategoryNotification = "notification" // 通知偏好
	PreferenceCategoryDashboard    = "dashboard"    // 仪表板偏好
	PreferenceCategoryReport       = "report"       // 报表偏好
)

// 值类型常量
const (
	ValueTypeString  = "string"  // 字符串类型
	ValueTypeNumber  = "number"  // 数字类型
	ValueTypeBoolean = "boolean" // 布尔类型
	ValueTypeJSON    = "json"    // JSON类型
)

// 默认系统设置
var DefaultSystemSettings = []SystemSetting{
	{
		Category:    SettingCategorySystem,
		Key:         "app_name",
		Value:       "数据库监控平台",
		ValueType:   ValueTypeString,
		Description: "应用程序名称",
		IsPublic:    true,
	},
	{
		Category:    SettingCategorySystem,
		Key:         "app_version",
		Value:       "1.2.0",
		ValueType:   ValueTypeString,
		Description: "应用程序版本",
		IsPublic:    true,
	},
	{
		Category:    SettingCategoryMonitoring,
		Key:         "collection_interval",
		Value:       "60",
		ValueType:   ValueTypeNumber,
		Description: "监控数据收集间隔(秒)",
		IsPublic:    false,
	},
	{
		Category:    SettingCategoryMonitoring,
		Key:         "retention_days",
		Value:       "30",
		ValueType:   ValueTypeNumber,
		Description: "监控数据保留天数",
		IsPublic:    false,
	},
	{
		Category:    SettingCategoryAlert,
		Key:         "default_threshold",
		Value:       "80",
		ValueType:   ValueTypeNumber,
		Description: "默认告警阈值(%)",
		IsPublic:    false,
	},
	{
		Category:    SettingCategoryAlert,
		Key:         "notification_enabled",
		Value:       "true",
		ValueType:   ValueTypeBoolean,
		Description: "是否启用告警通知",
		IsPublic:    false,
	},
	{
		Category:    SettingCategoryBackup,
		Key:         "auto_backup_enabled",
		Value:       "true",
		ValueType:   ValueTypeBoolean,
		Description: "是否启用自动备份",
		IsPublic:    false,
	},
	{
		Category:    SettingCategoryBackup,
		Key:         "backup_retention_days",
		Value:       "7",
		ValueType:   ValueTypeNumber,
		Description: "备份文件保留天数",
		IsPublic:    false,
	},
}

// 默认用户偏好设置
var DefaultUserPreferences = []UserPreference{
	{
		Category:  PreferenceCategoryUI,
		Key:       "theme",
		Value:     "light",
		ValueType: ValueTypeString,
	},
	{
		Category:  PreferenceCategoryUI,
		Key:       "language",
		Value:     "zh-CN",
		ValueType: ValueTypeString,
	},
	{
		Category:  PreferenceCategoryDashboard,
		Key:       "refresh_interval",
		Value:     "30",
		ValueType: ValueTypeNumber,
	},
	{
		Category:  PreferenceCategoryNotification,
		Key:       "email_enabled",
		Value:     "true",
		ValueType: ValueTypeBoolean,
	},
	{
		Category:  PreferenceCategoryReport,
		Key:       "default_format",
		Value:     "csv",
		ValueType: ValueTypeString,
	},
}

// ValidateSettingCategory 验证设置分类
func ValidateSettingCategory(category string) bool {
	validCategories := []string{
		SettingCategorySystem,
		SettingCategoryMonitoring,
		SettingCategoryAlert,
		SettingCategoryBackup,
		SettingCategorySecurity,
	}
	for _, validCategory := range validCategories {
		if category == validCategory {
			return true
		}
	}
	return false
}

// ValidatePreferenceCategory 验证偏好分类
func ValidatePreferenceCategory(category string) bool {
	validCategories := []string{
		PreferenceCategoryUI,
		PreferenceCategoryNotification,
		PreferenceCategoryDashboard,
		PreferenceCategoryReport,
	}
	for _, validCategory := range validCategories {
		if category == validCategory {
			return true
		}
	}
	return false
}

// ValidateValueType 验证值类型
func ValidateValueType(valueType string) bool {
	validTypes := []string{ValueTypeString, ValueTypeNumber, ValueTypeBoolean, ValueTypeJSON}
	for _, validType := range validTypes {
		if valueType == validType {
			return true
		}
	}
	return false
}
