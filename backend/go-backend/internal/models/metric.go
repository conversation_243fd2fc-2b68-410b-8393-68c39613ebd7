package models

import (
	"time"

	"gorm.io/gorm"
)

// Metric 监控指标模型
type Metric struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	DatabaseID uint      `json:"database_id" gorm:"index;not null"`
	MetricType string    `json:"metric_type" gorm:"index;not null;size:50" validate:"required,oneof=cpu memory disk connections qps tps slow_queries locks"`
	Value      float64   `json:"value" gorm:"not null" validate:"required,gte=0"`
	Unit       string    `json:"unit" gorm:"size:20" validate:"required"`
	Labels     string    `json:"labels" gorm:"type:jsonb"` // JSON格式存储额外标签
	Timestamp  time.Time `json:"timestamp" gorm:"index;not null"`
	CreatedAt  time.Time `json:"created_at"`

	// 关联关系
	Database DatabaseInstance `json:"database,omitempty" gorm:"foreignKey:DatabaseID"`
}

// TableName 指定表名
func (Metric) TableName() string {
	return "metrics"
}

// BeforeCreate 创建前钩子
func (m *Metric) BeforeCreate(tx *gorm.DB) error {
	if m.Timestamp.IsZero() {
		m.Timestamp = time.Now()
	}
	return nil
}

// MetricCreateRequest 监控指标创建请求
type MetricCreateRequest struct {
	DatabaseID uint      `json:"database_id" validate:"required"`
	MetricType string    `json:"metric_type" validate:"required,oneof=cpu memory disk connections qps tps slow_queries locks"`
	Value      float64   `json:"value" validate:"required,gte=0"`
	Unit       string    `json:"unit" validate:"required"`
	Labels     string    `json:"labels,omitempty"`
	Timestamp  time.Time `json:"timestamp,omitempty"`
}

// MetricResponse 监控指标响应
type MetricResponse struct {
	ID         uint      `json:"id"`
	DatabaseID uint      `json:"database_id"`
	MetricType string    `json:"metric_type"`
	Value      float64   `json:"value"`
	Unit       string    `json:"unit"`
	Labels     string    `json:"labels"`
	Timestamp  time.Time `json:"timestamp"`
	CreatedAt  time.Time `json:"created_at"`
}

// ToResponse 转换为响应格式
func (m *Metric) ToResponse() *MetricResponse {
	return &MetricResponse{
		ID:         m.ID,
		DatabaseID: m.DatabaseID,
		MetricType: m.MetricType,
		Value:      m.Value,
		Unit:       m.Unit,
		Labels:     m.Labels,
		Timestamp:  m.Timestamp,
		CreatedAt:  m.CreatedAt,
	}
}

// MetricQueryRequest 监控指标查询请求
type MetricQueryRequest struct {
	DatabaseID uint      `json:"database_id" validate:"required"`
	MetricType string    `json:"metric_type,omitempty"`
	StartTime  time.Time `json:"start_time" validate:"required"`
	EndTime    time.Time `json:"end_time" validate:"required"`
	Interval   string    `json:"interval,omitempty" validate:"omitempty,oneof=1m 5m 15m 30m 1h 6h 12h 1d"`
	Limit      int       `json:"limit,omitempty" validate:"omitempty,min=1,max=10000"`
}

// MetricAggregateResponse 聚合监控指标响应
type MetricAggregateResponse struct {
	MetricType string                    `json:"metric_type"`
	Unit       string                    `json:"unit"`
	DataPoints []MetricDataPoint         `json:"data_points"`
	Summary    MetricSummary             `json:"summary"`
}

// MetricDataPoint 监控数据点
type MetricDataPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Value     float64   `json:"value"`
	Min       float64   `json:"min,omitempty"`
	Max       float64   `json:"max,omitempty"`
	Avg       float64   `json:"avg,omitempty"`
	Count     int64     `json:"count,omitempty"`
}

// MetricSummary 监控指标摘要
type MetricSummary struct {
	Min       float64   `json:"min"`
	Max       float64   `json:"max"`
	Avg       float64   `json:"avg"`
	Current   float64   `json:"current"`
	Count     int64     `json:"count"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
}

// RealtimeMetric 实时监控指标
type RealtimeMetric struct {
	DatabaseID   uint                     `json:"database_id"`
	DatabaseName string                   `json:"database_name"`
	Status       string                   `json:"status"`
	Metrics      map[string]MetricValue   `json:"metrics"`
	Timestamp    time.Time                `json:"timestamp"`
}

// MetricValue 指标值
type MetricValue struct {
	Value     float64 `json:"value"`
	Unit      string  `json:"unit"`
	Status    string  `json:"status"` // normal, warning, critical
	Threshold float64 `json:"threshold,omitempty"`
}

// GetMetricDisplayName 获取指标显示名称
func GetMetricDisplayName(metricType string) string {
	displayNames := map[string]string{
		"cpu":          "CPU使用率",
		"memory":       "内存使用率",
		"disk":         "磁盘使用率",
		"connections":  "连接数",
		"qps":          "每秒查询数",
		"tps":          "每秒事务数",
		"slow_queries": "慢查询数",
		"locks":        "锁等待数",
	}
	
	if name, exists := displayNames[metricType]; exists {
		return name
	}
	return metricType
}

// GetMetricUnit 获取指标单位
func GetMetricUnit(metricType string) string {
	units := map[string]string{
		"cpu":          "%",
		"memory":       "%",
		"disk":         "%",
		"connections":  "count",
		"qps":          "queries/sec",
		"tps":          "transactions/sec",
		"slow_queries": "count",
		"locks":        "count",
	}
	
	if unit, exists := units[metricType]; exists {
		return unit
	}
	return "count"
}

// IsPercentageMetric 检查是否为百分比指标
func IsPercentageMetric(metricType string) bool {
	percentageMetrics := []string{"cpu", "memory", "disk"}
	for _, metric := range percentageMetrics {
		if metric == metricType {
			return true
		}
	}
	return false
}
