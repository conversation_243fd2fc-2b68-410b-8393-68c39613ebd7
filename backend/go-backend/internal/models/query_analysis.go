package models

import (
	"time"

	"gorm.io/gorm"
)

// QueryAnalysis 查询分析记录模型
type QueryAnalysis struct {
	ID                 uint           `json:"id" gorm:"primaryKey"`
	DatabaseID         uint           `json:"database_id" gorm:"index;not null"`
	UserID             uint           `json:"user_id" gorm:"index;not null"`
	OriginalQuery      string         `json:"original_query" gorm:"type:text;not null"`
	FormattedQuery     string         `json:"formatted_query" gorm:"type:text;not null"`
	QueryType          string         `json:"query_type" gorm:"size:20;not null" validate:"required,oneof=select insert update delete other"`
	EstimatedCost      float64        `json:"estimated_cost" gorm:"default:0"`
	ComplexityScore    int            `json:"complexity_score" gorm:"default:0"`
	ExecutionTime      float64        `json:"execution_time" gorm:"default:0"` // 毫秒
	RowsReturned       int            `json:"rows_returned" gorm:"default:0"`
	RowsExamined       int            `json:"rows_examined" gorm:"default:0"`
	Status             string         `json:"status" gorm:"size:20;default:pending" validate:"oneof=pending running completed failed"`
	ErrorMessage       string         `json:"error_message" gorm:"type:text"`
	AnalysisTimestamp  time.Time      `json:"analysis_timestamp" gorm:"index;not null"`
	CreatedAt          time.Time      `json:"created_at"`
	UpdatedAt          time.Time      `json:"updated_at"`
	DeletedAt          gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Database            DatabaseInstance       `json:"database,omitempty" gorm:"foreignKey:DatabaseID"`
	User                User                   `json:"user,omitempty" gorm:"foreignKey:UserID"`
	ExecutionPlan       *ExecutionPlan         `json:"execution_plan,omitempty" gorm:"foreignKey:QueryAnalysisID"`
	OptimizationSuggestions []OptimizationSuggestion `json:"optimization_suggestions,omitempty" gorm:"foreignKey:QueryAnalysisID"`
	IndexRecommendations []IndexRecommendation `json:"index_recommendations,omitempty" gorm:"foreignKey:QueryAnalysisID"`
}

// TableName 指定表名
func (QueryAnalysis) TableName() string {
	return "query_analyses"
}

// QueryAnalysisRequest 查询分析请求
type QueryAnalysisRequest struct {
	DatabaseID uint   `json:"database_id" validate:"required"`
	SQLQuery   string `json:"sql_query" validate:"required"`
	Options    QueryAnalysisOptions `json:"options"`
}

// QueryAnalysisOptions 查询分析选项
type QueryAnalysisOptions struct {
	IncludeExecutionPlan       bool `json:"include_execution_plan" default:"true"`
	IncludeSuggestions         bool `json:"include_suggestions" default:"true"`
	IncludeIndexRecommendations bool `json:"include_index_recommendations" default:"true"`
	RunExplainAnalyze          bool `json:"run_explain_analyze" default:"false"`
}

// QueryAnalysisResponse 查询分析响应
type QueryAnalysisResponse struct {
	ID                 uint                    `json:"id"`
	DatabaseID         uint                    `json:"database_id"`
	OriginalQuery      string                  `json:"original_query"`
	FormattedQuery     string                  `json:"formatted_query"`
	QueryType          string                  `json:"query_type"`
	EstimatedCost      float64                 `json:"estimated_cost"`
	ComplexityScore    int                     `json:"complexity_score"`
	ExecutionTime      float64                 `json:"execution_time"`
	RowsReturned       int                     `json:"rows_returned"`
	RowsExamined       int                     `json:"rows_examined"`
	Status             string                  `json:"status"`
	ErrorMessage       string                  `json:"error_message,omitempty"`
	AnalysisTimestamp  time.Time               `json:"analysis_timestamp"`
	ExecutionPlan      *ExecutionPlanResponse  `json:"execution_plan,omitempty"`
	OptimizationSuggestions []OptimizationSuggestionResponse `json:"optimization_suggestions,omitempty"`
	IndexRecommendations []IndexRecommendationResponse `json:"index_recommendations,omitempty"`
}

// ToResponse 转换为响应格式
func (qa *QueryAnalysis) ToResponse() *QueryAnalysisResponse {
	response := &QueryAnalysisResponse{
		ID:                uint(qa.ID),
		DatabaseID:        qa.DatabaseID,
		OriginalQuery:     qa.OriginalQuery,
		FormattedQuery:    qa.FormattedQuery,
		QueryType:         qa.QueryType,
		EstimatedCost:     qa.EstimatedCost,
		ComplexityScore:   qa.ComplexityScore,
		ExecutionTime:     qa.ExecutionTime,
		RowsReturned:      qa.RowsReturned,
		RowsExamined:      qa.RowsExamined,
		Status:            qa.Status,
		ErrorMessage:      qa.ErrorMessage,
		AnalysisTimestamp: qa.AnalysisTimestamp,
	}

	// 添加执行计划
	if qa.ExecutionPlan != nil {
		response.ExecutionPlan = qa.ExecutionPlan.ToResponse()
	}

	// 添加优化建议
	if len(qa.OptimizationSuggestions) > 0 {
		response.OptimizationSuggestions = make([]OptimizationSuggestionResponse, 0, len(qa.OptimizationSuggestions))
		for _, suggestion := range qa.OptimizationSuggestions {
			response.OptimizationSuggestions = append(response.OptimizationSuggestions, *suggestion.ToResponse())
		}
	}

	// 添加索引推荐
	if len(qa.IndexRecommendations) > 0 {
		response.IndexRecommendations = make([]IndexRecommendationResponse, 0, len(qa.IndexRecommendations))
		for _, recommendation := range qa.IndexRecommendations {
			response.IndexRecommendations = append(response.IndexRecommendations, *recommendation.ToResponse())
		}
	}

	return response
}

// AnalysisHistoryParams 分析历史查询参数
type AnalysisHistoryParams struct {
	Page       int    `form:"page" default:"1"`
	PageSize   int    `form:"page_size" default:"20"`
	DatabaseID uint   `form:"database_id"`
	QueryType  string `form:"query_type"`
	StartDate  string `form:"start_date"`
	EndDate    string `form:"end_date"`
	Status     string `form:"status"`
}

// QueryStatsParams 查询统计参数
type QueryStatsParams struct {
	DatabaseID uint   `form:"database_id"`
	Period     string `form:"period" default:"7d"`
	GroupBy    string `form:"group_by" default:"day"`
}

// SupportedDatabase 支持的数据库类型
type SupportedDatabase struct {
	Type        string   `json:"type"`
	Name        string   `json:"name"`
	Version     string   `json:"version"`
	Features    []string `json:"features"`
	Limitations []string `json:"limitations"`
}

// QueryStatsResponse 查询统计响应
type QueryStatsResponse struct {
	TotalAnalyses    int                    `json:"total_analyses"`
	SuccessfulAnalyses int                  `json:"successful_analyses"`
	FailedAnalyses   int                    `json:"failed_analyses"`
	AverageExecutionTime float64            `json:"average_execution_time"`
	TopQueryTypes    []QueryTypeStats      `json:"top_query_types"`
	PerformanceImprovements []PerformanceImprovement `json:"performance_improvements"`
	Timeline         []TimelineStats       `json:"timeline"`
}

// QueryTypeStats 查询类型统计
type QueryTypeStats struct {
	QueryType string  `json:"query_type"`
	Count     int     `json:"count"`
	Percentage float64 `json:"percentage"`
}

// PerformanceImprovement 性能改进统计
type PerformanceImprovement struct {
	Category    string  `json:"category"`
	Improvement float64 `json:"improvement"`
	Count       int     `json:"count"`
}

// TimelineStats 时间线统计
type TimelineStats struct {
	Date  string `json:"date"`
	Count int    `json:"count"`
}

// IndexSuggestionRequest 索引建议请求
type IndexSuggestionRequest struct {
	DatabaseID    uint     `json:"database_id" validate:"required"`
	TableName     string   `json:"table_name" validate:"required"`
	QueryPatterns []string `json:"query_patterns" validate:"required"`
}
