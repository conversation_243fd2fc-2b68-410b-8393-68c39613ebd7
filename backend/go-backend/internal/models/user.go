package models

import (
	"time"

	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	Email     string         `json:"email" gorm:"uniqueIndex;not null;size:255" validate:"required,email"`
	Password  string         `json:"-" gorm:"not null;size:255" validate:"required,min=6"`
	Name      string         `json:"name" gorm:"not null;size:100" validate:"required,min=2,max=50"`
	Role      string         `json:"role" gorm:"default:user;size:20" validate:"oneof=admin user viewer"`
	AvatarURL string         `json:"avatar_url" gorm:"size:500"`
	IsActive  bool           `json:"is_active" gorm:"default:true"`
	LastLogin *time.Time     `json:"last_login"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	DatabaseInstances []DatabaseInstance `json:"database_instances,omitempty" gorm:"foreignKey:CreatedBy"`
	AlertRules        []AlertRule        `json:"alert_rules,omitempty" gorm:"foreignKey:CreatedBy"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// BeforeCreate 创建前钩子
func (u *User) BeforeCreate(tx *gorm.DB) error {
	// 可以在这里添加创建前的逻辑，比如密码加密
	return nil
}

// BeforeUpdate 更新前钩子
func (u *User) BeforeUpdate(tx *gorm.DB) error {
	// 可以在这里添加更新前的逻辑
	return nil
}

// UserCreateRequest 用户创建请求
type UserCreateRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,min=6"`
	Name     string `json:"name" validate:"required,min=2,max=50"`
	Role     string `json:"role,omitempty" validate:"omitempty,oneof=admin user viewer"`
}

// UserUpdateRequest 用户更新请求
type UserUpdateRequest struct {
	Name      string `json:"name,omitempty" validate:"omitempty,min=2,max=50"`
	AvatarURL string `json:"avatar_url,omitempty" validate:"omitempty,url"`
	IsActive  *bool  `json:"is_active,omitempty"`
}

// UserLoginRequest 用户登录请求
type UserLoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

// UserResponse 用户响应
type UserResponse struct {
	ID        uint       `json:"id"`
	Email     string     `json:"email"`
	Name      string     `json:"name"`
	Role      string     `json:"role"`
	AvatarURL string     `json:"avatar_url"`
	IsActive  bool       `json:"is_active"`
	LastLogin *time.Time `json:"last_login"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (u *User) ToResponse() *UserResponse {
	return &UserResponse{
		ID:        u.ID,
		Email:     u.Email,
		Name:      u.Name,
		Role:      u.Role,
		AvatarURL: u.AvatarURL,
		IsActive:  u.IsActive,
		LastLogin: u.LastLogin,
		CreatedAt: u.CreatedAt,
		UpdatedAt: u.UpdatedAt,
	}
}

// IsAdmin 检查是否为管理员
func (u *User) IsAdmin() bool {
	return u.Role == "admin"
}

// CanManageDatabase 检查是否可以管理数据库
func (u *User) CanManageDatabase() bool {
	return u.Role == "admin" || u.Role == "user"
}

// CanViewOnly 检查是否只能查看
func (u *User) CanViewOnly() bool {
	return u.Role == "viewer"
}
