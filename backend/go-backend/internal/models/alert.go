package models

import (
	"time"

	"gorm.io/gorm"
)

// AlertRule 告警规则模型
type AlertRule struct {
	ID                   uint           `json:"id" gorm:"primaryKey"`
	Name                 string         `json:"name" gorm:"not null;size:100" validate:"required,min=2,max=50"`
	DatabaseID           uint           `json:"database_id" gorm:"index;not null"`
	MetricType           string         `json:"metric_type" gorm:"not null;size:50" validate:"required,oneof=cpu memory disk connections qps tps slow_queries locks"`
	Operator             string         `json:"operator" gorm:"not null;size:10" validate:"required,oneof=> < >= <= = !="`
	Threshold            float64        `json:"threshold" gorm:"not null" validate:"required,gte=0"`
	Duration             int            `json:"duration" gorm:"default:300"` // 持续时间（秒）
	Severity             string         `json:"severity" gorm:"default:warning;size:20" validate:"oneof=info warning error critical"`
	Enabled              bool           `json:"enabled" gorm:"default:true"`
	NotificationChannels string         `json:"notification_channels" gorm:"type:jsonb"` // JSON格式存储通知渠道
	Description          string         `json:"description" gorm:"size:500"`
	CreatedBy            uint           `json:"created_by" gorm:"not null"`
	CreatedAt            time.Time      `json:"created_at"`
	UpdatedAt            time.Time      `json:"updated_at"`
	DeletedAt            gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Database    DatabaseInstance `json:"database,omitempty" gorm:"foreignKey:DatabaseID"`
	User        User             `json:"user,omitempty" gorm:"foreignKey:CreatedBy"`
	AlertEvents []AlertEvent     `json:"alert_events,omitempty" gorm:"foreignKey:AlertRuleID"`
}

// TableName 指定表名
func (AlertRule) TableName() string {
	return "alert_rules"
}

// AlertEvent 告警事件模型
type AlertEvent struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	AlertRuleID uint           `json:"alert_rule_id" gorm:"index;not null"`
	DatabaseID  uint           `json:"database_id" gorm:"index;not null"`
	MetricType  string         `json:"metric_type" gorm:"not null;size:50"`
	Value       float64        `json:"value" gorm:"not null"`
	Threshold   float64        `json:"threshold" gorm:"not null"`
	Operator    string         `json:"operator" gorm:"not null;size:10"`
	Severity    string         `json:"severity" gorm:"not null;size:20"`
	Status      string         `json:"status" gorm:"default:active;size:20" validate:"oneof=active resolved suppressed"`
	Message     string         `json:"message" gorm:"not null;size:1000"`
	StartTime   time.Time      `json:"start_time" gorm:"not null"`
	EndTime     *time.Time     `json:"end_time"`
	Duration    int            `json:"duration"` // 持续时间（秒）
	ResolvedBy  *uint          `json:"resolved_by"`
	ResolvedAt  *time.Time     `json:"resolved_at"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Database DatabaseInstance `json:"database,omitempty" gorm:"foreignKey:DatabaseID"`
	Rule     AlertRule        `json:"rule,omitempty" gorm:"foreignKey:AlertRuleID"`
	Resolver *User            `json:"resolver,omitempty" gorm:"foreignKey:ResolvedBy"`
}

// TableName 指定表名
func (AlertEvent) TableName() string {
	return "alert_events"
}

// BeforeCreate 创建前钩子
func (a *AlertEvent) BeforeCreate(tx *gorm.DB) error {
	if a.StartTime.IsZero() {
		a.StartTime = time.Now()
	}
	return nil
}

// AlertRuleCreateRequest 告警规则创建请求
type AlertRuleCreateRequest struct {
	Name                 string  `json:"name" validate:"required,min=2,max=50"`
	DatabaseID           uint    `json:"database_id" validate:"required"`
	MetricType           string  `json:"metric_type" validate:"required,oneof=cpu memory disk connections qps tps slow_queries locks"`
	Operator             string  `json:"operator" validate:"required,oneof=> < >= <= = !="`
	Threshold            float64 `json:"threshold" validate:"required,gte=0"`
	Duration             *int    `json:"duration,omitempty" validate:"omitempty,min=60,max=3600"`
	Severity             string  `json:"severity,omitempty" validate:"omitempty,oneof=info warning error critical"`
	NotificationChannels string  `json:"notification_channels,omitempty"`
	Description          string  `json:"description,omitempty" validate:"max=500"`
	Enabled              *bool   `json:"enabled,omitempty"`
}

// AlertRuleUpdateRequest 告警规则更新请求
type AlertRuleUpdateRequest struct {
	Name                 string  `json:"name,omitempty" validate:"omitempty,min=2,max=50"`
	Operator             string  `json:"operator,omitempty" validate:"omitempty,oneof=> < >= <= = !="`
	Threshold            *float64 `json:"threshold,omitempty" validate:"omitempty,gte=0"`
	Duration             *int    `json:"duration,omitempty" validate:"omitempty,min=60,max=3600"`
	Severity             string  `json:"severity,omitempty" validate:"omitempty,oneof=info warning error critical"`
	NotificationChannels string  `json:"notification_channels,omitempty"`
	Description          string  `json:"description,omitempty" validate:"max=500"`
	Enabled              *bool   `json:"enabled,omitempty"`
}

// AlertRuleResponse 告警规则响应
type AlertRuleResponse struct {
	ID                   uint      `json:"id"`
	Name                 string    `json:"name"`
	DatabaseID           uint      `json:"database_id"`
	MetricType           string    `json:"metric_type"`
	Operator             string    `json:"operator"`
	Threshold            float64   `json:"threshold"`
	Duration             int       `json:"duration"`
	Severity             string    `json:"severity"`
	Enabled              bool      `json:"enabled"`
	NotificationChannels string    `json:"notification_channels"`
	Description          string    `json:"description"`
	CreatedBy            uint      `json:"created_by"`
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`
	Database             *DatabaseInstance `json:"database,omitempty"`
}

// ToResponse 转换为响应格式
func (a *AlertRule) ToResponse() *AlertRuleResponse {
	return &AlertRuleResponse{
		ID:                   a.ID,
		Name:                 a.Name,
		DatabaseID:           a.DatabaseID,
		MetricType:           a.MetricType,
		Operator:             a.Operator,
		Threshold:            a.Threshold,
		Duration:             a.Duration,
		Severity:             a.Severity,
		Enabled:              a.Enabled,
		NotificationChannels: a.NotificationChannels,
		Description:          a.Description,
		CreatedBy:            a.CreatedBy,
		CreatedAt:            a.CreatedAt,
		UpdatedAt:            a.UpdatedAt,
	}
}

// AlertEventResponse 告警事件响应
type AlertEventResponse struct {
	ID          uint       `json:"id"`
	AlertRuleID uint       `json:"alert_rule_id"`
	DatabaseID  uint       `json:"database_id"`
	MetricType  string     `json:"metric_type"`
	Value       float64    `json:"value"`
	Threshold   float64    `json:"threshold"`
	Operator    string     `json:"operator"`
	Severity    string     `json:"severity"`
	Status      string     `json:"status"`
	Message     string     `json:"message"`
	StartTime   time.Time  `json:"start_time"`
	EndTime     *time.Time `json:"end_time"`
	Duration    int        `json:"duration"`
	ResolvedBy  *uint      `json:"resolved_by"`
	ResolvedAt  *time.Time `json:"resolved_at"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	Database    *DatabaseInstance `json:"database,omitempty"`
	Rule        *AlertRule        `json:"rule,omitempty"`
}

// ToResponse 转换为响应格式
func (a *AlertEvent) ToResponse() *AlertEventResponse {
	return &AlertEventResponse{
		ID:          a.ID,
		AlertRuleID: a.AlertRuleID,
		DatabaseID:  a.DatabaseID,
		MetricType:  a.MetricType,
		Value:       a.Value,
		Threshold:   a.Threshold,
		Operator:    a.Operator,
		Severity:    a.Severity,
		Status:      a.Status,
		Message:     a.Message,
		StartTime:   a.StartTime,
		EndTime:     a.EndTime,
		Duration:    a.Duration,
		ResolvedBy:  a.ResolvedBy,
		ResolvedAt:  a.ResolvedAt,
		CreatedAt:   a.CreatedAt,
		UpdatedAt:   a.UpdatedAt,
	}
}

// IsActive 检查告警事件是否活跃
func (a *AlertEvent) IsActive() bool {
	return a.Status == "active"
}

// IsResolved 检查告警事件是否已解决
func (a *AlertEvent) IsResolved() bool {
	return a.Status == "resolved"
}

// Resolve 解决告警事件
func (a *AlertEvent) Resolve(userID uint) {
	now := time.Now()
	a.Status = "resolved"
	a.ResolvedBy = &userID
	a.ResolvedAt = &now
	if !a.StartTime.IsZero() {
		a.Duration = int(now.Sub(a.StartTime).Seconds())
	}
}

// GetSeverityLevel 获取严重级别数值
func GetSeverityLevel(severity string) int {
	levels := map[string]int{
		"info":     1,
		"warning":  2,
		"error":    3,
		"critical": 4,
	}
	
	if level, exists := levels[severity]; exists {
		return level
	}
	return 1
}
