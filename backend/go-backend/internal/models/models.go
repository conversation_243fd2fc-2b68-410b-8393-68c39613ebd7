package models

import (
	"gorm.io/gorm"
)

// AllModels 返回所有需要迁移的模型
func AllModels() []interface{} {
	return []interface{}{
		&User{},
		&DatabaseInstance{},
		&Metric{},
		&AlertRule{},
		&AlertEvent{},
		// 查询优化相关模型
		&QueryAnalysis{},
		&ExecutionPlan{},
		&OptimizationSuggestion{},
		&IndexRecommendation{},
		// 备份管理相关模型
		&BackupTask{},
		&BackupHistory{},
		// 报表系统相关模型
		&ReportTemplate{},
		&ReportExecution{},
		// 系统设置相关模型
		&SystemSetting{},
		&UserPreference{},
	}
}

// AutoMigrate 自动迁移所有模型
func AutoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(AllModels()...)
}

// CreateIndexes 创建额外的索引
func CreateIndexes(db *gorm.DB) error {
	// 为metrics表创建复合索引
	if err := db.Exec(`
		CREATE INDEX IF NOT EXISTS idx_metrics_database_time 
		ON metrics(database_id, timestamp DESC)
	`).Error; err != nil {
		return err
	}

	if err := db.Exec(`
		CREATE INDEX IF NOT EXISTS idx_metrics_type_time 
		ON metrics(metric_type, timestamp DESC)
	`).Error; err != nil {
		return err
	}

	if err := db.Exec(`
		CREATE INDEX IF NOT EXISTS idx_metrics_database_type_time 
		ON metrics(database_id, metric_type, timestamp DESC)
	`).Error; err != nil {
		return err
	}

	// 为alert_events表创建复合索引
	if err := db.Exec(`
		CREATE INDEX IF NOT EXISTS idx_alert_events_database_status 
		ON alert_events(database_id, status)
	`).Error; err != nil {
		return err
	}

	if err := db.Exec(`
		CREATE INDEX IF NOT EXISTS idx_alert_events_rule_time 
		ON alert_events(alert_rule_id, start_time DESC)
	`).Error; err != nil {
		return err
	}

	// 为database_instances表创建索引
	if err := db.Exec(`
		CREATE INDEX IF NOT EXISTS idx_database_instances_user_status 
		ON database_instances(created_by, status)
	`).Error; err != nil {
		return err
	}

	return nil
}

// DropIndexes 删除索引（用于测试或重置）
func DropIndexes(db *gorm.DB) error {
	indexes := []string{
		"idx_metrics_database_time",
		"idx_metrics_type_time",
		"idx_metrics_database_type_time",
		"idx_alert_events_database_status",
		"idx_alert_events_rule_time",
		"idx_database_instances_user_status",
	}

	for _, index := range indexes {
		if err := db.Exec("DROP INDEX IF EXISTS " + index).Error; err != nil {
			return err
		}
	}

	return nil
}

// CommonResponse 通用响应结构
type CommonResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// PaginationRequest 分页请求
type PaginationRequest struct {
	Page     int `json:"page" form:"page" validate:"min=1"`
	PageSize int `json:"page_size" form:"page_size" validate:"min=1,max=100"`
}

// PaginationResponse 分页响应
type PaginationResponse struct {
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	Total      int64       `json:"total"`
	TotalPages int         `json:"total_pages"`
	Items      interface{} `json:"items"`
}

// GetDefaultPagination 获取默认分页参数
func GetDefaultPagination() PaginationRequest {
	return PaginationRequest{
		Page:     1,
		PageSize: 20,
	}
}

// CalculateOffset 计算偏移量
func (p *PaginationRequest) CalculateOffset() int {
	return (p.Page - 1) * p.PageSize
}

// CalculateTotalPages 计算总页数
func CalculateTotalPages(total int64, pageSize int) int {
	if pageSize <= 0 {
		return 0
	}
	return int((total + int64(pageSize) - 1) / int64(pageSize))
}

// NewPaginationResponse 创建分页响应
func NewPaginationResponse(page, pageSize int, total int64, items interface{}) *PaginationResponse {
	return &PaginationResponse{
		Page:       page,
		PageSize:   pageSize,
		Total:      total,
		TotalPages: CalculateTotalPages(total, pageSize),
		Items:      items,
	}
}

// SuccessResponse 成功响应
func SuccessResponse(data interface{}) *CommonResponse {
	return &CommonResponse{
		Code:    200,
		Message: "success",
		Data:    data,
	}
}

// ErrorResponse 错误响应
func ErrorResponse(code int, message string) *CommonResponse {
	return &CommonResponse{
		Code:    code,
		Message: message,
	}
}

// ValidationErrorResponse 验证错误响应
func ValidationErrorResponse(message string) *CommonResponse {
	return &CommonResponse{
		Code:    400,
		Message: "validation error: " + message,
	}
}

// NotFoundResponse 未找到响应
func NotFoundResponse(resource string) *CommonResponse {
	return &CommonResponse{
		Code:    404,
		Message: resource + " not found",
	}
}

// UnauthorizedResponse 未授权响应
func UnauthorizedResponse() *CommonResponse {
	return &CommonResponse{
		Code:    401,
		Message: "unauthorized",
	}
}

// ForbiddenResponse 禁止访问响应
func ForbiddenResponse() *CommonResponse {
	return &CommonResponse{
		Code:    403,
		Message: "forbidden",
	}
}

// InternalErrorResponse 内部错误响应
func InternalErrorResponse() *CommonResponse {
	return &CommonResponse{
		Code:    500,
		Message: "internal server error",
	}
}
