package models

import "time"

// BackupTaskCreateRequest 创建备份任务请求
type BackupTaskCreateRequest struct {
	DatabaseID    uint   `json:"database_id" validate:"required"`
	TaskName      string `json:"task_name" validate:"required,min=2,max=100"`
	BackupType    string `json:"backup_type" validate:"required,oneof=full incremental differential"`
	Schedule      string `json:"schedule" validate:"max=100"`
	RetentionDays int    `json:"retention_days" validate:"min=1,max=365"`
	Compression   *bool  `json:"compression"`   // 指针类型，允许null
	Encryption    *bool  `json:"encryption"`    // 指针类型，允许null
}

// BackupTaskUpdateRequest 更新备份任务请求
type BackupTaskUpdateRequest struct {
	TaskName      *string `json:"task_name" validate:"omitempty,min=2,max=100"`
	BackupType    *string `json:"backup_type" validate:"omitempty,oneof=full incremental differential"`
	Schedule      *string `json:"schedule" validate:"omitempty,max=100"`
	RetentionDays *int    `json:"retention_days" validate:"omitempty,min=1,max=365"`
	Compression   *bool   `json:"compression"`
	Encryption    *bool   `json:"encryption"`
	Status        *string `json:"status" validate:"omitempty,oneof=active paused failed"`
}

// BackupExecuteRequest 执行备份请求
type BackupExecuteRequest struct {
	TaskID uint `json:"task_id" validate:"required"`
	Force  bool `json:"force"` // 是否强制执行（即使上次备份还在进行中）
}

// BackupHistoryListRequest 备份历史列表请求
type BackupHistoryListRequest struct {
	PaginationRequest
	TaskID     *uint      `json:"task_id" form:"task_id"`
	DatabaseID *uint      `json:"database_id" form:"database_id"`
	Status     *string    `json:"status" form:"status" validate:"omitempty,oneof=success failed running"`
	StartDate  *time.Time `json:"start_date" form:"start_date"`
	EndDate    *time.Time `json:"end_date" form:"end_date"`
}

// BackupTaskListRequest 备份任务列表请求
type BackupTaskListRequest struct {
	PaginationRequest
	DatabaseID *uint   `json:"database_id" form:"database_id"`
	Status     *string `json:"status" form:"status" validate:"omitempty,oneof=active paused failed"`
	BackupType *string `json:"backup_type" form:"backup_type" validate:"omitempty,oneof=full incremental differential"`
	Search     *string `json:"search" form:"search"` // 搜索任务名称
}

// BackupStatsRequest 备份统计请求
type BackupStatsRequest struct {
	DatabaseID *uint      `json:"database_id" form:"database_id"`
	StartDate  *time.Time `json:"start_date" form:"start_date"`
	EndDate    *time.Time `json:"end_date" form:"end_date"`
	Period     *string    `json:"period" form:"period" validate:"omitempty,oneof=day week month year"` // 统计周期
}

// BackupExecuteResponse 执行备份响应
type BackupExecuteResponse struct {
	HistoryID uint   `json:"history_id"`
	TaskID    uint   `json:"task_id"`
	Status    string `json:"status"`
	Message   string `json:"message"`
	StartTime time.Time `json:"start_time"`
}

// BackupTaskListResponse 备份任务列表响应
type BackupTaskListResponse struct {
	PaginationResponse
	Items []BackupTaskResponse `json:"items"`
}

// BackupHistoryListResponse 备份历史列表响应
type BackupHistoryListResponse struct {
	PaginationResponse
	Items []BackupHistoryResponse `json:"items"`
}

// BackupValidationError 备份验证错误
type BackupValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Value   interface{} `json:"value,omitempty"`
}

// BackupErrorResponse 备份错误响应
type BackupErrorResponse struct {
	CommonResponse
	Errors []BackupValidationError `json:"errors,omitempty"`
}

// 验证方法

// Validate 验证创建备份任务请求
func (req *BackupTaskCreateRequest) Validate() []BackupValidationError {
	var errors []BackupValidationError
	
	if req.DatabaseID == 0 {
		errors = append(errors, BackupValidationError{
			Field:   "database_id",
			Message: "database_id is required",
			Value:   req.DatabaseID,
		})
	}
	
	if req.TaskName == "" {
		errors = append(errors, BackupValidationError{
			Field:   "task_name",
			Message: "task_name is required",
		})
	} else if len(req.TaskName) < 2 || len(req.TaskName) > 100 {
		errors = append(errors, BackupValidationError{
			Field:   "task_name",
			Message: "task_name must be between 2 and 100 characters",
			Value:   req.TaskName,
		})
	}
	
	if req.BackupType == "" {
		errors = append(errors, BackupValidationError{
			Field:   "backup_type",
			Message: "backup_type is required",
		})
	} else if req.BackupType != "full" && req.BackupType != "incremental" && req.BackupType != "differential" {
		errors = append(errors, BackupValidationError{
			Field:   "backup_type",
			Message: "backup_type must be one of: full, incremental, differential",
			Value:   req.BackupType,
		})
	}
	
	if req.RetentionDays < 1 || req.RetentionDays > 365 {
		errors = append(errors, BackupValidationError{
			Field:   "retention_days",
			Message: "retention_days must be between 1 and 365",
			Value:   req.RetentionDays,
		})
	}
	
	return errors
}

// Validate 验证更新备份任务请求
func (req *BackupTaskUpdateRequest) Validate() []BackupValidationError {
	var errors []BackupValidationError
	
	if req.TaskName != nil {
		if len(*req.TaskName) < 2 || len(*req.TaskName) > 100 {
			errors = append(errors, BackupValidationError{
				Field:   "task_name",
				Message: "task_name must be between 2 and 100 characters",
				Value:   *req.TaskName,
			})
		}
	}
	
	if req.BackupType != nil {
		if *req.BackupType != "full" && *req.BackupType != "incremental" && *req.BackupType != "differential" {
			errors = append(errors, BackupValidationError{
				Field:   "backup_type",
				Message: "backup_type must be one of: full, incremental, differential",
				Value:   *req.BackupType,
			})
		}
	}
	
	if req.RetentionDays != nil {
		if *req.RetentionDays < 1 || *req.RetentionDays > 365 {
			errors = append(errors, BackupValidationError{
				Field:   "retention_days",
				Message: "retention_days must be between 1 and 365",
				Value:   *req.RetentionDays,
			})
		}
	}
	
	if req.Status != nil {
		if *req.Status != "active" && *req.Status != "paused" && *req.Status != "failed" {
			errors = append(errors, BackupValidationError{
				Field:   "status",
				Message: "status must be one of: active, paused, failed",
				Value:   *req.Status,
			})
		}
	}
	
	return errors
}

// SetDefaults 设置创建请求的默认值
func (req *BackupTaskCreateRequest) SetDefaults() {
	if req.RetentionDays == 0 {
		req.RetentionDays = 30
	}
	
	if req.Compression == nil {
		defaultCompression := true
		req.Compression = &defaultCompression
	}
	
	if req.Encryption == nil {
		defaultEncryption := false
		req.Encryption = &defaultEncryption
	}
}

// ToBackupTask 转换为BackupTask模型
func (req *BackupTaskCreateRequest) ToBackupTask(userID uint) *BackupTask {
	req.SetDefaults()
	
	task := &BackupTask{
		DatabaseID:    req.DatabaseID,
		TaskName:      req.TaskName,
		BackupType:    req.BackupType,
		Schedule:      req.Schedule,
		RetentionDays: req.RetentionDays,
		Compression:   *req.Compression,
		Encryption:    *req.Encryption,
		Status:        "active",
		CreatedBy:     userID,
	}
	
	return task
}

// ApplyToBackupTask 将更新请求应用到BackupTask模型
func (req *BackupTaskUpdateRequest) ApplyToBackupTask(task *BackupTask) {
	if req.TaskName != nil {
		task.TaskName = *req.TaskName
	}
	
	if req.BackupType != nil {
		task.BackupType = *req.BackupType
	}
	
	if req.Schedule != nil {
		task.Schedule = *req.Schedule
	}
	
	if req.RetentionDays != nil {
		task.RetentionDays = *req.RetentionDays
	}
	
	if req.Compression != nil {
		task.Compression = *req.Compression
	}
	
	if req.Encryption != nil {
		task.Encryption = *req.Encryption
	}
	
	if req.Status != nil {
		task.Status = *req.Status
	}
}
