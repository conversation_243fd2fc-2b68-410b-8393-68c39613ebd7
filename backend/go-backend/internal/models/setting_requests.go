package models

import "time"

// GetSystemSettingsRequest 获取系统设置请求
type GetSystemSettingsRequest struct {
	Category string `form:"category" binding:"omitempty,oneof=system monitoring alert backup security" example:"system"`
	Public   *bool  `form:"public" example:"true"`
	Search   string `form:"search" binding:"max=100" example:"app"`
}

// UpdateSystemSettingsRequest 批量更新系统设置请求
type UpdateSystemSettingsRequest struct {
	Settings []SystemSettingUpdate `json:"settings" binding:"required,dive"`
}

// SystemSettingUpdate 系统设置更新项
type SystemSettingUpdate struct {
	Category    string      `json:"category" binding:"required,oneof=system monitoring alert backup security" example:"system"`
	Key         string      `json:"key" binding:"required,max=100" example:"app_name"`
	Value       interface{} `json:"value" binding:"required" example:"数据库监控平台"`
	ValueType   string      `json:"value_type" binding:"omitempty,oneof=string number boolean json" example:"string"`
	Description string      `json:"description" binding:"max=500" example:"应用程序名称"`
	IsPublic    *bool       `json:"is_public" example:"true"`
}

// GetUserPreferencesRequest 获取用户偏好请求
type GetUserPreferencesRequest struct {
	Category string `form:"category" binding:"omitempty,oneof=ui notification dashboard report" example:"ui"`
	Search   string `form:"search" binding:"max=100" example:"theme"`
}

// UpdateUserPreferencesRequest 更新用户偏好请求
type UpdateUserPreferencesRequest struct {
	Preferences []UserPreferenceUpdate `json:"preferences" binding:"required,dive"`
}

// UserPreferenceUpdate 用户偏好更新项
type UserPreferenceUpdate struct {
	Category  string      `json:"category" binding:"required,oneof=ui notification dashboard report" example:"ui"`
	Key       string      `json:"key" binding:"required,max=100" example:"theme"`
	Value     interface{} `json:"value" binding:"required" example:"dark"`
	ValueType string      `json:"value_type" binding:"omitempty,oneof=string number boolean json" example:"string"`
}

// SystemSettingResponse 系统设置响应
type SystemSettingResponse struct {
	ID          uint      `json:"id" example:"1"`
	Category    string    `json:"category" example:"system"`
	Key         string    `json:"key" example:"app_name"`
	Value       string    `json:"value" example:"数据库监控平台"`
	ValueType   string    `json:"value_type" example:"string"`
	Description string    `json:"description" example:"应用程序名称"`
	IsPublic    bool      `json:"is_public" example:"true"`
	UpdatedBy   uint      `json:"updated_by" example:"1"`
	UpdatedAt   time.Time `json:"updated_at" example:"2025-07-19T10:00:00Z"`
	CreatedAt   time.Time `json:"created_at" example:"2025-07-19T10:00:00Z"`
	Updater     ReportUserResponse `json:"updater"`
}

// UserPreferenceResponse 用户偏好响应
type UserPreferenceResponse struct {
	ID        uint      `json:"id" example:"1"`
	UserID    uint      `json:"user_id" example:"1"`
	Category  string    `json:"category" example:"ui"`
	Key       string    `json:"key" example:"theme"`
	Value     string    `json:"value" example:"dark"`
	ValueType string    `json:"value_type" example:"string"`
	UpdatedAt time.Time `json:"updated_at" example:"2025-07-19T10:00:00Z"`
	CreatedAt time.Time `json:"created_at" example:"2025-07-19T10:00:00Z"`
}

// SystemSettingsGroupResponse 系统设置分组响应
type SystemSettingsGroupResponse struct {
	Category    string                  `json:"category" example:"system"`
	Description string                  `json:"description" example:"系统基础设置"`
	Settings    []SystemSettingResponse `json:"settings"`
}

// UserPreferencesGroupResponse 用户偏好分组响应
type UserPreferencesGroupResponse struct {
	Category    string                   `json:"category" example:"ui"`
	Description string                   `json:"description" example:"界面偏好设置"`
	Preferences []UserPreferenceResponse `json:"preferences"`
}

// SettingCategoryInfo 设置分类信息
type SettingCategoryInfo struct {
	Category    string `json:"category" example:"system"`
	Name        string `json:"name" example:"系统设置"`
	Description string `json:"description" example:"系统基础配置"`
	Icon        string `json:"icon" example:"cog"`
	Order       int    `json:"order" example:"1"`
}

// PreferenceCategoryInfo 偏好分类信息
type PreferenceCategoryInfo struct {
	Category    string `json:"category" example:"ui"`
	Name        string `json:"name" example:"界面偏好"`
	Description string `json:"description" example:"用户界面个性化设置"`
	Icon        string `json:"icon" example:"palette"`
	Order       int    `json:"order" example:"1"`
}

// SettingValidationRule 设置验证规则
type SettingValidationRule struct {
	Key         string      `json:"key" example:"collection_interval"`
	Required    bool        `json:"required" example:"true"`
	ValueType   string      `json:"value_type" example:"number"`
	MinValue    interface{} `json:"min_value" example:"10"`
	MaxValue    interface{} `json:"max_value" example:"3600"`
	AllowedValues []interface{} `json:"allowed_values" example:"[\"light\",\"dark\"]"`
	Pattern     string      `json:"pattern" example:"^[a-zA-Z0-9_]+$"`
	Description string      `json:"description" example:"监控数据收集间隔，范围10-3600秒"`
}

// 系统设置分类信息
var SystemSettingCategories = []SettingCategoryInfo{
	{
		Category:    "system",
		Name:        "系统设置",
		Description: "系统基础配置",
		Icon:        "cog",
		Order:       1,
	},
	{
		Category:    "monitoring",
		Name:        "监控设置",
		Description: "监控数据收集配置",
		Icon:        "chart-line",
		Order:       2,
	},
	{
		Category:    "alert",
		Name:        "告警设置",
		Description: "告警规则和通知配置",
		Icon:        "bell",
		Order:       3,
	},
	{
		Category:    "backup",
		Name:        "备份设置",
		Description: "数据备份配置",
		Icon:        "database",
		Order:       4,
	},
	{
		Category:    "security",
		Name:        "安全设置",
		Description: "安全策略配置",
		Icon:        "shield",
		Order:       5,
	},
}

// 用户偏好分类信息
var UserPreferenceCategories = []PreferenceCategoryInfo{
	{
		Category:    "ui",
		Name:        "界面偏好",
		Description: "用户界面个性化设置",
		Icon:        "palette",
		Order:       1,
	},
	{
		Category:    "notification",
		Name:        "通知偏好",
		Description: "通知方式和频率设置",
		Icon:        "bell",
		Order:       2,
	},
	{
		Category:    "dashboard",
		Name:        "仪表板偏好",
		Description: "仪表板显示和刷新设置",
		Icon:        "dashboard",
		Order:       3,
	},
	{
		Category:    "report",
		Name:        "报表偏好",
		Description: "报表生成和格式偏好",
		Icon:        "file-text",
		Order:       4,
	},
}

// 设置验证规则
var SettingValidationRules = map[string]SettingValidationRule{
	"collection_interval": {
		Key:         "collection_interval",
		Required:    true,
		ValueType:   "number",
		MinValue:    10,
		MaxValue:    3600,
		Description: "监控数据收集间隔，范围10-3600秒",
	},
	"retention_days": {
		Key:         "retention_days",
		Required:    true,
		ValueType:   "number",
		MinValue:    1,
		MaxValue:    365,
		Description: "数据保留天数，范围1-365天",
	},
	"default_threshold": {
		Key:         "default_threshold",
		Required:    true,
		ValueType:   "number",
		MinValue:    1,
		MaxValue:    100,
		Description: "默认告警阈值，范围1-100%",
	},
	"theme": {
		Key:           "theme",
		Required:      true,
		ValueType:     "string",
		AllowedValues: []interface{}{"light", "dark", "auto"},
		Description:   "界面主题，可选值：light, dark, auto",
	},
	"language": {
		Key:           "language",
		Required:      true,
		ValueType:     "string",
		AllowedValues: []interface{}{"zh-CN", "en-US"},
		Description:   "界面语言，可选值：zh-CN, en-US",
	},
}
