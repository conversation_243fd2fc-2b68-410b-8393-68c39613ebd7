package models

import (
	"time"
)

// CreateReportTemplateRequest 创建报表模板请求
type CreateReportTemplateRequest struct {
	Name        string                 `json:"name" binding:"required,max=100" example:"数据库性能报表"`
	Description string                 `json:"description" binding:"max=500" example:"数据库性能指标统计报表"`
	Type        string                 `json:"type" binding:"required,oneof=performance usage alert" example:"performance"`
	Config      map[string]interface{} `json:"config" binding:"required" example:"{\"metrics\":[\"cpu_usage\",\"memory_usage\"],\"chart_type\":\"line\"}"`
}

// UpdateReportTemplateRequest 更新报表模板请求
type UpdateReportTemplateRequest struct {
	Name        string                 `json:"name" binding:"max=100" example:"数据库性能报表"`
	Description string                 `json:"description" binding:"max=500" example:"数据库性能指标统计报表"`
	Type        string                 `json:"type" binding:"oneof=performance usage alert" example:"performance"`
	Config      map[string]interface{} `json:"config" example:"{\"metrics\":[\"cpu_usage\",\"memory_usage\"],\"chart_type\":\"line\"}"`
	IsActive    *bool                  `json:"is_active" example:"true"`
}

// GetReportTemplatesRequest 获取报表模板列表请求
type GetReportTemplatesRequest struct {
	Page     int    `form:"page" binding:"omitempty,min=1" example:"1"`
	PageSize int    `form:"page_size" binding:"omitempty,min=1,max=100" example:"10"`
	Type     string `form:"type" binding:"omitempty,oneof=performance usage alert" example:"performance"`
	Search   string `form:"search" binding:"max=100" example:"性能"`
	IsActive *bool  `form:"is_active" example:"true"`
}

// ExecuteReportRequest 执行报表请求
type ExecuteReportRequest struct {
	TemplateID  uint                   `json:"template_id" binding:"required" example:"1"`
	Parameters  map[string]interface{} `json:"parameters" example:"{\"granularity\":\"hour\"}"`
	Format      string                 `json:"format" binding:"required,oneof=csv excel json pdf" example:"csv"`
	TimeRange   TimeRangeRequest       `json:"time_range" binding:"required"`
	DatabaseIDs []uint                 `json:"database_ids" example:"[1,2,3]"`
}

// TimeRangeRequest 时间范围请求
type TimeRangeRequest struct {
	StartTime time.Time `json:"start_time" binding:"required" example:"2025-07-18T00:00:00Z"`
	EndTime   time.Time `json:"end_time" binding:"required" example:"2025-07-19T00:00:00Z"`
}

// GetReportExecutionsRequest 获取报表执行记录请求
type GetReportExecutionsRequest struct {
	Page       int    `form:"page" binding:"omitempty,min=1" example:"1"`
	PageSize   int    `form:"page_size" binding:"omitempty,min=1,max=100" example:"10"`
	TemplateID uint   `form:"template_id" example:"1"`
	Status     string `form:"status" binding:"omitempty,oneof=pending running completed failed" example:"completed"`
	ExecutedBy uint   `form:"executed_by" example:"1"`
}

// GetChartDataRequest 获取图表数据请求
type GetChartDataRequest struct {
	ChartType   string    `form:"chart_type" binding:"required,oneof=line bar pie area" example:"line"`
	MetricType  string    `form:"metric_type" binding:"required" example:"cpu_usage"`
	DatabaseID  uint      `form:"database_id" example:"1"`
	StartTime   time.Time `form:"start_time" binding:"required" example:"2025-07-18T00:00:00Z"`
	EndTime     time.Time `form:"end_time" binding:"required" example:"2025-07-19T00:00:00Z"`
	Granularity string    `form:"granularity" binding:"omitempty,oneof=minute hour day week month" example:"hour"`
}

// GetSummaryStatsRequest 获取汇总统计请求
type GetSummaryStatsRequest struct {
	DatabaseIDs []uint           `form:"database_ids" example:"1,2,3"`
	TimeRange   TimeRangeRequest `form:"time_range" binding:"required"`
	MetricTypes []string         `form:"metric_types" example:"cpu_usage,memory_usage"`
}

// ReportTemplateResponse 报表模板响应
type ReportTemplateResponse struct {
	ID          uint                   `json:"id" example:"1"`
	Name        string                 `json:"name" example:"数据库性能报表"`
	Description string                 `json:"description" example:"数据库性能指标统计报表"`
	Type        string                 `json:"type" example:"performance"`
	Config      map[string]interface{} `json:"config" example:"{\"metrics\":[\"cpu_usage\",\"memory_usage\"]}"`
	CreatedBy   uint                   `json:"created_by" example:"1"`
	IsActive    bool                   `json:"is_active" example:"true"`
	CreatedAt   time.Time              `json:"created_at" example:"2025-07-19T10:00:00Z"`
	UpdatedAt   time.Time              `json:"updated_at" example:"2025-07-19T10:00:00Z"`
	Creator     ReportUserResponse     `json:"creator"`
}

// ReportExecutionResponse 报表执行响应
type ReportExecutionResponse struct {
	ID           uint                    `json:"id" example:"1"`
	TemplateID   uint                    `json:"template_id" example:"1"`
	ExecutedBy   uint                    `json:"executed_by" example:"1"`
	Status       string                  `json:"status" example:"completed"`
	Parameters   map[string]interface{}  `json:"parameters" example:"{\"granularity\":\"hour\"}"`
	FilePath     string                  `json:"file_path" example:"/tmp/reports/report_1.csv"`
	FileSize     int64                   `json:"file_size" example:"1024"`
	ErrorMessage string                  `json:"error_message" example:""`
	StartTime    time.Time               `json:"start_time" example:"2025-07-19T10:00:00Z"`
	EndTime      *time.Time              `json:"end_time" example:"2025-07-19T10:01:00Z"`
	Duration     int64                   `json:"duration" example:"60000"`
	CreatedAt    time.Time               `json:"created_at" example:"2025-07-19T10:00:00Z"`
	Template     ReportTemplateResponse  `json:"template"`
	Executor     ReportUserResponse      `json:"executor"`
}

// ChartDataResponse 图表数据响应
type ChartDataResponse struct {
	Labels []string      `json:"labels" example:"[\"10:00\",\"11:00\",\"12:00\"]"`
	Series []ChartSeries `json:"series"`
}

// ChartSeries 图表数据系列
type ChartSeries struct {
	Name string        `json:"name" example:"CPU使用率"`
	Data []interface{} `json:"data" example:"[45.2,52.1,48.9]"`
	Unit string        `json:"unit" example:"%"`
}

// SummaryStatsResponse 汇总统计响应
type SummaryStatsResponse struct {
	DatabaseCount int64                              `json:"database_count" example:"3"`
	MetricCount   int64                              `json:"metric_count" example:"1000"`
	TimeRange     TimeRangeRequest                   `json:"time_range"`
	Metrics       map[string]ReportMetricSummary     `json:"metrics"`
	Databases     map[string]DatabaseSummary         `json:"databases"`
}

// ReportMetricSummary 报表指标汇总
type ReportMetricSummary struct {
	Name    string  `json:"name" example:"CPU使用率"`
	Unit    string  `json:"unit" example:"%"`
	Avg     float64 `json:"avg" example:"45.2"`
	Min     float64 `json:"min" example:"20.1"`
	Max     float64 `json:"max" example:"85.6"`
	Current float64 `json:"current" example:"48.9"`
}

// DatabaseSummary 数据库汇总
type DatabaseSummary struct {
	ID     uint   `json:"id" example:"1"`
	Name   string `json:"name" example:"生产数据库"`
	Status string `json:"status" example:"active"`
	Health string `json:"health" example:"healthy"`
}

// ReportUserResponse 报表用户响应(简化版)
type ReportUserResponse struct {
	ID       uint   `json:"id" example:"1"`
	Name     string `json:"name" example:"管理员"`
	Email    string `json:"email" example:"<EMAIL>"`
	Role     string `json:"role" example:"admin"`
}

// ReportStatsResponse 报表统计响应
type ReportStatsResponse struct {
	TotalTemplates   int64 `json:"total_templates" example:"10"`
	ActiveTemplates  int64 `json:"active_templates" example:"8"`
	TotalExecutions  int64 `json:"total_executions" example:"100"`
	SuccessfulRuns   int64 `json:"successful_runs" example:"95"`
	FailedRuns       int64 `json:"failed_runs" example:"5"`
	SuccessRate      float64 `json:"success_rate" example:"95.0"`
	AvgExecutionTime float64 `json:"avg_execution_time" example:"30.5"`
}
