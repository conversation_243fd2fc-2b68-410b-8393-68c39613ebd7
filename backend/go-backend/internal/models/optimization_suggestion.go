package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"gorm.io/gorm"
)

// OptimizationSuggestion 优化建议模型
type OptimizationSuggestion struct {
	ID                 uint           `json:"id" gorm:"primaryKey"`
	QueryAnalysisID    uint           `json:"query_analysis_id" gorm:"index;not null"`
	Type               string         `json:"type" gorm:"size:20;not null" validate:"required,oneof=index query_rewrite schema configuration"`
	Priority           string         `json:"priority" gorm:"size:10;not null" validate:"required,oneof=high medium low"`
	Title              string         `json:"title" gorm:"size:200;not null"`
	Description        string         `json:"description" gorm:"type:text;not null"`
	BeforeQuery        string         `json:"before_query,omitempty" gorm:"type:text"`
	AfterQuery         string         `json:"after_query,omitempty" gorm:"type:text"`
	EstimatedImprovement float64      `json:"estimated_improvement" gorm:"default:0"` // 百分比
	ImplementationEffort string       `json:"implementation_effort" gorm:"size:10;default:medium" validate:"oneof=easy medium hard"`
	ImpactAreas        StringArrayJSON `json:"impact_areas" gorm:"type:jsonb"`
	CreatedAt          time.Time      `json:"created_at"`
	UpdatedAt          time.Time      `json:"updated_at"`
	DeletedAt          gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	QueryAnalysis QueryAnalysis `json:"query_analysis,omitempty" gorm:"foreignKey:QueryAnalysisID"`
}

// TableName 指定表名
func (OptimizationSuggestion) TableName() string {
	return "optimization_suggestions"
}

// StringArrayJSON 自定义JSON类型用于存储字符串数组
type StringArrayJSON []string

// Value 实现driver.Valuer接口
func (s StringArrayJSON) Value() (driver.Value, error) {
	return json.Marshal(s)
}

// Scan 实现sql.Scanner接口
func (s *StringArrayJSON) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, &s)
}

// OptimizationSuggestionResponse 优化建议响应
type OptimizationSuggestionResponse struct {
	ID                  uint     `json:"id"`
	Type                string   `json:"type"`
	Priority            string   `json:"priority"`
	Title               string   `json:"title"`
	Description         string   `json:"description"`
	BeforeQuery         string   `json:"before_query,omitempty"`
	AfterQuery          string   `json:"after_query,omitempty"`
	EstimatedImprovement float64  `json:"estimated_improvement"`
	ImplementationEffort string   `json:"implementation_effort"`
	ImpactAreas         []string `json:"impact_areas"`
}

// ToResponse 转换为响应格式
func (os *OptimizationSuggestion) ToResponse() *OptimizationSuggestionResponse {
	return &OptimizationSuggestionResponse{
		ID:                  os.ID,
		Type:                os.Type,
		Priority:            os.Priority,
		Title:               os.Title,
		Description:         os.Description,
		BeforeQuery:         os.BeforeQuery,
		AfterQuery:          os.AfterQuery,
		EstimatedImprovement: os.EstimatedImprovement,
		ImplementationEffort: os.ImplementationEffort,
		ImpactAreas:         os.ImpactAreas,
	}
}

// IndexRecommendation 索引推荐模型
type IndexRecommendation struct {
	ID                  uint           `json:"id" gorm:"primaryKey"`
	QueryAnalysisID     uint           `json:"query_analysis_id" gorm:"index;not null"`
	RecommendedTable    string         `json:"table_name" gorm:"column:table_name;size:100;not null"`
	Columns             StringArrayJSON `json:"columns" gorm:"type:jsonb;not null"`
	IndexType           string         `json:"index_type" gorm:"size:20;not null" validate:"required,oneof=btree hash gin gist"`
	CreateStatement     string         `json:"create_statement" gorm:"type:text;not null"`
	EstimatedSize       string         `json:"estimated_size" gorm:"size:50"`
	EstimatedImprovement float64       `json:"estimated_improvement" gorm:"default:0"` // 百分比
	UsageFrequency      float64        `json:"usage_frequency" gorm:"default:0"` // 百分比
	MaintenanceCost     string         `json:"maintenance_cost" gorm:"size:10;default:medium" validate:"oneof=low medium high"`
	CreatedAt           time.Time      `json:"created_at"`
	UpdatedAt           time.Time      `json:"updated_at"`
	DeletedAt           gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	QueryAnalysis QueryAnalysis `json:"query_analysis,omitempty" gorm:"foreignKey:QueryAnalysisID"`
}

// TableName 指定表名
func (IndexRecommendation) TableName() string {
	return "index_recommendations"
}

// IndexRecommendationResponse 索引推荐响应
type IndexRecommendationResponse struct {
	ID                  uint     `json:"id"`
	TableName           string   `json:"table_name"`
	Columns             []string `json:"columns"`
	IndexType           string   `json:"index_type"`
	CreateStatement     string   `json:"create_statement"`
	EstimatedSize       string   `json:"estimated_size"`
	EstimatedImprovement float64  `json:"estimated_improvement"`
	UsageFrequency      float64  `json:"usage_frequency"`
	MaintenanceCost     string   `json:"maintenance_cost"`
}

// ToResponse 转换为响应格式
func (ir *IndexRecommendation) ToResponse() *IndexRecommendationResponse {
	return &IndexRecommendationResponse{
		ID:                  ir.ID,
		TableName:           ir.RecommendedTable,
		Columns:             ir.Columns,
		IndexType:           ir.IndexType,
		CreateStatement:     ir.CreateStatement,
		EstimatedSize:       ir.EstimatedSize,
		EstimatedImprovement: ir.EstimatedImprovement,
		UsageFrequency:      ir.UsageFrequency,
		MaintenanceCost:     ir.MaintenanceCost,
	}
}
