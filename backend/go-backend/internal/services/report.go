package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"db-monitor-platform/internal/models"
	"db-monitor-platform/internal/repository"
	"db-monitor-platform/internal/utils"

	"gorm.io/gorm"
)

// ReportService 报表服务接口
type ReportService interface {
	// 模板管理
	CreateTemplate(ctx context.Context, req *models.CreateReportTemplateRequest, userID uint) (*models.ReportTemplate, error)
	GetTemplates(ctx context.Context, req *models.GetReportTemplatesRequest, userID uint) (*models.PaginationResponse, error)
	GetTemplateByID(ctx context.Context, id uint, userID uint) (*models.ReportTemplate, error)
	UpdateTemplate(ctx context.Context, id uint, req *models.UpdateReportTemplateRequest, userID uint) error
	DeleteTemplate(ctx context.Context, id uint, userID uint) error
	
	// 报表执行
	ExecuteReport(ctx context.Context, req *models.ExecuteReportRequest, userID uint) (*models.ReportExecution, error)
	GetExecutionStatus(ctx context.Context, executionID uint, userID uint) (*models.ReportExecution, error)
	GetExecutions(ctx context.Context, req *models.GetReportExecutionsRequest, userID uint) (*models.PaginationResponse, error)
	
	// 图表数据
	GetChartData(ctx context.Context, req *models.GetChartDataRequest, userID uint) (*models.ChartDataResponse, error)
	GetSummaryStats(ctx context.Context, req *models.GetSummaryStatsRequest, userID uint) (*models.SummaryStatsResponse, error)
}

type reportService struct {
	templateRepo    repository.ReportTemplateRepository
	executionRepo   repository.ReportExecutionRepository
	metricRepo      repository.MetricRepository
	databaseRepo    repository.DatabaseRepository
	userRepo        repository.UserRepository
	reportGenerator ReportGenerator
	dataQuerySvc    DataQueryService
	fileManager     utils.FileManager
}

// NewReportService 创建报表服务实例
func NewReportService(db *gorm.DB) ReportService {
	// 创建依赖服务
	metricRepo := repository.NewMetricRepository(db)
	databaseRepo := repository.NewDatabaseRepository(db)
	fileManager := utils.NewFileManager(utils.GetReportStoragePath())
	reportGenerator := NewReportGenerator(fileManager)
	dataQuerySvc := NewDataQueryService(metricRepo, databaseRepo)

	return &reportService{
		templateRepo:    repository.NewReportTemplateRepository(db),
		executionRepo:   repository.NewReportExecutionRepository(db),
		metricRepo:      metricRepo,
		databaseRepo:    databaseRepo,
		userRepo:        repository.NewUserRepository(db),
		reportGenerator: reportGenerator,
		dataQuerySvc:    dataQuerySvc,
		fileManager:     fileManager,
	}
}

// CreateTemplate 创建报表模板
func (s *reportService) CreateTemplate(ctx context.Context, req *models.CreateReportTemplateRequest, userID uint) (*models.ReportTemplate, error) {
	// 验证报表类型
	if !models.ValidateReportType(req.Type) {
		return nil, fmt.Errorf("invalid report type: %s", req.Type)
	}

	// 序列化配置
	configJSON, err := json.Marshal(req.Config)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal config: %w", err)
	}

	template := &models.ReportTemplate{
		Name:        req.Name,
		Description: req.Description,
		Type:        req.Type,
		Config:      string(configJSON),
		CreatedBy:   userID,
		IsActive:    true,
	}

	if err := s.templateRepo.Create(ctx, template); err != nil {
		return nil, fmt.Errorf("failed to create template: %w", err)
	}

	// 重新获取包含关联数据的模板
	return s.templateRepo.GetByID(ctx, template.ID)
}

// GetTemplates 获取报表模板列表
func (s *reportService) GetTemplates(ctx context.Context, req *models.GetReportTemplatesRequest, userID uint) (*models.PaginationResponse, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	templates, total, err := s.templateRepo.GetList(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get templates: %w", err)
	}

	return models.NewPaginationResponse(req.Page, req.PageSize, total, templates), nil
}

// GetTemplateByID 根据ID获取报表模板
func (s *reportService) GetTemplateByID(ctx context.Context, id uint, userID uint) (*models.ReportTemplate, error) {
	template, err := s.templateRepo.GetByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("template not found")
		}
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	return template, nil
}

// UpdateTemplate 更新报表模板
func (s *reportService) UpdateTemplate(ctx context.Context, id uint, req *models.UpdateReportTemplateRequest, userID uint) error {
	// 获取现有模板
	template, err := s.templateRepo.GetByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("template not found")
		}
		return fmt.Errorf("failed to get template: %w", err)
	}

	// 检查权限（只有创建者或管理员可以修改）
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	if template.CreatedBy != userID && user.Role != "admin" {
		return fmt.Errorf("permission denied")
	}

	// 更新字段
	if req.Name != "" {
		template.Name = req.Name
	}
	if req.Description != "" {
		template.Description = req.Description
	}
	if req.Type != "" {
		if !models.ValidateReportType(req.Type) {
			return fmt.Errorf("invalid report type: %s", req.Type)
		}
		template.Type = req.Type
	}
	if req.Config != nil {
		configJSON, err := json.Marshal(req.Config)
		if err != nil {
			return fmt.Errorf("failed to marshal config: %w", err)
		}
		template.Config = string(configJSON)
	}
	if req.IsActive != nil {
		template.IsActive = *req.IsActive
	}

	return s.templateRepo.Update(ctx, template)
}

// DeleteTemplate 删除报表模板
func (s *reportService) DeleteTemplate(ctx context.Context, id uint, userID uint) error {
	// 获取现有模板
	template, err := s.templateRepo.GetByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("template not found")
		}
		return fmt.Errorf("failed to get template: %w", err)
	}

	// 检查权限
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	if template.CreatedBy != userID && user.Role != "admin" {
		return fmt.Errorf("permission denied")
	}

	return s.templateRepo.Delete(ctx, id)
}

// ExecuteReport 执行报表生成
func (s *reportService) ExecuteReport(ctx context.Context, req *models.ExecuteReportRequest, userID uint) (*models.ReportExecution, error) {
	// 验证模板存在
	template, err := s.templateRepo.GetByID(ctx, req.TemplateID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("template not found")
		}
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	if !template.IsActive {
		return nil, fmt.Errorf("template is not active")
	}

	// 验证报表格式
	if !models.ValidateReportFormat(req.Format) {
		return nil, fmt.Errorf("invalid report format: %s", req.Format)
	}

	// 验证时间范围
	if req.TimeRange.StartTime.After(req.TimeRange.EndTime) {
		return nil, fmt.Errorf("start time must be before end time")
	}

	// 序列化参数
	parametersJSON, err := json.Marshal(req.Parameters)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal parameters: %w", err)
	}

	// 创建执行记录
	execution := &models.ReportExecution{
		TemplateID:  req.TemplateID,
		ExecutedBy:  userID,
		Status:      models.ExecutionStatusPending,
		Parameters:  string(parametersJSON),
		StartTime:   time.Now(),
	}

	if err := s.executionRepo.Create(ctx, execution); err != nil {
		return nil, fmt.Errorf("failed to create execution: %w", err)
	}

	// 启动异步报表生成任务
	go s.generateReportAsync(execution.ID, req, userID, template)

	return s.executionRepo.GetByID(ctx, execution.ID)
}

// GetExecutionStatus 获取报表执行状态
func (s *reportService) GetExecutionStatus(ctx context.Context, executionID uint, userID uint) (*models.ReportExecution, error) {
	execution, err := s.executionRepo.GetByID(ctx, executionID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("execution not found")
		}
		return nil, fmt.Errorf("failed to get execution: %w", err)
	}

	// 检查权限（只有执行者或管理员可以查看）
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	if execution.ExecutedBy != userID && user.Role != "admin" {
		return nil, fmt.Errorf("permission denied")
	}

	return execution, nil
}

// GetExecutions 获取报表执行记录列表
func (s *reportService) GetExecutions(ctx context.Context, req *models.GetReportExecutionsRequest, userID uint) (*models.PaginationResponse, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 非管理员只能查看自己的执行记录
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	if user.Role != "admin" {
		req.ExecutedBy = userID
	}

	executions, total, err := s.executionRepo.GetList(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get executions: %w", err)
	}

	return models.NewPaginationResponse(req.Page, req.PageSize, total, executions), nil
}

// GetChartData 获取图表数据
func (s *reportService) GetChartData(ctx context.Context, req *models.GetChartDataRequest, userID uint) (*models.ChartDataResponse, error) {
	// TODO: 实现图表数据获取逻辑
	// 这里应该根据请求参数从metrics表获取数据并格式化为图表格式
	
	// 临时返回模拟数据
	return &models.ChartDataResponse{
		Labels: []string{"10:00", "11:00", "12:00", "13:00", "14:00"},
		Series: []models.ChartSeries{
			{
				Name: "CPU使用率",
				Data: []interface{}{45.2, 52.1, 48.9, 55.3, 49.7},
				Unit: "%",
			},
		},
	}, nil
}

// GetSummaryStats 获取汇总统计
func (s *reportService) GetSummaryStats(ctx context.Context, req *models.GetSummaryStatsRequest, userID uint) (*models.SummaryStatsResponse, error) {
	// TODO: 实现汇总统计逻辑
	// 这里应该根据请求参数计算各种统计指标
	
	// 临时返回模拟数据
	return &models.SummaryStatsResponse{
		DatabaseCount: 3,
		MetricCount:   1000,
		TimeRange:     req.TimeRange,
		Metrics: map[string]models.ReportMetricSummary{
			"cpu_usage": {
				Name:    "CPU使用率",
				Unit:    "%",
				Avg:     45.2,
				Min:     20.1,
				Max:     85.6,
				Current: 48.9,
			},
		},
		Databases: map[string]models.DatabaseSummary{
			"1": {
				ID:     1,
				Name:   "生产数据库",
				Status: "active",
				Health: "healthy",
			},
		},
	}, nil
}

// generateReportAsync 异步生成报表
func (s *reportService) generateReportAsync(executionID uint, req *models.ExecuteReportRequest, userID uint, template *models.ReportTemplate) {
	ctx := context.Background()

	// 添加调试日志
	fmt.Printf("Starting async report generation for execution ID: %d\n", executionID)

	// 更新状态为运行中
	if err := s.updateExecutionStatus(ctx, executionID, models.ExecutionStatusRunning, "", "", 0); err != nil {
		s.handleGenerationError(ctx, executionID, fmt.Errorf("failed to update status to running: %w", err))
		return
	}

	fmt.Printf("Updated execution %d status to running\n", executionID)

	startTime := time.Now()

	// 获取用户信息
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		s.handleGenerationError(ctx, executionID, fmt.Errorf("failed to get user: %w", err))
		return
	}

	// 构建数据查询请求
	queryReq := &MetricQueryRequest{
		DatabaseIDs: req.DatabaseIDs,
		MetricTypes: []string{}, // 使用默认指标类型
		TimeRange:   req.TimeRange,
		Granularity: "hour", // 默认按小时聚合
	}

	// 如果参数中指定了粒度，使用指定的粒度
	if granularity, ok := req.Parameters["granularity"].(string); ok {
		queryReq.Granularity = granularity
	}

	// 查询监控数据
	metrics, err := s.dataQuerySvc.QueryMetrics(queryReq)
	if err != nil {
		s.handleGenerationError(ctx, executionID, fmt.Errorf("failed to query metrics: %w", err))
		return
	}

	// 查询数据库信息
	databases, err := s.dataQuerySvc.QueryDatabases(req.DatabaseIDs)
	if err != nil {
		s.handleGenerationError(ctx, executionID, fmt.Errorf("failed to query databases: %w", err))
		return
	}

	// 构建报表数据
	reportData := &ReportData{
		Template:    template,
		TimeRange:   req.TimeRange,
		Metrics:     metrics,
		Databases:   databases,
		Parameters:  req.Parameters,
		UserInfo:    user,
		ExecutionID: executionID,
	}

	// 生成报表文件
	fmt.Printf("Generating report file for execution %d\n", executionID)
	filePath, err := s.reportGenerator.Generate(reportData, req.Format)
	if err != nil {
		s.handleGenerationError(ctx, executionID, fmt.Errorf("failed to generate report: %w", err))
		return
	}

	fmt.Printf("Report file generated successfully: %s\n", filePath)

	// 获取文件大小
	fileSize, err := s.fileManager.GetFileSize(filePath)
	if err != nil {
		s.handleGenerationError(ctx, executionID, fmt.Errorf("failed to get file size: %w", err))
		return
	}

	// 计算执行时长
	duration := time.Since(startTime).Milliseconds()

	// 更新执行记录为完成状态
	if err := s.updateExecutionStatus(ctx, executionID, models.ExecutionStatusCompleted, filePath, "", fileSize, duration); err != nil {
		s.handleGenerationError(ctx, executionID, fmt.Errorf("failed to update status to completed: %w", err))
		return
	}
}

// updateExecutionStatus 更新执行状态
func (s *reportService) updateExecutionStatus(ctx context.Context, executionID uint, status, filePath, errorMessage string, fileSize int64, duration ...int64) error {
	execution, err := s.executionRepo.GetByID(ctx, executionID)
	if err != nil {
		return err
	}

	execution.Status = status
	if filePath != "" {
		execution.FilePath = filePath
	}
	if errorMessage != "" {
		execution.ErrorMessage = errorMessage
	}
	if fileSize > 0 {
		execution.FileSize = fileSize
	}
	if len(duration) > 0 {
		execution.Duration = duration[0]
	}

	if status == models.ExecutionStatusCompleted || status == models.ExecutionStatusFailed {
		now := time.Now()
		execution.EndTime = &now
	}

	return s.executionRepo.Update(ctx, execution)
}

// handleGenerationError 处理生成错误
func (s *reportService) handleGenerationError(ctx context.Context, executionID uint, err error) {
	// 记录错误日志
	fmt.Printf("Report generation error for execution %d: %v\n", executionID, err)

	// 更新执行记录状态为失败
	if updateErr := s.updateExecutionStatus(ctx, executionID, models.ExecutionStatusFailed, "", err.Error(), 0); updateErr != nil {
		fmt.Printf("Failed to update execution status: %v\n", updateErr)
	}
}
