package services

import (
	"fmt"
	"strings"

	pg_query "github.com/pganalyze/pg_query_go/v4"
	"db-monitor-platform/pkg/logger"
)

// PostgreSQLParser PostgreSQL专用解析器
type PostgreSQLParser struct {
	config         *SQLParserConfig
	fallbackParser *FallbackParser
}

// NewPostgreSQLParser 创建PostgreSQL解析器
func NewPostgreSQLParser() *PostgreSQLParser {
	return &PostgreSQLParser{
		config: &SQLParserConfig{
			DatabaseType:   "postgresql",
			EnableAST:      true,
			EnablePatterns: true,
			EnableOptimize: true,
		},
		fallbackParser: NewFallbackParser(),
	}
}

// WithConfig 应用配置
func (p *PostgreSQLParser) WithConfig(config *SQLParserConfig) SQLParser {
	newParser := &PostgreSQLParser{
		config:         config,
		fallbackParser: NewFallbackParser().WithConfig(config).(*FallbackParser),
	}
	if newParser.config == nil {
		newParser.config = &SQLParserConfig{
			DatabaseType: "postgresql",
		}
	}
	return newParser
}

// Parse 解析SQL
func (p *PostgreSQLParser) Parse(sql string) (*SQLParseResult, error) {
	logger.Infof("PostgreSQL parser: parsing SQL query with AST validation")

	// 1. 首先尝试使用pg_query_go进行语法验证
	_, err := pg_query.Parse(sql)
	if err != nil {
		logger.Warnf("PostgreSQL AST parsing failed: %v, falling back to regex parser", err)

		// AST解析失败，使用fallback解析器
		result, fallbackErr := p.fallbackParser.Parse(sql)
		if fallbackErr != nil {
			return nil, fallbackErr
		}

		// 添加fallback状态信息
		result.ParserInfo = &ParserInfo{
			ParserType:     "postgresql_fallback",
			ParserVersion:  "1.0.0",
			FallbackUsed:   true,
			FallbackReason: fmt.Sprintf("AST parsing failed: %s", err.Error()),
			Capabilities:   []string{"basic_parsing", "table_extraction", "column_extraction", "complexity_analysis"},
			Warnings:       []string{"Using fallback parser due to AST parsing failure", "Some advanced features may not be available"},
		}

		// 添加fallback模式检测
		result.Patterns = append(result.Patterns, QueryPattern{
			Type:        PatternAntiPattern,
			Name:        "fallback_parser_used",
			Description: "AST parsing failed, using fallback regex parser",
			Severity:    "WARNING",
			Suggestion:  "Check SQL syntax for PostgreSQL compatibility",
		})

		return result, nil
	}

	// 2. AST解析成功，使用增强的fallback解析器
	result, err := p.fallbackParser.Parse(sql)
	if err != nil {
		return nil, err
	}

	// 3. 添加PostgreSQL特定的增强
	result = p.enhanceWithPostgreSQLFeatures(result, sql)

	// 4. 添加AST验证成功的状态信息
	result.ParserInfo = &ParserInfo{
		ParserType:    "postgresql_enhanced",
		ParserVersion: "1.0.0",
		FallbackUsed:  false,
		Capabilities:  p.GetSupportedFeatures(),
		Warnings:      []string{},
	}

	// 5. 标记为AST验证过的结果
	result.Patterns = append(result.Patterns, QueryPattern{
		Type:        PatternBestPractice,
		Name:        "ast_validated",
		Description: "SQL syntax validated by PostgreSQL AST parser",
		Severity:    "INFO",
		Suggestion:  "Query syntax is valid for PostgreSQL",
	})

	return result, nil
}

// Validate 验证SQL语法
func (p *PostgreSQLParser) Validate(sql string) error {
	logger.Debugf("PostgreSQL parser: validating SQL syntax")
	
	// 使用pg_query_go进行语法验证
	_, err := pg_query.Parse(sql)
	if err != nil {
		// 转换为标准的ParseError格式
		return &ParseError{
			Type:       "SYNTAX_ERROR",
			Message:    err.Error(),
			Suggestion: "Check PostgreSQL syntax documentation",
		}
	}
	
	return nil
}

// ExtractTables 提取表信息
func (p *PostgreSQLParser) ExtractTables(sql string) ([]TableInfo, error) {
	// 先验证语法
	if err := p.Validate(sql); err != nil {
		return p.fallbackParser.ExtractTables(sql)
	}
	return p.fallbackParser.ExtractTables(sql)
}

// ExtractColumns 提取列信息
func (p *PostgreSQLParser) ExtractColumns(sql string) ([]ColumnInfo, error) {
	// 先验证语法
	if err := p.Validate(sql); err != nil {
		return p.fallbackParser.ExtractColumns(sql)
	}
	return p.fallbackParser.ExtractColumns(sql)
}

// ExtractConditions 提取条件信息
func (p *PostgreSQLParser) ExtractConditions(sql string) ([]Condition, error) {
	// 先验证语法
	if err := p.Validate(sql); err != nil {
		return p.fallbackParser.ExtractConditions(sql)
	}
	return p.fallbackParser.ExtractConditions(sql)
}

// CalculateComplexity 计算复杂度
func (p *PostgreSQLParser) CalculateComplexity(sql string) (*ComplexityScore, error) {
	// 尝试使用AST进行精确复杂度计算
	astComplexity, err := p.calculateComplexityFromAST(sql)
	if err != nil {
		logger.Warnf("AST complexity calculation failed: %v, falling back to regex method", err)
		// AST计算失败，使用fallback方法
		complexity, fallbackErr := p.fallbackParser.CalculateComplexity(sql)
		if fallbackErr != nil {
			return nil, fallbackErr
		}
		// 添加PostgreSQL特定的复杂度因子
		complexity = p.enhanceComplexityWithPostgreSQLFeatures(complexity, sql)
		return complexity, nil
	}

	return astComplexity, nil
}

// calculateComplexityFromAST 基于AST计算精确复杂度
func (p *PostgreSQLParser) calculateComplexityFromAST(sql string) (*ComplexityScore, error) {
	// 解析SQL为AST
	parseResult, err := pg_query.Parse(sql)
	if err != nil {
		return nil, fmt.Errorf("failed to parse SQL: %w", err)
	}

	score := &ComplexityScore{
		Breakdown: make(map[string]int),
		Factors:   []ComplexityFactor{},
		Total:     1, // 基础分数
	}
	score.Breakdown["base"] = 1

	// 遍历AST节点计算复杂度
	for _, stmt := range parseResult.Stmts {
		p.analyzeASTNode(stmt.Stmt, score)
	}

	// 设置复杂度级别和建议
	score.Level = p.getComplexityLevel(score.Total)
	score.Recommendation = p.getComplexityRecommendation(score.Total)

	return score, nil
}

// analyzeASTNode 分析AST节点并累加复杂度
func (p *PostgreSQLParser) analyzeASTNode(node *pg_query.Node, score *ComplexityScore) {
	if node == nil {
		return
	}

	switch n := node.Node.(type) {
	case *pg_query.Node_SelectStmt:
		p.analyzeSelectStmt(n.SelectStmt, score)
	case *pg_query.Node_InsertStmt:
		p.analyzeInsertStmt(n.InsertStmt, score)
	case *pg_query.Node_UpdateStmt:
		p.analyzeUpdateStmt(n.UpdateStmt, score)
	case *pg_query.Node_DeleteStmt:
		p.analyzeDeleteStmt(n.DeleteStmt, score)
	case *pg_query.Node_WithClause:
		p.analyzeWithClause(n.WithClause, score)
	}
}

// analyzeSelectStmt 分析SELECT语句
func (p *PostgreSQLParser) analyzeSelectStmt(stmt *pg_query.SelectStmt, score *ComplexityScore) {
	// 分析FROM子句（表和JOIN）
	if stmt.FromClause != nil {
		for _, fromItem := range stmt.FromClause {
			p.analyzeFromClause(fromItem, score)
		}
	}

	// 分析WHERE子句
	if stmt.WhereClause != nil {
		points := p.analyzeWhereClause(stmt.WhereClause, score)
		if points > 0 {
			score.Breakdown["where_conditions"] += points
			score.Total += points
		}
	}

	// 分析GROUP BY
	if stmt.GroupClause != nil && len(stmt.GroupClause) > 0 {
		points := len(stmt.GroupClause)
		score.Breakdown["group_by"] = points
		score.Total += points
		score.Factors = append(score.Factors, ComplexityFactor{
			Type:        "GROUP_BY",
			Count:       len(stmt.GroupClause),
			Points:      points,
			Description: fmt.Sprintf("%d GROUP BY columns", len(stmt.GroupClause)),
			Impact:      p.getImpactLevel(points),
		})
	}

	// 分析HAVING子句
	if stmt.HavingClause != nil {
		points := 2
		score.Breakdown["having"] = points
		score.Total += points
		score.Factors = append(score.Factors, ComplexityFactor{
			Type:        "HAVING",
			Count:       1,
			Points:      points,
			Description: "HAVING clause",
			Impact:      p.getImpactLevel(points),
		})
	}

	// 分析ORDER BY
	if stmt.SortClause != nil && len(stmt.SortClause) > 0 {
		points := len(stmt.SortClause)
		score.Breakdown["order_by"] = points
		score.Total += points
		score.Factors = append(score.Factors, ComplexityFactor{
			Type:        "ORDER_BY",
			Count:       len(stmt.SortClause),
			Points:      points,
			Description: fmt.Sprintf("%d ORDER BY columns", len(stmt.SortClause)),
			Impact:      p.getImpactLevel(points),
		})
	}

	// 分析LIMIT
	if stmt.LimitCount != nil {
		points := 1
		score.Breakdown["limit"] = points
		score.Total += points
	}

	// 分析窗口函数
	if stmt.WindowClause != nil && len(stmt.WindowClause) > 0 {
		points := len(stmt.WindowClause) * 3
		score.Breakdown["window_functions"] = points
		score.Total += points
		score.Factors = append(score.Factors, ComplexityFactor{
			Type:        "WINDOW_FUNCTION",
			Count:       len(stmt.WindowClause),
			Points:      points,
			Description: fmt.Sprintf("%d window functions", len(stmt.WindowClause)),
			Impact:      p.getImpactLevel(points),
		})
	}

	// 分析UNION/INTERSECT/EXCEPT
	if stmt.Op != pg_query.SetOperation_SETOP_NONE {
		points := 3
		opName := stmt.Op.String()
		score.Breakdown["set_operations"] += points
		score.Total += points
		score.Factors = append(score.Factors, ComplexityFactor{
			Type:        "SET_OPERATION",
			Count:       1,
			Points:      points,
			Description: fmt.Sprintf("%s operation", opName),
			Impact:      p.getImpactLevel(points),
		})
	}

	// 递归分析子查询
	if stmt.Larg != nil {
		p.analyzeSelectStmt(stmt.Larg, score)
	}
	if stmt.Rarg != nil {
		p.analyzeSelectStmt(stmt.Rarg, score)
	}
}

// analyzeFromClause 分析FROM子句
func (p *PostgreSQLParser) analyzeFromClause(fromItem *pg_query.Node, score *ComplexityScore) {
	if fromItem == nil {
		return
	}

	switch n := fromItem.Node.(type) {
	case *pg_query.Node_JoinExpr:
		// JOIN操作
		points := 2
		joinType := "JOIN"
		if n.JoinExpr.Jointype != pg_query.JoinType_JOIN_INNER {
			joinType = n.JoinExpr.Jointype.String()
			if n.JoinExpr.Jointype == pg_query.JoinType_JOIN_FULL {
				points = 4 // FULL JOIN更复杂
			} else if n.JoinExpr.Jointype == pg_query.JoinType_JOIN_LEFT || n.JoinExpr.Jointype == pg_query.JoinType_JOIN_RIGHT {
				points = 3 // OUTER JOIN稍微复杂
			}
		}

		score.Breakdown["joins"] += points
		score.Total += points
		score.Factors = append(score.Factors, ComplexityFactor{
			Type:        "JOIN",
			Count:       1,
			Points:      points,
			Description: fmt.Sprintf("%s operation", joinType),
			Impact:      p.getImpactLevel(points),
		})

		// 递归分析JOIN的左右子树
		p.analyzeFromClause(n.JoinExpr.Larg, score)
		p.analyzeFromClause(n.JoinExpr.Rarg, score)

	case *pg_query.Node_RangeSubselect:
		// 子查询
		points := 3
		score.Breakdown["subqueries"] += points
		score.Total += points
		score.Factors = append(score.Factors, ComplexityFactor{
			Type:        "SUBQUERY",
			Count:       1,
			Points:      points,
			Description: "Subquery in FROM clause",
			Impact:      p.getImpactLevel(points),
		})

		// 递归分析子查询
		if n.RangeSubselect.Subquery != nil {
			if selectStmt, ok := n.RangeSubselect.Subquery.Node.(*pg_query.Node_SelectStmt); ok {
				p.analyzeSelectStmt(selectStmt.SelectStmt, score)
			}
		}
	}
}

// analyzeWhereClause 分析WHERE子句复杂度
func (p *PostgreSQLParser) analyzeWhereClause(whereClause *pg_query.Node, score *ComplexityScore) int {
	if whereClause == nil {
		return 0
	}

	points := 0

	switch n := whereClause.Node.(type) {
	case *pg_query.Node_BoolExpr:
		// AND/OR逻辑表达式
		if n.BoolExpr.Boolop == pg_query.BoolExprType_AND_EXPR {
			points += len(n.BoolExpr.Args) // AND条件数量
		} else if n.BoolExpr.Boolop == pg_query.BoolExprType_OR_EXPR {
			points += len(n.BoolExpr.Args) * 2 // OR条件更复杂
		}

		// 递归分析子表达式
		for _, arg := range n.BoolExpr.Args {
			points += p.analyzeWhereClause(arg, score)
		}

	case *pg_query.Node_SubLink:
		// 子查询
		points += 4 // 子查询在WHERE中复杂度较高
		score.Breakdown["subqueries"] += 4
		score.Factors = append(score.Factors, ComplexityFactor{
			Type:        "SUBQUERY",
			Count:       1,
			Points:      4,
			Description: "Subquery in WHERE clause",
			Impact:      p.getImpactLevel(4),
		})

	case *pg_query.Node_FuncCall:
		// 函数调用
		points += 1

	default:
		// 基本条件
		points += 1
	}

	return points
}

// analyzeInsertStmt 分析INSERT语句
func (p *PostgreSQLParser) analyzeInsertStmt(stmt *pg_query.InsertStmt, score *ComplexityScore) {
	// INSERT基础复杂度
	points := 1
	score.Breakdown["insert"] = points
	score.Total += points

	// 如果有子查询
	if stmt.SelectStmt != nil {
		if selectStmt, ok := stmt.SelectStmt.Node.(*pg_query.Node_SelectStmt); ok {
			p.analyzeSelectStmt(selectStmt.SelectStmt, score)
		}
	}

	// ON CONFLICT子句
	if stmt.OnConflictClause != nil {
		points := 2
		score.Breakdown["on_conflict"] = points
		score.Total += points
		score.Factors = append(score.Factors, ComplexityFactor{
			Type:        "ON_CONFLICT",
			Count:       1,
			Points:      points,
			Description: "ON CONFLICT clause",
			Impact:      p.getImpactLevel(points),
		})
	}
}

// analyzeUpdateStmt 分析UPDATE语句
func (p *PostgreSQLParser) analyzeUpdateStmt(stmt *pg_query.UpdateStmt, score *ComplexityScore) {
	// UPDATE基础复杂度
	points := 1
	score.Breakdown["update"] = points
	score.Total += points

	// 分析WHERE子句
	if stmt.WhereClause != nil {
		wherePoints := p.analyzeWhereClause(stmt.WhereClause, score)
		if wherePoints > 0 {
			score.Breakdown["where_conditions"] += wherePoints
			score.Total += wherePoints
		}
	}

	// 分析FROM子句（UPDATE ... FROM）
	if stmt.FromClause != nil {
		for _, fromItem := range stmt.FromClause {
			p.analyzeFromClause(fromItem, score)
		}
	}
}

// analyzeDeleteStmt 分析DELETE语句
func (p *PostgreSQLParser) analyzeDeleteStmt(stmt *pg_query.DeleteStmt, score *ComplexityScore) {
	// DELETE基础复杂度
	points := 1
	score.Breakdown["delete"] = points
	score.Total += points

	// 分析WHERE子句
	if stmt.WhereClause != nil {
		wherePoints := p.analyzeWhereClause(stmt.WhereClause, score)
		if wherePoints > 0 {
			score.Breakdown["where_conditions"] += wherePoints
			score.Total += wherePoints
		}
	}
}

// analyzeWithClause 分析WITH子句（CTE）
func (p *PostgreSQLParser) analyzeWithClause(stmt *pg_query.WithClause, score *ComplexityScore) {
	if stmt.Ctes == nil {
		return
	}

	points := len(stmt.Ctes) * 2
	score.Breakdown["cte"] = points
	score.Total += points
	score.Factors = append(score.Factors, ComplexityFactor{
		Type:        "CTE",
		Count:       len(stmt.Ctes),
		Points:      points,
		Description: fmt.Sprintf("%d Common Table Expressions", len(stmt.Ctes)),
		Impact:      p.getImpactLevel(points),
	})

	// 递归分析每个CTE
	for _, cte := range stmt.Ctes {
		if cteNode, ok := cte.Node.(*pg_query.Node_CommonTableExpr); ok {
			if cteNode.CommonTableExpr.Ctequery != nil {
				if selectStmt, ok := cteNode.CommonTableExpr.Ctequery.Node.(*pg_query.Node_SelectStmt); ok {
					p.analyzeSelectStmt(selectStmt.SelectStmt, score)
				}
			}
		}
	}
}

// getComplexityLevel 获取复杂度级别
func (p *PostgreSQLParser) getComplexityLevel(total int) string {
	if total <= 3 {
		return "LOW"
	} else if total <= 8 {
		return "MEDIUM"
	} else if total <= 15 {
		return "HIGH"
	} else {
		return "VERY_HIGH"
	}
}

// getComplexityRecommendation 获取复杂度建议
func (p *PostgreSQLParser) getComplexityRecommendation(total int) string {
	if total <= 3 {
		return "Query is simple and should perform well"
	} else if total <= 8 {
		return "Query has moderate complexity, consider indexing key columns"
	} else if total <= 15 {
		return "Query is complex, review for optimization opportunities"
	} else {
		return "Query is very complex, consider breaking into smaller parts"
	}
}

// getImpactLevel 获取影响级别
func (p *PostgreSQLParser) getImpactLevel(points int) string {
	if points <= 1 {
		return "LOW"
	} else if points <= 3 {
		return "MEDIUM"
	} else {
		return "HIGH"
	}
}

// DetectPatterns 检测查询模式
func (p *PostgreSQLParser) DetectPatterns(sql string) ([]QueryPattern, error) {
	patterns, err := p.fallbackParser.DetectPatterns(sql)
	if err != nil {
		return nil, err
	}
	
	// 添加PostgreSQL特定的模式检测
	pgPatterns := p.detectPostgreSQLPatterns(sql)
	patterns = append(patterns, pgPatterns...)
	
	return patterns, nil
}

// SuggestOptimizations 生成优化建议
func (p *PostgreSQLParser) SuggestOptimizations(sql string) ([]Optimization, error) {
	optimizations, err := p.fallbackParser.SuggestOptimizations(sql)
	if err != nil {
		return nil, err
	}
	
	// 添加PostgreSQL特定的优化建议
	pgOptimizations := p.generatePostgreSQLOptimizations(sql)
	optimizations = append(optimizations, pgOptimizations...)
	
	return optimizations, nil
}

// FormatSQL 格式化SQL
func (p *PostgreSQLParser) FormatSQL(sql string) (string, error) {
	// 尝试使用pg_query格式化
	parseResult, err := pg_query.Parse(sql)
	if err != nil {
		return p.fallbackParser.FormatSQL(sql)
	}
	
	formatted, err := pg_query.Deparse(parseResult)
	if err != nil {
		logger.Warnf("Failed to format SQL with pg_query: %v", err)
		return p.fallbackParser.FormatSQL(sql)
	}
	return formatted, nil
}

// GetParserType 获取解析器类型
func (p *PostgreSQLParser) GetParserType() string {
	return "postgresql_enhanced_regex"
}

// GetSupportedFeatures 获取支持的特性
func (p *PostgreSQLParser) GetSupportedFeatures() []string {
	features := p.fallbackParser.GetSupportedFeatures()
	
	// 添加PostgreSQL特定特性
	pgFeatures := []string{
		"syntax_validation_with_ast",
		"postgresql_specific_patterns",
		"window_functions_detection",
		"cte_support_detection",
		"array_operations_detection",
		"json_operations_detection",
		"full_text_search_detection",
		"recursive_queries_detection",
		"lateral_joins_detection",
		"postgresql_specific_functions",
	}
	
	return append(features, pgFeatures...)
}

// PostgreSQL特定的增强方法

// enhanceWithPostgreSQLFeatures 使用PostgreSQL特性增强解析结果
func (p *PostgreSQLParser) enhanceWithPostgreSQLFeatures(result *SQLParseResult, sql string) *SQLParseResult {
	// 检测PostgreSQL特定特性
	if p.hasWindowFunctions(sql) {
		result.Patterns = append(result.Patterns, QueryPattern{
			Type:        PatternBestPractice,
			Name:        "window_functions_detected",
			Description: "Query uses PostgreSQL window functions",
			Severity:    "INFO",
			Suggestion:  "Window functions are efficient for analytical queries",
		})
	}
	
	if p.hasCTE(sql) {
		result.Patterns = append(result.Patterns, QueryPattern{
			Type:        PatternBestPractice,
			Name:        "cte_detected",
			Description: "Query uses Common Table Expressions (CTE)",
			Severity:    "INFO",
			Suggestion:  "CTEs improve query readability and maintainability",
		})
	}
	
	return result
}

// enhanceComplexityWithPostgreSQLFeatures 使用PostgreSQL特性增强复杂度计算
func (p *PostgreSQLParser) enhanceComplexityWithPostgreSQLFeatures(complexity *ComplexityScore, sql string) *ComplexityScore {
	// 窗口函数复杂度
	if p.hasWindowFunctions(sql) {
		points := 3
		complexity.Breakdown["window_functions"] = points
		complexity.Total += points
		complexity.Factors = append(complexity.Factors, ComplexityFactor{
			Type:        "WINDOW_FUNCTION",
			Count:       1,
			Points:      points,
			Description: "PostgreSQL window functions",
			Impact:      "MEDIUM",
		})
	}
	
	// CTE复杂度
	if p.hasCTE(sql) {
		points := 2
		complexity.Breakdown["cte"] = points
		complexity.Total += points
		complexity.Factors = append(complexity.Factors, ComplexityFactor{
			Type:        "CTE",
			Count:       1,
			Points:      points,
			Description: "Common Table Expressions",
			Impact:      "LOW",
		})
	}
	
	// 重新计算复杂度级别
	complexity.Level = p.getComplexityLevel(complexity.Total)
	
	return complexity
}

// detectPostgreSQLPatterns 检测PostgreSQL特定模式
func (p *PostgreSQLParser) detectPostgreSQLPatterns(sql string) []QueryPattern {
	var patterns []QueryPattern
	
	// 检测LATERAL JOIN
	if p.hasLateralJoin(sql) {
		patterns = append(patterns, QueryPattern{
			Type:        PatternBestPractice,
			Name:        "lateral_join_detected",
			Description: "Query uses PostgreSQL LATERAL JOIN",
			Severity:    "INFO",
			Suggestion:  "LATERAL JOINs enable correlated subqueries in FROM clause",
		})
	}
	
	// 检测JSON操作
	if p.hasJSONOperations(sql) {
		patterns = append(patterns, QueryPattern{
			Type:        PatternOptimizationOpportunity,
			Name:        "json_operations_detected",
			Description: "Query uses JSON operations",
			Severity:    "INFO",
			Suggestion:  "Consider using GIN indexes for JSON columns",
		})
	}
	
	return patterns
}

// generatePostgreSQLOptimizations 生成PostgreSQL特定优化建议
func (p *PostgreSQLParser) generatePostgreSQLOptimizations(sql string) []Optimization {
	var optimizations []Optimization
	
	// JSON索引建议
	if p.hasJSONOperations(sql) {
		optimizations = append(optimizations, Optimization{
			Type:         OptimizationIndex,
			Priority:     "MEDIUM",
			Title:        "JSON Index Recommendation",
			Description:  "Consider creating GIN index for JSON operations",
			ExpectedGain: 30.0,
			Effort:       "LOW",
			Category:     "PERFORMANCE",
		})
	}
	
	// 部分索引建议
	if p.hasFilteredQueries(sql) {
		optimizations = append(optimizations, Optimization{
			Type:         OptimizationIndex,
			Priority:     "HIGH",
			Title:        "Partial Index Recommendation",
			Description:  "Consider creating partial indexes for filtered queries",
			ExpectedGain: 50.0,
			Effort:       "MEDIUM",
			Category:     "PERFORMANCE",
		})
	}
	
	return optimizations
}

// PostgreSQL特性检测辅助方法

func (p *PostgreSQLParser) hasWindowFunctions(sql string) bool {
	windowFunctions := []string{"ROW_NUMBER()", "RANK()", "DENSE_RANK()", "LAG(", "LEAD(", "OVER ("}
	sqlUpper := strings.ToUpper(sql)
	
	for _, fn := range windowFunctions {
		if strings.Contains(sqlUpper, fn) {
			return true
		}
	}
	return false
}

func (p *PostgreSQLParser) hasCTE(sql string) bool {
	sqlUpper := strings.ToUpper(sql)
	return strings.Contains(sqlUpper, "WITH ") && strings.Contains(sqlUpper, " AS (")
}

func (p *PostgreSQLParser) hasLateralJoin(sql string) bool {
	return strings.Contains(strings.ToUpper(sql), "LATERAL ")
}

func (p *PostgreSQLParser) hasJSONOperations(sql string) bool {
	jsonOps := []string{"->", "->>", "#>", "#>>", "?", "?&", "?|", "@>", "<@"}
	
	for _, op := range jsonOps {
		if strings.Contains(sql, op) {
			return true
		}
	}
	return false
}

func (p *PostgreSQLParser) hasFilteredQueries(sql string) bool {
	sqlUpper := strings.ToUpper(sql)
	return strings.Contains(sqlUpper, "WHERE ") && (strings.Contains(sqlUpper, "= ") || strings.Contains(sqlUpper, "IN ("))
}
