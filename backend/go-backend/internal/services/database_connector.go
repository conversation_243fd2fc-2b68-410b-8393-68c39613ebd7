package services

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"db-monitor-platform/internal/models"
	"db-monitor-platform/pkg/logger"

	_ "github.com/go-sql-driver/mysql"
	_ "github.com/lib/pq"
)

// DatabaseConnector 数据库连接器
type DatabaseConnector struct {
	connections map[uint]*sql.DB // 缓存数据库连接
}

// NewDatabaseConnector 创建数据库连接器
func NewDatabaseConnector() *DatabaseConnector {
	return &DatabaseConnector{
		connections: make(map[uint]*sql.DB),
	}
}

// GetConnection 获取数据库连接
func (dc *DatabaseConnector) GetConnection(database *models.DatabaseInstance) (*sql.DB, error) {
	// 检查缓存的连接
	if conn, exists := dc.connections[database.ID]; exists {
		// 测试连接是否仍然有效
		if err := conn.Ping(); err == nil {
			return conn, nil
		}
		// 连接失效，关闭并移除
		conn.Close()
		delete(dc.connections, database.ID)
	}

	// 创建新连接
	conn, err := dc.createConnection(database)
	if err != nil {
		return nil, err
	}

	// 缓存连接
	dc.connections[database.ID] = conn
	return conn, nil
}

// createConnection 创建数据库连接
func (dc *DatabaseConnector) createConnection(database *models.DatabaseInstance) (*sql.DB, error) {
	var dsn string
	var driverName string

	switch database.Type {
	case "postgresql":
		driverName = "postgres"
		dsn = fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
			database.Host, database.Port, database.Username, database.Password, database.DatabaseName)
	case "mysql":
		driverName = "mysql"
		dsn = fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true",
			database.Username, database.Password, database.Host, database.Port, database.DatabaseName)
	default:
		return nil, fmt.Errorf("unsupported database type: %s", database.Type)
	}

	// 创建连接
	db, err := sql.Open(driverName, dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database connection: %w", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(5)
	db.SetMaxIdleConns(2)
	db.SetConnMaxLifetime(5 * time.Minute)

	// 测试连接
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	logger.Infof("Successfully connected to %s database: %s", database.Type, database.Name)
	return db, nil
}

// ExplainQuery 执行EXPLAIN查询
func (dc *DatabaseConnector) ExplainQuery(database *models.DatabaseInstance, sqlQuery string, options *models.ExplainOptions) (*models.ExecutionPlan, error) {
	conn, err := dc.GetConnection(database)
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	switch database.Type {
	case "postgresql":
		return dc.explainPostgreSQL(conn, sqlQuery, options)
	case "mysql":
		return dc.explainMySQL(conn, sqlQuery, options)
	default:
		return nil, fmt.Errorf("EXPLAIN not supported for database type: %s", database.Type)
	}
}

// explainPostgreSQL 执行PostgreSQL的EXPLAIN查询
func (dc *DatabaseConnector) explainPostgreSQL(db *sql.DB, sqlQuery string, options *models.ExplainOptions) (*models.ExecutionPlan, error) {
	// 构建EXPLAIN查询
	explainQuery := "EXPLAIN"
	
	// 添加选项
	var explainOptions []string
	if options.Analyze {
		explainOptions = append(explainOptions, "ANALYZE")
	}
	// PostgreSQL默认支持VERBOSE，这里总是添加
	explainOptions = append(explainOptions, "VERBOSE")
	if options.Buffers {
		explainOptions = append(explainOptions, "BUFFERS")
	}
	if options.Format == "json" {
		explainOptions = append(explainOptions, "FORMAT JSON")
	}

	if len(explainOptions) > 0 {
		explainQuery += " (" + strings.Join(explainOptions, ", ") + ")"
	}

	explainQuery += " " + sqlQuery

	logger.Infof("Executing EXPLAIN query: %s", explainQuery)

	// 执行查询
	rows, err := db.Query(explainQuery)
	if err != nil {
		return nil, fmt.Errorf("failed to execute EXPLAIN query: %w", err)
	}
	defer rows.Close()

	// 解析结果
	if options.Format == "json" {
		return dc.parsePostgreSQLJSONPlan(rows, sqlQuery)
	} else {
		return dc.parsePostgreSQLTextPlan(rows, sqlQuery)
	}
}

// parsePostgreSQLJSONPlan 解析PostgreSQL JSON格式的执行计划
func (dc *DatabaseConnector) parsePostgreSQLJSONPlan(rows *sql.Rows, sqlQuery string) (*models.ExecutionPlan, error) {
	var jsonPlan string
	for rows.Next() {
		var line string
		if err := rows.Scan(&line); err != nil {
			return nil, err
		}
		jsonPlan += line
	}

	// 解析JSON
	var planData []map[string]interface{}
	if err := json.Unmarshal([]byte(jsonPlan), &planData); err != nil {
		return nil, fmt.Errorf("failed to parse JSON plan: %w", err)
	}

	if len(planData) == 0 {
		return nil, fmt.Errorf("empty execution plan")
	}

	planInfo := planData[0]["Plan"].(map[string]interface{})
	
	// 创建执行计划
	plan := &models.ExecutionPlan{
		PlanID:        fmt.Sprintf("plan_%d", time.Now().Unix()),
		TotalCost:     planInfo["Total Cost"].(float64),
		ExecutionTime: 0, // 如果没有ANALYZE，执行时间为0
		RawPlan:       jsonPlan,
		PlanFormat:    "json",
	}

	// 如果有执行时间信息
	if execTime, exists := planData[0]["Execution Time"]; exists {
		plan.ExecutionTime = execTime.(float64)
	}

	// 解析计划节点
	plan.PlanNodes = dc.parsePostgreSQLPlanNodes(planInfo, "1")

	// 提取真实的统计信息
	plan.PlanStatistics = dc.extractPostgreSQLStatistics(planData, plan.PlanNodes, plan.ExecutionTime)

	return plan, nil
}

// parsePostgreSQLPlanNodes 解析PostgreSQL计划节点
func (dc *DatabaseConnector) parsePostgreSQLPlanNodes(planNode map[string]interface{}, nodeID string) []models.PlanNode {
	var nodes []models.PlanNode

	// 创建当前节点
	node := models.PlanNode{
		ID:        nodeID,
		NodeType:  dc.safeStringExtract(planNode, "Node Type"),
		Operation: dc.safeStringExtract(planNode, "Node Type"),
	}

	// === 基本信息 ===
	if relationName, exists := planNode["Relation Name"]; exists {
		node.TableName = dc.safeStringValue(relationName)
	}

	if indexName, exists := planNode["Index Name"]; exists {
		node.IndexName = dc.safeStringValue(indexName)
	}

	if schemaName, exists := planNode["Schema"]; exists {
		node.SchemaName = dc.safeStringValue(schemaName)
	}

	if alias, exists := planNode["Alias"]; exists {
		node.Alias = dc.safeStringValue(alias)
	}

	// === 成本和行数统计 ===
	node.StartupCost = dc.safeFloatExtract(planNode, "Startup Cost")
	node.TotalCost = dc.safeFloatExtract(planNode, "Total Cost")
	node.Cost = node.TotalCost // 兼容字段

	node.PlanRows = dc.safeIntExtract(planNode, "Plan Rows")
	node.Rows = node.PlanRows // 兼容字段

	node.PlanWidth = dc.safeIntExtract(planNode, "Plan Width")
	node.Width = node.PlanWidth // 兼容字段

	// === 实际执行统计 (仅在ANALYZE模式下可用) ===
	if actualStartupTime, exists := planNode["Actual Startup Time"]; exists {
		node.ActualStartupTime = dc.safeFloatValue(actualStartupTime)
	}

	if actualTotalTime, exists := planNode["Actual Total Time"]; exists {
		node.ActualTotalTime = dc.safeFloatValue(actualTotalTime)
		node.ActualTime = node.ActualTotalTime // 兼容字段
	}

	if actualRows, exists := planNode["Actual Rows"]; exists {
		node.ActualRows = dc.safeIntValue(actualRows)
	}

	if actualLoops, exists := planNode["Actual Loops"]; exists {
		node.ActualLoops = dc.safeIntValue(actualLoops)
	}

	// === 条件和过滤 ===
	if filter, exists := planNode["Filter"]; exists {
		node.Filter = dc.safeStringValue(filter)
	}

	if indexCond, exists := planNode["Index Cond"]; exists {
		node.IndexCondition = dc.safeStringValue(indexCond)
	}

	if joinFilter, exists := planNode["Join Filter"]; exists {
		node.Condition = dc.safeStringValue(joinFilter)
	} else if hashCond, exists := planNode["Hash Cond"]; exists {
		node.Condition = dc.safeStringValue(hashCond)
	}

	// === 缓冲区统计 (节点级) ===
	if buffers, exists := planNode["Buffers"]; exists {
		if bufferMap, ok := buffers.(map[string]interface{}); ok {
			if sharedHit, exists := bufferMap["Shared Hit Blocks"]; exists {
				node.SharedHitBlocks = dc.safeIntValue(sharedHit)
			}
			if sharedRead, exists := bufferMap["Shared Read Blocks"]; exists {
				node.SharedReadBlocks = dc.safeIntValue(sharedRead)
			}
			if sharedDirtied, exists := bufferMap["Shared Dirtied Blocks"]; exists {
				node.SharedDirtiedBlocks = dc.safeIntValue(sharedDirtied)
			}
			if sharedWritten, exists := bufferMap["Shared Written Blocks"]; exists {
				node.SharedWrittenBlocks = dc.safeIntValue(sharedWritten)
			}
		}
	}

	// === 排序和哈希信息 ===
	if sortMethod, exists := planNode["Sort Method"]; exists {
		node.SortMethod = dc.safeStringValue(sortMethod)
	}

	if sortSpaceUsed, exists := planNode["Sort Space Used"]; exists {
		node.SortSpaceUsed = dc.safeIntValue(sortSpaceUsed)
	}

	if sortSpaceType, exists := planNode["Sort Space Type"]; exists {
		node.SortSpaceType = dc.safeStringValue(sortSpaceType)
	}

	if hashBuckets, exists := planNode["Hash Buckets"]; exists {
		node.HashBuckets = dc.safeIntValue(hashBuckets)
	}

	if hashBatches, exists := planNode["Hash Batches"]; exists {
		node.HashBatches = dc.safeIntValue(hashBatches)
	}

	if originalHashBatches, exists := planNode["Original Hash Batches"]; exists {
		node.OriginalHashBatches = dc.safeIntValue(originalHashBatches)
	}

	if peakMemory, exists := planNode["Peak Memory Usage"]; exists {
		node.PeakMemoryUsage = dc.safeIntValue(peakMemory)
	}

	nodes = append(nodes, node)

	// 递归处理子节点
	if plans, exists := planNode["Plans"]; exists {
		if plansList, ok := plans.([]interface{}); ok {
			for i, childPlan := range plansList {
				if childMap, ok := childPlan.(map[string]interface{}); ok {
					childNodeID := fmt.Sprintf("%s.%d", nodeID, i+1)
					childNodes := dc.parsePostgreSQLPlanNodes(childMap, childNodeID)
					nodes = append(nodes, childNodes...)
				}
			}
		}
	}

	return nodes
}

// parsePostgreSQLTextPlan 解析PostgreSQL文本格式的执行计划
func (dc *DatabaseConnector) parsePostgreSQLTextPlan(rows *sql.Rows, sqlQuery string) (*models.ExecutionPlan, error) {
	var planLines []string
	for rows.Next() {
		var line string
		if err := rows.Scan(&line); err != nil {
			return nil, err
		}
		planLines = append(planLines, line)
	}

	rawPlan := strings.Join(planLines, "\n")

	// 简单解析文本格式（这里可以进一步优化）
	plan := &models.ExecutionPlan{
		PlanID:        fmt.Sprintf("plan_%d", time.Now().Unix()),
		TotalCost:     0,
		ExecutionTime: 0,
		RawPlan:       rawPlan,
		PlanFormat:    "text",
		PlanNodes:     dc.parseTextPlanNodes(planLines),
	}

	// 计算统计信息 (fallback)
	plan.PlanStatistics = dc.calculateFallbackStatistics(plan.PlanNodes, plan.ExecutionTime)

	return plan, nil
}

// parseTextPlanNodes 解析文本格式的计划节点
func (dc *DatabaseConnector) parseTextPlanNodes(planLines []string) []models.PlanNode {
	var nodes []models.PlanNode
	
	for i, line := range planLines {
		// 简单的文本解析逻辑
		if strings.Contains(line, "Seq Scan") || strings.Contains(line, "Index Scan") || 
		   strings.Contains(line, "Hash Join") || strings.Contains(line, "Nested Loop") {
			
			node := models.PlanNode{
				ID:        fmt.Sprintf("%d", i+1),
				NodeType:  dc.extractNodeType(line),
				Operation: dc.extractNodeType(line),
				Cost:      dc.extractCost(line),
				Rows:      dc.extractRows(line),
				Width:     dc.extractWidth(line),
			}

			// 提取表名
			if tableName := dc.extractTableName(line); tableName != "" {
				node.TableName = tableName
			}

			nodes = append(nodes, node)
		}
	}

	return nodes
}

// 辅助函数用于从文本中提取信息
func (dc *DatabaseConnector) extractNodeType(line string) string {
	// 简单的节点类型提取
	if strings.Contains(line, "Seq Scan") {
		return "Seq Scan"
	} else if strings.Contains(line, "Index Scan") {
		return "Index Scan"
	} else if strings.Contains(line, "Hash Join") {
		return "Hash Join"
	} else if strings.Contains(line, "Nested Loop") {
		return "Nested Loop"
	}
	return "Unknown"
}

func (dc *DatabaseConnector) extractCost(line string) float64 {
	// 提取cost信息，这里需要正则表达式解析
	// 简化实现，返回默认值
	return 100.0
}

func (dc *DatabaseConnector) extractRows(line string) int {
	// 提取rows信息
	return 1000
}

func (dc *DatabaseConnector) extractWidth(line string) int {
	// 提取width信息
	return 32
}

func (dc *DatabaseConnector) extractTableName(line string) string {
	// 提取表名
	if strings.Contains(line, " on ") {
		parts := strings.Split(line, " on ")
		if len(parts) > 1 {
			tablePart := strings.TrimSpace(parts[1])
			return strings.Fields(tablePart)[0]
		}
	}
	return ""
}

// explainMySQL 执行MySQL的EXPLAIN查询
func (dc *DatabaseConnector) explainMySQL(db *sql.DB, sqlQuery string, options *models.ExplainOptions) (*models.ExecutionPlan, error) {
	// MySQL EXPLAIN实现
	explainQuery := "EXPLAIN FORMAT=JSON " + sqlQuery

	rows, err := db.Query(explainQuery)
	if err != nil {
		return nil, fmt.Errorf("failed to execute MySQL EXPLAIN: %w", err)
	}
	defer rows.Close()

	// 解析MySQL JSON结果
	var jsonPlan string
	for rows.Next() {
		if err := rows.Scan(&jsonPlan); err != nil {
			return nil, err
		}
	}

	plan := &models.ExecutionPlan{
		PlanID:        fmt.Sprintf("mysql_plan_%d", time.Now().Unix()),
		TotalCost:     0,
		ExecutionTime: 0,
		RawPlan:       jsonPlan,
		PlanFormat:    "json",
	}

	// 这里可以进一步解析MySQL的JSON格式
	// 简化实现
	plan.PlanNodes = []models.PlanNode{
		{
			ID:        "1",
			NodeType:  "Table Scan",
			Operation: "Table Scan",
			Cost:      100.0,
			Rows:      1000,
			Width:     32,
		},
	}

	plan.PlanStatistics = dc.calculateFallbackStatistics(plan.PlanNodes, plan.ExecutionTime)

	return plan, nil
}

// extractPostgreSQLStatistics 提取PostgreSQL真实统计信息
func (dc *DatabaseConnector) extractPostgreSQLStatistics(planData []map[string]interface{}, nodes []models.PlanNode, executionTime float64) models.StatisticsJSON {
	stats := models.PlanStatistics{}

	if len(planData) == 0 {
		return dc.calculateFallbackStatistics(nodes, executionTime)
	}

	// 1. 提取顶层统计信息
	dc.extractTopLevelStats(planData[0], &stats)

	// 2. 递归提取节点统计信息
	if plan, exists := planData[0]["Plan"]; exists {
		dc.extractNodeStatistics(plan.(map[string]interface{}), &stats)
	}

	// 3. 计算衍生指标
	dc.calculateDerivedMetrics(&stats)

	// 4. 设置兼容字段
	dc.setCompatibilityFields(&stats)

	return models.StatisticsJSON(stats)
}

// extractTopLevelStats 提取顶层统计信息
func (dc *DatabaseConnector) extractTopLevelStats(planData map[string]interface{}, stats *models.PlanStatistics) {
	// 规划时间
	if planningTime, exists := planData["Planning Time"]; exists {
		if time, ok := planningTime.(float64); ok {
			stats.PlanningTime = time
		}
	}

	// 执行时间
	if execTime, exists := planData["Execution Time"]; exists {
		if time, ok := execTime.(float64); ok {
			stats.ExecutionTime = time
			stats.TotalTime = stats.PlanningTime + stats.ExecutionTime
		}
	}
}

// extractNodeStatistics 递归提取节点统计信息
func (dc *DatabaseConnector) extractNodeStatistics(node map[string]interface{}, stats *models.PlanStatistics) {
	// 行数统计
	if planRows, exists := node["Plan Rows"]; exists {
		if rows, ok := planRows.(float64); ok {
			stats.EstimatedRows += int(rows)
		}
	}

	if actualRows, exists := node["Actual Rows"]; exists {
		if rows, ok := actualRows.(float64); ok {
			stats.ActualRows += int(rows)
		}
	}

	// 缓冲区统计 (仅在BUFFERS选项下可用)
	if buffers, exists := node["Buffers"]; exists {
		if bufferMap, ok := buffers.(map[string]interface{}); ok {
			dc.extractBufferStatistics(bufferMap, stats)
		}
	}

	// 递归处理子节点
	if plans, exists := node["Plans"]; exists {
		if plansList, ok := plans.([]interface{}); ok {
			for _, childPlan := range plansList {
				if childMap, ok := childPlan.(map[string]interface{}); ok {
					dc.extractNodeStatistics(childMap, stats)
				}
			}
		}
	}
}

// extractBufferStatistics 提取缓冲区统计信息
func (dc *DatabaseConnector) extractBufferStatistics(buffers map[string]interface{}, stats *models.PlanStatistics) {
	// 共享缓冲区统计
	if sharedHit, exists := buffers["Shared Hit Blocks"]; exists {
		if blocks, ok := sharedHit.(float64); ok {
			stats.SharedHitBlocks += int(blocks)
		}
	}

	if sharedRead, exists := buffers["Shared Read Blocks"]; exists {
		if blocks, ok := sharedRead.(float64); ok {
			stats.SharedReadBlocks += int(blocks)
		}
	}

	if sharedDirtied, exists := buffers["Shared Dirtied Blocks"]; exists {
		if blocks, ok := sharedDirtied.(float64); ok {
			stats.SharedDirtiedBlocks += int(blocks)
		}
	}

	if sharedWritten, exists := buffers["Shared Written Blocks"]; exists {
		if blocks, ok := sharedWritten.(float64); ok {
			stats.SharedWrittenBlocks += int(blocks)
		}
	}

	// 本地缓冲区统计
	if localHit, exists := buffers["Local Hit Blocks"]; exists {
		if blocks, ok := localHit.(float64); ok {
			stats.LocalHitBlocks += int(blocks)
		}
	}

	if localRead, exists := buffers["Local Read Blocks"]; exists {
		if blocks, ok := localRead.(float64); ok {
			stats.LocalReadBlocks += int(blocks)
		}
	}

	// 临时文件统计
	if tempRead, exists := buffers["Temp Read Blocks"]; exists {
		if blocks, ok := tempRead.(float64); ok {
			stats.TempReadBlocks += int(blocks)
		}
	}

	if tempWritten, exists := buffers["Temp Written Blocks"]; exists {
		if blocks, ok := tempWritten.(float64); ok {
			stats.TempWrittenBlocks += int(blocks)
			stats.TempFiles++ // 有临时写入就认为使用了临时文件
		}
	}
}

// calculateDerivedMetrics 计算衍生指标
func (dc *DatabaseConnector) calculateDerivedMetrics(stats *models.PlanStatistics) {
	// 1. 缓冲区命中率
	totalBufferAccess := stats.SharedHitBlocks + stats.SharedReadBlocks
	if totalBufferAccess > 0 {
		stats.BufferHitRatio = float64(stats.SharedHitBlocks) / float64(totalBufferAccess) * 100
	}

	// 2. 行数估算准确性
	if stats.EstimatedRows > 0 && stats.ActualRows > 0 {
		minRows := min(stats.EstimatedRows, stats.ActualRows)
		maxRows := max(stats.EstimatedRows, stats.ActualRows)
		if maxRows > 0 {
			stats.RowAccuracy = float64(minRows) / float64(maxRows) * 100
		}
	}

	// 3. I/O效率评分 (0-100分)
	stats.IOEfficiency = dc.calculateIOEfficiency(stats)

	// 4. 内存压力指标
	stats.MemoryPressure = dc.calculateMemoryPressure(stats)

	// 5. 成本准确性 (如果有实际执行时间)
	if stats.ExecutionTime > 0 && stats.EstimatedRows > 0 && stats.ActualRows > 0 {
		stats.CostAccuracy = dc.calculateCostAccuracy(stats)
	}
}

// calculateIOEfficiency I/O效率评分算法
func (dc *DatabaseConnector) calculateIOEfficiency(stats *models.PlanStatistics) float64 {
	score := 100.0

	// 缓冲区命中率影响 (权重40%)
	if stats.BufferHitRatio < 90 {
		score -= (90 - stats.BufferHitRatio) * 0.4
	}

	// 临时文件使用影响 (权重30%)
	if stats.TempFiles > 0 {
		tempPenalty := float64(stats.TempFiles) * 10
		score -= minFloat(tempPenalty, 30)
	}

	// 磁盘读取比例影响 (权重30%)
	totalIO := stats.SharedHitBlocks + stats.SharedReadBlocks
	if totalIO > 0 {
		diskReadRatio := float64(stats.SharedReadBlocks) / float64(totalIO) * 100
		if diskReadRatio > 10 {
			score -= (diskReadRatio - 10) * 0.3
		}
	}

	return maxFloat(score, 0)
}

// calculateMemoryPressure 内存压力指标
func (dc *DatabaseConnector) calculateMemoryPressure(stats *models.PlanStatistics) float64 {
	pressure := 0.0

	// 临时文件使用表示内存不足
	if stats.TempFiles > 0 {
		pressure += float64(stats.TempFiles) * 20
	}

	// 大量磁盘读取可能表示内存压力
	if stats.SharedReadBlocks > 1000 {
		pressure += float64(stats.SharedReadBlocks) / 1000 * 10
	}

	// 临时文件大小影响
	if stats.TempWrittenBlocks > 0 {
		pressure += float64(stats.TempWrittenBlocks) / 1000 * 5
	}

	return minFloat(pressure, 100)
}

// calculateCostAccuracy 成本准确性评估
func (dc *DatabaseConnector) calculateCostAccuracy(stats *models.PlanStatistics) float64 {
	// 基于行数估算准确性来评估成本准确性
	// 这是一个简化的实现，实际可以更复杂
	if stats.RowAccuracy > 0 {
		return stats.RowAccuracy
	}
	return 0
}

// setCompatibilityFields 设置兼容性字段
func (dc *DatabaseConnector) setCompatibilityFields(stats *models.PlanStatistics) {
	// 兼容性字段
	stats.BufferHits = stats.SharedHitBlocks + stats.LocalHitBlocks
	stats.BufferReads = stats.SharedReadBlocks + stats.LocalReadBlocks
	stats.TotalRowsProcessed = max(stats.EstimatedRows, stats.ActualRows)
	stats.TempSize = stats.TempWrittenBlocks * 8192 // PostgreSQL块大小8KB

	// 如果没有实际行数，使用估算行数
	if stats.ActualRows == 0 && stats.EstimatedRows > 0 {
		stats.TotalRowsProcessed = stats.EstimatedRows
	}
}

// calculateFallbackStatistics 计算fallback统计信息（兼容旧版本）
func (dc *DatabaseConnector) calculateFallbackStatistics(nodes []models.PlanNode, executionTime float64) models.StatisticsJSON {
	totalRows := 0
	for _, node := range nodes {
		totalRows += node.Rows
	}

	stats := models.PlanStatistics{
		TotalRowsProcessed: totalRows,
		TotalTime:          executionTime,
		ExecutionTime:      executionTime,
		EstimatedRows:      totalRows,
		BufferHits:         100, // 默认值
		BufferReads:        10,  // 默认值
		BufferHitRatio:     90.0, // 默认90%命中率
		IOEfficiency:       75.0, // 默认75分
		MemoryPressure:     20.0, // 默认20%内存压力
	}

	return models.StatisticsJSON(stats)
}

// 安全的数据提取辅助函数
func (dc *DatabaseConnector) safeStringExtract(data map[string]interface{}, key string) string {
	if value, exists := data[key]; exists {
		return dc.safeStringValue(value)
	}
	return ""
}

func (dc *DatabaseConnector) safeStringValue(value interface{}) string {
	if str, ok := value.(string); ok {
		return str
	}
	return ""
}

func (dc *DatabaseConnector) safeFloatExtract(data map[string]interface{}, key string) float64 {
	if value, exists := data[key]; exists {
		return dc.safeFloatValue(value)
	}
	return 0.0
}

func (dc *DatabaseConnector) safeFloatValue(value interface{}) float64 {
	if f, ok := value.(float64); ok {
		return f
	}
	if i, ok := value.(int); ok {
		return float64(i)
	}
	return 0.0
}

func (dc *DatabaseConnector) safeIntExtract(data map[string]interface{}, key string) int {
	if value, exists := data[key]; exists {
		return dc.safeIntValue(value)
	}
	return 0
}

func (dc *DatabaseConnector) safeIntValue(value interface{}) int {
	if f, ok := value.(float64); ok {
		return int(f)
	}
	if i, ok := value.(int); ok {
		return i
	}
	return 0
}

// 数学辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func minFloat(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}

func maxFloat(a, b float64) float64 {
	if a > b {
		return a
	}
	return b
}

// Close 关闭所有连接
func (dc *DatabaseConnector) Close() {
	for id, conn := range dc.connections {
		if err := conn.Close(); err != nil {
			logger.Errorf("Failed to close connection for database %d: %v", id, err)
		}
	}
	dc.connections = make(map[uint]*sql.DB)
}
