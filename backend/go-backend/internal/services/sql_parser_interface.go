package services

import (
	"fmt"
	"time"
)

// SQLParser 统一的SQL解析器接口
type SQLParser interface {
	// 核心解析功能
	Parse(sql string) (*SQLParseResult, error)
	Validate(sql string) error
	
	// 信息提取
	ExtractTables(sql string) ([]TableInfo, error)
	ExtractColumns(sql string) ([]ColumnInfo, error)
	ExtractConditions(sql string) ([]Condition, error)
	
	// 分析功能
	CalculateComplexity(sql string) (*ComplexityScore, error)
	DetectPatterns(sql string) ([]QueryPattern, error)
	SuggestOptimizations(sql string) ([]Optimization, error)
	
	// 格式化功能
	FormatSQL(sql string) (string, error)
	
	// 解析器信息
	GetParserType() string
	GetSupportedFeatures() []string
}

// SQLParseResult 增强的解析结果
type SQLParseResult struct {
	QueryType       string           `json:"query_type"`
	FormattedQuery  string           `json:"formatted_query"`
	Tables          []TableInfo      `json:"tables"`
	Columns         []ColumnInfo     `json:"columns"`
	Conditions      []Condition      `json:"conditions"`
	JoinInfo        []JoinInfo       `json:"joins"`
	ComplexityScore *ComplexityScore `json:"complexity"`
	Patterns        []QueryPattern   `json:"patterns"`
	Optimizations   []Optimization   `json:"optimizations"`
	AST             interface{}      `json:"ast,omitempty"`
	ParsedAt        time.Time        `json:"parsed_at"`

	// 解析器状态信息
	ParserInfo      *ParserInfo      `json:"parser_info"`
}

// ParserInfo 解析器状态信息
type ParserInfo struct {
	ParserType      string   `json:"parser_type"`      // postgresql_ast, postgresql_fallback, fallback
	ParserVersion   string   `json:"parser_version"`   // 解析器版本
	FallbackUsed    bool     `json:"fallback_used"`    // 是否使用了fallback
	FallbackReason  string   `json:"fallback_reason,omitempty"` // fallback原因
	Capabilities    []string `json:"capabilities"`     // 解析器能力
	Warnings        []string `json:"warnings,omitempty"` // 警告信息
}

// TableInfo 表信息
type TableInfo struct {
	Name          string `json:"name"`
	Alias         string `json:"alias,omitempty"`
	Schema        string `json:"schema,omitempty"`
	Database      string `json:"database,omitempty"`
	JoinType      string `json:"join_type,omitempty"`
	JoinCondition string `json:"join_condition,omitempty"`
	IsSubquery    bool   `json:"is_subquery"`
	SubqueryAlias string `json:"subquery_alias,omitempty"`
}

// ColumnInfo 列信息
type ColumnInfo struct {
	Name         string `json:"name"`
	Table        string `json:"table,omitempty"`
	Alias        string `json:"alias,omitempty"`
	DataType     string `json:"data_type,omitempty"`
	IsAggregate  bool   `json:"is_aggregate"`
	Function     string `json:"function,omitempty"`
	Expression   string `json:"expression,omitempty"`
	IsCalculated bool   `json:"is_calculated"`
}

// Condition 条件信息
type Condition struct {
	Type        string `json:"type"`        // WHERE, HAVING, JOIN, etc.
	Column      string `json:"column"`
	Operator    string `json:"operator"`    // =, >, <, LIKE, IN, etc.
	Value       string `json:"value"`
	IsParameter bool   `json:"is_parameter"`
	Subquery    string `json:"subquery,omitempty"`
}

// JoinInfo JOIN信息
type JoinInfo struct {
	Type        string `json:"type"`        // INNER, LEFT, RIGHT, FULL, CROSS
	LeftTable   string `json:"left_table"`
	RightTable  string `json:"right_table"`
	Condition   string `json:"condition"`
	IsImplicit  bool   `json:"is_implicit"` // 隐式JOIN (WHERE条件)
}

// ComplexityScore 复杂度评分
type ComplexityScore struct {
	Total          int                    `json:"total"`
	Breakdown      map[string]int         `json:"breakdown"`
	Factors        []ComplexityFactor     `json:"factors"`
	Level          string                 `json:"level"`          // LOW, MEDIUM, HIGH, VERY_HIGH
	Recommendation string                 `json:"recommendation"`
	EstimatedTime  float64                `json:"estimated_time"` // 预估执行时间(秒)
}

// ComplexityFactor 复杂度因子
type ComplexityFactor struct {
	Type        string `json:"type"`        // JOIN, SUBQUERY, AGGREGATE, etc.
	Count       int    `json:"count"`
	Points      int    `json:"points"`
	Description string `json:"description"`
	Impact      string `json:"impact"`      // LOW, MEDIUM, HIGH
}

// QueryPattern 查询模式
type QueryPattern struct {
	Type        string                 `json:"type"`        // ANTI_PATTERN, OPTIMIZATION_OPPORTUNITY, etc.
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Severity    string                 `json:"severity"`    // INFO, WARNING, ERROR
	Location    string                 `json:"location"`    // 在SQL中的位置
	Suggestion  string                 `json:"suggestion"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// Optimization 优化建议
type Optimization struct {
	Type         string  `json:"type"`         // INDEX, QUERY_REWRITE, SCHEMA, etc.
	Priority     string  `json:"priority"`     // HIGH, MEDIUM, LOW
	Title        string  `json:"title"`
	Description  string  `json:"description"`
	BeforeQuery  string  `json:"before_query,omitempty"`
	AfterQuery   string  `json:"after_query,omitempty"`
	ExpectedGain float64 `json:"expected_gain"` // 预期性能提升百分比
	Effort       string  `json:"effort"`        // LOW, MEDIUM, HIGH
	Category     string  `json:"category"`      // PERFORMANCE, MAINTAINABILITY, SECURITY
}

// SQLParserConfig 解析器配置
type SQLParserConfig struct {
	DatabaseType    string            `json:"database_type"`    // postgresql, mysql, etc.
	Version         string            `json:"version"`          // 数据库版本
	EnableAST       bool              `json:"enable_ast"`       // 是否返回AST
	EnablePatterns  bool              `json:"enable_patterns"`  // 是否检测模式
	EnableOptimize  bool              `json:"enable_optimize"`  // 是否生成优化建议
	CustomRules     []string          `json:"custom_rules"`     // 自定义规则
	Options         map[string]string `json:"options"`          // 其他选项
}

// ParseError 解析错误
type ParseError struct {
	Type        string `json:"type"`        // SYNTAX_ERROR, SEMANTIC_ERROR, etc.
	Message     string `json:"message"`
	Line        int    `json:"line"`
	Column      int    `json:"column"`
	Position    int    `json:"position"`
	Suggestion  string `json:"suggestion,omitempty"`
	ErrorCode   string `json:"error_code,omitempty"`
}

func (e *ParseError) Error() string {
	if e.Line > 0 && e.Column > 0 {
		return fmt.Sprintf("SQL parse error at line %d, column %d: %s", e.Line, e.Column, e.Message)
	}
	return fmt.Sprintf("SQL parse error: %s", e.Message)
}

// SQLParserFactory 解析器工厂接口
type SQLParserFactory interface {
	// 基础工厂方法
	CreateParser(dbType string, config *SQLParserConfig) (SQLParser, error)
	GetSupportedDatabases() []string
	GetParserCapabilities(dbType string) []string

	// 容错解析方法
	ParseWithFallback(dbType, sql string, config *SQLParserConfig) (*SQLParseResult, error)
	ValidateWithFallback(dbType, sql string, config *SQLParserConfig) error

	// 信息查询方法
	GetParserInfo(dbType string) map[string]interface{}
	GetAllParsersInfo() map[string]interface{}

	// 解析器管理方法
	RegisterParser(dbType string, parser SQLParser, capabilities []string) error
	IsParserAvailable(dbType string) bool
	GetParserHealth(dbType string) ParserHealthStatus
}

// ParserHealthStatus 解析器健康状态
type ParserHealthStatus struct {
	DatabaseType    string    `json:"database_type"`
	Available       bool      `json:"available"`
	LastChecked     time.Time `json:"last_checked"`
	ErrorCount      int       `json:"error_count"`
	SuccessCount    int       `json:"success_count"`
	SuccessRate     float64   `json:"success_rate"`
	AverageLatency  float64   `json:"average_latency_ms"`
	LastError       string    `json:"last_error,omitempty"`
	Status          string    `json:"status"` // healthy, degraded, failed
}

// ParserCapability 解析器能力
const (
	CapabilityASTParsing      = "ast_parsing"
	CapabilitySyntaxValidation = "syntax_validation"
	CapabilitySemanticAnalysis = "semantic_analysis"
	CapabilityQueryRewriting  = "query_rewriting"
	CapabilityPatternDetection = "pattern_detection"
	CapabilityComplexityAnalysis = "complexity_analysis"
	CapabilityOptimizationSuggestions = "optimization_suggestions"
	CapabilityMultiStatement  = "multi_statement"
	CapabilityProcedureSupport = "procedure_support"
	CapabilityTriggerSupport  = "trigger_support"
)

// QueryType 查询类型常量
const (
	QueryTypeSelect = "select"
	QueryTypeInsert = "insert"
	QueryTypeUpdate = "update"
	QueryTypeDelete = "delete"
	QueryTypeCreate = "create"
	QueryTypeAlter  = "alter"
	QueryTypeDrop   = "drop"
	QueryTypeWith   = "with"
	QueryTypeUnion  = "union"
	QueryTypeOther  = "other"
)

// ComplexityLevel 复杂度级别
const (
	ComplexityLow      = "LOW"      // 1-5分
	ComplexityMedium   = "MEDIUM"   // 6-10分
	ComplexityHigh     = "HIGH"     // 11-20分
	ComplexityVeryHigh = "VERY_HIGH" // 21+分
)

// PatternType 模式类型
const (
	PatternAntiPattern           = "ANTI_PATTERN"
	PatternOptimizationOpportunity = "OPTIMIZATION_OPPORTUNITY"
	PatternBestPractice         = "BEST_PRACTICE"
	PatternSecurityIssue        = "SECURITY_ISSUE"
	PatternPerformanceIssue     = "PERFORMANCE_ISSUE"
)

// OptimizationType 优化类型
const (
	OptimizationIndex        = "INDEX"
	OptimizationQueryRewrite = "QUERY_REWRITE"
	OptimizationSchema       = "SCHEMA"
	OptimizationConfiguration = "CONFIGURATION"
	OptimizationPartitioning = "PARTITIONING"
	OptimizationCaching      = "CACHING"
)
