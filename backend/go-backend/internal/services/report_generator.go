package services

import (
	"encoding/csv"
	"fmt"
	"os"
	"strconv"
	"time"

	"db-monitor-platform/internal/models"
	"db-monitor-platform/internal/utils"
)

// ReportGenerator 报表生成器接口
type ReportGenerator interface {
	Generate(data *ReportData, format string) (filePath string, err error)
	Validate(template *models.ReportTemplate) error
	GetSupportedFormats() []string
}

// ReportData 报表数据结构
type ReportData struct {
	Template    *models.ReportTemplate      `json:"template"`
	TimeRange   models.TimeRangeRequest     `json:"time_range"`
	Metrics     []MetricData                `json:"metrics"`
	Databases   []DatabaseInfo              `json:"databases"`
	Parameters  map[string]interface{}      `json:"parameters"`
	UserInfo    *models.User                `json:"user_info"`
	ExecutionID uint                        `json:"execution_id"`
}

// MetricData 监控数据结构
type MetricData struct {
	DatabaseID   uint      `json:"database_id"`
	DatabaseName string    `json:"database_name"`
	MetricType   string    `json:"metric_type"`
	Value        float64   `json:"value"`
	Unit         string    `json:"unit"`
	Timestamp    time.Time `json:"timestamp"`
	Description  string    `json:"description"`
}

// DatabaseInfo 数据库信息结构
type DatabaseInfo struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	Type        string `json:"type"`
	Host        string `json:"host"`
	Port        int    `json:"port"`
	Status      string `json:"status"`
	Description string `json:"description"`
}

// reportGeneratorImpl 报表生成器实现
type reportGeneratorImpl struct {
	fileManager utils.FileManager
}

// NewReportGenerator 创建报表生成器实例
func NewReportGenerator(fileManager utils.FileManager) ReportGenerator {
	return &reportGeneratorImpl{
		fileManager: fileManager,
	}
}

// Generate 生成报表文件
func (g *reportGeneratorImpl) Generate(data *ReportData, format string) (string, error) {
	// 验证格式
	if !g.isFormatSupported(format) {
		return "", fmt.Errorf("unsupported format: %s", format)
	}

	// 生成文件路径
	filePath := g.fileManager.GenerateFilePath(data.ExecutionID, format)

	// 确保目录存在
	if err := g.fileManager.EnsureDirectory(filePath); err != nil {
		return "", fmt.Errorf("failed to create directory: %w", err)
	}

	// 根据格式生成文件
	switch format {
	case "csv":
		return g.generateCSV(data, filePath)
	case "json":
		return g.generateJSON(data, filePath)
	default:
		return "", fmt.Errorf("format %s not implemented yet", format)
	}
}

// generateCSV 生成CSV格式报表
func (g *reportGeneratorImpl) generateCSV(data *ReportData, filePath string) (string, error) {
	// 创建文件
	file, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to create CSV file: %w", err)
	}
	defer file.Close()

	// 创建CSV写入器
	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 写入表头
	header := []string{
		"时间戳",
		"数据库ID",
		"数据库名称",
		"指标类型",
		"数值",
		"单位",
		"描述",
	}
	if err := writer.Write(header); err != nil {
		return "", fmt.Errorf("failed to write CSV header: %w", err)
	}

	// 写入数据行
	for _, metric := range data.Metrics {
		record := []string{
			metric.Timestamp.Format("2006-01-02 15:04:05"),
			strconv.FormatUint(uint64(metric.DatabaseID), 10),
			metric.DatabaseName,
			metric.MetricType,
			strconv.FormatFloat(metric.Value, 'f', 2, 64),
			metric.Unit,
			metric.Description,
		}
		if err := writer.Write(record); err != nil {
			return "", fmt.Errorf("failed to write CSV record: %w", err)
		}
	}

	// 写入报表元信息
	metaInfo := []string{
		"",
		"",
		"报表信息",
		"",
		"",
		"",
		"",
	}
	writer.Write(metaInfo)

	templateInfo := []string{
		"模板名称",
		"",
		data.Template.Name,
		"",
		"",
		"",
		data.Template.Description,
	}
	writer.Write(templateInfo)

	timeRangeInfo := []string{
		"时间范围",
		"",
		fmt.Sprintf("%s 至 %s", data.TimeRange.StartTime, data.TimeRange.EndTime),
		"",
		"",
		"",
		"",
	}
	writer.Write(timeRangeInfo)

	generatedInfo := []string{
		"生成时间",
		"",
		time.Now().Format("2006-01-02 15:04:05"),
		"",
		"",
		"",
		fmt.Sprintf("执行用户: %s", data.UserInfo.Name),
	}
	writer.Write(generatedInfo)

	return filePath, nil
}

// generateJSON 生成JSON格式报表
func (g *reportGeneratorImpl) generateJSON(data *ReportData, filePath string) (string, error) {
	// TODO: 实现JSON格式生成
	return "", fmt.Errorf("JSON format not implemented yet")
}

// Validate 验证报表模板
func (g *reportGeneratorImpl) Validate(template *models.ReportTemplate) error {
	if template == nil {
		return fmt.Errorf("template cannot be nil")
	}

	if template.Name == "" {
		return fmt.Errorf("template name cannot be empty")
	}

	if template.Type == "" {
		return fmt.Errorf("template type cannot be empty")
	}

	return nil
}

// GetSupportedFormats 获取支持的格式列表
func (g *reportGeneratorImpl) GetSupportedFormats() []string {
	return []string{"csv", "json"}
}

// isFormatSupported 检查格式是否支持
func (g *reportGeneratorImpl) isFormatSupported(format string) bool {
	supportedFormats := g.GetSupportedFormats()
	for _, supportedFormat := range supportedFormats {
		if format == supportedFormat {
			return true
		}
	}
	return false
}
