package services

import (
	"fmt"
	"time"

	"db-monitor-platform/internal/models"
	"db-monitor-platform/pkg/logger"

	"gorm.io/gorm"
)

// QueryOptimizerService 查询优化服务
type QueryOptimizerService struct {
	db              *gorm.DB
	databaseService *DatabaseService
	sqlAnalyzer     *SQLAnalyzer
	dbConnector     *DatabaseConnector
}

// NewQueryOptimizerService 创建查询优化服务
func NewQueryOptimizerService(db *gorm.DB) *QueryOptimizerService {
	return &QueryOptimizerService{
		db:              db,
		databaseService: NewDatabaseService(db),
		sqlAnalyzer:     NewSQLAnalyzer(),
		dbConnector:     NewDatabaseConnector(),
	}
}

// AnalyzeQuery 分析SQL查询
func (s *QueryOptimizerService) AnalyzeQuery(userID uint, userRole string, req *models.QueryAnalysisRequest) (*models.QueryAnalysisResponse, error) {
	// 验证数据库访问权限
	_, err := s.databaseService.GetDatabase(userID, req.DatabaseID, userRole)
	if err != nil {
		return nil, err
	}

	// 获取数据库实例
	var database models.DatabaseInstance
	if err := s.db.First(&database, req.DatabaseID).Error; err != nil {
		return nil, fmt.Errorf("database not found")
	}

	// 创建查询分析记录
	analysis := &models.QueryAnalysis{
		DatabaseID:        req.DatabaseID,
		UserID:            userID,
		OriginalQuery:     req.SQLQuery,
		Status:            "running",
		AnalysisTimestamp: time.Now(),
	}

	// 保存初始记录
	if err := s.db.Create(analysis).Error; err != nil {
		logger.Errorf("Failed to create query analysis: %v", err)
		return nil, fmt.Errorf("failed to create analysis record")
	}

	// 异步执行分析
	go s.performAnalysis(analysis, &database, &req.Options)

	// 返回初始响应
	return analysis.ToResponse(), nil
}

// performAnalysis 执行查询分析
func (s *QueryOptimizerService) performAnalysis(analysis *models.QueryAnalysis, database *models.DatabaseInstance, options *models.QueryAnalysisOptions) {
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("Query analysis panic: %v", r)
			s.updateAnalysisStatus(analysis.ID, "failed", fmt.Sprintf("Analysis failed: %v", r))
		}
	}()

	// 1. SQL语法分析和格式化
	sqlResult, err := s.sqlAnalyzer.AnalyzeSQL(analysis.OriginalQuery)
	if err != nil {
		s.updateAnalysisStatus(analysis.ID, "failed", fmt.Sprintf("SQL analysis failed: %v", err))
		return
	}

	// 更新分析结果
	updates := map[string]interface{}{
		"formatted_query":  sqlResult.FormattedQuery,
		"query_type":       sqlResult.QueryType,
		"complexity_score": sqlResult.ComplexityScore,
	}

	if err := s.db.Model(analysis).Updates(updates).Error; err != nil {
		logger.Errorf("Failed to update analysis: %v", err)
		return
	}

	// 2. 获取执行计划（如果需要）
	if options.IncludeExecutionPlan {
		if err := s.generateExecutionPlan(analysis.ID, database, analysis.OriginalQuery); err != nil {
			logger.Errorf("Failed to generate execution plan: %v", err)
		}
	}

	// 3. 生成优化建议（如果需要）
	if options.IncludeSuggestions {
		if err := s.generateOptimizationSuggestions(analysis.ID, sqlResult); err != nil {
			logger.Errorf("Failed to generate optimization suggestions: %v", err)
		}
	}

	// 4. 生成索引推荐（如果需要）
	if options.IncludeIndexRecommendations {
		if err := s.generateIndexRecommendations(analysis.ID, database, sqlResult); err != nil {
			logger.Errorf("Failed to generate index recommendations: %v", err)
		}
	}

	// 标记分析完成
	s.updateAnalysisStatus(analysis.ID, "completed", "")
}

// updateAnalysisStatus 更新分析状态
func (s *QueryOptimizerService) updateAnalysisStatus(analysisID uint, status, errorMessage string) {
	updates := map[string]interface{}{
		"status": status,
	}
	if errorMessage != "" {
		updates["error_message"] = errorMessage
	}

	if err := s.db.Model(&models.QueryAnalysis{}).Where("id = ?", analysisID).Updates(updates).Error; err != nil {
		logger.Errorf("Failed to update analysis status: %v", err)
	}
}

// GetAnalysis 获取查询分析结果
func (s *QueryOptimizerService) GetAnalysis(userID, analysisID uint, userRole string) (*models.QueryAnalysisResponse, error) {
	var analysis models.QueryAnalysis

	query := s.db.Preload("ExecutionPlan").
		Preload("OptimizationSuggestions").
		Preload("IndexRecommendations")

	// 权限控制
	if userRole != "admin" {
		query = query.Where("user_id = ?", userID)
	}

	if err := query.First(&analysis, analysisID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("analysis not found")
		}
		return nil, err
	}

	return analysis.ToResponse(), nil
}

// GetAnalysisHistory 获取查询分析历史
func (s *QueryOptimizerService) GetAnalysisHistory(userID uint, userRole string, params *models.AnalysisHistoryParams) (*models.PaginationResponse, error) {
	var analyses []models.QueryAnalysis
	var total int64

	query := s.db.Model(&models.QueryAnalysis{})

	// 权限控制
	if userRole != "admin" {
		query = query.Where("user_id = ?", userID)
	}

	// 筛选条件
	if params.DatabaseID > 0 {
		query = query.Where("database_id = ?", params.DatabaseID)
	}
	if params.QueryType != "" {
		query = query.Where("query_type = ?", params.QueryType)
	}
	if params.Status != "" {
		query = query.Where("status = ?", params.Status)
	}
	if params.StartDate != "" {
		query = query.Where("analysis_timestamp >= ?", params.StartDate)
	}
	if params.EndDate != "" {
		query = query.Where("analysis_timestamp <= ?", params.EndDate)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 分页查询
	offset := (params.Page - 1) * params.PageSize
	if err := query.Order("analysis_timestamp DESC").
		Offset(offset).
		Limit(params.PageSize).
		Find(&analyses).Error; err != nil {
		return nil, err
	}

	// 转换响应格式
	responses := make([]*models.QueryAnalysisResponse, 0, len(analyses))
	for _, analysis := range analyses {
		responses = append(responses, analysis.ToResponse())
	}

	return models.NewPaginationResponse(params.Page, params.PageSize, total, responses), nil
}

// ExplainQuery 获取查询执行计划
func (s *QueryOptimizerService) ExplainQuery(userID uint, userRole string, req *models.ExecutionPlanRequest) (*models.ExecutionPlanResponse, error) {
	// 验证数据库访问权限
	_, err := s.databaseService.GetDatabase(userID, req.DatabaseID, userRole)
	if err != nil {
		return nil, err
	}

	// 获取数据库实例
	var database models.DatabaseInstance
	if err := s.db.First(&database, req.DatabaseID).Error; err != nil {
		return nil, fmt.Errorf("database not found")
	}

	// 生成执行计划
	plan, err := s.generateExecutionPlanDirect(&database, req.SQLQuery, &req.ExplainOptions)
	if err != nil {
		return nil, err
	}

	return plan.ToResponse(), nil
}

// GetSupportedDatabases 获取支持的数据库类型
func (s *QueryOptimizerService) GetSupportedDatabases() []models.SupportedDatabase {
	return []models.SupportedDatabase{
		{
			Type:    "postgresql",
			Name:    "PostgreSQL",
			Version: "12+",
			Features: []string{
				"EXPLAIN ANALYZE",
				"JSON execution plans",
				"Buffer analysis",
				"Index recommendations",
				"Query optimization",
			},
			Limitations: []string{
				"Requires EXPLAIN privileges",
			},
		},
		{
			Type:    "mysql",
			Name:    "MySQL",
			Version: "8.0+",
			Features: []string{
				"EXPLAIN FORMAT=JSON",
				"Basic optimization suggestions",
				"Index analysis",
			},
			Limitations: []string{
				"Limited buffer analysis",
				"No EXPLAIN ANALYZE",
			},
		},
	}
}

// GetQueryStats 获取查询统计信息
func (s *QueryOptimizerService) GetQueryStats(userID uint, userRole string, params *models.QueryStatsParams) (*models.QueryStatsResponse, error) {
	query := s.db.Model(&models.QueryAnalysis{})

	// 权限控制
	if userRole != "admin" {
		query = query.Where("user_id = ?", userID)
	}

	// 筛选条件
	if params.DatabaseID > 0 {
		query = query.Where("database_id = ?", params.DatabaseID)
	}

	// 时间范围
	var startTime time.Time
	switch params.Period {
	case "1d":
		startTime = time.Now().AddDate(0, 0, -1)
	case "7d":
		startTime = time.Now().AddDate(0, 0, -7)
	case "30d":
		startTime = time.Now().AddDate(0, 0, -30)
	case "90d":
		startTime = time.Now().AddDate(0, 0, -90)
	default:
		startTime = time.Now().AddDate(0, 0, -7)
	}

	query = query.Where("analysis_timestamp >= ?", startTime)

	// 基础统计
	var totalAnalyses int64
	var successfulAnalyses int64
	var failedAnalyses int64

	query.Count(&totalAnalyses)
	query.Where("status = ?", "completed").Count(&successfulAnalyses)
	query.Where("status = ?", "failed").Count(&failedAnalyses)

	// 查询类型统计
	var queryTypeStats []models.QueryTypeStats
	s.db.Model(&models.QueryAnalysis{}).
		Select("query_type, COUNT(*) as count").
		Where("user_id = ? AND analysis_timestamp >= ?", userID, startTime).
		Group("query_type").
		Scan(&queryTypeStats)

	// 计算百分比
	for i := range queryTypeStats {
		if totalAnalyses > 0 {
			queryTypeStats[i].Percentage = float64(queryTypeStats[i].Count) / float64(totalAnalyses) * 100
		}
	}

	return &models.QueryStatsResponse{
		TotalAnalyses:      int(totalAnalyses),
		SuccessfulAnalyses: int(successfulAnalyses),
		FailedAnalyses:     int(failedAnalyses),
		TopQueryTypes:      queryTypeStats,
	}, nil
}

// GetIndexSuggestions 获取索引建议
func (s *QueryOptimizerService) GetIndexSuggestions(userID uint, userRole string, req *models.IndexSuggestionRequest) ([]models.IndexRecommendationResponse, error) {
	// 验证数据库访问权限
	_, err := s.databaseService.GetDatabase(userID, req.DatabaseID, userRole)
	if err != nil {
		return nil, err
	}

	// 获取数据库实例
	var database models.DatabaseInstance
	if err := s.db.First(&database, req.DatabaseID).Error; err != nil {
		return nil, fmt.Errorf("database not found")
	}

	// 生成索引建议
	recommendations, err := s.generateIndexSuggestions(&database, req.TableName, req.QueryPatterns)
	if err != nil {
		return nil, err
	}

	responses := make([]models.IndexRecommendationResponse, 0, len(recommendations))
	for _, rec := range recommendations {
		responses = append(responses, *rec.ToResponse())
	}

	return responses, nil
}

// GetIndexRecommendations 获取索引推荐
func (s *QueryOptimizerService) GetIndexRecommendations(userID, analysisID uint, userRole string) ([]models.IndexRecommendationResponse, error) {
	var recommendations []models.IndexRecommendation

	query := s.db.Where("query_analysis_id = ?", analysisID)

	// 权限控制 - 通过查询分析记录验证权限
	if userRole != "admin" {
		query = query.Joins("JOIN query_analyses ON query_analyses.id = index_recommendations.query_analysis_id").
			Where("query_analyses.user_id = ?", userID)
	}

	if err := query.Find(&recommendations).Error; err != nil {
		return nil, err
	}

	responses := make([]models.IndexRecommendationResponse, 0, len(recommendations))
	for _, rec := range recommendations {
		responses = append(responses, *rec.ToResponse())
	}

	return responses, nil
}

// GetOptimizationSuggestions 获取优化建议
func (s *QueryOptimizerService) GetOptimizationSuggestions(userID, analysisID uint, userRole string) ([]models.OptimizationSuggestionResponse, error) {
	var suggestions []models.OptimizationSuggestion

	query := s.db.Where("query_analysis_id = ?", analysisID)

	// 权限控制 - 通过查询分析记录验证权限
	if userRole != "admin" {
		query = query.Joins("JOIN query_analyses ON query_analyses.id = optimization_suggestions.query_analysis_id").
			Where("query_analyses.user_id = ?", userID)
	}

	if err := query.Order("priority DESC, estimated_improvement DESC").Find(&suggestions).Error; err != nil {
		return nil, err
	}

	responses := make([]models.OptimizationSuggestionResponse, 0, len(suggestions))
	for _, suggestion := range suggestions {
		responses = append(responses, *suggestion.ToResponse())
	}

	return responses, nil
}

// GetExecutionPlan 获取执行计划详情
func (s *QueryOptimizerService) GetExecutionPlan(userID, planID uint, userRole string) (*models.ExecutionPlanResponse, error) {
	var plan models.ExecutionPlan

	query := s.db

	// 权限控制 - 通过查询分析记录验证权限
	if userRole != "admin" {
		query = query.Joins("JOIN query_analyses ON query_analyses.id = execution_plans.query_analysis_id").
			Where("query_analyses.user_id = ?", userID)
	}

	if err := query.First(&plan, planID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("execution plan not found")
		}
		return nil, err
	}

	return plan.ToResponse(), nil
}

// 私有辅助方法

// generateExecutionPlan 生成执行计划
func (s *QueryOptimizerService) generateExecutionPlan(analysisID uint, database *models.DatabaseInstance, sqlQuery string) error {
	// 使用真实的数据库连接执行EXPLAIN
	options := &models.ExplainOptions{
		Format:  "json",
		Analyze: false, // 不执行ANALYZE以避免修改数据
		Buffers: true,
	}

	plan, err := s.dbConnector.ExplainQuery(database, sqlQuery, options)
	if err != nil {
		logger.Errorf("Failed to generate execution plan: %v", err)
		// 如果真实查询失败，记录错误但不阻止分析流程
		return fmt.Errorf("failed to generate execution plan: %w", err)
	}

	// 设置分析ID
	plan.QueryAnalysisID = analysisID
	plan.PlanID = fmt.Sprintf("plan_%d_%d", analysisID, time.Now().Unix())

	return s.db.Create(plan).Error
}

// generateExecutionPlanDirect 直接生成执行计划
func (s *QueryOptimizerService) generateExecutionPlanDirect(database *models.DatabaseInstance, sqlQuery string, options *models.ExplainOptions) (*models.ExecutionPlan, error) {
	// 使用真实的数据库连接执行EXPLAIN
	logger.Infof("Generating execution plan for database: %s, query: %s", database.Name, sqlQuery)

	plan, err := s.dbConnector.ExplainQuery(database, sqlQuery, options)
	if err != nil {
		logger.Errorf("Failed to execute EXPLAIN query: %v", err)
		// 如果真实查询失败，返回错误而不是模拟数据
		return nil, fmt.Errorf("failed to generate execution plan: %w", err)
	}

	return plan, nil
}

// generateOptimizationSuggestions 生成优化建议
func (s *QueryOptimizerService) generateOptimizationSuggestions(analysisID uint, sqlResult *SQLAnalysisResult) error {
	var suggestions []models.OptimizationSuggestion

	// 基于SQL分析结果生成建议
	if sqlResult.ComplexityScore > 10 {
		suggestions = append(suggestions, models.OptimizationSuggestion{
			QueryAnalysisID:      analysisID,
			Type:                 "query_rewrite",
			Priority:             "high",
			Title:                "简化复杂查询",
			Description:          "查询复杂度较高，建议考虑分解为多个简单查询或优化查询结构",
			EstimatedImprovement: 30.0,
			ImplementationEffort: "medium",
			ImpactAreas:          []string{"performance", "maintainability"},
		})
	}

	if len(sqlResult.JoinTypes) > 3 {
		suggestions = append(suggestions, models.OptimizationSuggestion{
			QueryAnalysisID:      analysisID,
			Type:                 "query_rewrite",
			Priority:             "medium",
			Title:                "减少JOIN操作",
			Description:          "查询包含多个JOIN操作，考虑是否可以通过数据预处理或视图来减少JOIN数量",
			EstimatedImprovement: 20.0,
			ImplementationEffort: "hard",
			ImpactAreas:          []string{"performance"},
		})
	}

	if sqlResult.HasSubquery {
		suggestions = append(suggestions, models.OptimizationSuggestion{
			QueryAnalysisID:      analysisID,
			Type:                 "query_rewrite",
			Priority:             "medium",
			Title:                "优化子查询",
			Description:          "考虑将子查询重写为JOIN操作，通常可以获得更好的性能",
			EstimatedImprovement: 25.0,
			ImplementationEffort: "medium",
			ImpactAreas:          []string{"performance"},
		})
	}

	// 批量插入建议
	if len(suggestions) > 0 {
		return s.db.Create(&suggestions).Error
	}

	return nil
}

// generateIndexRecommendations 生成索引推荐
func (s *QueryOptimizerService) generateIndexRecommendations(analysisID uint, database *models.DatabaseInstance, sqlResult *SQLAnalysisResult) error {
	var recommendations []models.IndexRecommendation

	// 基于查询分析生成索引建议
	for _, table := range sqlResult.Tables {
		if len(sqlResult.Conditions) > 0 {
			recommendations = append(recommendations, models.IndexRecommendation{
				QueryAnalysisID:      analysisID,
				RecommendedTable:     table,
				Columns:              []string{"id", "created_at"}, // 示例列
				IndexType:            "btree",
				CreateStatement:      fmt.Sprintf("CREATE INDEX idx_%s_id_created ON %s (id, created_at)", table, table),
				EstimatedSize:        "10MB",
				EstimatedImprovement: 40.0,
				UsageFrequency:       80.0,
				MaintenanceCost:      "low",
			})
		}
	}

	// 批量插入推荐
	if len(recommendations) > 0 {
		return s.db.Create(&recommendations).Error
	}

	return nil
}

// generateIndexSuggestions 生成索引建议
func (s *QueryOptimizerService) generateIndexSuggestions(database *models.DatabaseInstance, tableName string, queryPatterns []string) ([]models.IndexRecommendation, error) {
	var recommendations []models.IndexRecommendation

	// 基于查询模式生成索引建议
	for i, _ := range queryPatterns {
		recommendation := models.IndexRecommendation{
			RecommendedTable:     tableName,
			Columns:              []string{"column1", "column2"}, // 应该从查询模式中提取
			IndexType:            "btree",
			CreateStatement:      fmt.Sprintf("CREATE INDEX idx_%s_%d ON %s (column1, column2)", tableName, i+1, tableName),
			EstimatedSize:        "5MB",
			EstimatedImprovement: 35.0,
			UsageFrequency:       70.0,
			MaintenanceCost:      "medium",
		}
		recommendations = append(recommendations, recommendation)
	}

	return recommendations, nil
}
