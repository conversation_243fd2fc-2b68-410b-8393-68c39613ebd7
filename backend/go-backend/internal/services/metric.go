package services

import (
	"errors"
	"time"

	"db-monitor-platform/internal/models"
	"db-monitor-platform/internal/repository"
	"db-monitor-platform/internal/utils"
	"db-monitor-platform/pkg/logger"

	"gorm.io/gorm"
)

// MetricService 监控指标服务
type MetricService struct {
	metricRepo   repository.MetricRepository
	databaseRepo repository.DatabaseRepository
	userRepo     repository.UserRepository
}

// NewMetricService 创建监控指标服务
func NewMetricService(db *gorm.DB) *MetricService {
	return &MetricService{
		metricRepo:   repository.NewMetricRepository(db),
		databaseRepo: repository.NewDatabaseRepository(db),
		userRepo:     repository.NewUserRepository(db),
	}
}

// CreateMetric 创建监控指标
func (s *MetricService) CreateMetric(userID uint, req *models.MetricCreateRequest) (*models.MetricResponse, error) {
	// 验证输入
	if err := utils.ValidateAndSanitize(req); err != nil {
		logger.Errorf("Validation failed: %v", err)
		return nil, err
	}

	// 检查数据库实例是否存在且用户有权限
	database, err := s.databaseRepo.GetByID(req.DatabaseID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("database not found")
		}
		logger.Errorf("Failed to get database: %v", err)
		return nil, errors.New("failed to get database")
	}

	// 检查权限
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, errors.New("user not found")
	}

	if !user.IsAdmin() && database.CreatedBy != userID {
		return nil, errors.New("access denied")
	}

	// 创建监控指标
	metric := &models.Metric{
		DatabaseID: req.DatabaseID,
		MetricType: req.MetricType,
		Value:      req.Value,
		Unit:       req.Unit,
		Labels:     req.Labels,
		Timestamp:  req.Timestamp,
	}

	if metric.Timestamp.IsZero() {
		metric.Timestamp = time.Now()
	}

	if err := s.metricRepo.Create(metric); err != nil {
		logger.Errorf("Failed to create metric: %v", err)
		return nil, errors.New("failed to create metric")
	}

	logger.Infof("Metric created: %s for database %d", metric.MetricType, metric.DatabaseID)
	return metric.ToResponse(), nil
}

// CreateMetricsBatch 批量创建监控指标
func (s *MetricService) CreateMetricsBatch(userID uint, databaseID uint, metrics []models.MetricCreateRequest) error {
	// 检查数据库实例权限
	database, err := s.databaseRepo.GetByID(databaseID)
	if err != nil {
		return errors.New("database not found")
	}

	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return errors.New("user not found")
	}

	if !user.IsAdmin() && database.CreatedBy != userID {
		return errors.New("access denied")
	}

	// 转换为Metric模型
	var metricModels []models.Metric
	now := time.Now()

	for _, req := range metrics {
		if err := utils.ValidateStruct(&req); err != nil {
			logger.Errorf("Invalid metric in batch: %v", err)
			continue
		}

		metric := models.Metric{
			DatabaseID: databaseID,
			MetricType: req.MetricType,
			Value:      req.Value,
			Unit:       req.Unit,
			Labels:     req.Labels,
			Timestamp:  req.Timestamp,
		}

		if metric.Timestamp.IsZero() {
			metric.Timestamp = now
		}

		metricModels = append(metricModels, metric)
	}

	if len(metricModels) == 0 {
		return errors.New("no valid metrics to create")
	}

	if err := s.metricRepo.CreateBatch(metricModels); err != nil {
		logger.Errorf("Failed to create metrics batch: %v", err)
		return errors.New("failed to create metrics")
	}

	logger.Infof("Created %d metrics for database %d", len(metricModels), databaseID)
	return nil
}

// GetMetrics 获取监控指标列表
func (s *MetricService) GetMetrics(userID uint, databaseID uint, userRole string, pagination *models.PaginationRequest) (*models.PaginationResponse, error) {
	// 检查权限
	if err := s.checkDatabaseAccess(userID, databaseID, userRole); err != nil {
		return nil, err
	}

	metrics, total, err := s.metricRepo.GetByDatabaseID(databaseID, pagination)
	if err != nil {
		logger.Errorf("Failed to get metrics: %v", err)
		return nil, errors.New("failed to get metrics")
	}

	// 转换为响应格式
	var responses []models.MetricResponse
	for _, metric := range metrics {
		responses = append(responses, *metric.ToResponse())
	}

	return models.NewPaginationResponse(pagination.Page, pagination.PageSize, total, responses), nil
}

// GetMetricsByTimeRange 根据时间范围获取监控指标
func (s *MetricService) GetMetricsByTimeRange(userID uint, databaseID uint, userRole string, metricType string, startTime, endTime time.Time) ([]models.MetricResponse, error) {
	// 检查权限
	if err := s.checkDatabaseAccess(userID, databaseID, userRole); err != nil {
		return nil, err
	}

	metrics, err := s.metricRepo.GetByTimeRange(databaseID, metricType, startTime, endTime)
	if err != nil {
		logger.Errorf("Failed to get metrics by time range: %v", err)
		return nil, errors.New("failed to get metrics")
	}

	// 转换为响应格式
	var responses []models.MetricResponse
	for _, metric := range metrics {
		responses = append(responses, *metric.ToResponse())
	}

	return responses, nil
}

// GetLatestMetrics 获取最新监控指标
func (s *MetricService) GetLatestMetrics(userID uint, databaseID uint, userRole string) ([]models.MetricResponse, error) {
	// 检查权限
	if err := s.checkDatabaseAccess(userID, databaseID, userRole); err != nil {
		return nil, err
	}

	metrics, err := s.metricRepo.GetLatestMetrics(databaseID)
	if err != nil {
		logger.Errorf("Failed to get latest metrics: %v", err)
		return nil, errors.New("failed to get latest metrics")
	}

	// 转换为响应格式
	var responses []models.MetricResponse
	for _, metric := range metrics {
		responses = append(responses, *metric.ToResponse())
	}

	return responses, nil
}

// GetAggregatedMetrics 获取聚合监控指标
func (s *MetricService) GetAggregatedMetrics(userID uint, userRole string, req *models.MetricQueryRequest) (*models.MetricAggregateResponse, error) {
	// 验证输入
	if err := utils.ValidateStruct(req); err != nil {
		return nil, err
	}

	// 检查权限
	if err := s.checkDatabaseAccess(userID, req.DatabaseID, userRole); err != nil {
		return nil, err
	}

	// 验证时间范围
	if req.EndTime.Before(req.StartTime) {
		return nil, errors.New("end time must be after start time")
	}

	// 限制查询范围（防止查询过大的数据集）
	maxDuration := 30 * 24 * time.Hour // 30天
	if req.EndTime.Sub(req.StartTime) > maxDuration {
		return nil, errors.New("time range too large, maximum 30 days")
	}

	result, err := s.metricRepo.GetAggregatedMetrics(req)
	if err != nil {
		logger.Errorf("Failed to get aggregated metrics: %v", err)
		return nil, errors.New("failed to get aggregated metrics")
	}

	return result, nil
}

// GetRealtimeMetrics 获取实时监控指标
func (s *MetricService) GetRealtimeMetrics(userID uint, userRole string) ([]models.RealtimeMetric, error) {
	// 获取用户可访问的数据库列表
	var databaseIDs []uint

	if userRole == "admin" {
		// 管理员可以看到所有数据库
		databases, err := s.databaseRepo.GetActiveInstances()
		if err != nil {
			logger.Errorf("Failed to get active databases: %v", err)
			return nil, errors.New("failed to get databases")
		}
		for _, db := range databases {
			databaseIDs = append(databaseIDs, db.ID)
		}
	} else {
		// 普通用户只能看到自己的数据库
		pagination := &models.PaginationRequest{Page: 1, PageSize: 1000}
		databases, _, err := s.databaseRepo.GetByUserID(userID, pagination)
		if err != nil {
			logger.Errorf("Failed to get user databases: %v", err)
			return nil, errors.New("failed to get databases")
		}
		for _, db := range databases {
			if db.IsMonitored {
				databaseIDs = append(databaseIDs, db.ID)
			}
		}
	}

	if len(databaseIDs) == 0 {
		return []models.RealtimeMetric{}, nil
	}

	metrics, err := s.metricRepo.GetRealtimeMetrics(databaseIDs)
	if err != nil {
		logger.Errorf("Failed to get realtime metrics: %v", err)
		return nil, errors.New("failed to get realtime metrics")
	}

	return metrics, nil
}

// GetMetricTypes 获取数据库的指标类型
func (s *MetricService) GetMetricTypes(userID uint, databaseID uint, userRole string) ([]string, error) {
	// 检查权限
	if err := s.checkDatabaseAccess(userID, databaseID, userRole); err != nil {
		return nil, err
	}

	metricTypes, err := s.metricRepo.GetMetricTypes(databaseID)
	if err != nil {
		logger.Errorf("Failed to get metric types: %v", err)
		return nil, errors.New("failed to get metric types")
	}

	return metricTypes, nil
}

// GetMetricStats 获取指标统计信息
func (s *MetricService) GetMetricStats(userID uint, databaseID uint, userRole string, metricType string, duration time.Duration) (*repository.MetricStats, error) {
	// 检查权限
	if err := s.checkDatabaseAccess(userID, databaseID, userRole); err != nil {
		return nil, err
	}

	stats, err := s.metricRepo.GetMetricStats(databaseID, metricType, duration)
	if err != nil {
		logger.Errorf("Failed to get metric stats: %v", err)
		return nil, errors.New("failed to get metric statistics")
	}

	return stats, nil
}

// CleanupOldMetrics 清理过期的监控指标
func (s *MetricService) CleanupOldMetrics(retentionDays int) error {
	if retentionDays <= 0 {
		return errors.New("retention days must be positive")
	}

	err := s.metricRepo.DeleteOldMetrics(retentionDays)
	if err != nil {
		logger.Errorf("Failed to cleanup old metrics: %v", err)
		return errors.New("failed to cleanup old metrics")
	}

	logger.Infof("Cleaned up metrics older than %d days", retentionDays)
	return nil
}

// checkDatabaseAccess 检查数据库访问权限
func (s *MetricService) checkDatabaseAccess(userID uint, databaseID uint, userRole string) error {
	// 管理员可以访问所有数据库
	if userRole == "admin" {
		return nil
	}

	// 检查数据库是否存在
	database, err := s.databaseRepo.GetByID(databaseID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("database not found")
		}
		return errors.New("failed to get database")
	}

	// 检查用户是否有权限访问该数据库
	if database.CreatedBy != userID {
		return errors.New("access denied")
	}

	return nil
}

// MetricCollectionRequest 指标收集请求
type MetricCollectionRequest struct {
	DatabaseID uint                        `json:"database_id" validate:"required"`
	Metrics    []models.MetricCreateRequest `json:"metrics" validate:"required,dive"`
	Timestamp  time.Time                   `json:"timestamp,omitempty"`
}

// CollectMetrics 收集监控指标（用于监控代理）
func (s *MetricService) CollectMetrics(req *MetricCollectionRequest) error {
	// 验证输入
	if err := utils.ValidateStruct(req); err != nil {
		return err
	}

	// 检查数据库实例是否存在
	database, err := s.databaseRepo.GetByID(req.DatabaseID)
	if err != nil {
		return errors.New("database not found")
	}

	if !database.IsMonitored {
		return errors.New("database monitoring is disabled")
	}

	// 设置时间戳
	timestamp := req.Timestamp
	if timestamp.IsZero() {
		timestamp = time.Now()
	}

	// 转换为Metric模型
	var metrics []models.Metric
	for _, metricReq := range req.Metrics {
		metric := models.Metric{
			DatabaseID: req.DatabaseID,
			MetricType: metricReq.MetricType,
			Value:      metricReq.Value,
			Unit:       models.GetMetricUnit(metricReq.MetricType),
			Labels:     metricReq.Labels,
			Timestamp:  timestamp,
		}
		metrics = append(metrics, metric)
	}

	// 批量保存
	if err := s.metricRepo.CreateBatch(metrics); err != nil {
		logger.Errorf("Failed to collect metrics: %v", err)
		return errors.New("failed to collect metrics")
	}

	logger.Debugf("Collected %d metrics for database %d", len(metrics), req.DatabaseID)
	return nil
}
