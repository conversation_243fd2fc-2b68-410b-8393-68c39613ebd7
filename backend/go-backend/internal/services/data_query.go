package services

import (
	"fmt"
	"math/rand"
	"time"

	"db-monitor-platform/internal/models"
	"db-monitor-platform/internal/repository"
)

// DataQueryService 数据查询服务接口
type DataQueryService interface {
	QueryMetrics(req *MetricQueryRequest) ([]MetricData, error)
	QueryDatabases(databaseIDs []uint) ([]DatabaseInfo, error)
	AggregateData(data []MetricData, granularity string) ([]MetricData, error)
}

// MetricQueryRequest 指标查询请求
type MetricQueryRequest struct {
	DatabaseIDs []uint                      `json:"database_ids"`
	MetricTypes []string                    `json:"metric_types"`
	TimeRange   models.TimeRangeRequest     `json:"time_range"`
	Granularity string                      `json:"granularity"` // minute, hour, day
}

// dataQueryService 数据查询服务实现
type dataQueryService struct {
	metricRepo   repository.MetricRepository
	databaseRepo repository.DatabaseRepository
}

// NewDataQueryService 创建数据查询服务实例
func NewDataQueryService(metricRepo repository.MetricRepository, databaseRepo repository.DatabaseRepository) DataQueryService {
	return &dataQueryService{
		metricRepo:   metricRepo,
		databaseRepo: databaseRepo,
	}
}

// QueryMetrics 查询监控指标数据
func (s *dataQueryService) QueryMetrics(req *MetricQueryRequest) ([]MetricData, error) {
	var allMetrics []MetricData

	// 获取时间范围
	startTime := req.TimeRange.StartTime
	endTime := req.TimeRange.EndTime

	// 如果没有指定数据库ID，查询所有数据库
	databaseIDs := req.DatabaseIDs
	if len(databaseIDs) == 0 {
		pagination := &models.PaginationRequest{Page: 1, PageSize: 100}
		databases, _, err := s.databaseRepo.GetAll(pagination)
		if err != nil {
			return nil, fmt.Errorf("failed to get databases: %w", err)
		}
		for _, db := range databases {
			databaseIDs = append(databaseIDs, db.ID)
		}
	}

	// 如果没有指定指标类型，使用默认类型
	metricTypes := req.MetricTypes
	if len(metricTypes) == 0 {
		metricTypes = []string{"cpu", "memory", "disk", "connections", "qps", "tps"}
	}

	// 查询每个数据库的指标数据
	for _, databaseID := range databaseIDs {
		// 获取数据库信息
		database, err := s.databaseRepo.GetByID(databaseID)
		if err != nil {
			continue // 跳过不存在的数据库
		}

		// 查询每种指标类型
		for _, metricType := range metricTypes {
			metrics, err := s.metricRepo.GetByTimeRange(databaseID, metricType, startTime, endTime)
			if err != nil {
				continue // 跳过查询失败的指标
			}

			// 转换为报表数据格式
			for _, metric := range metrics {
				metricData := MetricData{
					DatabaseID:   metric.DatabaseID,
					DatabaseName: database.Name,
					MetricType:   metric.MetricType,
					Value:        metric.Value,
					Unit:         metric.Unit,
					Timestamp:    metric.Timestamp,
					Description:  s.getMetricDescription(metric.MetricType),
				}
				allMetrics = append(allMetrics, metricData)
			}
		}
	}

	// 如果没有真实数据，生成模拟数据用于演示
	if len(allMetrics) == 0 {
		allMetrics = s.generateMockData(req, databaseIDs)
	}

	return allMetrics, nil
}

// QueryDatabases 查询数据库信息
func (s *dataQueryService) QueryDatabases(databaseIDs []uint) ([]DatabaseInfo, error) {
	var databases []DatabaseInfo

	// 如果没有指定数据库ID，查询所有数据库
	if len(databaseIDs) == 0 {
		pagination := &models.PaginationRequest{Page: 1, PageSize: 100}
		allDatabases, _, err := s.databaseRepo.GetAll(pagination)
		if err != nil {
			return nil, fmt.Errorf("failed to get all databases: %w", err)
		}
		for _, db := range allDatabases {
			databases = append(databases, s.convertToDatabaseInfo(&db))
		}
	} else {
		// 查询指定的数据库
		for _, id := range databaseIDs {
			db, err := s.databaseRepo.GetByID(id)
			if err != nil {
				continue // 跳过不存在的数据库
			}
			databases = append(databases, s.convertToDatabaseInfo(db))
		}
	}

	return databases, nil
}

// AggregateData 聚合数据
func (s *dataQueryService) AggregateData(data []MetricData, granularity string) ([]MetricData, error) {
	if len(data) == 0 {
		return data, nil
	}

	// 根据粒度进行数据聚合
	aggregatedData := make(map[string][]MetricData)
	
	for _, metric := range data {
		key := s.generateAggregationKey(metric, granularity)
		aggregatedData[key] = append(aggregatedData[key], metric)
	}

	var result []MetricData
	for _, group := range aggregatedData {
		if len(group) == 0 {
			continue
		}

		// 计算平均值
		var totalValue float64
		for _, metric := range group {
			totalValue += metric.Value
		}

		avgMetric := group[0] // 使用第一个作为模板
		avgMetric.Value = totalValue / float64(len(group))
		result = append(result, avgMetric)
	}

	return result, nil
}

// convertToDatabaseInfo 转换数据库模型为报表数据格式
func (s *dataQueryService) convertToDatabaseInfo(db *models.DatabaseInstance) DatabaseInfo {
	return DatabaseInfo{
		ID:          db.ID,
		Name:        db.Name,
		Type:        db.Type,
		Host:        db.Host,
		Port:        db.Port,
		Status:      db.Status,
		Description: db.Description,
	}
}

// generateAggregationKey 生成聚合键
func (s *dataQueryService) generateAggregationKey(metric MetricData, granularity string) string {
	var timeKey string
	switch granularity {
	case "minute":
		timeKey = metric.Timestamp.Format("2006-01-02 15:04")
	case "hour":
		timeKey = metric.Timestamp.Format("2006-01-02 15")
	case "day":
		timeKey = metric.Timestamp.Format("2006-01-02")
	default:
		timeKey = metric.Timestamp.Format("2006-01-02 15:04:05")
	}
	
	return fmt.Sprintf("%d_%s_%s", metric.DatabaseID, metric.MetricType, timeKey)
}

// getMetricDescription 获取指标描述
func (s *dataQueryService) getMetricDescription(metricType string) string {
	descriptions := map[string]string{
		"cpu":         "CPU使用率",
		"memory":      "内存使用率",
		"disk":        "磁盘使用率",
		"connections": "数据库连接数",
		"qps":         "每秒查询数",
		"tps":         "每秒事务数",
		"slow_queries": "慢查询数量",
		"locks":       "锁等待数量",
	}
	
	if desc, exists := descriptions[metricType]; exists {
		return desc
	}
	return metricType
}

// generateMockData 生成模拟数据用于演示
func (s *dataQueryService) generateMockData(req *MetricQueryRequest, databaseIDs []uint) []MetricData {
	var mockData []MetricData
	
	startTime := req.TimeRange.StartTime
	endTime := req.TimeRange.EndTime
	
	// 生成时间点
	duration := endTime.Sub(startTime)
	interval := duration / 20 // 生成20个数据点
	
	metricTypes := req.MetricTypes
	if len(metricTypes) == 0 {
		metricTypes = []string{"cpu", "memory", "disk", "connections", "qps", "tps"}
	}
	
	for _, databaseID := range databaseIDs {
		for _, metricType := range metricTypes {
			for i := 0; i < 20; i++ {
				timestamp := startTime.Add(time.Duration(i) * interval)
				
				mockData = append(mockData, MetricData{
					DatabaseID:   databaseID,
					DatabaseName: fmt.Sprintf("数据库-%d", databaseID),
					MetricType:   metricType,
					Value:        s.generateMockValue(metricType),
					Unit:         s.getMetricUnit(metricType),
					Timestamp:    timestamp,
					Description:  s.getMetricDescription(metricType),
				})
			}
		}
	}
	
	return mockData
}

// generateMockValue 生成模拟数值
func (s *dataQueryService) generateMockValue(metricType string) float64 {
	switch metricType {
	case "cpu", "memory", "disk":
		return float64(rand.Intn(80) + 10) // 10-90%
	case "connections":
		return float64(rand.Intn(100) + 10) // 10-110
	case "qps":
		return float64(rand.Intn(1000) + 100) // 100-1100
	case "tps":
		return float64(rand.Intn(500) + 50) // 50-550
	case "slow_queries":
		return float64(rand.Intn(10)) // 0-10
	case "locks":
		return float64(rand.Intn(5)) // 0-5
	default:
		return float64(rand.Intn(100))
	}
}

// getMetricUnit 获取指标单位
func (s *dataQueryService) getMetricUnit(metricType string) string {
	units := map[string]string{
		"cpu":         "%",
		"memory":      "%",
		"disk":        "%",
		"connections": "个",
		"qps":         "次/秒",
		"tps":         "次/秒",
		"slow_queries": "个",
		"locks":       "个",
	}
	
	if unit, exists := units[metricType]; exists {
		return unit
	}
	return ""
}
