package services

import (
	"compress/gzip"
	"errors"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"db-monitor-platform/internal/models"
	"db-monitor-platform/internal/repository"
	"db-monitor-platform/pkg/logger"

	"gorm.io/gorm"
)

// BackupService 备份管理服务接口
type BackupService interface {
	// 备份任务管理
	CreateTask(req *models.BackupTaskCreateRequest, userID uint) (*models.BackupTask, error)
	UpdateTask(taskID uint, req *models.BackupTaskUpdateRequest, userID uint) (*models.BackupTask, error)
	DeleteTask(taskID uint, userID uint) error
	GetTask(taskID uint, userID uint) (*models.BackupTask, error)
	ListTasks(req *models.BackupTaskListRequest, userID uint) ([]models.BackupTask, int64, error)
	
	// 备份执行
	ExecuteBackup(taskID uint, userID uint, force bool) (*models.BackupHistory, error)
	GetRunningBackups(userID uint) ([]models.BackupHistory, error)
	CancelBackup(historyID uint, userID uint) error
	
	// 备份历史管理
	GetBackupHistory(req *models.BackupHistoryListRequest, userID uint) ([]models.BackupHistory, int64, error)
	GetHistoryByID(historyID uint, userID uint) (*models.BackupHistory, error)
	DeleteHistory(historyID uint, userID uint) error
	
	// 统计和分析
	GetBackupStats(req *models.BackupStatsRequest, userID uint) (*models.BackupStatsResponse, error)
	GetTaskStats(userID uint) (*repository.BackupTaskStats, error)
	GetSuccessRate(taskID *uint, days int, userID uint) (float64, error)
	
	// 系统管理
	CleanupOldBackups(retentionDays int) (int64, error)
	GetScheduledTasks() ([]models.BackupTask, error)
	UpdateTaskStatus(taskID uint, status string) error
}

// backupService 备份管理服务实现
type backupService struct {
	taskRepo    repository.BackupTaskRepository
	historyRepo repository.BackupHistoryRepository
	dbRepo      repository.DatabaseRepository
	userRepo    repository.UserRepository
}

// NewBackupService 创建备份管理服务
func NewBackupService(db *gorm.DB) BackupService {
	return &backupService{
		taskRepo:    repository.NewBackupTaskRepository(db),
		historyRepo: repository.NewBackupHistoryRepository(db),
		dbRepo:      repository.NewDatabaseRepository(db),
		userRepo:    repository.NewUserRepository(db),
	}
}

// CreateTask 创建备份任务
func (s *backupService) CreateTask(req *models.BackupTaskCreateRequest, userID uint) (*models.BackupTask, error) {
	// 验证请求
	if errors := req.Validate(); len(errors) > 0 {
		return nil, fmt.Errorf("validation failed: %v", errors[0].Message)
	}
	
	// 检查数据库实例是否存在且用户有权限
	database, err := s.dbRepo.GetByID(req.DatabaseID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("database instance not found")
		}
		return nil, fmt.Errorf("failed to get database: %w", err)
	}
	
	// 检查用户权限（只能为自己创建的数据库实例创建备份任务）
	if database.CreatedBy != userID {
		return nil, errors.New("permission denied: you can only create backup tasks for your own databases")
	}
	
	// 检查是否已存在同名任务
	existingTasks, _, err := s.taskRepo.GetByDatabaseID(req.DatabaseID, &models.PaginationRequest{Page: 1, PageSize: 100})
	if err != nil {
		return nil, fmt.Errorf("failed to check existing tasks: %w", err)
	}
	
	for _, task := range existingTasks {
		if task.TaskName == req.TaskName {
			return nil, errors.New("backup task with this name already exists for this database")
		}
	}
	
	// 创建备份任务
	task := req.ToBackupTask(userID)
	
	if err := s.taskRepo.Create(task); err != nil {
		return nil, fmt.Errorf("failed to create backup task: %w", err)
	}
	
	logger.Infof("Created backup task: %s for database %d by user %d", task.TaskName, task.DatabaseID, userID)
	return task, nil
}

// UpdateTask 更新备份任务
func (s *backupService) UpdateTask(taskID uint, req *models.BackupTaskUpdateRequest, userID uint) (*models.BackupTask, error) {
	// 验证请求
	if errors := req.Validate(); len(errors) > 0 {
		return nil, fmt.Errorf("validation failed: %v", errors[0].Message)
	}
	
	// 检查任务是否存在且用户有权限
	task, err := s.taskRepo.GetByID(taskID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("backup task not found")
		}
		return nil, fmt.Errorf("failed to get backup task: %w", err)
	}
	
	if task.CreatedBy != userID {
		return nil, errors.New("permission denied: you can only update your own backup tasks")
	}
	
	// 应用更新
	req.ApplyToBackupTask(task)
	
	if err := s.taskRepo.Update(task); err != nil {
		return nil, fmt.Errorf("failed to update backup task: %w", err)
	}
	
	logger.Infof("Updated backup task: %d by user %d", taskID, userID)
	return task, nil
}

// DeleteTask 删除备份任务
func (s *backupService) DeleteTask(taskID uint, userID uint) error {
	// 检查任务是否存在且用户有权限
	task, err := s.taskRepo.GetByID(taskID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("backup task not found")
		}
		return fmt.Errorf("failed to get backup task: %w", err)
	}
	
	if task.CreatedBy != userID {
		return errors.New("permission denied: you can only delete your own backup tasks")
	}
	
	// 检查是否有正在运行的备份
	runningBackups, err := s.historyRepo.GetRunningBackups()
	if err != nil {
		return fmt.Errorf("failed to check running backups: %w", err)
	}
	
	for _, backup := range runningBackups {
		if backup.TaskID == taskID {
			return errors.New("cannot delete task: backup is currently running")
		}
	}
	
	// 删除任务（这将级联删除相关的历史记录）
	if err := s.taskRepo.Delete(taskID); err != nil {
		return fmt.Errorf("failed to delete backup task: %w", err)
	}
	
	logger.Infof("Deleted backup task: %d by user %d", taskID, userID)
	return nil
}

// GetTask 获取备份任务详情
func (s *backupService) GetTask(taskID uint, userID uint) (*models.BackupTask, error) {
	task, err := s.taskRepo.GetByID(taskID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("backup task not found")
		}
		return nil, fmt.Errorf("failed to get backup task: %w", err)
	}
	
	// 检查权限
	if task.CreatedBy != userID {
		return nil, errors.New("permission denied: you can only view your own backup tasks")
	}
	
	return task, nil
}

// ListTasks 获取备份任务列表
func (s *backupService) ListTasks(req *models.BackupTaskListRequest, userID uint) ([]models.BackupTask, int64, error) {
	// 如果指定了数据库ID，检查用户是否有权限
	if req.DatabaseID != nil {
		database, err := s.dbRepo.GetByID(*req.DatabaseID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, 0, errors.New("database instance not found")
			}
			return nil, 0, fmt.Errorf("failed to get database: %w", err)
		}
		
		if database.CreatedBy != userID {
			return nil, 0, errors.New("permission denied: you can only view backup tasks for your own databases")
		}
	}
	
	// 获取用户的备份任务
	tasks, total, err := s.taskRepo.GetByUserID(userID, &req.PaginationRequest)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get backup tasks: %w", err)
	}
	
	// 如果有其他筛选条件，进一步过滤
	if req.DatabaseID != nil || req.Status != nil || req.BackupType != nil || (req.Search != nil && *req.Search != "") {
		tasks, total, err = s.taskRepo.List(req)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to filter backup tasks: %w", err)
		}
		
		// 过滤出用户有权限的任务
		var filteredTasks []models.BackupTask
		for _, task := range tasks {
			if task.CreatedBy == userID {
				filteredTasks = append(filteredTasks, task)
			}
		}
		tasks = filteredTasks
		total = int64(len(filteredTasks))
	}
	
	return tasks, total, nil
}

// ExecuteBackup 执行备份
func (s *backupService) ExecuteBackup(taskID uint, userID uint, force bool) (*models.BackupHistory, error) {
	// 检查任务是否存在且用户有权限
	task, err := s.taskRepo.GetByID(taskID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("backup task not found")
		}
		return nil, fmt.Errorf("failed to get backup task: %w", err)
	}
	
	if task.CreatedBy != userID {
		return nil, errors.New("permission denied: you can only execute your own backup tasks")
	}
	
	// 检查任务状态
	if task.Status != "active" {
		return nil, errors.New("backup task is not active")
	}
	
	// 检查是否已有正在运行的备份（除非强制执行）
	if !force {
		runningBackups, err := s.historyRepo.GetRunningBackups()
		if err != nil {
			return nil, fmt.Errorf("failed to check running backups: %w", err)
		}
		
		for _, backup := range runningBackups {
			if backup.TaskID == taskID {
				return nil, errors.New("backup is already running for this task")
			}
		}
	}
	
	// 创建备份历史记录
	history := &models.BackupHistory{
		TaskID:    taskID,
		Status:    "running",
		StartTime: time.Now(),
	}
	
	if err := s.historyRepo.Create(history); err != nil {
		return nil, fmt.Errorf("failed to create backup history: %w", err)
	}
	
	// 在实际实现中，这里应该启动异步备份进程
	// 现在我们模拟一个快速完成的备份
	go s.simulateBackupExecution(history, task)
	
	logger.Infof("Started backup execution for task %d by user %d", taskID, userID)
	return history, nil
}

// simulateBackupExecution 执行真实的备份（支持PostgreSQL和MySQL）
func (s *backupService) simulateBackupExecution(history *models.BackupHistory, task *models.BackupTask) {
	// 获取数据库实例信息
	database, err := s.dbRepo.GetByID(task.DatabaseID)
	if err != nil {
		s.updateBackupFailure(history, fmt.Sprintf("Failed to get database info: %v", err))
		return
	}

	// 创建备份目录
	backupDir := "/tmp/backups"
	if err := os.MkdirAll(backupDir, 0755); err != nil {
		s.updateBackupFailure(history, fmt.Sprintf("Failed to create backup directory: %v", err))
		return
	}

	// 生成备份文件名
	now := time.Now()
	fileName := fmt.Sprintf("%s_%s.sql", task.TaskName, now.Format("20060102_150405"))
	filePath := filepath.Join(backupDir, fileName)

	// 根据数据库类型执行备份
	var backupSize int64
	switch strings.ToLower(database.Type) {
	case "postgresql":
		backupSize, err = s.executePostgreSQLBackup(database, filePath)
	case "mysql":
		backupSize, err = s.executeMySQLBackup(database, filePath)
	default:
		// 对于其他数据库类型，创建一个模拟备份文件
		backupSize, err = s.createMockBackup(database, filePath)
	}

	if err != nil {
		s.updateBackupFailure(history, fmt.Sprintf("Backup execution failed: %v", err))
		return
	}

	// 如果启用压缩，压缩备份文件
	finalPath := filePath
	if task.Compression {
		compressedPath := filePath + ".gz"
		if err := s.compressFile(filePath, compressedPath); err != nil {
			logger.Warnf("Failed to compress backup file: %v", err)
		} else {
			os.Remove(filePath) // 删除原文件
			finalPath = compressedPath
			fileName = fileName + ".gz"
		}
	}

	// 更新备份成功状态
	history.EndTime = &now
	history.Status = "success"
	history.BackupSize = backupSize
	history.FilePath = finalPath
	history.Duration = int(now.Sub(history.StartTime).Seconds())

	// 更新历史记录
	if err := s.historyRepo.Update(history); err != nil {
		logger.Errorf("Failed to update backup history: %v", err)
		return
	}

	// 更新任务的最后备份时间
	task.UpdateLastBackup(history.BackupSize)
	if err := s.taskRepo.Update(task); err != nil {
		logger.Errorf("Failed to update task last backup time: %v", err)
	}

	logger.Infof("Backup completed successfully for task %d, file: %s, size: %d bytes", task.ID, finalPath, backupSize)
}

// updateBackupFailure 更新备份失败状态
func (s *backupService) updateBackupFailure(history *models.BackupHistory, errorMsg string) {
	now := time.Now()
	history.EndTime = &now
	history.Status = "failed"
	history.ErrorMessage = errorMsg
	history.Duration = int(now.Sub(history.StartTime).Seconds())

	if err := s.historyRepo.Update(history); err != nil {
		logger.Errorf("Failed to update backup failure: %v", err)
	}

	logger.Errorf("Backup failed for task %d: %s", history.TaskID, errorMsg)
}

// executePostgreSQLBackup 执行PostgreSQL备份
func (s *backupService) executePostgreSQLBackup(database *models.DatabaseInstance, filePath string) (int64, error) {
	// 构建pg_dump命令
	cmd := exec.Command("pg_dump",
		"-h", database.Host,
		"-p", fmt.Sprintf("%d", database.Port),
		"-U", database.Username,
		"-d", database.DatabaseName,
		"-f", filePath,
		"--no-password",
	)

	// 设置环境变量
	cmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", database.Password))

	// 执行命令
	if err := cmd.Run(); err != nil {
		return 0, fmt.Errorf("pg_dump failed: %w", err)
	}

	// 获取文件大小
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return 0, fmt.Errorf("failed to get file size: %w", err)
	}

	return fileInfo.Size(), nil
}

// executeMySQLBackup 执行MySQL备份
func (s *backupService) executeMySQLBackup(database *models.DatabaseInstance, filePath string) (int64, error) {
	// 构建mysqldump命令
	cmd := exec.Command("mysqldump",
		"-h", database.Host,
		"-P", fmt.Sprintf("%d", database.Port),
		"-u", database.Username,
		fmt.Sprintf("-p%s", database.Password),
		database.DatabaseName,
	)

	// 创建输出文件
	outFile, err := os.Create(filePath)
	if err != nil {
		return 0, fmt.Errorf("failed to create output file: %w", err)
	}
	defer outFile.Close()

	// 设置命令输出
	cmd.Stdout = outFile

	// 执行命令
	if err := cmd.Run(); err != nil {
		return 0, fmt.Errorf("mysqldump failed: %w", err)
	}

	// 获取文件大小
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return 0, fmt.Errorf("failed to get file size: %w", err)
	}

	return fileInfo.Size(), nil
}

// createMockBackup 为不支持的数据库类型创建模拟备份
func (s *backupService) createMockBackup(database *models.DatabaseInstance, filePath string) (int64, error) {
	// 创建一个包含基本信息的模拟备份文件
	content := fmt.Sprintf(`-- Database Backup
-- Database: %s (%s)
-- Host: %s:%d
-- Generated: %s
-- Note: This is a mock backup for unsupported database type

-- Mock data for demonstration
CREATE TABLE IF NOT EXISTS backup_info (
    id SERIAL PRIMARY KEY,
    database_name VARCHAR(255),
    backup_time TIMESTAMP,
    status VARCHAR(50)
);

INSERT INTO backup_info (database_name, backup_time, status)
VALUES ('%s', '%s', 'completed');
`,
		database.DatabaseName,
		database.Type,
		database.Host,
		database.Port,
		time.Now().Format("2006-01-02 15:04:05"),
		database.DatabaseName,
		time.Now().Format("2006-01-02 15:04:05"),
	)

	// 写入文件
	if err := os.WriteFile(filePath, []byte(content), 0644); err != nil {
		return 0, fmt.Errorf("failed to write mock backup file: %w", err)
	}

	// 获取文件大小
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return 0, fmt.Errorf("failed to get file size: %w", err)
	}

	return fileInfo.Size(), nil
}

// compressFile 压缩文件
func (s *backupService) compressFile(srcPath, dstPath string) error {
	// 打开源文件
	srcFile, err := os.Open(srcPath)
	if err != nil {
		return fmt.Errorf("failed to open source file: %w", err)
	}
	defer srcFile.Close()

	// 创建目标文件
	dstFile, err := os.Create(dstPath)
	if err != nil {
		return fmt.Errorf("failed to create destination file: %w", err)
	}
	defer dstFile.Close()

	// 创建gzip writer
	gzipWriter := gzip.NewWriter(dstFile)
	defer gzipWriter.Close()

	// 复制数据
	_, err = io.Copy(gzipWriter, srcFile)
	if err != nil {
		return fmt.Errorf("failed to compress file: %w", err)
	}

	return nil
}

// GetRunningBackups 获取正在运行的备份
func (s *backupService) GetRunningBackups(userID uint) ([]models.BackupHistory, error) {
	runningBackups, err := s.historyRepo.GetRunningBackups()
	if err != nil {
		return nil, fmt.Errorf("failed to get running backups: %w", err)
	}

	// 过滤出用户有权限的备份
	var userBackups []models.BackupHistory
	for _, backup := range runningBackups {
		if backup.Task.CreatedBy == userID {
			userBackups = append(userBackups, backup)
		}
	}

	return userBackups, nil
}

// CancelBackup 取消备份
func (s *backupService) CancelBackup(historyID uint, userID uint) error {
	history, err := s.historyRepo.GetByID(historyID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("backup history not found")
		}
		return fmt.Errorf("failed to get backup history: %w", err)
	}

	// 检查权限
	if history.Task.CreatedBy != userID {
		return errors.New("permission denied: you can only cancel your own backups")
	}

	// 检查状态
	if history.Status != "running" {
		return errors.New("backup is not running")
	}

	// 更新状态为失败
	now := time.Now()
	history.EndTime = &now
	history.Status = "failed"
	history.ErrorMessage = "Backup cancelled by user"
	history.Duration = int(now.Sub(history.StartTime).Seconds())

	if err := s.historyRepo.Update(history); err != nil {
		return fmt.Errorf("failed to cancel backup: %w", err)
	}

	logger.Infof("Cancelled backup %d by user %d", historyID, userID)
	return nil
}

// GetBackupHistory 获取备份历史
func (s *backupService) GetBackupHistory(req *models.BackupHistoryListRequest, userID uint) ([]models.BackupHistory, int64, error) {
	// 如果指定了任务ID，检查用户是否有权限
	if req.TaskID != nil {
		task, err := s.taskRepo.GetByID(*req.TaskID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, 0, errors.New("backup task not found")
			}
			return nil, 0, fmt.Errorf("failed to get backup task: %w", err)
		}

		if task.CreatedBy != userID {
			return nil, 0, errors.New("permission denied: you can only view history for your own backup tasks")
		}
	}

	// 如果指定了数据库ID，检查用户是否有权限
	if req.DatabaseID != nil {
		database, err := s.dbRepo.GetByID(*req.DatabaseID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, 0, errors.New("database instance not found")
			}
			return nil, 0, fmt.Errorf("failed to get database: %w", err)
		}

		if database.CreatedBy != userID {
			return nil, 0, errors.New("permission denied: you can only view history for your own databases")
		}
	}

	histories, _, err := s.historyRepo.List(req)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get backup history: %w", err)
	}

	// 过滤出用户有权限的历史记录
	var userHistories []models.BackupHistory
	for _, history := range histories {
		if history.Task.CreatedBy == userID {
			userHistories = append(userHistories, history)
		}
	}

	return userHistories, int64(len(userHistories)), nil
}

// GetHistoryByID 获取备份历史详情
func (s *backupService) GetHistoryByID(historyID uint, userID uint) (*models.BackupHistory, error) {
	history, err := s.historyRepo.GetByID(historyID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("backup history not found")
		}
		return nil, fmt.Errorf("failed to get backup history: %w", err)
	}

	// 检查权限
	if history.Task.CreatedBy != userID {
		return nil, errors.New("permission denied: you can only view your own backup history")
	}

	return history, nil
}

// DeleteHistory 删除备份历史
func (s *backupService) DeleteHistory(historyID uint, userID uint) error {
	history, err := s.historyRepo.GetByID(historyID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("backup history not found")
		}
		return fmt.Errorf("failed to get backup history: %w", err)
	}

	// 检查权限
	if history.Task.CreatedBy != userID {
		return errors.New("permission denied: you can only delete your own backup history")
	}

	// 不能删除正在运行的备份
	if history.Status == "running" {
		return errors.New("cannot delete running backup")
	}

	if err := s.historyRepo.Delete(historyID); err != nil {
		return fmt.Errorf("failed to delete backup history: %w", err)
	}

	logger.Infof("Deleted backup history %d by user %d", historyID, userID)
	return nil
}

// GetBackupStats 获取备份统计
func (s *backupService) GetBackupStats(req *models.BackupStatsRequest, userID uint) (*models.BackupStatsResponse, error) {
	// 如果指定了数据库ID，检查用户是否有权限
	if req.DatabaseID != nil {
		database, err := s.dbRepo.GetByID(*req.DatabaseID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("database instance not found")
			}
			return nil, fmt.Errorf("failed to get database: %w", err)
		}

		if database.CreatedBy != userID {
			return nil, errors.New("permission denied: you can only view stats for your own databases")
		}
	}

	stats, err := s.historyRepo.GetBackupStats(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get backup stats: %w", err)
	}

	// 获取任务统计
	taskStats, err := s.taskRepo.GetTaskStats(&userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get task stats: %w", err)
	}

	stats.TotalTasks = int(taskStats.TotalTasks)
	stats.ActiveTasks = int(taskStats.ActiveTasks)

	return stats, nil
}

// GetTaskStats 获取任务统计
func (s *backupService) GetTaskStats(userID uint) (*repository.BackupTaskStats, error) {
	return s.taskRepo.GetTaskStats(&userID)
}

// GetSuccessRate 获取成功率
func (s *backupService) GetSuccessRate(taskID *uint, days int, userID uint) (float64, error) {
	// 如果指定了任务ID，检查用户是否有权限
	if taskID != nil {
		task, err := s.taskRepo.GetByID(*taskID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return 0, errors.New("backup task not found")
			}
			return 0, fmt.Errorf("failed to get backup task: %w", err)
		}

		if task.CreatedBy != userID {
			return 0, errors.New("permission denied: you can only view success rate for your own backup tasks")
		}
	}

	return s.historyRepo.GetSuccessRate(taskID, days)
}

// CleanupOldBackups 清理过期备份
func (s *backupService) CleanupOldBackups(retentionDays int) (int64, error) {
	return s.historyRepo.DeleteOldBackups(retentionDays)
}

// GetScheduledTasks 获取计划任务
func (s *backupService) GetScheduledTasks() ([]models.BackupTask, error) {
	return s.taskRepo.GetTasksNeedingBackup()
}

// UpdateTaskStatus 更新任务状态
func (s *backupService) UpdateTaskStatus(taskID uint, status string) error {
	task, err := s.taskRepo.GetByID(taskID)
	if err != nil {
		return fmt.Errorf("failed to get backup task: %w", err)
	}

	task.Status = status
	return s.taskRepo.Update(task)
}
