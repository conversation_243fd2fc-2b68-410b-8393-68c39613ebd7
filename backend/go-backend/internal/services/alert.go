package services

import (
	"errors"
	"fmt"
	"time"

	"db-monitor-platform/internal/models"
	"db-monitor-platform/internal/repository"
	"db-monitor-platform/internal/utils"
	"db-monitor-platform/pkg/logger"

	"gorm.io/gorm"
)

// AlertService 告警服务
type AlertService struct {
	alertRepo    repository.AlertRepository
	databaseRepo repository.DatabaseRepository
	metricRepo   repository.MetricRepository
	userRepo     repository.UserRepository
}

// NewAlertService 创建告警服务
func NewAlertService(db *gorm.DB) *AlertService {
	return &AlertService{
		alertRepo:    repository.NewAlertRepository(db),
		databaseRepo: repository.NewDatabaseRepository(db),
		metricRepo:   repository.NewMetricRepository(db),
		userRepo:     repository.NewUserRepository(db),
	}
}

// CreateAlertRule 创建告警规则
func (s *AlertService) CreateAlertRule(userID uint, req *models.AlertRuleCreateRequest) (*models.AlertRuleResponse, error) {
	// 验证输入
	if err := utils.ValidateAndSanitize(req); err != nil {
		logger.Errorf("Validation failed: %v", err)
		return nil, err
	}

	// 检查数据库实例是否存在且用户有权限
	database, err := s.databaseRepo.GetByID(req.DatabaseID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("database not found")
		}
		logger.Errorf("Failed to get database: %v", err)
		return nil, errors.New("failed to get database")
	}

	// 检查权限
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, errors.New("user not found")
	}

	if !user.IsAdmin() && database.CreatedBy != userID {
		return nil, errors.New("access denied")
	}

	// 验证告警条件
	if err := s.validateAlertConditions(req.Operator, req.Threshold); err != nil {
		return nil, err
	}

	// 创建告警规则
	rule := &models.AlertRule{
		Name:        req.Name,
		Description: req.Description,
		DatabaseID:  req.DatabaseID,
		MetricType:  req.MetricType,
		Operator:    req.Operator,
		Threshold:   req.Threshold,
		Severity:    req.Severity,
		Enabled:     true,
		CreatedBy:   userID,
	}

	if req.Duration != nil {
		rule.Duration = *req.Duration
	}

	if req.Enabled != nil {
		rule.Enabled = *req.Enabled
	}

	if err := s.alertRepo.CreateRule(rule); err != nil {
		logger.Errorf("Failed to create alert rule: %v", err)
		return nil, errors.New("failed to create alert rule")
	}

	logger.Infof("Alert rule created: %s for database %d by user %d", rule.Name, rule.DatabaseID, userID)
	return rule.ToResponse(), nil
}

// GetAlertRule 获取告警规则详情
func (s *AlertService) GetAlertRule(userID uint, ruleID uint, userRole string) (*models.AlertRuleResponse, error) {
	rule, err := s.alertRepo.GetRuleByID(ruleID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("alert rule not found")
		}
		logger.Errorf("Failed to get alert rule: %v", err)
		return nil, errors.New("failed to get alert rule")
	}

	// 检查权限
	if err := s.checkRuleAccess(userID, rule, userRole); err != nil {
		return nil, err
	}

	response := rule.ToResponse()
	if rule.Database.ID > 0 {
		response.Database = &rule.Database
	}
	return response, nil
}

// GetAlertRules 获取告警规则列表
func (s *AlertService) GetAlertRules(userID uint, userRole string, pagination *models.PaginationRequest) (*models.PaginationResponse, error) {
	var rules []models.AlertRule
	var total int64
	var err error

	// 根据用户角色获取数据
	if userRole == "admin" {
		rules, total, err = s.alertRepo.GetAllRules(pagination)
	} else {
		rules, total, err = s.alertRepo.GetRulesByUserID(userID, pagination)
	}

	if err != nil {
		logger.Errorf("Failed to get alert rules: %v", err)
		return nil, errors.New("failed to get alert rules")
	}

	// 转换为响应格式
	var responses []models.AlertRuleResponse
	for _, rule := range rules {
		response := rule.ToResponse()
		if rule.Database.ID > 0 {
			response.Database = &rule.Database
		}
		responses = append(responses, *response)
	}

	return models.NewPaginationResponse(pagination.Page, pagination.PageSize, total, responses), nil
}

// UpdateAlertRule 更新告警规则
func (s *AlertService) UpdateAlertRule(userID uint, ruleID uint, userRole string, req *models.AlertRuleUpdateRequest) (*models.AlertRuleResponse, error) {
	// 验证输入
	if err := utils.ValidateAndSanitize(req); err != nil {
		logger.Errorf("Validation failed: %v", err)
		return nil, err
	}

	// 获取告警规则
	rule, err := s.alertRepo.GetRuleByID(ruleID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("alert rule not found")
		}
		logger.Errorf("Failed to get alert rule: %v", err)
		return nil, errors.New("failed to get alert rule")
	}

	// 检查权限
	if err := s.checkRuleAccess(userID, rule, userRole); err != nil {
		return nil, err
	}

	// 更新字段
	if req.Name != "" {
		rule.Name = req.Name
	}
	if req.Description != "" {
		rule.Description = req.Description
	}
	if req.Operator != "" {
		rule.Operator = req.Operator
	}
	if req.Threshold != nil {
		if err := s.validateAlertConditions(req.Operator, *req.Threshold); err != nil {
			return nil, err
		}
		rule.Threshold = *req.Threshold
	}
	if req.Duration != nil {
		rule.Duration = *req.Duration
	}
	if req.Severity != "" {
		rule.Severity = req.Severity
	}
	if req.Enabled != nil {
		rule.Enabled = *req.Enabled
	}

	// 保存更新
	if err := s.alertRepo.UpdateRule(rule); err != nil {
		logger.Errorf("Failed to update alert rule: %v", err)
		return nil, errors.New("failed to update alert rule")
	}

	logger.Infof("Alert rule updated: %s by user %d", rule.Name, userID)
	return rule.ToResponse(), nil
}

// DeleteAlertRule 删除告警规则
func (s *AlertService) DeleteAlertRule(userID uint, ruleID uint, userRole string) error {
	// 获取告警规则
	rule, err := s.alertRepo.GetRuleByID(ruleID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("alert rule not found")
		}
		logger.Errorf("Failed to get alert rule: %v", err)
		return errors.New("failed to get alert rule")
	}

	// 检查权限
	if err := s.checkRuleAccess(userID, rule, userRole); err != nil {
		return err
	}

	// 删除告警规则
	if err := s.alertRepo.DeleteRule(ruleID); err != nil {
		logger.Errorf("Failed to delete alert rule: %v", err)
		return errors.New("failed to delete alert rule")
	}

	logger.Infof("Alert rule deleted: %s by user %d", rule.Name, userID)
	return nil
}

// GetAlertEvents 获取告警事件列表
func (s *AlertService) GetAlertEvents(userID uint, userRole string, pagination *models.PaginationRequest) (*models.PaginationResponse, error) {
	var events []models.AlertEvent
	var total int64
	var err error

	// 根据用户角色获取数据
	if userRole == "admin" {
		events, total, err = s.alertRepo.GetActiveEvents(pagination)
	} else {
		events, total, err = s.alertRepo.GetEventsByUserID(userID, pagination)
	}

	if err != nil {
		logger.Errorf("Failed to get alert events: %v", err)
		return nil, errors.New("failed to get alert events")
	}

	// 转换为响应格式
	var responses []models.AlertEventResponse
	for _, event := range events {
		response := event.ToResponse()
		if event.Database.ID > 0 {
			response.Database = &event.Database
		}
		if event.Rule.ID > 0 {
			response.Rule = &event.Rule
		}
		// Resolver字段在AlertEventResponse中可能不存在，先注释掉
		// if event.Resolver != nil {
		//     response.Resolver = event.Resolver
		// }
		responses = append(responses, *response)
	}

	return models.NewPaginationResponse(pagination.Page, pagination.PageSize, total, responses), nil
}

// GetAlertEvent 获取告警事件详情
func (s *AlertService) GetAlertEvent(userID uint, eventID uint, userRole string) (*models.AlertEventResponse, error) {
	event, err := s.alertRepo.GetEventByID(eventID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("alert event not found")
		}
		logger.Errorf("Failed to get alert event: %v", err)
		return nil, errors.New("failed to get alert event")
	}

	// 检查权限
	if err := s.checkEventAccess(userID, event, userRole); err != nil {
		return nil, err
	}

	response := event.ToResponse()
	if event.Database.ID > 0 {
		response.Database = &event.Database
	}
	if event.Rule.ID > 0 {
		response.Rule = &event.Rule
	}
	// Resolver字段在AlertEventResponse中可能不存在，先注释掉
	// if event.Resolver != nil {
	//     response.Resolver = event.Resolver
	// }
	return response, nil
}

// ResolveAlertEvent 解决告警事件
func (s *AlertService) ResolveAlertEvent(userID uint, eventID uint, userRole string) error {
	// 获取告警事件
	event, err := s.alertRepo.GetEventByID(eventID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("alert event not found")
		}
		logger.Errorf("Failed to get alert event: %v", err)
		return errors.New("failed to get alert event")
	}

	// 检查权限
	if err := s.checkEventAccess(userID, event, userRole); err != nil {
		return err
	}

	// 检查事件状态
	if event.Status != "active" {
		return errors.New("alert event is not active")
	}

	// 解决告警事件
	if err := s.alertRepo.ResolveEvent(eventID, userID); err != nil {
		logger.Errorf("Failed to resolve alert event: %v", err)
		return errors.New("failed to resolve alert event")
	}

	logger.Infof("Alert event resolved: %d by user %d", eventID, userID)
	return nil
}

// GetAlertStats 获取告警统计信息
func (s *AlertService) GetAlertStats(userID uint, userRole string) (*repository.AlertStats, error) {
	var statsUserID uint
	if userRole != "admin" {
		statsUserID = userID
	}

	stats, err := s.alertRepo.GetEventStats(statsUserID)
	if err != nil {
		logger.Errorf("Failed to get alert stats: %v", err)
		return nil, errors.New("failed to get alert statistics")
	}

	return stats, nil
}

// SearchAlertRules 搜索告警规则
func (s *AlertService) SearchAlertRules(userID uint, userRole string, keyword string, pagination *models.PaginationRequest) (*models.PaginationResponse, error) {
	rules, total, err := s.alertRepo.SearchRules(keyword, pagination)
	if err != nil {
		logger.Errorf("Failed to search alert rules: %v", err)
		return nil, errors.New("failed to search alert rules")
	}

	// 过滤权限：非管理员只能看到自己的
	if userRole != "admin" {
		var filteredRules []models.AlertRule
		for _, rule := range rules {
			if rule.CreatedBy == userID {
				filteredRules = append(filteredRules, rule)
			}
		}
		rules = filteredRules
		total = int64(len(rules))
	}

	// 转换为响应格式
	var responses []models.AlertRuleResponse
	for _, rule := range rules {
		response := rule.ToResponse()
		if rule.Database.ID > 0 {
			response.Database = &rule.Database
		}
		responses = append(responses, *response)
	}

	return models.NewPaginationResponse(pagination.Page, pagination.PageSize, total, responses), nil
}

// EvaluateAlerts 评估告警规则（用于定时任务）
func (s *AlertService) EvaluateAlerts() error {
	// 获取所有启用的告警规则
	rules, err := s.alertRepo.GetActiveRules()
	if err != nil {
		logger.Errorf("Failed to get active rules: %v", err)
		return errors.New("failed to get active rules")
	}

	for _, rule := range rules {
		if err := s.evaluateRule(&rule); err != nil {
			logger.Errorf("Failed to evaluate rule %d: %v", rule.ID, err)
			// 继续处理其他规则
		}
	}

	return nil
}

// evaluateRule 评估单个告警规则
func (s *AlertService) evaluateRule(rule *models.AlertRule) error {
	// 获取最新的监控指标
	metrics, err := s.metricRepo.GetLatestMetrics(rule.DatabaseID)
	if err != nil {
		return fmt.Errorf("failed to get latest metrics: %v", err)
	}

	// 查找对应的指标
	var targetMetric *models.Metric
	for _, metric := range metrics {
		if metric.MetricType == rule.MetricType {
			targetMetric = &metric
			break
		}
	}

	if targetMetric == nil {
		logger.Debugf("No metric found for rule %d, metric type: %s", rule.ID, rule.MetricType)
		return nil
	}

	// 评估告警条件
	triggered, err := s.evaluateConditions(rule.Operator, rule.Threshold, targetMetric.Value)
	if err != nil {
		return fmt.Errorf("failed to evaluate conditions: %v", err)
	}

	if triggered {
		// 创建告警事件
		event := &models.AlertEvent{
			AlertRuleID: rule.ID,
			DatabaseID:  rule.DatabaseID,
			MetricType:  rule.MetricType,
			Value:       targetMetric.Value,
			Severity:    rule.Severity,
			Status:      "active",
			Message:     fmt.Sprintf("Alert triggered: %s", rule.Name),
			StartTime:   time.Now(),
		}

		if err := s.alertRepo.CreateEvent(event); err != nil {
			return fmt.Errorf("failed to create alert event: %v", err)
		}

		logger.Infof("Alert triggered: rule %d, value %f", rule.ID, targetMetric.Value)
	}

	return nil
}

// validateAlertConditions 验证告警条件
func (s *AlertService) validateAlertConditions(operator string, threshold float64) error {
	// 验证操作符
	validOperators := []string{">", "<", ">=", "<=", "=", "!="}
	isValidOperator := false
	for _, op := range validOperators {
		if operator == op {
			isValidOperator = true
			break
		}
	}

	if !isValidOperator {
		return errors.New("invalid operator")
	}

	// 验证阈值
	if threshold < 0 {
		return errors.New("threshold must be non-negative")
	}

	return nil
}

// evaluateConditions 评估告警条件
func (s *AlertService) evaluateConditions(operator string, threshold float64, value float64) (bool, error) {
	switch operator {
	case ">":
		return value > threshold, nil
	case "<":
		return value < threshold, nil
	case ">=":
		return value >= threshold, nil
	case "<=":
		return value <= threshold, nil
	case "=":
		return value == threshold, nil
	case "!=":
		return value != threshold, nil
	default:
		return false, errors.New("invalid operator")
	}
}

// checkRuleAccess 检查告警规则访问权限
func (s *AlertService) checkRuleAccess(userID uint, rule *models.AlertRule, userRole string) error {
	if userRole == "admin" {
		return nil
	}
	
	if rule.CreatedBy != userID {
		return errors.New("access denied")
	}
	
	return nil
}

// checkEventAccess 检查告警事件访问权限
func (s *AlertService) checkEventAccess(userID uint, event *models.AlertEvent, userRole string) error {
	if userRole == "admin" {
		return nil
	}
	
	// 通过告警规则检查权限
	rule, err := s.alertRepo.GetRuleByID(event.AlertRuleID)
	if err != nil {
		return errors.New("failed to get alert rule")
	}
	
	if rule.CreatedBy != userID {
		return errors.New("access denied")
	}
	
	return nil
}

// CleanupOldEvents 清理过期的告警事件
func (s *AlertService) CleanupOldEvents(retentionDays int) error {
	if retentionDays <= 0 {
		return errors.New("retention days must be positive")
	}

	err := s.alertRepo.CleanupOldEvents(retentionDays)
	if err != nil {
		logger.Errorf("Failed to cleanup old alert events: %v", err)
		return errors.New("failed to cleanup old alert events")
	}

	logger.Infof("Cleaned up alert events older than %d days", retentionDays)
	return nil
}
