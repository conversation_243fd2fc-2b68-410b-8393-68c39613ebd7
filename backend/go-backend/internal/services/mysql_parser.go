package services

import (
	"strings"

	"db-monitor-platform/pkg/logger"
)

// MySQLParser MySQL专用解析器
// 注意：这是一个占位符实现，未来将集成pingcap/parser库
type MySQLParser struct {
	config         *SQLParserConfig
	fallbackParser *FallbackParser
}

// NewMySQLParser 创建MySQL解析器
func NewMySQLParser() *MySQLParser {
	return &MySQLParser{
		config: &SQLParserConfig{
			DatabaseType:   "mysql",
			EnableAST:      true,
			EnablePatterns: true,
			EnableOptimize: true,
		},
		fallbackParser: NewFallbackParser(),
	}
}

// WithConfig 应用配置
func (p *MySQLParser) WithConfig(config *SQLParserConfig) SQLParser {
	newParser := &MySQLParser{
		config:         config,
		fallbackParser: NewFallbackParser().WithConfig(config).(*FallbackParser),
	}
	if newParser.config == nil {
		newParser.config = &SQLParserConfig{
			DatabaseType: "mysql",
		}
	}
	return newParser
}

// Parse 解析SQL
func (p *MySQLParser) Parse(sql string) (*SQLParseResult, error) {
	logger.Infof("MySQL parser: parsing SQL query")
	
	// TODO: 集成pingcap/parser库进行真正的AST解析
	// 目前使用fallback解析器
	result, err := p.fallbackParser.Parse(sql)
	if err != nil {
		return nil, err
	}
	
	// 添加MySQL特定的增强
	result = p.enhanceWithMySQLFeatures(result, sql)
	
	return result, nil
}

// Validate 验证SQL语法
func (p *MySQLParser) Validate(sql string) error {
	// TODO: 使用pingcap/parser进行真正的语法验证
	// 目前使用fallback验证
	return p.fallbackParser.Validate(sql)
}

// ExtractTables 提取表信息
func (p *MySQLParser) ExtractTables(sql string) ([]TableInfo, error) {
	// TODO: 基于AST的精确表提取
	return p.fallbackParser.ExtractTables(sql)
}

// ExtractColumns 提取列信息
func (p *MySQLParser) ExtractColumns(sql string) ([]ColumnInfo, error) {
	// TODO: 基于AST的精确列提取
	return p.fallbackParser.ExtractColumns(sql)
}

// ExtractConditions 提取条件信息
func (p *MySQLParser) ExtractConditions(sql string) ([]Condition, error) {
	// TODO: 基于AST的精确条件提取
	return p.fallbackParser.ExtractConditions(sql)
}

// CalculateComplexity 计算复杂度
func (p *MySQLParser) CalculateComplexity(sql string) (*ComplexityScore, error) {
	// TODO: 基于AST的精确复杂度计算
	complexity, err := p.fallbackParser.CalculateComplexity(sql)
	if err != nil {
		return nil, err
	}
	
	// 添加MySQL特定的复杂度因子
	complexity = p.enhanceComplexityWithMySQLFeatures(complexity, sql)
	
	return complexity, nil
}

// DetectPatterns 检测查询模式
func (p *MySQLParser) DetectPatterns(sql string) ([]QueryPattern, error) {
	patterns, err := p.fallbackParser.DetectPatterns(sql)
	if err != nil {
		return nil, err
	}
	
	// 添加MySQL特定的模式检测
	mysqlPatterns := p.detectMySQLPatterns(sql)
	patterns = append(patterns, mysqlPatterns...)
	
	return patterns, nil
}

// SuggestOptimizations 生成优化建议
func (p *MySQLParser) SuggestOptimizations(sql string) ([]Optimization, error) {
	optimizations, err := p.fallbackParser.SuggestOptimizations(sql)
	if err != nil {
		return nil, err
	}
	
	// 添加MySQL特定的优化建议
	mysqlOptimizations := p.generateMySQLOptimizations(sql)
	optimizations = append(optimizations, mysqlOptimizations...)
	
	return optimizations, nil
}

// FormatSQL 格式化SQL
func (p *MySQLParser) FormatSQL(sql string) (string, error) {
	// TODO: 使用pingcap/parser进行格式化
	return p.fallbackParser.FormatSQL(sql)
}

// GetParserType 获取解析器类型
func (p *MySQLParser) GetParserType() string {
	return "mysql_ast" // 未来版本
	// return "mysql_fallback" // 当前版本
}

// GetSupportedFeatures 获取支持的特性
func (p *MySQLParser) GetSupportedFeatures() []string {
	features := p.fallbackParser.GetSupportedFeatures()
	
	// 添加MySQL特定特性
	mysqlFeatures := []string{
		"mysql_specific_functions",
		"storage_engines",
		"partitioning",
		"fulltext_search",
		"spatial_data",
		"mysql_json_functions",
	}
	
	return append(features, mysqlFeatures...)
}

// MySQL特定的增强方法

// enhanceWithMySQLFeatures 使用MySQL特性增强解析结果
func (p *MySQLParser) enhanceWithMySQLFeatures(result *SQLParseResult, sql string) *SQLParseResult {
	// 检测MySQL特定特性
	if p.hasFullTextSearch(sql) {
		result.Patterns = append(result.Patterns, QueryPattern{
			Type:        PatternBestPractice,
			Name:        "fulltext_search_detected",
			Description: "Query uses MySQL FULLTEXT search",
			Severity:    "INFO",
			Suggestion:  "FULLTEXT indexes are efficient for text search",
		})
	}
	
	return result
}

// enhanceComplexityWithMySQLFeatures 使用MySQL特性增强复杂度计算
func (p *MySQLParser) enhanceComplexityWithMySQLFeatures(complexity *ComplexityScore, sql string) *ComplexityScore {
	// FULLTEXT搜索复杂度
	if p.hasFullTextSearch(sql) {
		points := 2
		complexity.Breakdown["fulltext_search"] = points
		complexity.Total += points
		complexity.Factors = append(complexity.Factors, ComplexityFactor{
			Type:        "FULLTEXT_SEARCH",
			Count:       1,
			Points:      points,
			Description: "MySQL FULLTEXT search",
			Impact:      "MEDIUM",
		})
	}
	
	return complexity
}

// detectMySQLPatterns 检测MySQL特定模式
func (p *MySQLParser) detectMySQLPatterns(sql string) []QueryPattern {
	var patterns []QueryPattern
	
	// 检测LIMIT without ORDER BY
	if p.hasLimitWithoutOrderBy(sql) {
		patterns = append(patterns, QueryPattern{
			Type:        PatternAntiPattern,
			Name:        "limit_without_order_by",
			Description: "LIMIT without ORDER BY may return inconsistent results",
			Severity:    "WARNING",
			Suggestion:  "Add ORDER BY clause to ensure consistent results",
		})
	}
	
	return patterns
}

// generateMySQLOptimizations 生成MySQL特定优化建议
func (p *MySQLParser) generateMySQLOptimizations(sql string) []Optimization {
	var optimizations []Optimization
	
	// 索引建议
	if p.needsIndex(sql) {
		optimizations = append(optimizations, Optimization{
			Type:         OptimizationIndex,
			Priority:     "HIGH",
			Title:        "MySQL Index Recommendation",
			Description:  "Consider creating indexes for WHERE clause columns",
			ExpectedGain: 40.0,
			Effort:       "LOW",
			Category:     "PERFORMANCE",
		})
	}
	
	return optimizations
}

// MySQL特性检测辅助方法

func (p *MySQLParser) hasFullTextSearch(sql string) bool {
	return strings.Contains(strings.ToUpper(sql), "MATCH(") && 
		   strings.Contains(strings.ToUpper(sql), "AGAINST(")
}

func (p *MySQLParser) hasLimitWithoutOrderBy(sql string) bool {
	sqlUpper := strings.ToUpper(sql)
	return strings.Contains(sqlUpper, "LIMIT ") && 
		   !strings.Contains(sqlUpper, "ORDER BY")
}

func (p *MySQLParser) needsIndex(sql string) bool {
	sqlUpper := strings.ToUpper(sql)
	return strings.Contains(sqlUpper, "WHERE ") && 
		   (strings.Contains(sqlUpper, "= ") || strings.Contains(sqlUpper, "IN ("))
}
