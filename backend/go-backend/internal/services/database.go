package services

import (
	"database/sql"
	"errors"
	"fmt"
	"net"
	"time"

	"db-monitor-platform/internal/models"
	"db-monitor-platform/internal/monitor"
	"db-monitor-platform/internal/repository"
	"db-monitor-platform/internal/utils"
	"db-monitor-platform/pkg/logger"

	"gorm.io/gorm"
	_ "github.com/go-sql-driver/mysql"
	_ "github.com/lib/pq"
)

// DatabaseService 数据库实例服务
type DatabaseService struct {
	databaseRepo repository.DatabaseRepository
	userRepo     repository.UserRepository
}

// NewDatabaseService 创建数据库实例服务
func NewDatabaseService(db *gorm.DB) *DatabaseService {
	return &DatabaseService{
		databaseRepo: repository.NewDatabaseRepository(db),
		userRepo:     repository.NewUserRepository(db),
	}
}

// CreateDatabase 创建数据库实例
func (s *DatabaseService) CreateDatabase(userID uint, req *models.DatabaseCreateRequest) (*models.DatabaseResponse, error) {
	// 验证输入
	if err := utils.ValidateAndSanitize(req); err != nil {
		logger.Errorf("Validation failed: %v", err)
		return nil, err
	}

	// 检查用户权限
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		logger.Errorf("Failed to get user: %v", err)
		return nil, errors.New("user not found")
	}

	if !user.CanManageDatabase() {
		return nil, errors.New("insufficient permissions to create database")
	}

	// 检查连接是否已存在
	existing, err := s.databaseRepo.CheckConnectionExists(req.Host, req.Port, req.DatabaseName)
	if err == nil && existing != nil {
		return nil, errors.New("database connection already exists")
	}

	// 创建数据库实例
	database := &models.DatabaseInstance{
		Name:         req.Name,
		Type:         req.Type,
		Host:         req.Host,
		Port:         req.Port,
		DatabaseName: req.DatabaseName,
		Username:     req.Username,
		Password:     req.Password, // 在实际项目中应该加密存储
		Status:       "active",
		Description:  req.Description,
		Tags:         req.Tags,
		IsMonitored:  true,
		CreatedBy:    userID,
	}

	if req.IsMonitored != nil {
		database.IsMonitored = *req.IsMonitored
	}

	// 保存到数据库
	if err := s.databaseRepo.Create(database); err != nil {
		logger.Errorf("Failed to create database: %v", err)
		return nil, errors.New("failed to create database instance")
	}

	logger.Infof("Database instance created: %s by user %d", database.Name, userID)
	return database.ToResponse(), nil
}

// GetDatabase 获取数据库实例详情
func (s *DatabaseService) GetDatabase(userID uint, databaseID uint, userRole string) (*models.DatabaseResponse, error) {
	database, err := s.databaseRepo.GetByID(databaseID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("database not found")
		}
		logger.Errorf("Failed to get database: %v", err)
		return nil, errors.New("failed to get database")
	}

	// 检查权限：管理员可以查看所有，其他用户只能查看自己的
	if userRole != "admin" && database.CreatedBy != userID {
		return nil, errors.New("access denied")
	}

	response := database.ToResponse()
	if database.User.ID > 0 {
		response.User = &database.User
	}
	return response, nil
}

// GetDatabases 获取数据库实例列表
func (s *DatabaseService) GetDatabases(userID uint, userRole string, pagination *models.PaginationRequest) (*models.PaginationResponse, error) {
	var databases []models.DatabaseInstance
	var total int64
	var err error

	// 根据用户角色获取数据
	if userRole == "admin" {
		databases, total, err = s.databaseRepo.GetAll(pagination)
	} else {
		databases, total, err = s.databaseRepo.GetByUserID(userID, pagination)
	}

	if err != nil {
		logger.Errorf("Failed to get databases: %v", err)
		return nil, errors.New("failed to get databases")
	}

	// 转换为响应格式
	var responses []models.DatabaseResponse
	for _, db := range databases {
		response := db.ToResponse()
		if db.User.ID > 0 {
			response.User = &db.User
		}
		responses = append(responses, *response)
	}

	return models.NewPaginationResponse(pagination.Page, pagination.PageSize, total, responses), nil
}

// UpdateDatabase 更新数据库实例
func (s *DatabaseService) UpdateDatabase(userID uint, databaseID uint, userRole string, req *models.DatabaseUpdateRequest) (*models.DatabaseResponse, error) {
	// 验证输入
	if err := utils.ValidateAndSanitize(req); err != nil {
		logger.Errorf("Validation failed: %v", err)
		return nil, err
	}

	// 获取数据库实例
	database, err := s.databaseRepo.GetByID(databaseID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("database not found")
		}
		logger.Errorf("Failed to get database: %v", err)
		return nil, errors.New("failed to get database")
	}

	// 检查权限
	if userRole != "admin" && database.CreatedBy != userID {
		return nil, errors.New("access denied")
	}

	// 更新字段
	if req.Name != "" {
		database.Name = req.Name
	}
	if req.Host != "" {
		database.Host = req.Host
	}
	if req.Port != nil {
		database.Port = *req.Port
	}
	if req.DatabaseName != "" {
		database.DatabaseName = req.DatabaseName
	}
	if req.Username != "" {
		database.Username = req.Username
	}
	if req.Password != "" {
		database.Password = req.Password // 在实际项目中应该加密
	}
	if req.Description != "" {
		database.Description = req.Description
	}
	if req.Tags != "" {
		database.Tags = req.Tags
	}
	if req.IsMonitored != nil {
		database.IsMonitored = *req.IsMonitored
	}

	// 保存更新
	if err := s.databaseRepo.Update(database); err != nil {
		logger.Errorf("Failed to update database: %v", err)
		return nil, errors.New("failed to update database")
	}

	logger.Infof("Database instance updated: %s by user %d", database.Name, userID)
	return database.ToResponse(), nil
}

// DeleteDatabase 删除数据库实例
func (s *DatabaseService) DeleteDatabase(userID uint, databaseID uint, userRole string) error {
	// 获取数据库实例
	database, err := s.databaseRepo.GetByID(databaseID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("database not found")
		}
		logger.Errorf("Failed to get database: %v", err)
		return errors.New("failed to get database")
	}

	// 检查权限
	if userRole != "admin" && database.CreatedBy != userID {
		return errors.New("access denied")
	}

	// 删除数据库实例
	if err := s.databaseRepo.Delete(databaseID); err != nil {
		logger.Errorf("Failed to delete database: %v", err)
		return errors.New("failed to delete database")
	}

	logger.Infof("Database instance deleted: %s by user %d", database.Name, userID)
	return nil
}

// TestConnection 测试数据库连接
func (s *DatabaseService) TestConnection(userID uint, databaseID uint, userRole string) (*ConnectionTestResult, error) {
	// 获取数据库实例
	database, err := s.databaseRepo.GetByID(databaseID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("database not found")
		}
		logger.Errorf("Failed to get database: %v", err)
		return nil, errors.New("failed to get database")
	}

	// 检查权限
	if userRole != "admin" && database.CreatedBy != userID {
		return nil, errors.New("access denied")
	}

	// 测试连接
	result := s.testDatabaseConnection(database)
	
	// 更新数据库状态
	if result.Success {
		database.Status = "active"
	} else {
		database.Status = "error"
	}
	
	if err := s.databaseRepo.Update(database); err != nil {
		logger.Warnf("Failed to update database status: %v", err)
	}

	return result, nil
}

// SearchDatabases 搜索数据库实例
func (s *DatabaseService) SearchDatabases(userID uint, userRole string, keyword string, pagination *models.PaginationRequest) (*models.PaginationResponse, error) {
	databases, total, err := s.databaseRepo.SearchDatabases(keyword, pagination)
	if err != nil {
		logger.Errorf("Failed to search databases: %v", err)
		return nil, errors.New("failed to search databases")
	}

	// 过滤权限：非管理员只能看到自己的
	if userRole != "admin" {
		var filteredDatabases []models.DatabaseInstance
		for _, db := range databases {
			if db.CreatedBy == userID {
				filteredDatabases = append(filteredDatabases, db)
			}
		}
		databases = filteredDatabases
		total = int64(len(databases))
	}

	// 转换为响应格式
	var responses []models.DatabaseResponse
	for _, db := range databases {
		response := db.ToResponse()
		if db.User.ID > 0 {
			response.User = &db.User
		}
		responses = append(responses, *response)
	}

	return models.NewPaginationResponse(pagination.Page, pagination.PageSize, total, responses), nil
}

// GetDatabaseStats 获取数据库统计信息
func (s *DatabaseService) GetDatabaseStats(userID uint, userRole string) (*repository.DatabaseStats, error) {
	var statsUserID uint
	if userRole != "admin" {
		statsUserID = userID
	}

	stats, err := s.databaseRepo.GetDatabaseStats(statsUserID)
	if err != nil {
		logger.Errorf("Failed to get database stats: %v", err)
		return nil, errors.New("failed to get database statistics")
	}

	return stats, nil
}

// testDatabaseConnection 测试数据库连接
func (s *DatabaseService) testDatabaseConnection(database *models.DatabaseInstance) *ConnectionTestResult {
	result := &ConnectionTestResult{
		DatabaseID: database.ID,
		Success:    false,
		StartTime:  time.Now(),
	}

	// 首先测试网络连接
	address := fmt.Sprintf("%s:%d", database.Host, database.Port)
	conn, err := net.DialTimeout("tcp", address, 5*time.Second)
	if err != nil {
		result.Error = fmt.Sprintf("Network connection failed: %v", err)
		result.EndTime = time.Now()
		result.Duration = result.EndTime.Sub(result.StartTime).String()
		return result
	}
	conn.Close()

	// 根据数据库类型测试具体连接
	switch database.Type {
	case "postgresql":
		err = s.testPostgreSQLConnection(database)
	case "mysql":
		err = s.testMySQLConnection(database)
	case "redis":
		err = s.testRedisConnection(database)
	case "mongodb":
		err = s.testMongoDBConnection(database)
	default:
		err = errors.New("unsupported database type")
	}

	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime).String()

	if err != nil {
		result.Error = err.Error()
	} else {
		result.Success = true
		result.Message = "Connection successful"
	}

	return result
}

// testPostgreSQLConnection 测试PostgreSQL连接
func (s *DatabaseService) testPostgreSQLConnection(database *models.DatabaseInstance) error {
	// 实现真实的PostgreSQL连接测试
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable connect_timeout=5",
		database.Host, database.Port, database.Username, database.Password, database.DatabaseName)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		return fmt.Errorf("failed to open PostgreSQL connection: %w", err)
	}
	defer db.Close()

	// 设置连接超时
	db.SetConnMaxLifetime(5 * time.Second)

	// 测试连接
	if err := db.Ping(); err != nil {
		return fmt.Errorf("PostgreSQL ping failed: %w", err)
	}

	// 测试简单查询
	var version string
	if err := db.QueryRow("SELECT version()").Scan(&version); err != nil {
		return fmt.Errorf("PostgreSQL query test failed: %w", err)
	}

	logger.Infof("Successfully connected to PostgreSQL %s:%d, version: %s", database.Host, database.Port, version)
	return nil
}

// testMySQLConnection 测试MySQL连接
func (s *DatabaseService) testMySQLConnection(database *models.DatabaseInstance) error {
	// 实现真实的MySQL连接测试
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?timeout=5s",
		database.Username, database.Password, database.Host, database.Port, database.DatabaseName)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("failed to open MySQL connection: %w", err)
	}
	defer db.Close()

	// 设置连接超时
	db.SetConnMaxLifetime(5 * time.Second)

	// 测试连接
	if err := db.Ping(); err != nil {
		return fmt.Errorf("MySQL ping failed: %w", err)
	}

	// 测试简单查询
	var version string
	if err := db.QueryRow("SELECT VERSION()").Scan(&version); err != nil {
		return fmt.Errorf("MySQL query test failed: %w", err)
	}

	logger.Infof("Successfully connected to MySQL %s:%d, version: %s", database.Host, database.Port, version)
	return nil
}

// testRedisConnection 测试Redis连接
func (s *DatabaseService) testRedisConnection(database *models.DatabaseInstance) error {
	// 这里应该实现实际的Redis连接测试
	logger.Infof("Testing Redis connection to %s:%d", database.Host, database.Port)
	return nil
}

// testMongoDBConnection 测试MongoDB连接
func (s *DatabaseService) testMongoDBConnection(database *models.DatabaseInstance) error {
	// 这里应该实现实际的MongoDB连接测试
	logger.Infof("Testing MongoDB connection to %s:%d", database.Host, database.Port)
	return nil
}

// ConnectionTestResult 连接测试结果
type ConnectionTestResult struct {
	DatabaseID uint      `json:"database_id"`
	Success    bool      `json:"success"`
	Message    string    `json:"message,omitempty"`
	Error      string    `json:"error,omitempty"`
	StartTime  time.Time `json:"start_time"`
	EndTime    time.Time `json:"end_time"`
	Duration   string    `json:"duration"` // 改为字符串格式，如 "150ms"
}

// GetRealDatabaseMetrics 获取真实的数据库监控指标
func (s *DatabaseService) GetRealDatabaseMetrics(userID uint, databaseID uint, userRole string) (*RealDatabaseMetrics, error) {
	// 检查权限
	database, err := s.databaseRepo.GetByID(databaseID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("database not found")
		}
		logger.Errorf("Failed to get database: %v", err)
		return nil, errors.New("failed to get database")
	}

	// 检查权限：管理员可以查看所有，其他用户只能查看自己的
	if userRole != "admin" && database.CreatedBy != userID {
		return nil, errors.New("access denied")
	}

	// 创建监控数据收集器
	collector := monitor.NewRealMetricsCollector(
		s.databaseRepo.GetDB(), // 假设repository有GetDB方法
		database.Type,
		database.Host,
		database.Port,
		database.DatabaseName,
	)

	// 收集各种指标
	connectionMetrics, err := collector.CollectConnectionMetrics()
	if err != nil {
		logger.Errorf("Failed to collect connection metrics: %v", err)
		connectionMetrics = &monitor.ConnectionMetrics{} // 使用空值
	}

	databaseInfo, err := collector.CollectDatabaseInfo()
	if err != nil {
		logger.Errorf("Failed to collect database info: %v", err)
		databaseInfo = &monitor.DatabaseInfo{} // 使用空值
	}

	systemMetrics, err := collector.CollectSystemMetrics()
	if err != nil {
		logger.Errorf("Failed to collect system metrics: %v", err)
		systemMetrics = &monitor.SystemMetrics{} // 使用空值
	}

	// 测试连接状态
	connectionStatus := "connected"
	if err := collector.TestConnection(); err != nil {
		connectionStatus = "disconnected"
		logger.Errorf("Database connection test failed: %v", err)
	}

	return &RealDatabaseMetrics{
		DatabaseID:        databaseID,
		DatabaseName:      database.Name,
		DatabaseType:      database.Type,
		Host:              database.Host,
		Port:              database.Port,
		Status:            connectionStatus,
		ConnectionMetrics: connectionMetrics,
		DatabaseInfo:      databaseInfo,
		SystemMetrics:     systemMetrics,
		Timestamp:         time.Now(),
	}, nil
}

// RealDatabaseMetrics 真实数据库监控指标
type RealDatabaseMetrics struct {
	DatabaseID        uint                        `json:"database_id"`
	DatabaseName      string                      `json:"database_name"`
	DatabaseType      string                      `json:"database_type"`
	Host              string                      `json:"host"`
	Port              int                         `json:"port"`
	Status            string                      `json:"status"` // connected, disconnected
	ConnectionMetrics *monitor.ConnectionMetrics  `json:"connection_metrics"`
	DatabaseInfo      *monitor.DatabaseInfo       `json:"database_info"`
	SystemMetrics     *monitor.SystemMetrics      `json:"system_metrics"`
	Timestamp         time.Time                   `json:"timestamp"`
}
