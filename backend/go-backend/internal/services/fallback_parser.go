package services

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"db-monitor-platform/pkg/logger"
)

// FallbackParser 基于正则表达式的fallback解析器
type FallbackParser struct {
	config *SQLParserConfig
}

// NewFallbackParser 创建fallback解析器
func NewFallbackParser() *FallbackParser {
	return &FallbackParser{
		config: &SQLParserConfig{
			DatabaseType: "fallback",
			EnableAST:    false,
			EnablePatterns: true,
			EnableOptimize: true,
		},
	}
}

// WithConfig 应用配置
func (p *FallbackParser) WithConfig(config *SQLParserConfig) SQLParser {
	newParser := &FallbackParser{
		config: config,
	}
	if newParser.config == nil {
		newParser.config = &SQLParserConfig{
			DatabaseType: "fallback",
		}
	}
	return newParser
}

// Parse 解析SQL
func (p *FallbackParser) Parse(sql string) (*SQLParseResult, error) {
	result := &SQLParseResult{
		ParsedAt: time.Now(),
	}
	
	// 清理SQL
	cleanSQL := p.cleanSQL(sql)
	result.FormattedQuery = cleanSQL
	
	// 识别查询类型
	result.QueryType = p.identifyQueryType(cleanSQL)
	
	// 提取表信息
	tables, err := p.ExtractTables(cleanSQL)
	if err != nil {
		logger.Warnf("Failed to extract tables: %v", err)
	}
	result.Tables = tables
	
	// 提取列信息
	columns, err := p.ExtractColumns(cleanSQL)
	if err != nil {
		logger.Warnf("Failed to extract columns: %v", err)
	}
	result.Columns = columns
	
	// 提取条件信息
	conditions, err := p.ExtractConditions(cleanSQL)
	if err != nil {
		logger.Warnf("Failed to extract conditions: %v", err)
	}
	result.Conditions = conditions
	
	// 计算复杂度
	complexity, err := p.CalculateComplexity(cleanSQL)
	if err != nil {
		logger.Warnf("Failed to calculate complexity: %v", err)
	}
	result.ComplexityScore = complexity
	
	// 检测模式
	if p.config.EnablePatterns {
		patterns, err := p.DetectPatterns(cleanSQL)
		if err != nil {
			logger.Warnf("Failed to detect patterns: %v", err)
		}
		result.Patterns = patterns
	}
	
	// 生成优化建议
	if p.config.EnableOptimize {
		optimizations, err := p.SuggestOptimizations(cleanSQL)
		if err != nil {
			logger.Warnf("Failed to suggest optimizations: %v", err)
		}
		result.Optimizations = optimizations
	}

	// 添加解析器状态信息
	result.ParserInfo = &ParserInfo{
		ParserType:    "fallback_regex",
		ParserVersion: "1.0.0",
		FallbackUsed:  true,
		FallbackReason: "Using fallback regex parser",
		Capabilities:  p.GetSupportedFeatures(),
		Warnings:      []string{"Using basic regex parser", "Advanced SQL features may not be fully supported"},
	}

	return result, nil
}

// Validate 验证SQL语法
func (p *FallbackParser) Validate(sql string) error {
	// 简单的语法验证
	cleanSQL := strings.TrimSpace(sql)
	if cleanSQL == "" {
		return &ParseError{
			Type:    "SYNTAX_ERROR",
			Message: "Empty SQL statement",
		}
	}
	
	// 检查基本的SQL关键字
	sqlUpper := strings.ToUpper(cleanSQL)
	validStarts := []string{"SELECT", "INSERT", "UPDATE", "DELETE", "CREATE", "ALTER", "DROP", "WITH"}
	
	hasValidStart := false
	for _, start := range validStarts {
		if strings.HasPrefix(sqlUpper, start) {
			hasValidStart = true
			break
		}
	}
	
	if !hasValidStart {
		return &ParseError{
			Type:    "SYNTAX_ERROR",
			Message: "SQL statement must start with a valid keyword",
			Suggestion: "SQL should start with SELECT, INSERT, UPDATE, DELETE, CREATE, ALTER, DROP, or WITH",
		}
	}
	
	// 检查括号匹配
	if err := p.validateParentheses(cleanSQL); err != nil {
		return err
	}
	
	return nil
}

// ExtractTables 提取表名
func (p *FallbackParser) ExtractTables(sql string) ([]TableInfo, error) {
	var tables []TableInfo
	
	// FROM子句中的表
	fromRegex := regexp.MustCompile(`(?i)FROM\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)?)\s*(?:AS\s+([a-zA-Z_][a-zA-Z0-9_]*))?`)
	fromMatches := fromRegex.FindAllStringSubmatch(sql, -1)
	
	for _, match := range fromMatches {
		table := TableInfo{
			Name: match[1],
		}
		if len(match) > 2 && match[2] != "" {
			table.Alias = match[2]
		}
		tables = append(tables, table)
	}
	
	// JOIN子句中的表
	joinRegex := regexp.MustCompile(`(?i)(INNER\s+JOIN|LEFT\s+JOIN|RIGHT\s+JOIN|FULL\s+JOIN|JOIN)\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)?)\s*(?:AS\s+([a-zA-Z_][a-zA-Z0-9_]*))?`)
	joinMatches := joinRegex.FindAllStringSubmatch(sql, -1)
	
	for _, match := range joinMatches {
		table := TableInfo{
			Name:     match[2],
			JoinType: strings.ToUpper(strings.TrimSpace(match[1])),
		}
		if len(match) > 3 && match[3] != "" {
			table.Alias = match[3]
		}
		tables = append(tables, table)
	}
	
	return tables, nil
}

// ExtractColumns 提取列名
func (p *FallbackParser) ExtractColumns(sql string) ([]ColumnInfo, error) {
	var columns []ColumnInfo
	
	// 简单的SELECT列提取
	selectRegex := regexp.MustCompile(`(?i)SELECT\s+(.*?)\s+FROM`)
	selectMatch := selectRegex.FindStringSubmatch(sql)
	
	if len(selectMatch) > 1 {
		columnsPart := selectMatch[1]
		
		// 处理 SELECT *
		if strings.TrimSpace(columnsPart) == "*" {
			columns = append(columns, ColumnInfo{
				Name: "*",
			})
			return columns, nil
		}
		
		// 分割列名（简单实现）
		columnList := strings.Split(columnsPart, ",")
		for _, col := range columnList {
			col = strings.TrimSpace(col)
			if col != "" {
				column := ColumnInfo{
					Name: col,
				}
				
				// 检查是否是聚合函数
				if p.isAggregateFunction(col) {
					column.IsAggregate = true
					column.Function = p.extractFunction(col)
				}
				
				columns = append(columns, column)
			}
		}
	}
	
	return columns, nil
}

// ExtractConditions 提取条件
func (p *FallbackParser) ExtractConditions(sql string) ([]Condition, error) {
	var conditions []Condition
	
	// WHERE条件
	whereRegex := regexp.MustCompile(`(?i)WHERE\s+(.*?)(?:\s+GROUP\s+BY|\s+ORDER\s+BY|\s+LIMIT|\s+HAVING|$)`)
	whereMatch := whereRegex.FindStringSubmatch(sql)
	
	if len(whereMatch) > 1 {
		whereClause := whereMatch[1]
		// 简单的条件解析
		conditionRegex := regexp.MustCompile(`([a-zA-Z_][a-zA-Z0-9_.]*)\s*(=|>|<|>=|<=|!=|<>|LIKE|IN)\s*([^AND|OR]+)`)
		condMatches := conditionRegex.FindAllStringSubmatch(whereClause, -1)
		
		for _, match := range condMatches {
			condition := Condition{
				Type:     "WHERE",
				Column:   strings.TrimSpace(match[1]),
				Operator: strings.TrimSpace(match[2]),
				Value:    strings.TrimSpace(match[3]),
			}
			conditions = append(conditions, condition)
		}
	}
	
	return conditions, nil
}

// CalculateComplexity 计算复杂度
func (p *FallbackParser) CalculateComplexity(sql string) (*ComplexityScore, error) {
	sqlUpper := strings.ToUpper(sql)
	
	score := &ComplexityScore{
		Breakdown: make(map[string]int),
		Factors:   []ComplexityFactor{},
	}
	
	// 基础分数
	score.Breakdown["base"] = 1
	score.Total = 1
	
	// JOIN计数
	joinCount := strings.Count(sqlUpper, "JOIN")
	if joinCount > 0 {
		points := joinCount * 2
		score.Breakdown["joins"] = points
		score.Total += points
		score.Factors = append(score.Factors, ComplexityFactor{
			Type:        "JOIN",
			Count:       joinCount,
			Points:      points,
			Description: fmt.Sprintf("%d JOIN operations", joinCount),
			Impact:      p.getImpactLevel(points),
		})
	}
	
	// 子查询计数
	subqueryCount := strings.Count(sqlUpper, "SELECT") - 1
	if subqueryCount > 0 {
		points := subqueryCount * 3
		score.Breakdown["subqueries"] = points
		score.Total += points
		score.Factors = append(score.Factors, ComplexityFactor{
			Type:        "SUBQUERY",
			Count:       subqueryCount,
			Points:      points,
			Description: fmt.Sprintf("%d subqueries", subqueryCount),
			Impact:      p.getImpactLevel(points),
		})
	}
	
	// 聚合函数计数
	aggregateFunctions := []string{"COUNT", "SUM", "AVG", "MAX", "MIN", "GROUP_CONCAT"}
	aggregateCount := 0
	for _, fn := range aggregateFunctions {
		aggregateCount += strings.Count(sqlUpper, fn+"(")
	}
	if aggregateCount > 0 {
		points := aggregateCount * 1
		score.Breakdown["aggregates"] = points
		score.Total += points
		score.Factors = append(score.Factors, ComplexityFactor{
			Type:        "AGGREGATE",
			Count:       aggregateCount,
			Points:      points,
			Description: fmt.Sprintf("%d aggregate functions", aggregateCount),
			Impact:      p.getImpactLevel(points),
		})
	}
	
	// UNION计数
	unionCount := strings.Count(sqlUpper, "UNION")
	if unionCount > 0 {
		points := unionCount * 2
		score.Breakdown["unions"] = points
		score.Total += points
		score.Factors = append(score.Factors, ComplexityFactor{
			Type:        "UNION",
			Count:       unionCount,
			Points:      points,
			Description: fmt.Sprintf("%d UNION operations", unionCount),
			Impact:      p.getImpactLevel(points),
		})
	}
	
	// 设置复杂度级别
	score.Level = p.getComplexityLevel(score.Total)
	score.Recommendation = p.getComplexityRecommendation(score.Total)
	
	return score, nil
}

// DetectPatterns 检测查询模式
func (p *FallbackParser) DetectPatterns(sql string) ([]QueryPattern, error) {
	var patterns []QueryPattern
	
	sqlUpper := strings.ToUpper(sql)
	
	// 检测SELECT *
	if strings.Contains(sqlUpper, "SELECT *") {
		patterns = append(patterns, QueryPattern{
			Type:        PatternAntiPattern,
			Name:        "select_star",
			Description: "Using SELECT * can impact performance",
			Severity:    "WARNING",
			Suggestion:  "Specify only the columns you need",
		})
	}
	
	// 检测没有WHERE子句的UPDATE/DELETE
	if (strings.Contains(sqlUpper, "UPDATE ") || strings.Contains(sqlUpper, "DELETE ")) && !strings.Contains(sqlUpper, "WHERE") {
		patterns = append(patterns, QueryPattern{
			Type:        PatternSecurityIssue,
			Name:        "missing_where_clause",
			Description: "UPDATE/DELETE without WHERE clause affects all rows",
			Severity:    "ERROR",
			Suggestion:  "Add WHERE clause to limit affected rows",
		})
	}
	
	return patterns, nil
}

// SuggestOptimizations 生成优化建议
func (p *FallbackParser) SuggestOptimizations(sql string) ([]Optimization, error) {
	var optimizations []Optimization
	
	// 基于检测到的模式生成建议
	patterns, _ := p.DetectPatterns(sql)
	
	for _, pattern := range patterns {
		if pattern.Type == PatternAntiPattern {
			optimization := Optimization{
				Type:        OptimizationQueryRewrite,
				Priority:    "MEDIUM",
				Title:       "Query Rewrite Suggestion",
				Description: pattern.Suggestion,
				Category:    "PERFORMANCE",
				Effort:      "LOW",
			}
			optimizations = append(optimizations, optimization)
		}
	}
	
	return optimizations, nil
}

// FormatSQL 格式化SQL
func (p *FallbackParser) FormatSQL(sql string) (string, error) {
	// 简单的格式化
	return p.cleanSQL(sql), nil
}

// GetParserType 获取解析器类型
func (p *FallbackParser) GetParserType() string {
	return "fallback_regex"
}

// GetSupportedFeatures 获取支持的特性
func (p *FallbackParser) GetSupportedFeatures() []string {
	return []string{
		"basic_parsing",
		"table_extraction",
		"column_extraction",
		"complexity_analysis",
		"pattern_detection",
		"simple_optimization",
	}
}

// 辅助方法
func (p *FallbackParser) cleanSQL(sql string) string {
	// 移除注释
	commentRegex := regexp.MustCompile(`--.*$|/\*.*?\*/`)
	cleaned := commentRegex.ReplaceAllString(sql, "")
	
	// 标准化空白字符
	spaceRegex := regexp.MustCompile(`\s+`)
	cleaned = spaceRegex.ReplaceAllString(cleaned, " ")
	
	return strings.TrimSpace(cleaned)
}

func (p *FallbackParser) identifyQueryType(sql string) string {
	sqlUpper := strings.ToUpper(strings.TrimSpace(sql))
	
	if strings.HasPrefix(sqlUpper, "SELECT") {
		return QueryTypeSelect
	} else if strings.HasPrefix(sqlUpper, "INSERT") {
		return QueryTypeInsert
	} else if strings.HasPrefix(sqlUpper, "UPDATE") {
		return QueryTypeUpdate
	} else if strings.HasPrefix(sqlUpper, "DELETE") {
		return QueryTypeDelete
	} else if strings.HasPrefix(sqlUpper, "CREATE") {
		return QueryTypeCreate
	} else if strings.HasPrefix(sqlUpper, "ALTER") {
		return QueryTypeAlter
	} else if strings.HasPrefix(sqlUpper, "DROP") {
		return QueryTypeDrop
	} else if strings.HasPrefix(sqlUpper, "WITH") {
		return QueryTypeWith
	}
	
	return QueryTypeOther
}

func (p *FallbackParser) validateParentheses(sql string) error {
	stack := 0
	for i, char := range sql {
		if char == '(' {
			stack++
		} else if char == ')' {
			stack--
			if stack < 0 {
				return &ParseError{
					Type:     "SYNTAX_ERROR",
					Message:  "Unmatched closing parenthesis",
					Position: i,
				}
			}
		}
	}
	
	if stack > 0 {
		return &ParseError{
			Type:    "SYNTAX_ERROR",
			Message: "Unmatched opening parenthesis",
		}
	}
	
	return nil
}

func (p *FallbackParser) isAggregateFunction(col string) bool {
	colUpper := strings.ToUpper(col)
	aggregateFunctions := []string{"COUNT(", "SUM(", "AVG(", "MAX(", "MIN(", "GROUP_CONCAT("}
	
	for _, fn := range aggregateFunctions {
		if strings.Contains(colUpper, fn) {
			return true
		}
	}
	
	return false
}

func (p *FallbackParser) extractFunction(col string) string {
	functionRegex := regexp.MustCompile(`([A-Z_]+)\(`)
	match := functionRegex.FindStringSubmatch(strings.ToUpper(col))
	
	if len(match) > 1 {
		return match[1]
	}
	
	return ""
}

func (p *FallbackParser) getImpactLevel(points int) string {
	if points <= 2 {
		return "LOW"
	} else if points <= 5 {
		return "MEDIUM"
	}
	return "HIGH"
}

func (p *FallbackParser) getComplexityLevel(total int) string {
	if total <= 5 {
		return ComplexityLow
	} else if total <= 10 {
		return ComplexityMedium
	} else if total <= 20 {
		return ComplexityHigh
	}
	return ComplexityVeryHigh
}

func (p *FallbackParser) getComplexityRecommendation(total int) string {
	if total <= 5 {
		return "Simple query, good performance expected"
	} else if total <= 10 {
		return "Moderate complexity, consider indexing"
	} else if total <= 20 {
		return "Complex query, review for optimization opportunities"
	}
	return "Very complex query, consider breaking into smaller parts"
}
