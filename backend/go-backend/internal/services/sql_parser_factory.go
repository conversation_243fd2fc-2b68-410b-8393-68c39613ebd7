package services

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"db-monitor-platform/pkg/logger"
)

// DefaultSQLParserFactory 默认的SQL解析器工厂实现
type DefaultSQLParserFactory struct {
	parsers      map[string]SQLParser
	capabilities map[string][]string
	healthStats  map[string]*ParserHealthStatus
	mutex        sync.RWMutex
}

// NewSQLParserFactory 创建SQL解析器工厂
func NewSQLParserFactory() SQLParserFactory {
	factory := &DefaultSQLParserFactory{
		parsers:      make(map[string]SQLParser),
		capabilities: make(map[string][]string),
		healthStats:  make(map[string]*ParserHealthStatus),
	}

	// 注册默认解析器
	factory.registerDefaultParsers()

	return factory
}

// registerDefaultParsers 注册默认解析器
func (f *DefaultSQLParserFactory) registerDefaultParsers() {
	// PostgreSQL解析器
	f.registerParser("postgresql", NewPostgreSQLParser(), []string{
		CapabilityASTParsing,
		CapabilitySyntaxValidation,
		CapabilitySemanticAnalysis,
		CapabilityQueryRewriting,
		CapabilityPatternDetection,
		CapabilityComplexityAnalysis,
		CapabilityOptimizationSuggestions,
		CapabilityMultiStatement,
		CapabilityProcedureSupport,
		CapabilityTriggerSupport,
	})
	
	// MySQL解析器 (未来实现)
	f.registerParser("mysql", NewMySQLParser(), []string{
		CapabilityASTParsing,
		CapabilitySyntaxValidation,
		CapabilityComplexityAnalysis,
		CapabilityPatternDetection,
		CapabilityMultiStatement,
	})
	
	// Fallback解析器 (基于正则表达式)
	f.registerParser("fallback", NewFallbackParser(), []string{
		CapabilityComplexityAnalysis,
		CapabilityPatternDetection,
	})
}

// registerParser 注册解析器 (内部方法)
func (f *DefaultSQLParserFactory) registerParser(dbType string, parser SQLParser, capabilities []string) {
	// 使用公共方法，忽略错误（内部调用保证参数正确）
	_ = f.RegisterParser(dbType, parser, capabilities)
}

// CreateParser 创建解析器
func (f *DefaultSQLParserFactory) CreateParser(dbType string, config *SQLParserConfig) (SQLParser, error) {
	f.mutex.RLock()
	defer f.mutex.RUnlock()
	
	dbType = strings.ToLower(dbType)
	
	if parser, exists := f.parsers[dbType]; exists {
		// 如果解析器支持配置，应用配置
		if configurableParser, ok := parser.(ConfigurableParser); ok {
			return configurableParser.WithConfig(config), nil
		}
		return parser, nil
	}
	
	// 如果找不到指定的解析器，返回fallback解析器
	if fallbackParser, exists := f.parsers["fallback"]; exists {
		logger.Warnf("Parser for %s not found, using fallback parser", dbType)
		return fallbackParser, nil
	}
	
	return nil, fmt.Errorf("no parser available for database type: %s", dbType)
}

// GetSupportedDatabases 获取支持的数据库类型
func (f *DefaultSQLParserFactory) GetSupportedDatabases() []string {
	f.mutex.RLock()
	defer f.mutex.RUnlock()
	
	var databases []string
	for dbType := range f.parsers {
		if dbType != "fallback" { // 排除fallback解析器
			databases = append(databases, dbType)
		}
	}
	
	return databases
}

// GetParserCapabilities 获取解析器能力
func (f *DefaultSQLParserFactory) GetParserCapabilities(dbType string) []string {
	f.mutex.RLock()
	defer f.mutex.RUnlock()
	
	dbType = strings.ToLower(dbType)
	
	if capabilities, exists := f.capabilities[dbType]; exists {
		return capabilities
	}
	
	return []string{}
}

// ConfigurableParser 可配置的解析器接口
type ConfigurableParser interface {
	WithConfig(config *SQLParserConfig) SQLParser
}

// ParseWithFallback 带容错的解析方法
func (f *DefaultSQLParserFactory) ParseWithFallback(dbType, sql string, config *SQLParserConfig) (*SQLParseResult, error) {
	startTime := time.Now()

	// 尝试使用指定的解析器
	parser, err := f.CreateParser(dbType, config)
	if err != nil {
		f.updateParserHealth(dbType, false, 0, err)
		return nil, err
	}

	result, err := parser.Parse(sql)
	latency := time.Since(startTime)

	if err != nil && dbType != "fallback" {
		// 更新主解析器健康状态
		f.updateParserHealth(dbType, false, latency, err)

		// 主解析器失败，尝试使用fallback
		logger.Warnf("Primary parser (%s) failed: %v, trying fallback parser", dbType, err)

		fallbackStartTime := time.Now()
		fallbackParser, fallbackErr := f.CreateParser("fallback", config)
		if fallbackErr != nil {
			f.updateParserHealth("fallback", false, 0, fallbackErr)
			return nil, fmt.Errorf("primary parser failed: %w, fallback parser unavailable: %v", err, fallbackErr)
		}

		fallbackResult, fallbackParseErr := fallbackParser.Parse(sql)
		fallbackLatency := time.Since(fallbackStartTime)

		if fallbackParseErr != nil {
			f.updateParserHealth("fallback", false, fallbackLatency, fallbackParseErr)
			return nil, fmt.Errorf("both primary and fallback parsers failed: %w, %v", err, fallbackParseErr)
		}

		// 更新fallback解析器健康状态
		f.updateParserHealth("fallback", true, fallbackLatency, nil)

		// 更新解析器状态信息
		if fallbackResult.ParserInfo != nil {
			fallbackResult.ParserInfo.FallbackReason = fmt.Sprintf("Primary parser (%s) failed: %s", dbType, err.Error())
			fallbackResult.ParserInfo.Warnings = append(fallbackResult.ParserInfo.Warnings,
				fmt.Sprintf("Primary %s parser failed", dbType))
		}

		// 标记结果来自fallback解析器
		fallbackResult.Patterns = append(fallbackResult.Patterns, QueryPattern{
			Type:        PatternAntiPattern,
			Name:        "primary_parser_failed",
			Description: fmt.Sprintf("Primary parser (%s) failed, using fallback parser", dbType),
			Severity:    "WARNING",
			Suggestion:  "Consider checking SQL syntax or updating parser support",
		})

		return fallbackResult, nil
	}

	// 更新解析器健康状态
	f.updateParserHealth(dbType, err == nil, latency, err)

	return result, err
}

// ValidateWithFallback 带容错的验证方法
func (f *DefaultSQLParserFactory) ValidateWithFallback(dbType, sql string, config *SQLParserConfig) error {
	parser, err := f.CreateParser(dbType, config)
	if err != nil {
		return err
	}
	
	err = parser.Validate(sql)
	if err != nil && dbType != "fallback" {
		// 主解析器验证失败，尝试fallback
		fallbackParser, fallbackErr := f.CreateParser("fallback", config)
		if fallbackErr != nil {
			return err // 返回原始错误
		}
		
		return fallbackParser.Validate(sql)
	}
	
	return err
}

// GetParserInfo 获取解析器信息
func (f *DefaultSQLParserFactory) GetParserInfo(dbType string) map[string]interface{} {
	f.mutex.RLock()
	defer f.mutex.RUnlock()
	
	dbType = strings.ToLower(dbType)
	
	info := map[string]interface{}{
		"database_type": dbType,
		"available":     false,
		"capabilities":  []string{},
		"parser_type":   "unknown",
	}
	
	if parser, exists := f.parsers[dbType]; exists {
		info["available"] = true
		info["parser_type"] = parser.GetParserType()
		info["capabilities"] = f.capabilities[dbType]
		info["supported_features"] = parser.GetSupportedFeatures()
	}
	
	return info
}

// GetAllParsersInfo 获取所有解析器信息
func (f *DefaultSQLParserFactory) GetAllParsersInfo() map[string]interface{} {
	f.mutex.RLock()
	defer f.mutex.RUnlock()
	
	allInfo := make(map[string]interface{})
	
	for dbType := range f.parsers {
		allInfo[dbType] = f.GetParserInfo(dbType)
	}
	
	return allInfo
}

// 全局解析器工厂实例
var (
	globalParserFactory SQLParserFactory
	factoryOnce         sync.Once
)

// GetGlobalParserFactory 获取全局解析器工厂实例
func GetGlobalParserFactory() SQLParserFactory {
	factoryOnce.Do(func() {
		globalParserFactory = NewSQLParserFactory()
	})
	return globalParserFactory
}

// RegisterParser 注册解析器
func (f *DefaultSQLParserFactory) RegisterParser(dbType string, parser SQLParser, capabilities []string) error {
	f.mutex.Lock()
	defer f.mutex.Unlock()

	dbType = strings.ToLower(dbType)

	if parser == nil {
		return fmt.Errorf("parser cannot be nil")
	}

	f.parsers[dbType] = parser
	f.capabilities[dbType] = capabilities

	// 初始化健康状态
	f.healthStats[dbType] = &ParserHealthStatus{
		DatabaseType: dbType,
		Available:    true,
		LastChecked:  time.Now(),
		Status:       "healthy",
	}

	logger.Infof("Registered SQL parser for %s with capabilities: %v", dbType, capabilities)
	return nil
}

// IsParserAvailable 检查解析器是否可用
func (f *DefaultSQLParserFactory) IsParserAvailable(dbType string) bool {
	f.mutex.RLock()
	defer f.mutex.RUnlock()

	dbType = strings.ToLower(dbType)
	_, exists := f.parsers[dbType]
	return exists
}

// GetParserHealth 获取解析器健康状态
func (f *DefaultSQLParserFactory) GetParserHealth(dbType string) ParserHealthStatus {
	f.mutex.RLock()
	defer f.mutex.RUnlock()

	dbType = strings.ToLower(dbType)

	if health, exists := f.healthStats[dbType]; exists {
		// 返回副本以避免并发修改
		return *health
	}

	// 返回默认状态
	return ParserHealthStatus{
		DatabaseType: dbType,
		Available:    false,
		LastChecked:  time.Now(),
		Status:       "unavailable",
	}
}

// updateParserHealth 更新解析器健康状态
func (f *DefaultSQLParserFactory) updateParserHealth(dbType string, success bool, latency time.Duration, err error) {
	f.mutex.Lock()
	defer f.mutex.Unlock()

	dbType = strings.ToLower(dbType)

	health, exists := f.healthStats[dbType]
	if !exists {
		health = &ParserHealthStatus{
			DatabaseType: dbType,
			Available:    true,
			LastChecked:  time.Now(),
		}
		f.healthStats[dbType] = health
	}

	health.LastChecked = time.Now()

	if success {
		health.SuccessCount++
	} else {
		health.ErrorCount++
		if err != nil {
			health.LastError = err.Error()
		}
	}

	// 计算成功率
	total := health.SuccessCount + health.ErrorCount
	if total > 0 {
		health.SuccessRate = float64(health.SuccessCount) / float64(total) * 100
	}

	// 更新平均延迟
	if success && latency > 0 {
		if health.AverageLatency == 0 {
			health.AverageLatency = float64(latency.Nanoseconds()) / 1e6 // 转换为毫秒
		} else {
			// 简单的移动平均
			health.AverageLatency = (health.AverageLatency + float64(latency.Nanoseconds())/1e6) / 2
		}
	}

	// 更新状态
	if health.SuccessRate >= 95 {
		health.Status = "healthy"
	} else if health.SuccessRate >= 80 {
		health.Status = "degraded"
	} else {
		health.Status = "failed"
	}
}
