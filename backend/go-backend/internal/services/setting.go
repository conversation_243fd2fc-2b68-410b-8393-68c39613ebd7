package services

import (
	"context"
	"fmt"
	"strconv"

	"db-monitor-platform/internal/models"
	"db-monitor-platform/internal/repository"

	"gorm.io/gorm"
)

// SettingService 设置服务接口
type SettingService interface {
	// 系统设置
	GetSystemSettings(ctx context.Context, req *models.GetSystemSettingsRequest, userID uint) (map[string]interface{}, error)
	UpdateSystemSettings(ctx context.Context, req *models.UpdateSystemSettingsRequest, userID uint) error
	GetSystemSettingsByCategory(ctx context.Context, category string, userID uint) ([]*models.SystemSetting, error)
	
	// 用户偏好
	GetUserPreferences(ctx context.Context, req *models.GetUserPreferencesRequest, userID uint) (map[string]interface{}, error)
	UpdateUserPreferences(ctx context.Context, req *models.UpdateUserPreferencesRequest, userID uint) error
	GetUserPreferencesByCategory(ctx context.Context, category string, userID uint) ([]*models.UserPreference, error)
	
	// 初始化
	InitializeUserDefaults(ctx context.Context, userID uint) error
}

type settingService struct {
	systemSettingRepo repository.SystemSettingRepository
	userPrefRepo      repository.UserPreferenceRepository
	userRepo          repository.UserRepository
}

// NewSettingService 创建设置服务实例
func NewSettingService(db *gorm.DB) SettingService {
	return &settingService{
		systemSettingRepo: repository.NewSystemSettingRepository(db),
		userPrefRepo:      repository.NewUserPreferenceRepository(db),
		userRepo:          repository.NewUserRepository(db),
	}
}

// GetSystemSettings 获取系统设置
func (s *settingService) GetSystemSettings(ctx context.Context, req *models.GetSystemSettingsRequest, userID uint) (map[string]interface{}, error) {
	// 检查用户权限
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// 非管理员只能查看公开设置
	if user.Role != "admin" && (req.Public == nil || !*req.Public) {
		isPublic := true
		req.Public = &isPublic
	}

	settings, err := s.systemSettingRepo.GetList(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get system settings: %w", err)
	}

	// 按分类组织设置
	result := make(map[string]interface{})
	categories := make(map[string]map[string]interface{})

	for _, setting := range settings {
		if categories[setting.Category] == nil {
			categories[setting.Category] = make(map[string]interface{})
		}

		// 根据值类型转换值
		value, err := s.convertSettingValue(setting.Value, setting.ValueType)
		if err != nil {
			return nil, fmt.Errorf("failed to convert setting value for %s.%s: %w", setting.Category, setting.Key, err)
		}

		categories[setting.Category][setting.Key] = map[string]interface{}{
			"value":       value,
			"type":        setting.ValueType,
			"description": setting.Description,
			"is_public":   setting.IsPublic,
		}
	}

	result["categories"] = categories
	return result, nil
}

// UpdateSystemSettings 更新系统设置
func (s *settingService) UpdateSystemSettings(ctx context.Context, req *models.UpdateSystemSettingsRequest, userID uint) error {
	// 检查管理员权限
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	if user.Role != "admin" {
		return fmt.Errorf("permission denied: only admin can update system settings")
	}

	// 验证和更新每个设置
	var settingsToUpdate []*models.SystemSetting

	for _, settingUpdate := range req.Settings {
		// 验证分类
		if !models.ValidateSettingCategory(settingUpdate.Category) {
			return fmt.Errorf("invalid setting category: %s", settingUpdate.Category)
		}

		// 验证值类型
		if settingUpdate.ValueType != "" && !models.ValidateValueType(settingUpdate.ValueType) {
			return fmt.Errorf("invalid value type: %s", settingUpdate.ValueType)
		}

		// 获取现有设置或创建新设置
		setting, err := s.systemSettingRepo.GetByCategoryAndKey(ctx, settingUpdate.Category, settingUpdate.Key)
		if err != nil && err != gorm.ErrRecordNotFound {
			return fmt.Errorf("failed to get setting: %w", err)
		}

		if err == gorm.ErrRecordNotFound {
			// 创建新设置
			setting = &models.SystemSetting{
				Category:  settingUpdate.Category,
				Key:       settingUpdate.Key,
				ValueType: settingUpdate.ValueType,
				UpdatedBy: userID,
			}
			if settingUpdate.ValueType == "" {
				setting.ValueType = "string"
			}
		}

		// 转换和验证值
		valueStr, err := s.convertValueToString(settingUpdate.Value, setting.ValueType)
		if err != nil {
			return fmt.Errorf("failed to convert value for %s.%s: %w", settingUpdate.Category, settingUpdate.Key, err)
		}

		// 更新字段
		setting.Value = valueStr
		if settingUpdate.Description != "" {
			setting.Description = settingUpdate.Description
		}
		if settingUpdate.IsPublic != nil {
			setting.IsPublic = *settingUpdate.IsPublic
		}
		setting.UpdatedBy = userID

		settingsToUpdate = append(settingsToUpdate, setting)
	}

	// 批量更新
	return s.systemSettingRepo.BatchUpdate(ctx, settingsToUpdate)
}

// GetSystemSettingsByCategory 根据分类获取系统设置
func (s *settingService) GetSystemSettingsByCategory(ctx context.Context, category string, userID uint) ([]*models.SystemSetting, error) {
	// 检查用户权限
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	var isPublic *bool
	if user.Role != "admin" {
		public := true
		isPublic = &public
	}

	return s.systemSettingRepo.GetByCategory(ctx, category, isPublic)
}

// GetUserPreferences 获取用户偏好
func (s *settingService) GetUserPreferences(ctx context.Context, req *models.GetUserPreferencesRequest, userID uint) (map[string]interface{}, error) {
	preferences, err := s.userPrefRepo.GetByUser(ctx, userID, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user preferences: %w", err)
	}

	// 按分类组织偏好
	result := make(map[string]interface{})
	categories := make(map[string]map[string]interface{})

	for _, pref := range preferences {
		if categories[pref.Category] == nil {
			categories[pref.Category] = make(map[string]interface{})
		}

		// 根据值类型转换值
		value, err := s.convertSettingValue(pref.Value, pref.ValueType)
		if err != nil {
			return nil, fmt.Errorf("failed to convert preference value for %s.%s: %w", pref.Category, pref.Key, err)
		}

		categories[pref.Category][pref.Key] = map[string]interface{}{
			"value": value,
			"type":  pref.ValueType,
		}
	}

	result["categories"] = categories
	return result, nil
}

// UpdateUserPreferences 更新用户偏好
func (s *settingService) UpdateUserPreferences(ctx context.Context, req *models.UpdateUserPreferencesRequest, userID uint) error {
	// 验证和更新每个偏好
	var preferencesToUpdate []*models.UserPreference

	for _, prefUpdate := range req.Preferences {
		// 验证分类
		if !models.ValidatePreferenceCategory(prefUpdate.Category) {
			return fmt.Errorf("invalid preference category: %s", prefUpdate.Category)
		}

		// 验证值类型
		if prefUpdate.ValueType != "" && !models.ValidateValueType(prefUpdate.ValueType) {
			return fmt.Errorf("invalid value type: %s", prefUpdate.ValueType)
		}

		// 获取现有偏好或创建新偏好
		preference, err := s.userPrefRepo.GetByUserCategoryAndKey(ctx, userID, prefUpdate.Category, prefUpdate.Key)
		if err != nil && err != gorm.ErrRecordNotFound {
			return fmt.Errorf("failed to get preference: %w", err)
		}

		if err == gorm.ErrRecordNotFound {
			// 创建新偏好
			preference = &models.UserPreference{
				UserID:    userID,
				Category:  prefUpdate.Category,
				Key:       prefUpdate.Key,
				ValueType: prefUpdate.ValueType,
			}
			if prefUpdate.ValueType == "" {
				preference.ValueType = "string"
			}
		}

		// 转换和验证值
		valueStr, err := s.convertValueToString(prefUpdate.Value, preference.ValueType)
		if err != nil {
			return fmt.Errorf("failed to convert value for %s.%s: %w", prefUpdate.Category, prefUpdate.Key, err)
		}

		preference.Value = valueStr
		preferencesToUpdate = append(preferencesToUpdate, preference)
	}

	// 批量更新
	return s.userPrefRepo.BatchUpdate(ctx, userID, preferencesToUpdate)
}

// GetUserPreferencesByCategory 根据分类获取用户偏好
func (s *settingService) GetUserPreferencesByCategory(ctx context.Context, category string, userID uint) ([]*models.UserPreference, error) {
	return s.userPrefRepo.GetByUserAndCategory(ctx, userID, category)
}

// InitializeUserDefaults 初始化用户默认设置
func (s *settingService) InitializeUserDefaults(ctx context.Context, userID uint) error {
	return s.userPrefRepo.InitializeDefaults(ctx, userID)
}

// convertSettingValue 根据类型转换设置值
func (s *settingService) convertSettingValue(value, valueType string) (interface{}, error) {
	switch valueType {
	case "boolean":
		return strconv.ParseBool(value)
	case "number":
		if val, err := strconv.ParseFloat(value, 64); err == nil {
			return val, nil
		}
		return strconv.ParseInt(value, 10, 64)
	case "json":
		// TODO: 解析JSON
		return value, nil
	default: // string
		return value, nil
	}
}

// convertValueToString 将值转换为字符串
func (s *settingService) convertValueToString(value interface{}, valueType string) (string, error) {
	switch valueType {
	case "boolean":
		if val, ok := value.(bool); ok {
			return strconv.FormatBool(val), nil
		}
		return "", fmt.Errorf("value is not a boolean")
	case "number":
		switch val := value.(type) {
		case int:
			return strconv.Itoa(val), nil
		case int64:
			return strconv.FormatInt(val, 10), nil
		case float64:
			return strconv.FormatFloat(val, 'f', -1, 64), nil
		default:
			return "", fmt.Errorf("value is not a number")
		}
	case "json":
		// TODO: 序列化JSON
		return fmt.Sprintf("%v", value), nil
	default: // string
		if val, ok := value.(string); ok {
			return val, nil
		}
		return fmt.Sprintf("%v", value), nil
	}
}
