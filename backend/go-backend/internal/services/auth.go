package services

import (
	"errors"
	"time"

	"db-monitor-platform/internal/config"
	"db-monitor-platform/internal/models"
	"db-monitor-platform/internal/repository"
	"db-monitor-platform/internal/utils"
	"db-monitor-platform/pkg/logger"

	"gorm.io/gorm"
)

// AuthService 认证服务
type AuthService struct {
	userRepo   repository.UserRepository
	jwtManager *utils.JWTManager
}

// NewAuthService 创建认证服务
func NewAuthService(db *gorm.DB, cfg *config.Config) *AuthService {
	return &AuthService{
		userRepo:   repository.NewUserRepository(db),
		jwtManager: utils.NewJWTManager(cfg.JWT),
	}
}

// Register 用户注册
func (s *AuthService) Register(req *models.UserCreateRequest) (*models.UserResponse, error) {
	// 验证输入
	if err := utils.ValidateAndSanitize(req); err != nil {
		logger.Errorf("Validation failed: %v", err)
		return nil, err
	}

	// 检查邮箱是否已存在
	existingUser, err := s.userRepo.GetByEmail(req.Email)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Errorf("Failed to check existing user: %v", err)
		return nil, errors.New("failed to check user existence")
	}
	if existingUser != nil {
		return nil, errors.New("email already exists")
	}

	// 加密密码
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		logger.Errorf("Failed to hash password: %v", err)
		return nil, errors.New("failed to process password")
	}

	// 创建用户
	user := &models.User{
		Email:    req.Email,
		Password: hashedPassword,
		Name:     req.Name,
		Role:     req.Role,
		IsActive: true,
	}

	// 如果没有指定角色，默认为user
	if user.Role == "" {
		user.Role = "user"
	}

	// 保存用户
	if err := s.userRepo.Create(user); err != nil {
		logger.Errorf("Failed to create user: %v", err)
		return nil, errors.New("failed to create user")
	}

	logger.Infof("User registered successfully: %s", user.Email)
	return user.ToResponse(), nil
}

// Login 用户登录
func (s *AuthService) Login(req *models.UserLoginRequest) (*LoginResponse, error) {
	// 验证输入
	if err := utils.ValidateAndSanitize(req); err != nil {
		logger.Errorf("Validation failed: %v", err)
		return nil, err
	}

	// 查找用户
	user, err := s.userRepo.GetByEmail(req.Email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invalid email or password")
		}
		logger.Errorf("Failed to get user: %v", err)
		return nil, errors.New("login failed")
	}

	// 检查用户是否激活
	if !user.IsActive {
		return nil, errors.New("account is disabled")
	}

	// 验证密码
	if !utils.CheckPassword(req.Password, user.Password) {
		return nil, errors.New("invalid email or password")
	}

	// 生成JWT token
	token, err := s.jwtManager.GenerateToken(user)
	if err != nil {
		logger.Errorf("Failed to generate token: %v", err)
		return nil, errors.New("failed to generate token")
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLogin = &now
	if err := s.userRepo.Update(user); err != nil {
		logger.Warnf("Failed to update last login time: %v", err)
		// 不影响登录流程，只记录警告
	}

	logger.Infof("User logged in successfully: %s", user.Email)

	return &LoginResponse{
		User:  user.ToResponse(),
		Token: s.jwtManager.CreateTokenResponse(token),
	}, nil
}

// GetProfile 获取用户资料
func (s *AuthService) GetProfile(userID uint) (*models.UserResponse, error) {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		logger.Errorf("Failed to get user profile: %v", err)
		return nil, errors.New("failed to get profile")
	}

	return user.ToResponse(), nil
}

// UpdateProfile 更新用户资料
func (s *AuthService) UpdateProfile(userID uint, req *models.UserUpdateRequest) (*models.UserResponse, error) {
	// 验证输入
	if err := utils.ValidateAndSanitize(req); err != nil {
		logger.Errorf("Validation failed: %v", err)
		return nil, err
	}

	// 获取用户
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		logger.Errorf("Failed to get user: %v", err)
		return nil, errors.New("failed to get user")
	}

	// 更新字段
	if req.Name != "" {
		user.Name = req.Name
	}
	if req.AvatarURL != "" {
		user.AvatarURL = req.AvatarURL
	}
	if req.IsActive != nil {
		user.IsActive = *req.IsActive
	}

	// 保存更新
	if err := s.userRepo.Update(user); err != nil {
		logger.Errorf("Failed to update user: %v", err)
		return nil, errors.New("failed to update profile")
	}

	logger.Infof("User profile updated: %s", user.Email)
	return user.ToResponse(), nil
}

// ChangePassword 修改密码
func (s *AuthService) ChangePassword(userID uint, oldPassword, newPassword string) error {
	// 验证新密码
	if err := utils.ValidatePassword(newPassword); err != nil {
		return err
	}

	// 获取用户
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("user not found")
		}
		logger.Errorf("Failed to get user: %v", err)
		return errors.New("failed to get user")
	}

	// 验证旧密码
	if !utils.CheckPassword(oldPassword, user.Password) {
		return errors.New("invalid old password")
	}

	// 加密新密码
	hashedPassword, err := utils.HashPassword(newPassword)
	if err != nil {
		logger.Errorf("Failed to hash password: %v", err)
		return errors.New("failed to process password")
	}

	// 更新密码
	user.Password = hashedPassword
	if err := s.userRepo.Update(user); err != nil {
		logger.Errorf("Failed to update password: %v", err)
		return errors.New("failed to update password")
	}

	logger.Infof("Password changed for user: %s", user.Email)
	return nil
}

// ValidateToken 验证token
func (s *AuthService) ValidateToken(tokenString string) (*utils.Claims, error) {
	return s.jwtManager.ValidateToken(tokenString)
}

// RefreshToken 刷新token
func (s *AuthService) RefreshToken(tokenString string) (*utils.TokenResponse, error) {
	newToken, err := s.jwtManager.RefreshToken(tokenString)
	if err != nil {
		return nil, err
	}

	return s.jwtManager.CreateTokenResponse(newToken), nil
}

// LoginResponse 登录响应
type LoginResponse struct {
	User  *models.UserResponse  `json:"user"`
	Token *utils.TokenResponse  `json:"token"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,password"`
}
