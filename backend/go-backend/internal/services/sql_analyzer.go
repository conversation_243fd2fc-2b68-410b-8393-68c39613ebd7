package services

import (
	"fmt"
	"regexp"
	"strings"

	"db-monitor-platform/pkg/logger"
)

// SQLAnalyzer SQL分析引擎
type SQLAnalyzer struct {
	parserFactory *DefaultSQLParserFactory
}

// NewSQLAnalyzer 创建SQL分析引擎
func NewSQLAnalyzer() *SQLAnalyzer {
	return &SQLAnalyzer{
		parserFactory: GetGlobalParserFactory().(*DefaultSQLParserFactory),
	}
}

// SQLAnalysisResult SQL分析结果
type SQLAnalysisResult struct {
	FormattedQuery  string
	QueryType       string
	ComplexityScore int
	Tables          []string
	Columns         []string
	Conditions      []string
	JoinTypes       []string
	HasSubquery     bool
	HasAggregate    bool
	HasOrderBy      bool
	HasGroupBy      bool
	HasLimit        bool
}

// AnalyzeSQL 分析SQL查询 (兼容旧版本)
func (a *SQLAnalyzer) AnalyzeSQL(sqlQuery string) (*SQLAnalysisResult, error) {
	if strings.TrimSpace(sqlQuery) == "" {
		return nil, fmt.Errorf("empty SQL query")
	}

	// 使用新的增强分析，然后转换为旧格式
	enhancedResult, err := a.AnalyzeSQLEnhanced(sqlQuery, "postgresql")
	if err != nil {
		// 如果增强分析失败，回退到旧方法
		logger.Warnf("Enhanced SQL analysis failed, falling back to legacy method: %v", err)
		return a.analyzeSQLLegacy(sqlQuery)
	}

	// 转换为旧格式以保持兼容性
	return a.convertToLegacyFormat(enhancedResult), nil
}

// AnalyzeSQLEnhanced 增强的SQL分析 (使用新解析器架构)
func (a *SQLAnalyzer) AnalyzeSQLEnhanced(sqlQuery, dbType string) (*SQLParseResult, error) {
	if strings.TrimSpace(sqlQuery) == "" {
		return nil, fmt.Errorf("empty SQL query")
	}

	logger.Infof("Enhanced SQL analysis for database type: %s", dbType)

	// 创建解析器配置
	config := &SQLParserConfig{
		DatabaseType:   dbType,
		EnableAST:      true,
		EnablePatterns: true,
		EnableOptimize: true,
	}

	// 使用解析器工厂进行带容错的解析
	result, err := a.parserFactory.ParseWithFallback(dbType, sqlQuery, config)
	if err != nil {
		return nil, fmt.Errorf("failed to parse SQL: %w", err)
	}

	logger.Infof("SQL analysis completed successfully with parser type: %s",
		a.getParserTypeFromResult(result))

	return result, nil
}

// analyzeSQLLegacy 旧版SQL分析方法 (保持向后兼容)
func (a *SQLAnalyzer) analyzeSQLLegacy(sqlQuery string) (*SQLAnalysisResult, error) {
	// 清理和标准化SQL
	cleanSQL := a.cleanSQL(sqlQuery)

	// 确定查询类型
	queryType := a.determineQueryType(cleanSQL)

	// 格式化SQL
	formattedSQL := a.formatSQL(cleanSQL)

	// 分析查询复杂度
	complexityScore := a.calculateComplexity(cleanSQL)

	// 提取表名
	tables := a.extractTables(cleanSQL, queryType)

	// 提取列名
	columns := a.extractColumns(cleanSQL, queryType)

	// 提取条件
	conditions := a.extractConditions(cleanSQL)

	// 提取JOIN类型
	joinTypes := a.extractJoinTypes(cleanSQL)

	// 检查特殊特性
	hasSubquery := a.hasSubquery(cleanSQL)
	hasAggregate := a.hasAggregate(cleanSQL)
	hasOrderBy := a.hasOrderBy(cleanSQL)
	hasGroupBy := a.hasGroupBy(cleanSQL)
	hasLimit := a.hasLimit(cleanSQL)

	return &SQLAnalysisResult{
		FormattedQuery:  formattedSQL,
		QueryType:       queryType,
		ComplexityScore: complexityScore,
		Tables:          tables,
		Columns:         columns,
		Conditions:      conditions,
		JoinTypes:       joinTypes,
		HasSubquery:     hasSubquery,
		HasAggregate:    hasAggregate,
		HasOrderBy:      hasOrderBy,
		HasGroupBy:      hasGroupBy,
		HasLimit:        hasLimit,
	}, nil
}

// convertToLegacyFormat 将新格式转换为旧格式
func (a *SQLAnalyzer) convertToLegacyFormat(result *SQLParseResult) *SQLAnalysisResult {
	// 提取表名
	var tables []string
	for _, table := range result.Tables {
		tables = append(tables, table.Name)
	}

	// 提取列名
	var columns []string
	for _, column := range result.Columns {
		columns = append(columns, column.Name)
	}

	// 提取条件
	var conditions []string
	for _, condition := range result.Conditions {
		condStr := fmt.Sprintf("%s %s %s", condition.Column, condition.Operator, condition.Value)
		conditions = append(conditions, condStr)
	}

	// 提取JOIN类型
	var joinTypes []string
	for _, join := range result.JoinInfo {
		joinTypes = append(joinTypes, join.Type)
	}

	// 检查特殊特性
	hasSubquery := a.hasSubqueryInTables(result.Tables)
	hasAggregate := a.hasAggregateInColumns(result.Columns)
	hasOrderBy := strings.Contains(strings.ToUpper(result.FormattedQuery), "ORDER BY")
	hasGroupBy := strings.Contains(strings.ToUpper(result.FormattedQuery), "GROUP BY")
	hasLimit := strings.Contains(strings.ToUpper(result.FormattedQuery), "LIMIT")

	// 获取复杂度分数
	complexityScore := 0
	if result.ComplexityScore != nil {
		complexityScore = result.ComplexityScore.Total
	}

	return &SQLAnalysisResult{
		FormattedQuery:  result.FormattedQuery,
		QueryType:       result.QueryType,
		ComplexityScore: complexityScore,
		Tables:          tables,
		Columns:         columns,
		Conditions:      conditions,
		JoinTypes:       joinTypes,
		HasSubquery:     hasSubquery,
		HasAggregate:    hasAggregate,
		HasOrderBy:      hasOrderBy,
		HasGroupBy:      hasGroupBy,
		HasLimit:        hasLimit,
	}
}

// cleanSQL 清理SQL语句
func (a *SQLAnalyzer) cleanSQL(sql string) string {
	// 移除多余的空白字符
	sql = regexp.MustCompile(`\s+`).ReplaceAllString(sql, " ")
	
	// 移除注释
	sql = regexp.MustCompile(`--.*`).ReplaceAllString(sql, "")
	sql = regexp.MustCompile(`/\*.*?\*/`).ReplaceAllString(sql, "")
	
	// 移除首尾空白
	sql = strings.TrimSpace(sql)
	
	return sql
}

// determineQueryType 确定查询类型
func (a *SQLAnalyzer) determineQueryType(sql string) string {
	sql = strings.ToUpper(strings.TrimSpace(sql))
	
	if strings.HasPrefix(sql, "SELECT") {
		return "select"
	} else if strings.HasPrefix(sql, "INSERT") {
		return "insert"
	} else if strings.HasPrefix(sql, "UPDATE") {
		return "update"
	} else if strings.HasPrefix(sql, "DELETE") {
		return "delete"
	} else if strings.HasPrefix(sql, "CREATE") {
		return "create"
	} else if strings.HasPrefix(sql, "ALTER") {
		return "alter"
	} else if strings.HasPrefix(sql, "DROP") {
		return "drop"
	}
	
	return "other"
}

// formatSQL 格式化SQL语句
func (a *SQLAnalyzer) formatSQL(sql string) string {
	// 简单的SQL格式化
	keywords := []string{
		"SELECT", "FROM", "WHERE", "JOIN", "INNER JOIN", "LEFT JOIN", "RIGHT JOIN", "FULL JOIN",
		"GROUP BY", "ORDER BY", "HAVING", "LIMIT", "OFFSET", "UNION", "INSERT", "UPDATE", "DELETE",
		"CREATE", "ALTER", "DROP", "INDEX", "TABLE", "DATABASE", "SCHEMA",
	}
	
	formatted := sql
	
	// 关键字大写
	for _, keyword := range keywords {
		re := regexp.MustCompile(`(?i)\b` + regexp.QuoteMeta(keyword) + `\b`)
		formatted = re.ReplaceAllString(formatted, strings.ToUpper(keyword))
	}
	
	// 添加适当的换行和缩进
	formatted = a.addLineBreaks(formatted)
	
	return formatted
}

// addLineBreaks 添加换行符
func (a *SQLAnalyzer) addLineBreaks(sql string) string {
	// 在主要关键字前添加换行
	keywords := []string{"FROM", "WHERE", "JOIN", "INNER JOIN", "LEFT JOIN", "RIGHT JOIN", "FULL JOIN", "GROUP BY", "ORDER BY", "HAVING", "LIMIT"}
	
	for _, keyword := range keywords {
		re := regexp.MustCompile(`\s+` + keyword + `\s+`)
		sql = re.ReplaceAllString(sql, "\n"+keyword+" ")
	}
	
	return sql
}

// calculateComplexity 计算查询复杂度
func (a *SQLAnalyzer) calculateComplexity(sql string) int {
	score := 0
	sqlUpper := strings.ToUpper(sql)
	
	// 基础分数
	score += 1
	
	// JOIN 增加复杂度
	joinCount := strings.Count(sqlUpper, "JOIN")
	score += joinCount * 2
	
	// 子查询增加复杂度
	subqueryCount := strings.Count(sqlUpper, "SELECT") - 1 // 减去主查询
	if subqueryCount > 0 {
		score += subqueryCount * 3
	}
	
	// 聚合函数增加复杂度
	aggregates := []string{"COUNT", "SUM", "AVG", "MAX", "MIN", "GROUP_CONCAT"}
	for _, agg := range aggregates {
		score += strings.Count(sqlUpper, agg)
	}
	
	// UNION 增加复杂度
	score += strings.Count(sqlUpper, "UNION") * 2
	
	// 窗口函数增加复杂度
	if strings.Contains(sqlUpper, "OVER") {
		score += 3
	}
	
	// CTE (WITH) 增加复杂度
	score += strings.Count(sqlUpper, "WITH") * 2
	
	// 嵌套层级
	nestingLevel := a.calculateNestingLevel(sql)
	score += nestingLevel * 2
	
	return score
}

// calculateNestingLevel 计算嵌套层级
func (a *SQLAnalyzer) calculateNestingLevel(sql string) int {
	maxLevel := 0
	currentLevel := 0
	
	for _, char := range sql {
		if char == '(' {
			currentLevel++
			if currentLevel > maxLevel {
				maxLevel = currentLevel
			}
		} else if char == ')' {
			currentLevel--
		}
	}
	
	return maxLevel
}

// extractTables 提取表名
func (a *SQLAnalyzer) extractTables(sql, queryType string) []string {
	var tables []string
	
	switch queryType {
	case "select":
		// 提取FROM子句中的表名
		fromRegex := regexp.MustCompile(`(?i)FROM\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)?)`)
		matches := fromRegex.FindAllStringSubmatch(sql, -1)
		for _, match := range matches {
			if len(match) > 1 {
				tables = append(tables, strings.ToLower(match[1]))
			}
		}
		
		// 提取JOIN子句中的表名
		joinRegex := regexp.MustCompile(`(?i)JOIN\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)?)`)
		joinMatches := joinRegex.FindAllStringSubmatch(sql, -1)
		for _, match := range joinMatches {
			if len(match) > 1 {
				tables = append(tables, strings.ToLower(match[1]))
			}
		}
		
	case "insert":
		insertRegex := regexp.MustCompile(`(?i)INSERT\s+INTO\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)?)`)
		matches := insertRegex.FindAllStringSubmatch(sql, -1)
		for _, match := range matches {
			if len(match) > 1 {
				tables = append(tables, strings.ToLower(match[1]))
			}
		}
		
	case "update":
		updateRegex := regexp.MustCompile(`(?i)UPDATE\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)?)`)
		matches := updateRegex.FindAllStringSubmatch(sql, -1)
		for _, match := range matches {
			if len(match) > 1 {
				tables = append(tables, strings.ToLower(match[1]))
			}
		}
		
	case "delete":
		deleteRegex := regexp.MustCompile(`(?i)DELETE\s+FROM\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)?)`)
		matches := deleteRegex.FindAllStringSubmatch(sql, -1)
		for _, match := range matches {
			if len(match) > 1 {
				tables = append(tables, strings.ToLower(match[1]))
			}
		}
	}
	
	// 去重
	return a.removeDuplicates(tables)
}

// extractColumns 提取列名
func (a *SQLAnalyzer) extractColumns(sql, queryType string) []string {
	var columns []string
	
	if queryType == "select" {
		// 简单的列名提取（可以进一步优化）
		selectRegex := regexp.MustCompile(`(?i)SELECT\s+(.*?)\s+FROM`)
		matches := selectRegex.FindStringSubmatch(sql)
		if len(matches) > 1 {
			columnsPart := matches[1]
			if columnsPart != "*" {
				// 分割列名
				cols := strings.Split(columnsPart, ",")
				for _, col := range cols {
					col = strings.TrimSpace(col)
					// 移除别名
					if strings.Contains(col, " AS ") {
						parts := strings.Split(col, " AS ")
						col = strings.TrimSpace(parts[0])
					}
					if col != "" && col != "*" {
						columns = append(columns, col)
					}
				}
			}
		}
	}
	
	return columns
}

// extractConditions 提取WHERE条件
func (a *SQLAnalyzer) extractConditions(sql string) []string {
	var conditions []string
	
	whereRegex := regexp.MustCompile(`(?i)WHERE\s+(.*?)(?:\s+GROUP\s+BY|\s+ORDER\s+BY|\s+LIMIT|\s+HAVING|$)`)
	matches := whereRegex.FindStringSubmatch(sql)
	if len(matches) > 1 {
		conditionsPart := matches[1]
		// 简单分割条件（可以进一步优化）
		conds := strings.Split(conditionsPart, " AND ")
		for _, cond := range conds {
			cond = strings.TrimSpace(cond)
			if cond != "" {
				conditions = append(conditions, cond)
			}
		}
	}
	
	return conditions
}

// extractJoinTypes 提取JOIN类型
func (a *SQLAnalyzer) extractJoinTypes(sql string) []string {
	var joinTypes []string
	sqlUpper := strings.ToUpper(sql)
	
	joins := []string{"INNER JOIN", "LEFT JOIN", "RIGHT JOIN", "FULL JOIN", "CROSS JOIN", "JOIN"}
	
	for _, joinType := range joins {
		if strings.Contains(sqlUpper, joinType) {
			joinTypes = append(joinTypes, joinType)
		}
	}
	
	return a.removeDuplicates(joinTypes)
}

// hasSubquery 检查是否有子查询
func (a *SQLAnalyzer) hasSubquery(sql string) bool {
	selectCount := strings.Count(strings.ToUpper(sql), "SELECT")
	return selectCount > 1
}

// hasAggregate 检查是否有聚合函数
func (a *SQLAnalyzer) hasAggregate(sql string) bool {
	sqlUpper := strings.ToUpper(sql)
	aggregates := []string{"COUNT", "SUM", "AVG", "MAX", "MIN", "GROUP_CONCAT"}
	
	for _, agg := range aggregates {
		if strings.Contains(sqlUpper, agg) {
			return true
		}
	}
	
	return false
}

// hasOrderBy 检查是否有ORDER BY
func (a *SQLAnalyzer) hasOrderBy(sql string) bool {
	return strings.Contains(strings.ToUpper(sql), "ORDER BY")
}

// hasGroupBy 检查是否有GROUP BY
func (a *SQLAnalyzer) hasGroupBy(sql string) bool {
	return strings.Contains(strings.ToUpper(sql), "GROUP BY")
}

// hasLimit 检查是否有LIMIT
func (a *SQLAnalyzer) hasLimit(sql string) bool {
	return strings.Contains(strings.ToUpper(sql), "LIMIT")
}

// removeDuplicates 去重
func (a *SQLAnalyzer) removeDuplicates(slice []string) []string {
	keys := make(map[string]bool)
	var result []string
	
	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}
	
	return result
}

// 新增的辅助方法

// getParserTypeFromResult 从结果中获取解析器类型
func (a *SQLAnalyzer) getParserTypeFromResult(result *SQLParseResult) string {
	// 检查是否有AST验证标记
	for _, pattern := range result.Patterns {
		if pattern.Name == "ast_validated" {
			return "postgresql_enhanced"
		}
	}
	return "fallback"
}

// hasSubqueryInTables 检查表信息中是否有子查询
func (a *SQLAnalyzer) hasSubqueryInTables(tables []TableInfo) bool {
	for _, table := range tables {
		if table.IsSubquery {
			return true
		}
	}
	return false
}

// hasAggregateInColumns 检查列信息中是否有聚合函数
func (a *SQLAnalyzer) hasAggregateInColumns(columns []ColumnInfo) bool {
	for _, column := range columns {
		if column.IsAggregate {
			return true
		}
	}
	return false
}

// ValidateSQL 验证SQL语法 (新增方法)
func (a *SQLAnalyzer) ValidateSQL(sqlQuery, dbType string) error {
	if strings.TrimSpace(sqlQuery) == "" {
		return fmt.Errorf("empty SQL query")
	}

	config := &SQLParserConfig{
		DatabaseType: dbType,
		EnableAST:    true,
	}

	return a.parserFactory.ValidateWithFallback(dbType, sqlQuery, config)
}

// GetParserInfo 获取解析器信息 (新增方法)
func (a *SQLAnalyzer) GetParserInfo(dbType string) map[string]interface{} {
	return a.parserFactory.GetParserInfo(dbType)
}

// GetSupportedDatabases 获取支持的数据库类型 (新增方法)
func (a *SQLAnalyzer) GetSupportedDatabases() []string {
	return a.parserFactory.GetSupportedDatabases()
}
