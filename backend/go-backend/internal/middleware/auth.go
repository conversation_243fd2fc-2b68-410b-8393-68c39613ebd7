package middleware

import (
	"errors"
	"net/http"

	"db-monitor-platform/internal/config"
	"db-monitor-platform/internal/models"
	"db-monitor-platform/internal/utils"
	"db-monitor-platform/pkg/logger"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware JWT认证中间件
func AuthMiddleware(cfg *config.Config) gin.HandlerFunc {
	jwtManager := utils.NewJWTManager(cfg.JWT)
	
	return func(c *gin.Context) {
		// 从Authorization header获取token
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse(401, "Authorization header is required"))
			c.Abort()
			return
		}

		// 提取token
		tokenString, err := utils.ExtractTokenFromHeader(authHeader)
		if err != nil {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse(401, err.Error()))
			c.Abort()
			return
		}

		// 验证token
		claims, err := jwtManager.ValidateToken(tokenString)
		if err != nil {
			logger.Errorf("Token validation failed: %v", err)
			c.JSON(http.StatusUnauthorized, models.ErrorResponse(401, "Invalid or expired token"))
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		c.Set("user_role", claims.Role)
		c.Set("claims", claims)

		c.Next()
	}
}

// OptionalAuthMiddleware 可选认证中间件（不强制要求认证）
func OptionalAuthMiddleware(cfg *config.Config) gin.HandlerFunc {
	jwtManager := utils.NewJWTManager(cfg.JWT)
	
	return func(c *gin.Context) {
		// 从Authorization header获取token
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			// 没有token，继续处理但不设置用户信息
			c.Next()
			return
		}

		// 提取token
		tokenString, err := utils.ExtractTokenFromHeader(authHeader)
		if err != nil {
			// token格式错误，继续处理但不设置用户信息
			c.Next()
			return
		}

		// 验证token
		claims, err := jwtManager.ValidateToken(tokenString)
		if err != nil {
			// token无效，继续处理但不设置用户信息
			c.Next()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		c.Set("user_role", claims.Role)
		c.Set("claims", claims)

		c.Next()
	}
}

// RequireRole 角色权限中间件
func RequireRole(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户角色
		userRole, exists := c.Get("user_role")
		if !exists {
			c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
			c.Abort()
			return
		}

		// 检查角色权限
		roleStr := userRole.(string)
		hasPermission := false
		for _, role := range roles {
			if roleStr == role {
				hasPermission = true
				break
			}
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAdmin 管理员权限中间件
func RequireAdmin() gin.HandlerFunc {
	return RequireRole("admin")
}

// RequireAdminOrUser 管理员或普通用户权限中间件
func RequireAdminOrUser() gin.HandlerFunc {
	return RequireRole("admin", "user")
}

// RequireOwnershipOrAdmin 资源所有者或管理员权限中间件
func RequireOwnershipOrAdmin(getResourceOwnerID func(*gin.Context) (uint, error)) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取当前用户信息
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
			c.Abort()
			return
		}

		userRole, exists := c.Get("user_role")
		if !exists {
			c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
			c.Abort()
			return
		}

		// 如果是管理员，直接通过
		if userRole.(string) == "admin" {
			c.Next()
			return
		}

		// 获取资源所有者ID
		ownerID, err := getResourceOwnerID(c)
		if err != nil {
			logger.Errorf("Failed to get resource owner ID: %v", err)
			c.JSON(http.StatusInternalServerError, models.InternalErrorResponse())
			c.Abort()
			return
		}

		// 检查是否为资源所有者
		if userID.(uint) != ownerID {
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			c.Abort()
			return
		}

		c.Next()
	}
}

// RateLimitMiddleware 简单的速率限制中间件
func RateLimitMiddleware() gin.HandlerFunc {
	// 这里可以实现基于IP或用户的速率限制
	// 为了简化，这里只是一个占位符
	return func(c *gin.Context) {
		// TODO: 实现速率限制逻辑
		c.Next()
	}
}

// APIKeyMiddleware API密钥认证中间件（用于服务间调用）
func APIKeyMiddleware(validAPIKeys []string) gin.HandlerFunc {
	return func(c *gin.Context) {
		apiKey := c.GetHeader("X-API-Key")
		if apiKey == "" {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse(401, "API key is required"))
			c.Abort()
			return
		}

		// 验证API密钥
		valid := false
		for _, validKey := range validAPIKeys {
			if apiKey == validKey {
				valid = true
				break
			}
		}

		if !valid {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse(401, "Invalid API key"))
			c.Abort()
			return
		}

		// 设置API调用标识
		c.Set("api_call", true)
		c.Next()
	}
}

// GetCurrentUser 获取当前用户信息的辅助函数
func GetCurrentUser(c *gin.Context) (*CurrentUser, error) {
	userID, exists := c.Get("user_id")
	if !exists {
		return nil, errors.New("user not authenticated")
	}

	userEmail, exists := c.Get("user_email")
	if !exists {
		return nil, errors.New("user email not found")
	}

	userRole, exists := c.Get("user_role")
	if !exists {
		return nil, errors.New("user role not found")
	}

	return &CurrentUser{
		ID:    userID.(uint),
		Email: userEmail.(string),
		Role:  userRole.(string),
	}, nil
}

// CurrentUser 当前用户信息
type CurrentUser struct {
	ID    uint   `json:"id"`
	Email string `json:"email"`
	Role  string `json:"role"`
}

// IsAdmin 检查是否为管理员
func (u *CurrentUser) IsAdmin() bool {
	return u.Role == "admin"
}

// CanManageDatabase 检查是否可以管理数据库
func (u *CurrentUser) CanManageDatabase() bool {
	return u.Role == "admin" || u.Role == "user"
}

// CanViewOnly 检查是否只能查看
func (u *CurrentUser) CanViewOnly() bool {
	return u.Role == "viewer"
}

// SecurityHeadersMiddleware 安全头中间件
func SecurityHeadersMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置安全相关的HTTP头
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		
		// 在生产环境中启用HSTS
		if gin.Mode() == gin.ReleaseMode {
			c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		}

		c.Next()
	}
}

// RequestIDMiddleware 请求ID中间件
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 生成或获取请求ID
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			// 生成新的请求ID
			requestID = generateRequestID()
		}

		// 设置请求ID到上下文和响应头
		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)

		c.Next()
	}
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	// 简单的请求ID生成，实际项目中可以使用UUID
	return "req-" + utils.GenerateRandomString(12)
}
