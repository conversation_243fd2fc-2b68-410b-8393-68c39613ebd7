package repository

import (
	"db-monitor-platform/internal/models"

	"gorm.io/gorm"
)

// DatabaseRepository 数据库实例数据访问接口
type DatabaseRepository interface {
	Create(database *models.DatabaseInstance) error
	GetByID(id uint) (*models.DatabaseInstance, error)
	GetByUserID(userID uint, pagination *models.PaginationRequest) ([]models.DatabaseInstance, int64, error)
	GetAll(pagination *models.PaginationRequest) ([]models.DatabaseInstance, int64, error)
	Update(database *models.DatabaseInstance) error
	Delete(id uint) error
	GetByType(dbType string) ([]models.DatabaseInstance, error)
	GetActiveInstances() ([]models.DatabaseInstance, error)
	SearchDatabases(keyword string, pagination *models.PaginationRequest) ([]models.DatabaseInstance, int64, error)
	GetDatabaseStats(userID uint) (*DatabaseStats, error)
	CheckConnectionExists(host string, port int, dbName string) (*models.DatabaseInstance, error)
	GetDB() *gorm.DB // 添加获取数据库连接的方法
}

// databaseRepository 数据库实例数据访问实现
type databaseRepository struct {
	db *gorm.DB
}

// NewDatabaseRepository 创建数据库实例数据访问实例
func NewDatabaseRepository(db *gorm.DB) DatabaseRepository {
	return &databaseRepository{db: db}
}

// Create 创建数据库实例
func (r *databaseRepository) Create(database *models.DatabaseInstance) error {
	return r.db.Create(database).Error
}

// GetByID 根据ID获取数据库实例
func (r *databaseRepository) GetByID(id uint) (*models.DatabaseInstance, error) {
	var database models.DatabaseInstance
	err := r.db.Preload("User").First(&database, id).Error
	if err != nil {
		return nil, err
	}
	return &database, nil
}

// GetByUserID 根据用户ID获取数据库实例列表
func (r *databaseRepository) GetByUserID(userID uint, pagination *models.PaginationRequest) ([]models.DatabaseInstance, int64, error) {
	var databases []models.DatabaseInstance
	var total int64

	// 获取总数
	query := r.db.Model(&models.DatabaseInstance{}).Where("created_by = ?", userID)
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := pagination.CalculateOffset()
	err := query.Preload("User").
		Offset(offset).
		Limit(pagination.PageSize).
		Order("created_at DESC").
		Find(&databases).Error
	if err != nil {
		return nil, 0, err
	}

	return databases, total, nil
}

// GetAll 获取所有数据库实例（管理员用）
func (r *databaseRepository) GetAll(pagination *models.PaginationRequest) ([]models.DatabaseInstance, int64, error) {
	var databases []models.DatabaseInstance
	var total int64

	// 获取总数
	if err := r.db.Model(&models.DatabaseInstance{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := pagination.CalculateOffset()
	err := r.db.Preload("User").
		Offset(offset).
		Limit(pagination.PageSize).
		Order("created_at DESC").
		Find(&databases).Error
	if err != nil {
		return nil, 0, err
	}

	return databases, total, nil
}

// Update 更新数据库实例
func (r *databaseRepository) Update(database *models.DatabaseInstance) error {
	return r.db.Save(database).Error
}

// Delete 删除数据库实例（软删除）
func (r *databaseRepository) Delete(id uint) error {
	return r.db.Delete(&models.DatabaseInstance{}, id).Error
}

// GetByType 根据数据库类型获取实例
func (r *databaseRepository) GetByType(dbType string) ([]models.DatabaseInstance, error) {
	var databases []models.DatabaseInstance
	err := r.db.Where("type = ? AND status = ?", dbType, "active").
		Preload("User").
		Find(&databases).Error
	return databases, err
}

// GetActiveInstances 获取所有活跃的数据库实例
func (r *databaseRepository) GetActiveInstances() ([]models.DatabaseInstance, error) {
	var databases []models.DatabaseInstance
	err := r.db.Where("status = ? AND is_monitored = ?", "active", true).
		Preload("User").
		Find(&databases).Error
	return databases, err
}

// SearchDatabases 搜索数据库实例
func (r *databaseRepository) SearchDatabases(keyword string, pagination *models.PaginationRequest) ([]models.DatabaseInstance, int64, error) {
	var databases []models.DatabaseInstance
	var total int64

	query := r.db.Model(&models.DatabaseInstance{})
	
	if keyword != "" {
		searchPattern := "%" + keyword + "%"
		query = query.Where("name ILIKE ? OR host ILIKE ? OR description ILIKE ?", 
			searchPattern, searchPattern, searchPattern)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := pagination.CalculateOffset()
	err := query.Preload("User").
		Offset(offset).
		Limit(pagination.PageSize).
		Order("created_at DESC").
		Find(&databases).Error
	if err != nil {
		return nil, 0, err
	}

	return databases, total, nil
}

// GetDatabaseStats 获取数据库统计信息
func (r *databaseRepository) GetDatabaseStats(userID uint) (*DatabaseStats, error) {
	var stats DatabaseStats

	// 总数据库实例数
	query := r.db.Model(&models.DatabaseInstance{})
	if userID > 0 {
		query = query.Where("created_by = ?", userID)
	}
	if err := query.Count(&stats.TotalCount).Error; err != nil {
		return nil, err
	}

	// 活跃实例数
	query = r.db.Model(&models.DatabaseInstance{}).Where("status = ?", "active")
	if userID > 0 {
		query = query.Where("created_by = ?", userID)
	}
	if err := query.Count(&stats.ActiveCount).Error; err != nil {
		return nil, err
	}

	// 监控中的实例数
	query = r.db.Model(&models.DatabaseInstance{}).Where("is_monitored = ?", true)
	if userID > 0 {
		query = query.Where("created_by = ?", userID)
	}
	if err := query.Count(&stats.MonitoredCount).Error; err != nil {
		return nil, err
	}

	// 按类型统计
	var typeStats []TypeStat
	query = r.db.Model(&models.DatabaseInstance{}).
		Select("type, COUNT(*) as count").
		Group("type")
	if userID > 0 {
		query = query.Where("created_by = ?", userID)
	}
	if err := query.Scan(&typeStats).Error; err != nil {
		return nil, err
	}
	stats.TypeStats = typeStats

	return &stats, nil
}

// CheckConnectionExists 检查连接是否已存在
func (r *databaseRepository) CheckConnectionExists(host string, port int, dbName string) (*models.DatabaseInstance, error) {
	var database models.DatabaseInstance
	err := r.db.Where("host = ? AND port = ? AND database_name = ?", host, port, dbName).
		First(&database).Error
	if err != nil {
		return nil, err
	}
	return &database, nil
}

// GetDatabasesWithMetrics 获取数据库实例及其最新指标
func (r *databaseRepository) GetDatabasesWithMetrics(userID uint) ([]DatabaseWithMetrics, error) {
	var results []DatabaseWithMetrics

	query := `
		SELECT 
			di.*,
			COALESCE(latest_metrics.cpu_value, 0) as latest_cpu,
			COALESCE(latest_metrics.memory_value, 0) as latest_memory,
			COALESCE(latest_metrics.connections_value, 0) as latest_connections,
			latest_metrics.last_metric_time
		FROM database_instances di
		LEFT JOIN (
			SELECT 
				database_id,
				MAX(CASE WHEN metric_type = 'cpu' THEN value END) as cpu_value,
				MAX(CASE WHEN metric_type = 'memory' THEN value END) as memory_value,
				MAX(CASE WHEN metric_type = 'connections' THEN value END) as connections_value,
				MAX(timestamp) as last_metric_time
			FROM metrics 
			WHERE timestamp > NOW() - INTERVAL '1 hour'
			GROUP BY database_id
		) latest_metrics ON di.id = latest_metrics.database_id
		WHERE di.deleted_at IS NULL
	`

	if userID > 0 {
		query += " AND di.created_by = ?"
		err := r.db.Raw(query, userID).Scan(&results).Error
		return results, err
	}

	err := r.db.Raw(query).Scan(&results).Error
	return results, err
}

// DatabaseStats 数据库统计信息
type DatabaseStats struct {
	TotalCount     int64      `json:"total_count"`
	ActiveCount    int64      `json:"active_count"`
	MonitoredCount int64      `json:"monitored_count"`
	TypeStats      []TypeStat `json:"type_stats"`
}

// TypeStat 类型统计
type TypeStat struct {
	Type  string `json:"type"`
	Count int64  `json:"count"`
}

// DatabaseWithMetrics 数据库实例及其指标
type DatabaseWithMetrics struct {
	models.DatabaseInstance
	LatestCPU         float64 `json:"latest_cpu"`
	LatestMemory      float64 `json:"latest_memory"`
	LatestConnections float64 `json:"latest_connections"`
	LastMetricTime    *string `json:"last_metric_time"`
}

// GetDB 获取数据库连接
func (r *databaseRepository) GetDB() *gorm.DB {
	return r.db
}
