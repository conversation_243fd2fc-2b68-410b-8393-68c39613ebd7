package repository

import (
	"context"

	"db-monitor-platform/internal/models"

	"gorm.io/gorm"
)

// ReportExecutionRepository 报表执行仓储接口
type ReportExecutionRepository interface {
	Create(ctx context.Context, execution *models.ReportExecution) error
	GetByID(ctx context.Context, id uint) (*models.ReportExecution, error)
	GetList(ctx context.Context, req *models.GetReportExecutionsRequest) ([]*models.ReportExecution, int64, error)
	Update(ctx context.Context, execution *models.ReportExecution) error
	Delete(ctx context.Context, id uint) error
	GetByTemplateID(ctx context.Context, templateID uint, page, pageSize int) ([]*models.ReportExecution, int64, error)
	GetByUserID(ctx context.Context, userID uint, page, pageSize int) ([]*models.ReportExecution, int64, error)
	GetRunningExecutions(ctx context.Context) ([]*models.ReportExecution, error)
	GetRecentExecutions(ctx context.Context, limit int) ([]*models.ReportExecution, error)
}

type reportExecutionRepository struct {
	db *gorm.DB
}

// NewReportExecutionRepository 创建报表执行仓储实例
func NewReportExecutionRepository(db *gorm.DB) ReportExecutionRepository {
	return &reportExecutionRepository{db: db}
}

// Create 创建报表执行记录
func (r *reportExecutionRepository) Create(ctx context.Context, execution *models.ReportExecution) error {
	return r.db.WithContext(ctx).Create(execution).Error
}

// GetByID 根据ID获取报表执行记录
func (r *reportExecutionRepository) GetByID(ctx context.Context, id uint) (*models.ReportExecution, error) {
	var execution models.ReportExecution
	err := r.db.WithContext(ctx).
		Preload("Template").
		Preload("Template.Creator").
		Preload("Executor").
		First(&execution, id).Error
	if err != nil {
		return nil, err
	}
	return &execution, nil
}

// GetList 获取报表执行记录列表
func (r *reportExecutionRepository) GetList(ctx context.Context, req *models.GetReportExecutionsRequest) ([]*models.ReportExecution, int64, error) {
	var executions []*models.ReportExecution
	var total int64

	// 构建查询
	query := r.db.WithContext(ctx).Model(&models.ReportExecution{})

	// 添加过滤条件
	if req.TemplateID != 0 {
		query = query.Where("template_id = ?", req.TemplateID)
	}

	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	if req.ExecutedBy != 0 {
		query = query.Where("executed_by = ?", req.ExecutedBy)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	err := query.
		Preload("Template").
		Preload("Template.Creator").
		Preload("Executor").
		Order("created_at DESC").
		Offset(offset).
		Limit(req.PageSize).
		Find(&executions).Error

	return executions, total, err
}

// Update 更新报表执行记录
func (r *reportExecutionRepository) Update(ctx context.Context, execution *models.ReportExecution) error {
	return r.db.WithContext(ctx).Save(execution).Error
}

// Delete 删除报表执行记录
func (r *reportExecutionRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.ReportExecution{}, id).Error
}

// GetByTemplateID 根据模板ID获取执行记录
func (r *reportExecutionRepository) GetByTemplateID(ctx context.Context, templateID uint, page, pageSize int) ([]*models.ReportExecution, int64, error) {
	var executions []*models.ReportExecution
	var total int64

	query := r.db.WithContext(ctx).Model(&models.ReportExecution{}).Where("template_id = ?", templateID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err := query.
		Preload("Template").
		Preload("Executor").
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&executions).Error

	return executions, total, err
}

// GetByUserID 根据用户ID获取执行记录
func (r *reportExecutionRepository) GetByUserID(ctx context.Context, userID uint, page, pageSize int) ([]*models.ReportExecution, int64, error) {
	var executions []*models.ReportExecution
	var total int64

	query := r.db.WithContext(ctx).Model(&models.ReportExecution{}).Where("executed_by = ?", userID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err := query.
		Preload("Template").
		Preload("Executor").
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&executions).Error

	return executions, total, err
}

// GetRunningExecutions 获取正在运行的执行记录
func (r *reportExecutionRepository) GetRunningExecutions(ctx context.Context) ([]*models.ReportExecution, error) {
	var executions []*models.ReportExecution
	err := r.db.WithContext(ctx).
		Where("status IN ?", []string{models.ExecutionStatusPending, models.ExecutionStatusRunning}).
		Preload("Template").
		Preload("Executor").
		Order("created_at ASC").
		Find(&executions).Error
	return executions, err
}

// GetRecentExecutions 获取最近的执行记录
func (r *reportExecutionRepository) GetRecentExecutions(ctx context.Context, limit int) ([]*models.ReportExecution, error) {
	var executions []*models.ReportExecution
	err := r.db.WithContext(ctx).
		Preload("Template").
		Preload("Executor").
		Order("created_at DESC").
		Limit(limit).
		Find(&executions).Error
	return executions, err
}
