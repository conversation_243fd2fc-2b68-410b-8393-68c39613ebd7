package repository

import (
	"database/sql"
	"db-monitor-platform/internal/models"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// BackupHistoryRepository 备份历史数据访问接口
type BackupHistoryRepository interface {
	// 基础CRUD操作
	Create(history *models.BackupHistory) error
	GetByID(id uint) (*models.BackupHistory, error)
	Update(history *models.BackupHistory) error
	Delete(id uint) error
	
	// 查询操作
	GetByTaskID(taskID uint, pagination *models.PaginationRequest) ([]models.BackupHistory, int64, error)
	List(req *models.BackupHistoryListRequest) ([]models.BackupHistory, int64, error)
	
	// 业务查询
	GetLatestByTaskID(taskID uint) (*models.BackupHistory, error)
	GetRunningBackups() ([]models.BackupHistory, error)
	GetRecentBackups(limit int) ([]models.BackupHistory, error)
	
	// 统计查询
	GetBackupStats(req *models.BackupStatsRequest) (*models.BackupStatsResponse, error)
	GetSuccessRate(taskID *uint, days int) (float64, error)
	GetBackupTrend(taskID *uint, days int) ([]BackupTrendData, error)
	
	// 清理操作
	DeleteOldBackups(retentionDays int) (int64, error)
	DeleteByTaskID(taskID uint) error
	
	// 数据库连接
	GetDB() *gorm.DB
}

// BackupTrendData 备份趋势数据
type BackupTrendData struct {
	Date         time.Time `json:"date"`
	SuccessCount int       `json:"success_count"`
	FailedCount  int       `json:"failed_count"`
	TotalSize    int64     `json:"total_size"`
}

// backupHistoryRepository 备份历史数据访问实现
type backupHistoryRepository struct {
	db *gorm.DB
}

// NewBackupHistoryRepository 创建备份历史数据访问实例
func NewBackupHistoryRepository(db *gorm.DB) BackupHistoryRepository {
	return &backupHistoryRepository{db: db}
}

// Create 创建备份历史记录
func (r *backupHistoryRepository) Create(history *models.BackupHistory) error {
	return r.db.Create(history).Error
}

// GetByID 根据ID获取备份历史记录
func (r *backupHistoryRepository) GetByID(id uint) (*models.BackupHistory, error) {
	var history models.BackupHistory
	err := r.db.Preload("Task").Preload("Task.Database").First(&history, id).Error
	if err != nil {
		return nil, err
	}
	return &history, nil
}

// Update 更新备份历史记录
func (r *backupHistoryRepository) Update(history *models.BackupHistory) error {
	return r.db.Save(history).Error
}

// Delete 删除备份历史记录（软删除）
func (r *backupHistoryRepository) Delete(id uint) error {
	return r.db.Delete(&models.BackupHistory{}, id).Error
}

// GetByTaskID 根据任务ID获取备份历史列表
func (r *backupHistoryRepository) GetByTaskID(taskID uint, pagination *models.PaginationRequest) ([]models.BackupHistory, int64, error) {
	var histories []models.BackupHistory
	var total int64
	
	query := r.db.Model(&models.BackupHistory{}).Where("task_id = ?", taskID)
	
	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := pagination.CalculateOffset()
	err := query.Preload("Task").
		Offset(offset).Limit(pagination.PageSize).
		Order("start_time DESC").
		Find(&histories).Error
	
	return histories, total, err
}

// List 根据条件查询备份历史列表
func (r *backupHistoryRepository) List(req *models.BackupHistoryListRequest) ([]models.BackupHistory, int64, error) {
	var histories []models.BackupHistory
	var total int64
	
	query := r.db.Model(&models.BackupHistory{})
	
	// 添加筛选条件
	if req.TaskID != nil {
		query = query.Where("task_id = ?", *req.TaskID)
	}
	
	if req.DatabaseID != nil {
		query = query.Joins("JOIN backup_tasks ON backup_histories.task_id = backup_tasks.id").
			Where("backup_tasks.database_id = ?", *req.DatabaseID)
	}
	
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}
	
	if req.StartDate != nil {
		query = query.Where("start_time >= ?", *req.StartDate)
	}
	
	if req.EndDate != nil {
		query = query.Where("start_time <= ?", *req.EndDate)
	}
	
	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := req.CalculateOffset()
	err := query.Preload("Task").Preload("Task.Database").
		Offset(offset).Limit(req.PageSize).
		Order("start_time DESC").
		Find(&histories).Error
	
	return histories, total, err
}

// GetLatestByTaskID 获取任务的最新备份历史
func (r *backupHistoryRepository) GetLatestByTaskID(taskID uint) (*models.BackupHistory, error) {
	var history models.BackupHistory
	err := r.db.Where("task_id = ?", taskID).
		Order("start_time DESC").
		First(&history).Error
	if err != nil {
		return nil, err
	}
	return &history, nil
}

// GetRunningBackups 获取正在运行的备份
func (r *backupHistoryRepository) GetRunningBackups() ([]models.BackupHistory, error) {
	var histories []models.BackupHistory
	err := r.db.Where("status = ?", "running").
		Preload("Task").Preload("Task.Database").
		Find(&histories).Error
	return histories, err
}

// GetRecentBackups 获取最近的备份记录
func (r *backupHistoryRepository) GetRecentBackups(limit int) ([]models.BackupHistory, error) {
	var histories []models.BackupHistory
	err := r.db.Preload("Task").Preload("Task.Database").
		Order("start_time DESC").
		Limit(limit).
		Find(&histories).Error
	return histories, err
}

// GetBackupStats 获取备份统计信息
func (r *backupHistoryRepository) GetBackupStats(req *models.BackupStatsRequest) (*models.BackupStatsResponse, error) {
	var stats models.BackupStatsResponse
	
	query := r.db.Model(&models.BackupHistory{})
	
	// 添加筛选条件
	if req.DatabaseID != nil {
		query = query.Joins("JOIN backup_tasks ON backup_histories.task_id = backup_tasks.id").
			Where("backup_tasks.database_id = ?", *req.DatabaseID)
	}
	
	if req.StartDate != nil {
		query = query.Where("start_time >= ?", *req.StartDate)
	}
	
	if req.EndDate != nil {
		query = query.Where("start_time <= ?", *req.EndDate)
	}
	
	// 总备份数
	var totalBackups int64
	if err := query.Count(&totalBackups).Error; err != nil {
		return nil, err
	}
	stats.TotalBackups = int(totalBackups)

	// 成功备份数
	var successfulBackups int64
	if err := query.Where("status = ?", "success").Count(&successfulBackups).Error; err != nil {
		return nil, err
	}
	stats.SuccessfulBackups = int(successfulBackups)

	// 失败备份数
	var failedBackups int64
	if err := query.Where("status = ?", "failed").Count(&failedBackups).Error; err != nil {
		return nil, err
	}
	stats.FailedBackups = int(failedBackups)
	
	// 计算成功率
	if stats.TotalBackups > 0 {
		stats.SuccessRate = float64(stats.SuccessfulBackups) / float64(stats.TotalBackups) * 100
	}
	
	// 总备份大小
	var totalSize sql.NullInt64
	if err := query.Select("SUM(backup_size)").Where("status = ?", "success").Scan(&totalSize).Error; err != nil {
		return nil, err
	}
	if totalSize.Valid {
		stats.TotalSize = totalSize.Int64
		stats.TotalSizeFormatted = formatSize(totalSize.Int64)
	}
	
	// 最后备份时间
	var lastBackup models.BackupHistory
	if err := query.Where("status = ?", "success").Order("start_time DESC").First(&lastBackup).Error; err == nil {
		stats.LastBackupTime = &lastBackup.StartTime
	}
	
	// 下次备份时间（从任务中获取）
	taskQuery := r.db.Model(&models.BackupTask{}).Where("status = ?", "active")
	if req.DatabaseID != nil {
		taskQuery = taskQuery.Where("database_id = ?", *req.DatabaseID)
	}
	
	var nextTask models.BackupTask
	if err := taskQuery.Where("next_backup IS NOT NULL").Order("next_backup ASC").First(&nextTask).Error; err == nil {
		stats.NextBackupTime = nextTask.NextBackup
	}
	
	return &stats, nil
}

// GetSuccessRate 获取成功率
func (r *backupHistoryRepository) GetSuccessRate(taskID *uint, days int) (float64, error) {
	query := r.db.Model(&models.BackupHistory{})
	
	if taskID != nil {
		query = query.Where("task_id = ?", *taskID)
	}
	
	if days > 0 {
		startDate := time.Now().AddDate(0, 0, -days)
		query = query.Where("start_time >= ?", startDate)
	}
	
	var total, success int64
	
	// 总数
	if err := query.Count(&total).Error; err != nil {
		return 0, err
	}
	
	// 成功数
	if err := query.Where("status = ?", "success").Count(&success).Error; err != nil {
		return 0, err
	}
	
	if total == 0 {
		return 0, nil
	}
	
	return float64(success) / float64(total) * 100, nil
}

// GetBackupTrend 获取备份趋势数据
func (r *backupHistoryRepository) GetBackupTrend(taskID *uint, days int) ([]BackupTrendData, error) {
	var trends []BackupTrendData
	
	query := `
		SELECT 
			DATE(start_time) as date,
			COUNT(CASE WHEN status = 'success' THEN 1 END) as success_count,
			COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
			COALESCE(SUM(CASE WHEN status = 'success' THEN backup_size ELSE 0 END), 0) as total_size
		FROM backup_histories 
		WHERE start_time >= ? 
	`
	
	args := []interface{}{time.Now().AddDate(0, 0, -days)}
	
	if taskID != nil {
		query += " AND task_id = ?"
		args = append(args, *taskID)
	}
	
	query += " GROUP BY DATE(start_time) ORDER BY date DESC"
	
	err := r.db.Raw(query, args...).Scan(&trends).Error
	return trends, err
}

// DeleteOldBackups 删除过期的备份记录
func (r *backupHistoryRepository) DeleteOldBackups(retentionDays int) (int64, error) {
	cutoffDate := time.Now().AddDate(0, 0, -retentionDays)
	result := r.db.Where("start_time < ?", cutoffDate).Delete(&models.BackupHistory{})
	return result.RowsAffected, result.Error
}

// DeleteByTaskID 删除指定任务的所有备份历史
func (r *backupHistoryRepository) DeleteByTaskID(taskID uint) error {
	return r.db.Where("task_id = ?", taskID).Delete(&models.BackupHistory{}).Error
}

// GetDB 获取数据库连接
func (r *backupHistoryRepository) GetDB() *gorm.DB {
	return r.db
}

// formatSize 格式化文件大小
func formatSize(size int64) string {
	if size == 0 {
		return "0 B"
	}
	
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}
