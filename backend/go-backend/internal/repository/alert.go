package repository

import (
	"time"

	"db-monitor-platform/internal/models"

	"gorm.io/gorm"
)

// AlertRepository 告警数据访问接口
type AlertRepository interface {
	// 告警规则相关
	CreateRule(rule *models.AlertRule) error
	GetRuleByID(id uint) (*models.AlertRule, error)
	GetRulesByUserID(userID uint, pagination *models.PaginationRequest) ([]models.AlertRule, int64, error)
	GetRulesByDatabaseID(databaseID uint) ([]models.AlertRule, error)
	GetAllRules(pagination *models.PaginationRequest) ([]models.AlertRule, int64, error)
	UpdateRule(rule *models.AlertRule) error
	DeleteRule(id uint) error
	GetActiveRules() ([]models.AlertRule, error)
	SearchRules(keyword string, pagination *models.PaginationRequest) ([]models.AlertRule, int64, error)

	// 告警事件相关
	CreateEvent(event *models.AlertEvent) error
	GetEventByID(id uint) (*models.AlertEvent, error)
	GetEventsByRuleID(ruleID uint, pagination *models.PaginationRequest) ([]models.AlertEvent, int64, error)
	GetEventsByDatabaseID(databaseID uint, pagination *models.PaginationRequest) ([]models.AlertEvent, int64, error)
	GetActiveEvents(pagination *models.PaginationRequest) ([]models.AlertEvent, int64, error)
	GetEventsByUserID(userID uint, pagination *models.PaginationRequest) ([]models.AlertEvent, int64, error)
	UpdateEvent(event *models.AlertEvent) error
	ResolveEvent(eventID uint, userID uint) error
	GetEventStats(userID uint) (*AlertStats, error)
	CleanupOldEvents(retentionDays int) error
}

// alertRepository 告警数据访问实现
type alertRepository struct {
	db *gorm.DB
}

// NewAlertRepository 创建告警数据访问实例
func NewAlertRepository(db *gorm.DB) AlertRepository {
	return &alertRepository{db: db}
}

// CreateRule 创建告警规则
func (r *alertRepository) CreateRule(rule *models.AlertRule) error {
	return r.db.Create(rule).Error
}

// GetRuleByID 根据ID获取告警规则
func (r *alertRepository) GetRuleByID(id uint) (*models.AlertRule, error) {
	var rule models.AlertRule
	err := r.db.Preload("Database").Preload("User").First(&rule, id).Error
	if err != nil {
		return nil, err
	}
	return &rule, nil
}

// GetRulesByUserID 根据用户ID获取告警规则
func (r *alertRepository) GetRulesByUserID(userID uint, pagination *models.PaginationRequest) ([]models.AlertRule, int64, error) {
	var rules []models.AlertRule
	var total int64

	// 获取总数
	query := r.db.Model(&models.AlertRule{}).Where("created_by = ?", userID)
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := pagination.CalculateOffset()
	err := query.Preload("Database").Preload("User").
		Offset(offset).
		Limit(pagination.PageSize).
		Order("created_at DESC").
		Find(&rules).Error
	if err != nil {
		return nil, 0, err
	}

	return rules, total, nil
}

// GetRulesByDatabaseID 根据数据库ID获取告警规则
func (r *alertRepository) GetRulesByDatabaseID(databaseID uint) ([]models.AlertRule, error) {
	var rules []models.AlertRule
	err := r.db.Where("database_id = ? AND enabled = ?", databaseID, true).
		Preload("Database").Preload("User").
		Find(&rules).Error
	return rules, err
}

// GetAllRules 获取所有告警规则（管理员用）
func (r *alertRepository) GetAllRules(pagination *models.PaginationRequest) ([]models.AlertRule, int64, error) {
	var rules []models.AlertRule
	var total int64

	// 获取总数
	if err := r.db.Model(&models.AlertRule{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := pagination.CalculateOffset()
	err := r.db.Preload("Database").Preload("User").
		Offset(offset).
		Limit(pagination.PageSize).
		Order("created_at DESC").
		Find(&rules).Error
	if err != nil {
		return nil, 0, err
	}

	return rules, total, nil
}

// UpdateRule 更新告警规则
func (r *alertRepository) UpdateRule(rule *models.AlertRule) error {
	return r.db.Save(rule).Error
}

// DeleteRule 删除告警规则
func (r *alertRepository) DeleteRule(id uint) error {
	return r.db.Delete(&models.AlertRule{}, id).Error
}

// GetActiveRules 获取所有启用的告警规则
func (r *alertRepository) GetActiveRules() ([]models.AlertRule, error) {
	var rules []models.AlertRule
	err := r.db.Where("enabled = ?", true).
		Preload("Database").
		Find(&rules).Error
	return rules, err
}

// SearchRules 搜索告警规则
func (r *alertRepository) SearchRules(keyword string, pagination *models.PaginationRequest) ([]models.AlertRule, int64, error) {
	var rules []models.AlertRule
	var total int64

	query := r.db.Model(&models.AlertRule{})
	
	if keyword != "" {
		searchPattern := "%" + keyword + "%"
		query = query.Where("name ILIKE ? OR description ILIKE ?", searchPattern, searchPattern)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := pagination.CalculateOffset()
	err := query.Preload("Database").Preload("User").
		Offset(offset).
		Limit(pagination.PageSize).
		Order("created_at DESC").
		Find(&rules).Error
	if err != nil {
		return nil, 0, err
	}

	return rules, total, nil
}

// CreateEvent 创建告警事件
func (r *alertRepository) CreateEvent(event *models.AlertEvent) error {
	return r.db.Create(event).Error
}

// GetEventByID 根据ID获取告警事件
func (r *alertRepository) GetEventByID(id uint) (*models.AlertEvent, error) {
	var event models.AlertEvent
	err := r.db.Preload("Database").Preload("Rule").Preload("Resolver").First(&event, id).Error
	if err != nil {
		return nil, err
	}
	return &event, nil
}

// GetEventsByRuleID 根据规则ID获取告警事件
func (r *alertRepository) GetEventsByRuleID(ruleID uint, pagination *models.PaginationRequest) ([]models.AlertEvent, int64, error) {
	var events []models.AlertEvent
	var total int64

	// 获取总数
	query := r.db.Model(&models.AlertEvent{}).Where("alert_rule_id = ?", ruleID)
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := pagination.CalculateOffset()
	err := query.Preload("Database").Preload("Rule").Preload("Resolver").
		Offset(offset).
		Limit(pagination.PageSize).
		Order("start_time DESC").
		Find(&events).Error
	if err != nil {
		return nil, 0, err
	}

	return events, total, nil
}

// GetEventsByDatabaseID 根据数据库ID获取告警事件
func (r *alertRepository) GetEventsByDatabaseID(databaseID uint, pagination *models.PaginationRequest) ([]models.AlertEvent, int64, error) {
	var events []models.AlertEvent
	var total int64

	// 获取总数
	query := r.db.Model(&models.AlertEvent{}).Where("database_id = ?", databaseID)
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := pagination.CalculateOffset()
	err := query.Preload("Database").Preload("Rule").Preload("Resolver").
		Offset(offset).
		Limit(pagination.PageSize).
		Order("start_time DESC").
		Find(&events).Error
	if err != nil {
		return nil, 0, err
	}

	return events, total, nil
}

// GetActiveEvents 获取活跃的告警事件
func (r *alertRepository) GetActiveEvents(pagination *models.PaginationRequest) ([]models.AlertEvent, int64, error) {
	var events []models.AlertEvent
	var total int64

	// 获取总数
	query := r.db.Model(&models.AlertEvent{}).Where("status = ?", "active")
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := pagination.CalculateOffset()
	err := query.Preload("Database").Preload("Rule").
		Offset(offset).
		Limit(pagination.PageSize).
		Order("start_time DESC").
		Find(&events).Error
	if err != nil {
		return nil, 0, err
	}

	return events, total, nil
}

// GetEventsByUserID 根据用户ID获取告警事件
func (r *alertRepository) GetEventsByUserID(userID uint, pagination *models.PaginationRequest) ([]models.AlertEvent, int64, error) {
	var events []models.AlertEvent
	var total int64

	// 通过告警规则关联查询用户的告警事件
	query := r.db.Model(&models.AlertEvent{}).
		Joins("JOIN alert_rules ON alert_events.alert_rule_id = alert_rules.id").
		Where("alert_rules.created_by = ?", userID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := pagination.CalculateOffset()
	err := query.Preload("Database").Preload("Rule").Preload("Resolver").
		Offset(offset).
		Limit(pagination.PageSize).
		Order("alert_events.start_time DESC").
		Find(&events).Error
	if err != nil {
		return nil, 0, err
	}

	return events, total, nil
}

// UpdateEvent 更新告警事件
func (r *alertRepository) UpdateEvent(event *models.AlertEvent) error {
	return r.db.Save(event).Error
}

// ResolveEvent 解决告警事件
func (r *alertRepository) ResolveEvent(eventID uint, userID uint) error {
	now := time.Now()
	return r.db.Model(&models.AlertEvent{}).
		Where("id = ? AND status = ?", eventID, "active").
		Updates(map[string]interface{}{
			"status":      "resolved",
			"resolved_by": userID,
			"resolved_at": now,
			"end_time":    now,
		}).Error
}

// GetEventStats 获取告警事件统计
func (r *alertRepository) GetEventStats(userID uint) (*AlertStats, error) {
	var stats AlertStats

	baseQuery := r.db.Model(&models.AlertEvent{})
	if userID > 0 {
		baseQuery = baseQuery.Joins("JOIN alert_rules ON alert_events.alert_rule_id = alert_rules.id").
			Where("alert_rules.created_by = ?", userID)
	}

	// 总告警数
	if err := baseQuery.Count(&stats.TotalEvents).Error; err != nil {
		return nil, err
	}

	// 活跃告警数
	activeQuery := baseQuery
	if userID > 0 {
		activeQuery = r.db.Model(&models.AlertEvent{}).
			Joins("JOIN alert_rules ON alert_events.alert_rule_id = alert_rules.id").
			Where("alert_rules.created_by = ? AND alert_events.status = ?", userID, "active")
	} else {
		activeQuery = baseQuery.Where("status = ?", "active")
	}
	if err := activeQuery.Count(&stats.ActiveEvents).Error; err != nil {
		return nil, err
	}

	// 已解决告警数
	resolvedQuery := baseQuery
	if userID > 0 {
		resolvedQuery = r.db.Model(&models.AlertEvent{}).
			Joins("JOIN alert_rules ON alert_events.alert_rule_id = alert_rules.id").
			Where("alert_rules.created_by = ? AND alert_events.status = ?", userID, "resolved")
	} else {
		resolvedQuery = baseQuery.Where("status = ?", "resolved")
	}
	if err := resolvedQuery.Count(&stats.ResolvedEvents).Error; err != nil {
		return nil, err
	}

	// 按严重级别统计
	var severityStats []SeverityStat
	severityQuery := baseQuery.Select("alert_events.severity, COUNT(*) as count").Group("alert_events.severity")
	if err := severityQuery.Scan(&severityStats).Error; err != nil {
		return nil, err
	}
	stats.SeverityStats = severityStats

	// 今日新增告警
	today := time.Now().Truncate(24 * time.Hour)
	todayQuery := baseQuery.Where("start_time >= ?", today)
	if err := todayQuery.Count(&stats.TodayEvents).Error; err != nil {
		return nil, err
	}

	return &stats, nil
}

// CleanupOldEvents 清理过期的告警事件
func (r *alertRepository) CleanupOldEvents(retentionDays int) error {
	cutoffTime := time.Now().AddDate(0, 0, -retentionDays)
	return r.db.Where("start_time < ? AND status = ?", cutoffTime, "resolved").
		Delete(&models.AlertEvent{}).Error
}

// AlertStats 告警统计信息
type AlertStats struct {
	TotalEvents    int64           `json:"total_events"`
	ActiveEvents   int64           `json:"active_events"`
	ResolvedEvents int64           `json:"resolved_events"`
	TodayEvents    int64           `json:"today_events"`
	SeverityStats  []SeverityStat  `json:"severity_stats"`
}

// SeverityStat 严重级别统计
type SeverityStat struct {
	Severity string `json:"severity"`
	Count    int64  `json:"count"`
}
