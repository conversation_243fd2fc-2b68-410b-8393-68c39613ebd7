package repository

import (
	"database/sql"
	"db-monitor-platform/internal/models"

	"gorm.io/gorm"
)

// BackupTaskRepository 备份任务数据访问接口
type BackupTaskRepository interface {
	// 基础CRUD操作
	Create(task *models.BackupTask) error
	GetByID(id uint) (*models.BackupTask, error)
	Update(task *models.BackupTask) error
	Delete(id uint) error
	
	// 查询操作
	GetByDatabaseID(databaseID uint, pagination *models.PaginationRequest) ([]models.BackupTask, int64, error)
	GetByUserID(userID uint, pagination *models.PaginationRequest) ([]models.BackupTask, int64, error)
	List(req *models.BackupTaskListRequest) ([]models.BackupTask, int64, error)
	
	// 业务查询
	GetActiveTasks() ([]models.BackupTask, error)
	GetTasksNeedingBackup() ([]models.BackupTask, error)
	GetTasksByStatus(status string) ([]models.BackupTask, error)
	
	// 统计查询
	GetTaskStats(userID *uint) (*BackupTaskStats, error)
	GetTasksByDatabase(databaseID uint) ([]models.BackupTask, error)
	
	// 权限检查
	CheckTaskOwnership(taskID, userID uint) (bool, error)
	
	// 数据库连接
	GetDB() *gorm.DB
}

// BackupTaskStats 备份任务统计
type BackupTaskStats struct {
	TotalTasks   int64 `json:"total_tasks"`
	ActiveTasks  int64 `json:"active_tasks"`
	PausedTasks  int64 `json:"paused_tasks"`
	FailedTasks  int64 `json:"failed_tasks"`
	TotalSize    int64 `json:"total_size"`
}

// backupTaskRepository 备份任务数据访问实现
type backupTaskRepository struct {
	db *gorm.DB
}

// NewBackupTaskRepository 创建备份任务数据访问实例
func NewBackupTaskRepository(db *gorm.DB) BackupTaskRepository {
	return &backupTaskRepository{db: db}
}

// Create 创建备份任务
func (r *backupTaskRepository) Create(task *models.BackupTask) error {
	return r.db.Create(task).Error
}

// GetByID 根据ID获取备份任务
func (r *backupTaskRepository) GetByID(id uint) (*models.BackupTask, error) {
	var task models.BackupTask
	err := r.db.Preload("Database").Preload("User").First(&task, id).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// Update 更新备份任务
func (r *backupTaskRepository) Update(task *models.BackupTask) error {
	return r.db.Save(task).Error
}

// Delete 删除备份任务（软删除）
func (r *backupTaskRepository) Delete(id uint) error {
	return r.db.Delete(&models.BackupTask{}, id).Error
}

// GetByDatabaseID 根据数据库ID获取备份任务列表
func (r *backupTaskRepository) GetByDatabaseID(databaseID uint, pagination *models.PaginationRequest) ([]models.BackupTask, int64, error) {
	var tasks []models.BackupTask
	var total int64
	
	query := r.db.Model(&models.BackupTask{}).Where("database_id = ?", databaseID)
	
	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := pagination.CalculateOffset()
	err := query.Preload("Database").Preload("User").
		Offset(offset).Limit(pagination.PageSize).
		Order("created_at DESC").
		Find(&tasks).Error
	
	return tasks, total, err
}

// GetByUserID 根据用户ID获取备份任务列表
func (r *backupTaskRepository) GetByUserID(userID uint, pagination *models.PaginationRequest) ([]models.BackupTask, int64, error) {
	var tasks []models.BackupTask
	var total int64
	
	query := r.db.Model(&models.BackupTask{}).Where("created_by = ?", userID)
	
	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := pagination.CalculateOffset()
	err := query.Preload("Database").Preload("User").
		Offset(offset).Limit(pagination.PageSize).
		Order("created_at DESC").
		Find(&tasks).Error
	
	return tasks, total, err
}

// List 根据条件查询备份任务列表
func (r *backupTaskRepository) List(req *models.BackupTaskListRequest) ([]models.BackupTask, int64, error) {
	var tasks []models.BackupTask
	var total int64
	
	query := r.db.Model(&models.BackupTask{})
	
	// 添加筛选条件
	if req.DatabaseID != nil {
		query = query.Where("database_id = ?", *req.DatabaseID)
	}
	
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}
	
	if req.BackupType != nil {
		query = query.Where("backup_type = ?", *req.BackupType)
	}
	
	if req.Search != nil && *req.Search != "" {
		query = query.Where("task_name ILIKE ?", "%"+*req.Search+"%")
	}
	
	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := req.CalculateOffset()
	err := query.Preload("Database").Preload("User").
		Offset(offset).Limit(req.PageSize).
		Order("created_at DESC").
		Find(&tasks).Error
	
	return tasks, total, err
}

// GetActiveTasks 获取所有活跃的备份任务
func (r *backupTaskRepository) GetActiveTasks() ([]models.BackupTask, error) {
	var tasks []models.BackupTask
	err := r.db.Where("status = ?", "active").
		Preload("Database").
		Find(&tasks).Error
	return tasks, err
}

// GetTasksNeedingBackup 获取需要执行备份的任务
func (r *backupTaskRepository) GetTasksNeedingBackup() ([]models.BackupTask, error) {
	var tasks []models.BackupTask
	err := r.db.Where("status = ? AND (next_backup IS NULL OR next_backup <= NOW())", "active").
		Preload("Database").
		Find(&tasks).Error
	return tasks, err
}

// GetTasksByStatus 根据状态获取备份任务
func (r *backupTaskRepository) GetTasksByStatus(status string) ([]models.BackupTask, error) {
	var tasks []models.BackupTask
	err := r.db.Where("status = ?", status).
		Preload("Database").Preload("User").
		Find(&tasks).Error
	return tasks, err
}

// GetTaskStats 获取备份任务统计信息
func (r *backupTaskRepository) GetTaskStats(userID *uint) (*BackupTaskStats, error) {
	var stats BackupTaskStats
	
	query := r.db.Model(&models.BackupTask{})
	if userID != nil {
		query = query.Where("created_by = ?", *userID)
	}
	
	// 总任务数
	if err := query.Count(&stats.TotalTasks).Error; err != nil {
		return nil, err
	}
	
	// 活跃任务数
	if err := query.Where("status = ?", "active").Count(&stats.ActiveTasks).Error; err != nil {
		return nil, err
	}
	
	// 暂停任务数
	if err := query.Where("status = ?", "paused").Count(&stats.PausedTasks).Error; err != nil {
		return nil, err
	}
	
	// 失败任务数
	if err := query.Where("status = ?", "failed").Count(&stats.FailedTasks).Error; err != nil {
		return nil, err
	}
	
	// 总备份大小
	var totalSize sql.NullInt64
	if err := query.Select("SUM(backup_size)").Scan(&totalSize).Error; err != nil {
		return nil, err
	}
	if totalSize.Valid {
		stats.TotalSize = totalSize.Int64
	}
	
	return &stats, nil
}

// GetTasksByDatabase 获取指定数据库的所有备份任务
func (r *backupTaskRepository) GetTasksByDatabase(databaseID uint) ([]models.BackupTask, error) {
	var tasks []models.BackupTask
	err := r.db.Where("database_id = ?", databaseID).
		Preload("User").
		Order("created_at DESC").
		Find(&tasks).Error
	return tasks, err
}

// CheckTaskOwnership 检查任务所有权
func (r *backupTaskRepository) CheckTaskOwnership(taskID, userID uint) (bool, error) {
	var count int64
	err := r.db.Model(&models.BackupTask{}).
		Where("id = ? AND created_by = ?", taskID, userID).
		Count(&count).Error
	return count > 0, err
}

// GetDB 获取数据库连接
func (r *backupTaskRepository) GetDB() *gorm.DB {
	return r.db
}
