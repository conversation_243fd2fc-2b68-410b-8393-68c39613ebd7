package repository

import (
	"time"

	"db-monitor-platform/internal/models"

	"gorm.io/gorm"
)

// MetricRepository 监控指标数据访问接口
type MetricRepository interface {
	Create(metric *models.Metric) error
	CreateBatch(metrics []models.Metric) error
	GetByDatabaseID(databaseID uint, pagination *models.PaginationRequest) ([]models.Metric, int64, error)
	GetByTimeRange(databaseID uint, metricType string, startTime, endTime time.Time) ([]models.Metric, error)
	GetLatestMetrics(databaseID uint) ([]models.Metric, error)
	GetAggregatedMetrics(req *models.MetricQueryRequest) (*models.MetricAggregateResponse, error)
	GetRealtimeMetrics(databaseIDs []uint) ([]models.RealtimeMetric, error)
	DeleteOldMetrics(retentionDays int) error
	GetMetricTypes(databaseID uint) ([]string, error)
	GetMetricStats(databaseID uint, metricType string, duration time.Duration) (*MetricStats, error)
}

// metricRepository 监控指标数据访问实现
type metricRepository struct {
	db *gorm.DB
}

// NewMetricRepository 创建监控指标数据访问实例
func NewMetricRepository(db *gorm.DB) MetricRepository {
	return &metricRepository{db: db}
}

// Create 创建监控指标
func (r *metricRepository) Create(metric *models.Metric) error {
	return r.db.Create(metric).Error
}

// CreateBatch 批量创建监控指标
func (r *metricRepository) CreateBatch(metrics []models.Metric) error {
	if len(metrics) == 0 {
		return nil
	}
	return r.db.CreateInBatches(metrics, 100).Error
}

// GetByDatabaseID 根据数据库ID获取监控指标
func (r *metricRepository) GetByDatabaseID(databaseID uint, pagination *models.PaginationRequest) ([]models.Metric, int64, error) {
	var metrics []models.Metric
	var total int64

	// 获取总数
	query := r.db.Model(&models.Metric{}).Where("database_id = ?", databaseID)
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := pagination.CalculateOffset()
	err := query.Offset(offset).
		Limit(pagination.PageSize).
		Order("timestamp DESC").
		Find(&metrics).Error
	if err != nil {
		return nil, 0, err
	}

	return metrics, total, nil
}

// GetByTimeRange 根据时间范围获取监控指标
func (r *metricRepository) GetByTimeRange(databaseID uint, metricType string, startTime, endTime time.Time) ([]models.Metric, error) {
	var metrics []models.Metric
	
	query := r.db.Where("database_id = ? AND timestamp BETWEEN ? AND ?", databaseID, startTime, endTime)
	
	if metricType != "" {
		query = query.Where("metric_type = ?", metricType)
	}
	
	err := query.Order("timestamp ASC").Find(&metrics).Error
	return metrics, err
}

// GetLatestMetrics 获取最新的监控指标
func (r *metricRepository) GetLatestMetrics(databaseID uint) ([]models.Metric, error) {
	var metrics []models.Metric
	
	// 获取每种指标类型的最新值
	err := r.db.Raw(`
		SELECT DISTINCT ON (metric_type) *
		FROM metrics 
		WHERE database_id = ? 
		ORDER BY metric_type, timestamp DESC
	`, databaseID).Scan(&metrics).Error
	
	return metrics, err
}

// GetAggregatedMetrics 获取聚合监控指标
func (r *metricRepository) GetAggregatedMetrics(req *models.MetricQueryRequest) (*models.MetricAggregateResponse, error) {
	var dataPoints []models.MetricDataPoint
	
	// 根据时间间隔确定聚合粒度
	interval := r.getAggregationInterval(req.Interval, req.StartTime, req.EndTime)
	
	query := `
		SELECT 
			date_trunc(?, timestamp) as timestamp,
			AVG(value) as avg,
			MIN(value) as min,
			MAX(value) as max,
			COUNT(*) as count
		FROM metrics 
		WHERE database_id = ? 
		AND timestamp BETWEEN ? AND ?
	`
	
	args := []interface{}{interval, req.DatabaseID, req.StartTime, req.EndTime}
	
	if req.MetricType != "" {
		query += " AND metric_type = ?"
		args = append(args, req.MetricType)
	}
	
	query += " GROUP BY date_trunc(?, timestamp) ORDER BY timestamp"
	args = append(args, interval)
	
	err := r.db.Raw(query, args...).Scan(&dataPoints).Error
	if err != nil {
		return nil, err
	}
	
	// 计算摘要信息
	summary := r.calculateSummary(dataPoints, req.StartTime, req.EndTime)
	
	// 获取单位
	unit := models.GetMetricUnit(req.MetricType)
	
	return &models.MetricAggregateResponse{
		MetricType: req.MetricType,
		Unit:       unit,
		DataPoints: dataPoints,
		Summary:    summary,
	}, nil
}

// GetRealtimeMetrics 获取实时监控指标
func (r *metricRepository) GetRealtimeMetrics(databaseIDs []uint) ([]models.RealtimeMetric, error) {
	var results []models.RealtimeMetric
	
	for _, dbID := range databaseIDs {
		// 获取数据库信息
		var database models.DatabaseInstance
		if err := r.db.First(&database, dbID).Error; err != nil {
			continue
		}
		
		// 获取最新指标
		latestMetrics, err := r.GetLatestMetrics(dbID)
		if err != nil {
			continue
		}
		
		// 构建实时指标
		realtimeMetric := models.RealtimeMetric{
			DatabaseID:   dbID,
			DatabaseName: database.Name,
			Status:       database.Status,
			Metrics:      make(map[string]models.MetricValue),
			Timestamp:    time.Now(),
		}
		
		for _, metric := range latestMetrics {
			status := r.getMetricStatus(metric.MetricType, metric.Value)
			realtimeMetric.Metrics[metric.MetricType] = models.MetricValue{
				Value:  metric.Value,
				Unit:   metric.Unit,
				Status: status,
			}
			
			// 更新时间戳为最新指标时间
			if metric.Timestamp.After(realtimeMetric.Timestamp) {
				realtimeMetric.Timestamp = metric.Timestamp
			}
		}
		
		results = append(results, realtimeMetric)
	}
	
	return results, nil
}

// DeleteOldMetrics 删除过期的监控指标
func (r *metricRepository) DeleteOldMetrics(retentionDays int) error {
	cutoffTime := time.Now().AddDate(0, 0, -retentionDays)
	return r.db.Where("timestamp < ?", cutoffTime).Delete(&models.Metric{}).Error
}

// GetMetricTypes 获取数据库的所有指标类型
func (r *metricRepository) GetMetricTypes(databaseID uint) ([]string, error) {
	var metricTypes []string
	err := r.db.Model(&models.Metric{}).
		Where("database_id = ?", databaseID).
		Distinct("metric_type").
		Pluck("metric_type", &metricTypes).Error
	return metricTypes, err
}

// GetMetricStats 获取指标统计信息
func (r *metricRepository) GetMetricStats(databaseID uint, metricType string, duration time.Duration) (*MetricStats, error) {
	var stats MetricStats
	
	startTime := time.Now().Add(-duration)
	
	err := r.db.Model(&models.Metric{}).
		Where("database_id = ? AND metric_type = ? AND timestamp >= ?", databaseID, metricType, startTime).
		Select(`
			AVG(value) as avg_value,
			MIN(value) as min_value,
			MAX(value) as max_value,
			COUNT(*) as data_points
		`).
		Scan(&stats).Error
	
	if err != nil {
		return nil, err
	}
	
	// 获取最新值
	var latestMetric models.Metric
	err = r.db.Where("database_id = ? AND metric_type = ?", databaseID, metricType).
		Order("timestamp DESC").
		First(&latestMetric).Error
	
	if err == nil {
		stats.CurrentValue = latestMetric.Value
		stats.LastUpdated = latestMetric.Timestamp
	}
	
	return &stats, nil
}

// getAggregationInterval 根据查询参数确定聚合间隔
func (r *metricRepository) getAggregationInterval(interval string, startTime, endTime time.Time) string {
	if interval != "" {
		switch interval {
		case "1m":
			return "minute"
		case "5m":
			return "5 minutes"
		case "15m":
			return "15 minutes"
		case "30m":
			return "30 minutes"
		case "1h":
			return "hour"
		case "6h":
			return "6 hours"
		case "12h":
			return "12 hours"
		case "1d":
			return "day"
		}
	}
	
	// 根据时间范围自动确定间隔
	duration := endTime.Sub(startTime)
	switch {
	case duration <= time.Hour:
		return "minute"
	case duration <= 6*time.Hour:
		return "5 minutes"
	case duration <= 24*time.Hour:
		return "15 minutes"
	case duration <= 7*24*time.Hour:
		return "hour"
	default:
		return "day"
	}
}

// calculateSummary 计算摘要信息
func (r *metricRepository) calculateSummary(dataPoints []models.MetricDataPoint, startTime, endTime time.Time) models.MetricSummary {
	if len(dataPoints) == 0 {
		return models.MetricSummary{
			StartTime: startTime,
			EndTime:   endTime,
		}
	}
	
	summary := models.MetricSummary{
		Min:       dataPoints[0].Min,
		Max:       dataPoints[0].Max,
		Count:     0,
		StartTime: startTime,
		EndTime:   endTime,
	}
	
	var totalAvg float64
	for _, point := range dataPoints {
		if point.Min < summary.Min {
			summary.Min = point.Min
		}
		if point.Max > summary.Max {
			summary.Max = point.Max
		}
		totalAvg += point.Avg
		summary.Count += point.Count
	}
	
	summary.Avg = totalAvg / float64(len(dataPoints))
	
	// 最新值
	if len(dataPoints) > 0 {
		summary.Current = dataPoints[len(dataPoints)-1].Avg
	}
	
	return summary
}

// getMetricStatus 根据指标值确定状态
func (r *metricRepository) getMetricStatus(metricType string, value float64) string {
	// 这里可以根据预设的阈值来判断状态
	// 为了简化，使用固定的阈值
	switch metricType {
	case "cpu", "memory", "disk":
		if value >= 90 {
			return "critical"
		} else if value >= 80 {
			return "warning"
		}
		return "normal"
	case "connections":
		if value >= 900 {
			return "critical"
		} else if value >= 700 {
			return "warning"
		}
		return "normal"
	default:
		return "normal"
	}
}

// MetricStats 指标统计信息
type MetricStats struct {
	AvgValue     float64   `json:"avg_value"`
	MinValue     float64   `json:"min_value"`
	MaxValue     float64   `json:"max_value"`
	CurrentValue float64   `json:"current_value"`
	DataPoints   int64     `json:"data_points"`
	LastUpdated  time.Time `json:"last_updated"`
}
