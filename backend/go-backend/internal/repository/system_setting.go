package repository

import (
	"context"
	"strings"

	"db-monitor-platform/internal/models"

	"gorm.io/gorm"
)

// SystemSettingRepository 系统设置仓储接口
type SystemSettingRepository interface {
	Create(ctx context.Context, setting *models.SystemSetting) error
	GetByID(ctx context.Context, id uint) (*models.SystemSetting, error)
	GetByCategoryAndKey(ctx context.Context, category, key string) (*models.SystemSetting, error)
	GetByCategory(ctx context.Context, category string, isPublic *bool) ([]*models.SystemSetting, error)
	GetList(ctx context.Context, req *models.GetSystemSettingsRequest) ([]*models.SystemSetting, error)
	Update(ctx context.Context, setting *models.SystemSetting) error
	Delete(ctx context.Context, id uint) error
	BatchUpdate(ctx context.Context, settings []*models.SystemSetting) error
	GetPublicSettings(ctx context.Context) ([]*models.SystemSetting, error)
	InitializeDefaults(ctx context.Context, userID uint) error
}

type systemSettingRepository struct {
	db *gorm.DB
}

// NewSystemSettingRepository 创建系统设置仓储实例
func NewSystemSettingRepository(db *gorm.DB) SystemSettingRepository {
	return &systemSettingRepository{db: db}
}

// Create 创建系统设置
func (r *systemSettingRepository) Create(ctx context.Context, setting *models.SystemSetting) error {
	return r.db.WithContext(ctx).Create(setting).Error
}

// GetByID 根据ID获取系统设置
func (r *systemSettingRepository) GetByID(ctx context.Context, id uint) (*models.SystemSetting, error) {
	var setting models.SystemSetting
	err := r.db.WithContext(ctx).
		Preload("Updater").
		First(&setting, id).Error
	if err != nil {
		return nil, err
	}
	return &setting, nil
}

// GetByCategoryAndKey 根据分类和键获取系统设置
func (r *systemSettingRepository) GetByCategoryAndKey(ctx context.Context, category, key string) (*models.SystemSetting, error) {
	var setting models.SystemSetting
	err := r.db.WithContext(ctx).
		Where("category = ? AND key = ?", category, key).
		Preload("Updater").
		First(&setting).Error
	if err != nil {
		return nil, err
	}
	return &setting, nil
}

// GetByCategory 根据分类获取系统设置
func (r *systemSettingRepository) GetByCategory(ctx context.Context, category string, isPublic *bool) ([]*models.SystemSetting, error) {
	var settings []*models.SystemSetting
	query := r.db.WithContext(ctx).Where("category = ?", category)
	
	if isPublic != nil {
		query = query.Where("is_public = ?", *isPublic)
	}
	
	err := query.
		Preload("Updater").
		Order("key ASC").
		Find(&settings).Error
	return settings, err
}

// GetList 获取系统设置列表
func (r *systemSettingRepository) GetList(ctx context.Context, req *models.GetSystemSettingsRequest) ([]*models.SystemSetting, error) {
	var settings []*models.SystemSetting
	query := r.db.WithContext(ctx).Model(&models.SystemSetting{})

	// 添加过滤条件
	if req.Category != "" {
		query = query.Where("category = ?", req.Category)
	}

	if req.Public != nil {
		query = query.Where("is_public = ?", *req.Public)
	}

	if req.Search != "" {
		searchPattern := "%" + strings.ToLower(req.Search) + "%"
		query = query.Where("LOWER(key) LIKE ? OR LOWER(description) LIKE ?", searchPattern, searchPattern)
	}

	err := query.
		Preload("Updater").
		Order("category ASC, key ASC").
		Find(&settings).Error
	return settings, err
}

// Update 更新系统设置
func (r *systemSettingRepository) Update(ctx context.Context, setting *models.SystemSetting) error {
	return r.db.WithContext(ctx).Save(setting).Error
}

// Delete 删除系统设置
func (r *systemSettingRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.SystemSetting{}, id).Error
}

// BatchUpdate 批量更新系统设置
func (r *systemSettingRepository) BatchUpdate(ctx context.Context, settings []*models.SystemSetting) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, setting := range settings {
			if err := tx.Save(setting).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// GetPublicSettings 获取公开的系统设置
func (r *systemSettingRepository) GetPublicSettings(ctx context.Context) ([]*models.SystemSetting, error) {
	var settings []*models.SystemSetting
	err := r.db.WithContext(ctx).
		Where("is_public = ?", true).
		Order("category ASC, key ASC").
		Find(&settings).Error
	return settings, err
}

// InitializeDefaults 初始化默认系统设置
func (r *systemSettingRepository) InitializeDefaults(ctx context.Context, userID uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, defaultSetting := range models.DefaultSystemSettings {
			// 检查设置是否已存在
			var count int64
			err := tx.Model(&models.SystemSetting{}).
				Where("category = ? AND key = ?", defaultSetting.Category, defaultSetting.Key).
				Count(&count).Error
			if err != nil {
				return err
			}

			// 如果不存在则创建
			if count == 0 {
				setting := models.SystemSetting{
					Category:    defaultSetting.Category,
					Key:         defaultSetting.Key,
					Value:       defaultSetting.Value,
					ValueType:   defaultSetting.ValueType,
					Description: defaultSetting.Description,
					IsPublic:    defaultSetting.IsPublic,
					UpdatedBy:   userID,
				}
				if err := tx.Create(&setting).Error; err != nil {
					return err
				}
			}
		}
		return nil
	})
}

// UserPreferenceRepository 用户偏好仓储接口
type UserPreferenceRepository interface {
	Create(ctx context.Context, preference *models.UserPreference) error
	GetByID(ctx context.Context, id uint) (*models.UserPreference, error)
	GetByUserCategoryAndKey(ctx context.Context, userID uint, category, key string) (*models.UserPreference, error)
	GetByUserAndCategory(ctx context.Context, userID uint, category string) ([]*models.UserPreference, error)
	GetByUser(ctx context.Context, userID uint, req *models.GetUserPreferencesRequest) ([]*models.UserPreference, error)
	Update(ctx context.Context, preference *models.UserPreference) error
	Delete(ctx context.Context, id uint) error
	BatchUpdate(ctx context.Context, userID uint, preferences []*models.UserPreference) error
	InitializeDefaults(ctx context.Context, userID uint) error
}

type userPreferenceRepository struct {
	db *gorm.DB
}

// NewUserPreferenceRepository 创建用户偏好仓储实例
func NewUserPreferenceRepository(db *gorm.DB) UserPreferenceRepository {
	return &userPreferenceRepository{db: db}
}

// Create 创建用户偏好
func (r *userPreferenceRepository) Create(ctx context.Context, preference *models.UserPreference) error {
	return r.db.WithContext(ctx).Create(preference).Error
}

// GetByID 根据ID获取用户偏好
func (r *userPreferenceRepository) GetByID(ctx context.Context, id uint) (*models.UserPreference, error) {
	var preference models.UserPreference
	err := r.db.WithContext(ctx).
		Preload("User").
		First(&preference, id).Error
	if err != nil {
		return nil, err
	}
	return &preference, nil
}

// GetByUserCategoryAndKey 根据用户、分类和键获取偏好
func (r *userPreferenceRepository) GetByUserCategoryAndKey(ctx context.Context, userID uint, category, key string) (*models.UserPreference, error) {
	var preference models.UserPreference
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND category = ? AND key = ?", userID, category, key).
		First(&preference).Error
	if err != nil {
		return nil, err
	}
	return &preference, nil
}

// GetByUserAndCategory 根据用户和分类获取偏好
func (r *userPreferenceRepository) GetByUserAndCategory(ctx context.Context, userID uint, category string) ([]*models.UserPreference, error) {
	var preferences []*models.UserPreference
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND category = ?", userID, category).
		Order("key ASC").
		Find(&preferences).Error
	return preferences, err
}

// GetByUser 根据用户获取偏好列表
func (r *userPreferenceRepository) GetByUser(ctx context.Context, userID uint, req *models.GetUserPreferencesRequest) ([]*models.UserPreference, error) {
	var preferences []*models.UserPreference
	query := r.db.WithContext(ctx).Where("user_id = ?", userID)

	if req.Category != "" {
		query = query.Where("category = ?", req.Category)
	}

	if req.Search != "" {
		searchPattern := "%" + strings.ToLower(req.Search) + "%"
		query = query.Where("LOWER(key) LIKE ?", searchPattern)
	}

	err := query.
		Order("category ASC, key ASC").
		Find(&preferences).Error
	return preferences, err
}

// Update 更新用户偏好
func (r *userPreferenceRepository) Update(ctx context.Context, preference *models.UserPreference) error {
	return r.db.WithContext(ctx).Save(preference).Error
}

// Delete 删除用户偏好
func (r *userPreferenceRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.UserPreference{}, id).Error
}

// BatchUpdate 批量更新用户偏好
func (r *userPreferenceRepository) BatchUpdate(ctx context.Context, userID uint, preferences []*models.UserPreference) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, preference := range preferences {
			preference.UserID = userID
			
			// 尝试更新现有记录
			var existing models.UserPreference
			err := tx.Where("user_id = ? AND category = ? AND key = ?", 
				userID, preference.Category, preference.Key).First(&existing).Error
			
			if err == gorm.ErrRecordNotFound {
				// 记录不存在，创建新记录
				if err := tx.Create(preference).Error; err != nil {
					return err
				}
			} else if err != nil {
				return err
			} else {
				// 记录存在，更新
				existing.Value = preference.Value
				existing.ValueType = preference.ValueType
				if err := tx.Save(&existing).Error; err != nil {
					return err
				}
			}
		}
		return nil
	})
}

// InitializeDefaults 初始化默认用户偏好
func (r *userPreferenceRepository) InitializeDefaults(ctx context.Context, userID uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, defaultPref := range models.DefaultUserPreferences {
			// 检查偏好是否已存在
			var count int64
			err := tx.Model(&models.UserPreference{}).
				Where("user_id = ? AND category = ? AND key = ?", userID, defaultPref.Category, defaultPref.Key).
				Count(&count).Error
			if err != nil {
				return err
			}

			// 如果不存在则创建
			if count == 0 {
				preference := models.UserPreference{
					UserID:    userID,
					Category:  defaultPref.Category,
					Key:       defaultPref.Key,
					Value:     defaultPref.Value,
					ValueType: defaultPref.ValueType,
				}
				if err := tx.Create(&preference).Error; err != nil {
					return err
				}
			}
		}
		return nil
	})
}
