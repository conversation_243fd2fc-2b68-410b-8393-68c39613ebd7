package repository

import (
	"context"
	"strings"

	"db-monitor-platform/internal/models"

	"gorm.io/gorm"
)

// ReportTemplateRepository 报表模板仓储接口
type ReportTemplateRepository interface {
	Create(ctx context.Context, template *models.ReportTemplate) error
	GetByID(ctx context.Context, id uint) (*models.ReportTemplate, error)
	GetList(ctx context.Context, req *models.GetReportTemplatesRequest) ([]*models.ReportTemplate, int64, error)
	Update(ctx context.Context, template *models.ReportTemplate) error
	Delete(ctx context.Context, id uint) error
	GetByUserID(ctx context.Context, userID uint, page, pageSize int) ([]*models.ReportTemplate, int64, error)
	GetByType(ctx context.Context, templateType string) ([]*models.ReportTemplate, error)
	GetActiveTemplates(ctx context.Context) ([]*models.ReportTemplate, error)
}

type reportTemplateRepository struct {
	db *gorm.DB
}

// NewReportTemplateRepository 创建报表模板仓储实例
func NewReportTemplateRepository(db *gorm.DB) ReportTemplateRepository {
	return &reportTemplateRepository{db: db}
}

// Create 创建报表模板
func (r *reportTemplateRepository) Create(ctx context.Context, template *models.ReportTemplate) error {
	return r.db.WithContext(ctx).Create(template).Error
}

// GetByID 根据ID获取报表模板
func (r *reportTemplateRepository) GetByID(ctx context.Context, id uint) (*models.ReportTemplate, error) {
	var template models.ReportTemplate
	err := r.db.WithContext(ctx).
		Preload("Creator").
		First(&template, id).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

// GetList 获取报表模板列表
func (r *reportTemplateRepository) GetList(ctx context.Context, req *models.GetReportTemplatesRequest) ([]*models.ReportTemplate, int64, error) {
	var templates []*models.ReportTemplate
	var total int64

	// 构建查询
	query := r.db.WithContext(ctx).Model(&models.ReportTemplate{})

	// 添加过滤条件
	if req.Type != "" {
		query = query.Where("type = ?", req.Type)
	}

	if req.IsActive != nil {
		query = query.Where("is_active = ?", *req.IsActive)
	}

	if req.Search != "" {
		searchPattern := "%" + strings.ToLower(req.Search) + "%"
		query = query.Where("LOWER(name) LIKE ? OR LOWER(description) LIKE ?", searchPattern, searchPattern)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	err := query.
		Preload("Creator").
		Order("created_at DESC").
		Offset(offset).
		Limit(req.PageSize).
		Find(&templates).Error

	return templates, total, err
}

// Update 更新报表模板
func (r *reportTemplateRepository) Update(ctx context.Context, template *models.ReportTemplate) error {
	return r.db.WithContext(ctx).Save(template).Error
}

// Delete 删除报表模板
func (r *reportTemplateRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.ReportTemplate{}, id).Error
}

// GetByUserID 根据用户ID获取报表模板
func (r *reportTemplateRepository) GetByUserID(ctx context.Context, userID uint, page, pageSize int) ([]*models.ReportTemplate, int64, error) {
	var templates []*models.ReportTemplate
	var total int64

	query := r.db.WithContext(ctx).Model(&models.ReportTemplate{}).Where("created_by = ?", userID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err := query.
		Preload("Creator").
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&templates).Error

	return templates, total, err
}

// GetByType 根据类型获取报表模板
func (r *reportTemplateRepository) GetByType(ctx context.Context, templateType string) ([]*models.ReportTemplate, error) {
	var templates []*models.ReportTemplate
	err := r.db.WithContext(ctx).
		Where("type = ? AND is_active = ?", templateType, true).
		Preload("Creator").
		Order("created_at DESC").
		Find(&templates).Error
	return templates, err
}

// GetActiveTemplates 获取所有活跃的报表模板
func (r *reportTemplateRepository) GetActiveTemplates(ctx context.Context) ([]*models.ReportTemplate, error) {
	var templates []*models.ReportTemplate
	err := r.db.WithContext(ctx).
		Where("is_active = ?", true).
		Preload("Creator").
		Order("type ASC, created_at DESC").
		Find(&templates).Error
	return templates, err
}
