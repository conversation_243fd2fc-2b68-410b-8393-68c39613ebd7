package repository

import (
	"db-monitor-platform/internal/models"

	"gorm.io/gorm"
)

// UserRepository 用户数据访问接口
type UserRepository interface {
	Create(user *models.User) error
	GetByID(id uint) (*models.User, error)
	GetByEmail(email string) (*models.User, error)
	Update(user *models.User) error
	Delete(id uint) error
	List(pagination *models.PaginationRequest) ([]models.User, int64, error)
	GetActiveUsers() ([]models.User, error)
	GetUsersByRole(role string) ([]models.User, error)
}

// userRepository 用户数据访问实现
type userRepository struct {
	db *gorm.DB
}

// NewUserRepository 创建用户数据访问实例
func NewUserRepository(db *gorm.DB) UserRepository {
	return &userRepository{db: db}
}

// Create 创建用户
func (r *userRepository) Create(user *models.User) error {
	return r.db.Create(user).Error
}

// GetByID 根据ID获取用户
func (r *userRepository) GetByID(id uint) (*models.User, error) {
	var user models.User
	err := r.db.First(&user, id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetByEmail 根据邮箱获取用户
func (r *userRepository) GetByEmail(email string) (*models.User, error) {
	var user models.User
	err := r.db.Where("email = ?", email).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// Update 更新用户
func (r *userRepository) Update(user *models.User) error {
	return r.db.Save(user).Error
}

// Delete 删除用户（软删除）
func (r *userRepository) Delete(id uint) error {
	return r.db.Delete(&models.User{}, id).Error
}

// List 获取用户列表（分页）
func (r *userRepository) List(pagination *models.PaginationRequest) ([]models.User, int64, error) {
	var users []models.User
	var total int64

	// 获取总数
	if err := r.db.Model(&models.User{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := pagination.CalculateOffset()
	err := r.db.Offset(offset).Limit(pagination.PageSize).Find(&users).Error
	if err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// GetActiveUsers 获取活跃用户
func (r *userRepository) GetActiveUsers() ([]models.User, error) {
	var users []models.User
	err := r.db.Where("is_active = ?", true).Find(&users).Error
	return users, err
}

// GetUsersByRole 根据角色获取用户
func (r *userRepository) GetUsersByRole(role string) ([]models.User, error) {
	var users []models.User
	err := r.db.Where("role = ?", role).Find(&users).Error
	return users, err
}

// GetUsersWithStats 获取用户及其统计信息
func (r *userRepository) GetUsersWithStats(pagination *models.PaginationRequest) ([]UserWithStats, int64, error) {
	var results []UserWithStats
	var total int64

	// 获取总数
	if err := r.db.Model(&models.User{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询用户及其统计信息
	offset := pagination.CalculateOffset()
	err := r.db.Table("users").
		Select(`
			users.*,
			COALESCE(db_count.count, 0) as database_count,
			COALESCE(alert_count.count, 0) as alert_rule_count
		`).
		Joins(`
			LEFT JOIN (
				SELECT created_by, COUNT(*) as count 
				FROM database_instances 
				WHERE deleted_at IS NULL 
				GROUP BY created_by
			) db_count ON users.id = db_count.created_by
		`).
		Joins(`
			LEFT JOIN (
				SELECT created_by, COUNT(*) as count 
				FROM alert_rules 
				WHERE deleted_at IS NULL 
				GROUP BY created_by
			) alert_count ON users.id = alert_count.created_by
		`).
		Offset(offset).
		Limit(pagination.PageSize).
		Scan(&results).Error

	return results, total, err
}

// UserWithStats 用户及其统计信息
type UserWithStats struct {
	models.User
	DatabaseCount   int `json:"database_count"`
	AlertRuleCount  int `json:"alert_rule_count"`
}

// SearchUsers 搜索用户
func (r *userRepository) SearchUsers(keyword string, pagination *models.PaginationRequest) ([]models.User, int64, error) {
	var users []models.User
	var total int64

	query := r.db.Model(&models.User{})
	
	if keyword != "" {
		searchPattern := "%" + keyword + "%"
		query = query.Where("name ILIKE ? OR email ILIKE ?", searchPattern, searchPattern)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := pagination.CalculateOffset()
	err := query.Offset(offset).Limit(pagination.PageSize).Find(&users).Error
	if err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// UpdateLastLogin 更新最后登录时间
func (r *userRepository) UpdateLastLogin(userID uint) error {
	return r.db.Model(&models.User{}).
		Where("id = ?", userID).
		Update("last_login", gorm.Expr("NOW()")).Error
}

// GetUserDashboardStats 获取用户仪表板统计
func (r *userRepository) GetUserDashboardStats(userID uint) (*UserDashboardStats, error) {
	var stats UserDashboardStats

	// 获取数据库实例数量
	err := r.db.Model(&models.DatabaseInstance{}).
		Where("created_by = ?", userID).
		Count(&stats.DatabaseCount).Error
	if err != nil {
		return nil, err
	}

	// 获取告警规则数量
	err = r.db.Model(&models.AlertRule{}).
		Where("created_by = ?", userID).
		Count(&stats.AlertRuleCount).Error
	if err != nil {
		return nil, err
	}

	// 获取活跃告警数量
	err = r.db.Model(&models.AlertEvent{}).
		Joins("JOIN alert_rules ON alert_events.alert_rule_id = alert_rules.id").
		Where("alert_rules.created_by = ? AND alert_events.status = ?", userID, "active").
		Count(&stats.ActiveAlertCount).Error
	if err != nil {
		return nil, err
	}

	return &stats, nil
}

// UserDashboardStats 用户仪表板统计
type UserDashboardStats struct {
	DatabaseCount    int64 `json:"database_count"`
	AlertRuleCount   int64 `json:"alert_rule_count"`
	ActiveAlertCount int64 `json:"active_alert_count"`
}
