package monitor

import (
	"database/sql"
	"fmt"
	"time"

	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/mem"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// RealMetricsCollector 真实监控数据收集器
type RealMetricsCollector struct {
	db     *gorm.DB
	dbType string
	host   string
	port   int
	dbName string
}

// ConnectionMetrics 连接指标
type ConnectionMetrics struct {
	ActiveConnections int     `json:"active_connections"`
	MaxConnections    int     `json:"max_connections"`
	ConnectionUsage   float64 `json:"connection_usage"` // 连接使用率百分比
	IdleConnections   int     `json:"idle_connections"`
}

// DatabaseInfo 数据库基础信息
type DatabaseInfo struct {
	Version      string        `json:"version"`
	Uptime       time.Duration `json:"uptime"`
	DatabaseSize int64         `json:"database_size"` // 字节
	TableCount   int           `json:"table_count"`
	IndexCount   int           `json:"index_count"`
}

// SystemMetrics 系统指标
type SystemMetrics struct {
	CPUUsage    float64 `json:"cpu_usage"`
	MemoryUsage float64 `json:"memory_usage"`
	Timestamp   time.Time `json:"timestamp"`
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	QPS              float64 `json:"qps"`               // 每秒查询数
	TPS              float64 `json:"tps"`               // 每秒事务数
	AvgResponseTime  float64 `json:"avg_response_time"` // 平均响应时间(ms)
	SlowQueryCount   int     `json:"slow_query_count"`  // 慢查询数量
	LockWaitCount    int     `json:"lock_wait_count"`   // 锁等待数量
}

// NewRealMetricsCollector 创建真实监控数据收集器
func NewRealMetricsCollector(db *gorm.DB, dbType, host string, port int, dbName string) *RealMetricsCollector {
	return &RealMetricsCollector{
		db:     db,
		dbType: dbType,
		host:   host,
		port:   port,
		dbName: dbName,
	}
}

// CollectConnectionMetrics 收集连接指标
func (c *RealMetricsCollector) CollectConnectionMetrics() (*ConnectionMetrics, error) {
	switch c.dbType {
	case "postgresql":
		return c.collectPostgreSQLConnections()
	case "mysql":
		return c.collectMySQLConnections()
	default:
		return nil, fmt.Errorf("unsupported database type: %s", c.dbType)
	}
}

// collectPostgreSQLConnections 收集PostgreSQL连接信息
func (c *RealMetricsCollector) collectPostgreSQLConnections() (*ConnectionMetrics, error) {
	var metrics ConnectionMetrics
	
	// 获取当前活跃连接数
	var activeConnections int
	err := c.db.Raw(`
		SELECT count(*) 
		FROM pg_stat_activity 
		WHERE state = 'active'
	`).Scan(&activeConnections).Error
	if err != nil {
		logrus.Errorf("Failed to get active connections: %v", err)
		return nil, err
	}

	// 获取总连接数和最大连接数
	var totalConnections, maxConnections int
	err = c.db.Raw(`
		SELECT 
			(SELECT count(*) FROM pg_stat_activity) as total_connections,
			(SELECT setting::int FROM pg_settings WHERE name = 'max_connections') as max_connections
	`).Row().Scan(&totalConnections, &maxConnections)
	if err != nil {
		logrus.Errorf("Failed to get connection stats: %v", err)
		return nil, err
	}

	// 获取空闲连接数
	var idleConnections int
	err = c.db.Raw(`
		SELECT count(*) 
		FROM pg_stat_activity 
		WHERE state = 'idle'
	`).Scan(&idleConnections).Error
	if err != nil {
		logrus.Errorf("Failed to get idle connections: %v", err)
		// 不是致命错误，继续执行
		idleConnections = totalConnections - activeConnections
	}

	metrics.ActiveConnections = activeConnections
	metrics.MaxConnections = maxConnections
	metrics.IdleConnections = idleConnections
	
	// 计算连接使用率
	if maxConnections > 0 {
		metrics.ConnectionUsage = float64(totalConnections) / float64(maxConnections) * 100
	}

	return &metrics, nil
}

// collectMySQLConnections 收集MySQL连接信息
func (c *RealMetricsCollector) collectMySQLConnections() (*ConnectionMetrics, error) {
	var metrics ConnectionMetrics
	
	// MySQL连接统计查询
	var activeConnections, maxConnections int
	err := c.db.Raw(`
		SELECT 
			(SELECT count(*) FROM information_schema.processlist WHERE command != 'Sleep') as active_connections,
			(SELECT @@max_connections) as max_connections
	`).Row().Scan(&activeConnections, &maxConnections)
	if err != nil {
		logrus.Errorf("Failed to get MySQL connection stats: %v", err)
		return nil, err
	}

	// 获取总连接数
	var totalConnections int
	err = c.db.Raw("SELECT count(*) FROM information_schema.processlist").Scan(&totalConnections).Error
	if err != nil {
		logrus.Errorf("Failed to get total connections: %v", err)
		return nil, err
	}

	metrics.ActiveConnections = activeConnections
	metrics.MaxConnections = maxConnections
	metrics.IdleConnections = totalConnections - activeConnections
	
	if maxConnections > 0 {
		metrics.ConnectionUsage = float64(totalConnections) / float64(maxConnections) * 100
	}

	return &metrics, nil
}

// CollectDatabaseInfo 收集数据库基础信息
func (c *RealMetricsCollector) CollectDatabaseInfo() (*DatabaseInfo, error) {
	switch c.dbType {
	case "postgresql":
		return c.collectPostgreSQLInfo()
	case "mysql":
		return c.collectMySQLInfo()
	default:
		return nil, fmt.Errorf("unsupported database type: %s", c.dbType)
	}
}

// collectPostgreSQLInfo 收集PostgreSQL基础信息
func (c *RealMetricsCollector) collectPostgreSQLInfo() (*DatabaseInfo, error) {
	var info DatabaseInfo
	
	// 获取版本信息
	var version string
	err := c.db.Raw("SELECT version()").Scan(&version).Error
	if err != nil {
		logrus.Errorf("Failed to get PostgreSQL version: %v", err)
		return nil, err
	}
	info.Version = version

	// 获取数据库运行时间
	var uptimeSeconds float64
	err = c.db.Raw(`
		SELECT EXTRACT(EPOCH FROM (now() - pg_postmaster_start_time()))
	`).Scan(&uptimeSeconds).Error
	if err != nil {
		logrus.Errorf("Failed to get uptime: %v", err)
	} else {
		info.Uptime = time.Duration(uptimeSeconds) * time.Second
	}

	// 获取数据库大小
	var dbSize int64
	err = c.db.Raw(`
		SELECT pg_database_size(current_database())
	`).Scan(&dbSize).Error
	if err != nil {
		logrus.Errorf("Failed to get database size: %v", err)
	} else {
		info.DatabaseSize = dbSize
	}

	// 获取表数量
	var tableCount int
	err = c.db.Raw(`
		SELECT count(*) 
		FROM information_schema.tables 
		WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
	`).Scan(&tableCount).Error
	if err != nil {
		logrus.Errorf("Failed to get table count: %v", err)
	} else {
		info.TableCount = tableCount
	}

	// 获取索引数量
	var indexCount int
	err = c.db.Raw(`
		SELECT count(*) 
		FROM pg_indexes 
		WHERE schemaname = 'public'
	`).Scan(&indexCount).Error
	if err != nil {
		logrus.Errorf("Failed to get index count: %v", err)
	} else {
		info.IndexCount = indexCount
	}

	return &info, nil
}

// collectMySQLInfo 收集MySQL基础信息
func (c *RealMetricsCollector) collectMySQLInfo() (*DatabaseInfo, error) {
	var info DatabaseInfo
	
	// 获取版本信息
	var version string
	err := c.db.Raw("SELECT @@version").Scan(&version).Error
	if err != nil {
		logrus.Errorf("Failed to get MySQL version: %v", err)
		return nil, err
	}
	info.Version = version

	// 获取运行时间
	var uptimeSeconds int64
	err = c.db.Raw("SHOW STATUS LIKE 'Uptime'").Row().Scan(nil, &uptimeSeconds)
	if err != nil {
		logrus.Errorf("Failed to get uptime: %v", err)
	} else {
		info.Uptime = time.Duration(uptimeSeconds) * time.Second
	}

	// 获取数据库大小
	var dbSize sql.NullInt64
	err = c.db.Raw(`
		SELECT SUM(data_length + index_length) 
		FROM information_schema.tables 
		WHERE table_schema = ?
	`, c.dbName).Scan(&dbSize).Error
	if err != nil {
		logrus.Errorf("Failed to get database size: %v", err)
	} else if dbSize.Valid {
		info.DatabaseSize = dbSize.Int64
	}

	// 获取表数量
	var tableCount int
	err = c.db.Raw(`
		SELECT count(*) 
		FROM information_schema.tables 
		WHERE table_schema = ? AND table_type = 'BASE TABLE'
	`, c.dbName).Scan(&tableCount).Error
	if err != nil {
		logrus.Errorf("Failed to get table count: %v", err)
	} else {
		info.TableCount = tableCount
	}

	// 获取索引数量
	var indexCount int
	err = c.db.Raw(`
		SELECT count(*) 
		FROM information_schema.statistics 
		WHERE table_schema = ?
	`, c.dbName).Scan(&indexCount).Error
	if err != nil {
		logrus.Errorf("Failed to get index count: %v", err)
	} else {
		info.IndexCount = indexCount
	}

	return &info, nil
}

// CollectSystemMetrics 收集系统指标
func (c *RealMetricsCollector) CollectSystemMetrics() (*SystemMetrics, error) {
	var metrics SystemMetrics
	metrics.Timestamp = time.Now()

	// 获取CPU使用率
	cpuPercent, err := cpu.Percent(time.Second, false)
	if err != nil {
		logrus.Errorf("Failed to get CPU usage: %v", err)
		return nil, err
	}
	if len(cpuPercent) > 0 {
		metrics.CPUUsage = cpuPercent[0]
	}

	// 获取内存使用率
	memInfo, err := mem.VirtualMemory()
	if err != nil {
		logrus.Errorf("Failed to get memory usage: %v", err)
		return nil, err
	}
	metrics.MemoryUsage = memInfo.UsedPercent

	return &metrics, nil
}

// TestConnection 测试数据库连接
func (c *RealMetricsCollector) TestConnection() error {
	sqlDB, err := c.db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %v", err)
	}

	// 测试连接
	err = sqlDB.Ping()
	if err != nil {
		return fmt.Errorf("database connection test failed: %v", err)
	}

	return nil
}
