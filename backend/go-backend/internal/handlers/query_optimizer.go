package handlers

import (
	"net/http"
	"strconv"

	"db-monitor-platform/internal/middleware"
	"db-monitor-platform/internal/models"
	"db-monitor-platform/internal/services"
	"db-monitor-platform/internal/utils"
	"db-monitor-platform/pkg/logger"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// QueryOptimizerHandler 查询优化工具处理器
type QueryOptimizerHandler struct {
	queryOptimizerService *services.QueryOptimizerService
}

// NewQueryOptimizerHandler 创建查询优化工具处理器
func NewQueryOptimizerHandler(db *gorm.DB) *QueryOptimizerHandler {
	return &QueryOptimizerHandler{
		queryOptimizerService: services.NewQueryOptimizerService(db),
	}
}

// AnalyzeQuery 分析SQL查询
// @Summary 分析SQL查询性能
// @Description 对提供的SQL查询进行性能分析，生成执行计划、优化建议和索引推荐
// @Tags query-optimizer
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.QueryAnalysisRequest true "查询分析请求"
// @Success 200 {object} models.CommonResponse{data=models.QueryAnalysisResponse} "分析成功"
// @Failure 400 {object} models.CommonResponse "请求参数错误"
// @Failure 401 {object} models.CommonResponse "未认证"
// @Failure 403 {object} models.CommonResponse "权限不足"
// @Failure 422 {object} models.CommonResponse "SQL语法错误"
// @Failure 500 {object} models.CommonResponse "服务器内部错误"
// @Router /api/v1/query-optimizer/analyze [post]
func (h *QueryOptimizerHandler) AnalyzeQuery(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	var req models.QueryAnalysisRequest
	
	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind request: %v", err)
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid request data"))
		return
	}

	// 验证请求数据
	if validationErrors := utils.FormatValidationErrors(utils.ValidateStruct(&req)); len(validationErrors) > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Validation failed",
			"errors":  validationErrors,
		})
		return
	}

	// 分析SQL查询
	result, err := h.queryOptimizerService.AnalyzeQuery(currentUser.ID, currentUser.Role, &req)
	if err != nil {
		logger.Errorf("Failed to analyze query: %v", err)
		
		switch err.Error() {
		case "database not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Database"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		case "invalid sql syntax":
			c.JSON(http.StatusUnprocessableEntity, models.ErrorResponse(422, "SQL syntax error"))
			return
		case "database connection failed":
			c.JSON(http.StatusServiceUnavailable, models.ErrorResponse(503, "Database connection failed"))
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to analyze query"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// GetAnalysis 获取查询分析结果
// @Summary 获取查询分析结果
// @Description 根据ID获取特定的查询分析结果
// @Tags query-optimizer
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "分析ID"
// @Success 200 {object} models.CommonResponse{data=models.QueryAnalysisResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/query-optimizer/analyze/{id} [get]
func (h *QueryOptimizerHandler) GetAnalysis(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析分析ID
	analysisID, err := parseUintParam(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid analysis ID"))
		return
	}

	// 获取分析结果
	result, err := h.queryOptimizerService.GetAnalysis(currentUser.ID, analysisID, currentUser.Role)
	if err != nil {
		logger.Errorf("Failed to get analysis: %v", err)
		
		switch err.Error() {
		case "analysis not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Analysis"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get analysis"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// GetAnalysisHistory 获取查询分析历史
// @Summary 获取查询分析历史
// @Description 获取用户的查询分析历史记录，支持分页和筛选
// @Tags query-optimizer
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param database_id query int false "数据库ID"
// @Param query_type query string false "查询类型" Enums(select,insert,update,delete)
// @Param start_date query string false "开始日期" format(date)
// @Param end_date query string false "结束日期" format(date)
// @Param status query string false "状态" Enums(pending,running,completed,failed)
// @Success 200 {object} models.CommonResponse{data=models.PaginationResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/query-optimizer/analyze [get]
func (h *QueryOptimizerHandler) GetAnalysisHistory(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析查询参数
	params := parseAnalysisHistoryParams(c)

	// 获取分析历史
	result, err := h.queryOptimizerService.GetAnalysisHistory(currentUser.ID, currentUser.Role, params)
	if err != nil {
		logger.Errorf("Failed to get analysis history: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get analysis history"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// ExplainQuery 获取查询执行计划
// @Summary 获取查询执行计划
// @Description 获取SQL查询的详细执行计划
// @Tags query-optimizer
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.ExecutionPlanRequest true "执行计划请求"
// @Success 200 {object} models.CommonResponse{data=models.ExecutionPlanResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 422 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/query-optimizer/explain [post]
func (h *QueryOptimizerHandler) ExplainQuery(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	var req models.ExecutionPlanRequest
	
	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind request: %v", err)
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid request data"))
		return
	}

	// 验证请求数据
	if validationErrors := utils.FormatValidationErrors(utils.ValidateStruct(&req)); len(validationErrors) > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Validation failed",
			"errors":  validationErrors,
		})
		return
	}

	// 获取执行计划
	result, err := h.queryOptimizerService.ExplainQuery(currentUser.ID, currentUser.Role, &req)
	if err != nil {
		logger.Errorf("Failed to explain query: %v", err)
		
		switch err.Error() {
		case "database not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Database"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		case "invalid sql syntax":
			c.JSON(http.StatusUnprocessableEntity, models.ErrorResponse(422, "SQL syntax error"))
			return
		case "database connection failed":
			c.JSON(http.StatusServiceUnavailable, models.ErrorResponse(503, "Database connection failed"))
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to explain query"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// GetSupportedDatabases 获取支持的数据库类型
// @Summary 获取支持的数据库类型
// @Description 获取查询优化工具支持的数据库类型列表
// @Tags query-optimizer
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.CommonResponse{data=[]models.SupportedDatabase}
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/query-optimizer/databases [get]
func (h *QueryOptimizerHandler) GetSupportedDatabases(c *gin.Context) {
	// 获取当前用户信息
	_, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 获取支持的数据库类型
	result := h.queryOptimizerService.GetSupportedDatabases()
	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// GetIndexSuggestions 获取索引建议
// @Summary 获取索引建议
// @Description 基于查询模式生成索引建议
// @Tags query-optimizer
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.IndexSuggestionRequest true "索引建议请求"
// @Success 200 {object} models.CommonResponse{data=[]models.IndexRecommendationResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/query-optimizer/index-suggestions [post]
func (h *QueryOptimizerHandler) GetIndexSuggestions(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	var req models.IndexSuggestionRequest

	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind request: %v", err)
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid request data"))
		return
	}

	// 验证请求数据
	if validationErrors := utils.FormatValidationErrors(utils.ValidateStruct(&req)); len(validationErrors) > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Validation failed",
			"errors":  validationErrors,
		})
		return
	}

	// 获取索引建议
	result, err := h.queryOptimizerService.GetIndexSuggestions(currentUser.ID, currentUser.Role, &req)
	if err != nil {
		logger.Errorf("Failed to get index suggestions: %v", err)

		switch err.Error() {
		case "database not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Database"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		case "table not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Table"))
			return
		}

		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get index suggestions"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// GetIndexRecommendations 获取索引推荐
// @Summary 获取索引推荐
// @Description 获取特定分析的索引推荐
// @Tags query-optimizer
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param analysis_id path int true "分析ID"
// @Success 200 {object} models.CommonResponse{data=[]models.IndexRecommendationResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/query-optimizer/index-suggestions/{analysis_id} [get]
func (h *QueryOptimizerHandler) GetIndexRecommendations(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析分析ID
	analysisID, err := parseUintParam(c, "analysis_id")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid analysis ID"))
		return
	}

	// 获取索引推荐
	result, err := h.queryOptimizerService.GetIndexRecommendations(currentUser.ID, analysisID, currentUser.Role)
	if err != nil {
		logger.Errorf("Failed to get index recommendations: %v", err)

		switch err.Error() {
		case "analysis not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Analysis"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}

		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get index recommendations"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// GetOptimizationSuggestions 获取优化建议
// @Summary 获取优化建议
// @Description 获取特定分析的优化建议
// @Tags query-optimizer
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param analysis_id path int true "分析ID"
// @Success 200 {object} models.CommonResponse{data=[]models.OptimizationSuggestionResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/query-optimizer/suggestions/{analysis_id} [get]
func (h *QueryOptimizerHandler) GetOptimizationSuggestions(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析分析ID
	analysisID, err := parseUintParam(c, "analysis_id")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid analysis ID"))
		return
	}

	// 获取优化建议
	result, err := h.queryOptimizerService.GetOptimizationSuggestions(currentUser.ID, analysisID, currentUser.Role)
	if err != nil {
		logger.Errorf("Failed to get optimization suggestions: %v", err)

		switch err.Error() {
		case "analysis not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Analysis"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}

		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get optimization suggestions"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// GetExecutionPlan 获取执行计划详情
// @Summary 获取执行计划详情
// @Description 根据ID获取特定的执行计划详情
// @Tags query-optimizer
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "执行计划ID"
// @Success 200 {object} models.CommonResponse{data=models.ExecutionPlanResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/query-optimizer/explain/{id} [get]
func (h *QueryOptimizerHandler) GetExecutionPlan(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析执行计划ID
	planID, err := parseUintParam(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid execution plan ID"))
		return
	}

	// 获取执行计划
	result, err := h.queryOptimizerService.GetExecutionPlan(currentUser.ID, planID, currentUser.Role)
	if err != nil {
		logger.Errorf("Failed to get execution plan: %v", err)

		switch err.Error() {
		case "execution plan not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Execution Plan"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}

		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get execution plan"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// GetQueryStats 获取查询统计信息
// @Summary 获取查询统计信息
// @Description 获取查询优化的统计信息，包括分析次数、性能改进等
// @Tags query-optimizer
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param database_id query int false "数据库ID"
// @Param period query string false "统计周期" default(7d) Enums(1d,7d,30d,90d)
// @Param group_by query string false "分组方式" default(day) Enums(hour,day,week)
// @Success 200 {object} models.CommonResponse{data=models.QueryStatsResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/query-optimizer/stats [get]
func (h *QueryOptimizerHandler) GetQueryStats(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析查询参数
	params := parseQueryStatsParams(c)

	// 获取查询统计
	result, err := h.queryOptimizerService.GetQueryStats(currentUser.ID, currentUser.Role, params)
	if err != nil {
		logger.Errorf("Failed to get query stats: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get query stats"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// 辅助函数

// parseAnalysisHistoryParams 解析分析历史查询参数
func parseAnalysisHistoryParams(c *gin.Context) *models.AnalysisHistoryParams {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	databaseID, _ := strconv.ParseUint(c.Query("database_id"), 10, 32)

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	return &models.AnalysisHistoryParams{
		Page:       page,
		PageSize:   pageSize,
		DatabaseID: uint(databaseID),
		QueryType:  c.Query("query_type"),
		StartDate:  c.Query("start_date"),
		EndDate:    c.Query("end_date"),
		Status:     c.Query("status"),
	}
}

// parseQueryStatsParams 解析查询统计参数
func parseQueryStatsParams(c *gin.Context) *models.QueryStatsParams {
	databaseID, _ := strconv.ParseUint(c.Query("database_id"), 10, 32)

	return &models.QueryStatsParams{
		DatabaseID: uint(databaseID),
		Period:     c.DefaultQuery("period", "7d"),
		GroupBy:    c.DefaultQuery("group_by", "day"),
	}
}


