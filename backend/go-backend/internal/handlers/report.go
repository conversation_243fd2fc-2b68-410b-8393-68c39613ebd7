package handlers

import (
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strconv"

	"db-monitor-platform/internal/models"
	"db-monitor-platform/internal/services"
	"db-monitor-platform/internal/utils"
	"db-monitor-platform/pkg/logger"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// ReportHandler 报表处理器
type ReportHandler struct {
	reportService services.ReportService
}

// NewReportHandler 创建报表处理器实例
func NewReportHandler(db *gorm.DB) *ReportHandler {
	return &ReportHandler{
		reportService: services.NewReportService(db),
	}
}

// @Summary 创建报表模板
// @Description 创建新的报表模板
// @Tags Reports
// @Accept json
// @Produce json
// @Param request body models.CreateReportTemplateRequest true "创建报表模板请求"
// @Success 201 {object} models.CommonResponse{data=models.ReportTemplate}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/reports/templates [post]
// @Security BearerAuth
func (h *ReportHandler) CreateTemplate(c *gin.Context) {
	userID := c.GetUint("user_id")
	
	var req models.CreateReportTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind request", "error", err)
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse(err.Error()))
		return
	}

	template, err := h.reportService.CreateTemplate(c.Request.Context(), &req, userID)
	if err != nil {
		logger.Error("Failed to create template", "error", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, err.Error()))
		return
	}

	c.JSON(http.StatusCreated, models.SuccessResponse(template))
}

// @Summary 获取报表模板列表
// @Description 获取报表模板列表，支持分页和过滤
// @Tags Reports
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Param type query string false "报表类型" Enums(performance,usage,alert)
// @Param search query string false "搜索关键词"
// @Param is_active query bool false "是否启用"
// @Success 200 {object} models.CommonResponse{data=models.PaginationResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/reports/templates [get]
// @Security BearerAuth
func (h *ReportHandler) GetTemplates(c *gin.Context) {
	userID := c.GetUint("user_id")
	
	var req models.GetReportTemplatesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query", "error", err)
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse(err.Error()))
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	result, err := h.reportService.GetTemplates(c.Request.Context(), &req, userID)
	if err != nil {
		logger.Error("Failed to get templates", "error", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// @Summary 获取报表模板详情
// @Description 根据ID获取报表模板详情
// @Tags Reports
// @Accept json
// @Produce json
// @Param id path int true "模板ID"
// @Success 200 {object} models.CommonResponse{data=models.ReportTemplate}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/reports/templates/{id} [get]
// @Security BearerAuth
func (h *ReportHandler) GetTemplate(c *gin.Context) {
	userID := c.GetUint("user_id")
	
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse("Invalid template ID"))
		return
	}

	template, err := h.reportService.GetTemplateByID(c.Request.Context(), uint(id), userID)
	if err != nil {
		if err.Error() == "template not found" {
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Template"))
			return
		}
		logger.Error("Failed to get template", "error", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(template))
}

// @Summary 更新报表模板
// @Description 更新报表模板信息
// @Tags Reports
// @Accept json
// @Produce json
// @Param id path int true "模板ID"
// @Param request body models.UpdateReportTemplateRequest true "更新报表模板请求"
// @Success 200 {object} models.CommonResponse
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/reports/templates/{id} [put]
// @Security BearerAuth
func (h *ReportHandler) UpdateTemplate(c *gin.Context) {
	userID := c.GetUint("user_id")
	
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse("Invalid template ID"))
		return
	}

	var req models.UpdateReportTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind request", "error", err)
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse(err.Error()))
		return
	}

	err = h.reportService.UpdateTemplate(c.Request.Context(), uint(id), &req, userID)
	if err != nil {
		if err.Error() == "template not found" {
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Template"))
			return
		}
		if err.Error() == "permission denied" {
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		logger.Error("Failed to update template", "error", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(nil))
}

// @Summary 删除报表模板
// @Description 删除报表模板
// @Tags Reports
// @Accept json
// @Produce json
// @Param id path int true "模板ID"
// @Success 200 {object} models.CommonResponse
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/reports/templates/{id} [delete]
// @Security BearerAuth
func (h *ReportHandler) DeleteTemplate(c *gin.Context) {
	userID := c.GetUint("user_id")
	
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse("Invalid template ID"))
		return
	}

	err = h.reportService.DeleteTemplate(c.Request.Context(), uint(id), userID)
	if err != nil {
		if err.Error() == "template not found" {
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Template"))
			return
		}
		if err.Error() == "permission denied" {
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		logger.Error("Failed to delete template", "error", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(nil))
}

// @Summary 执行报表生成
// @Description 执行报表生成任务
// @Tags Reports
// @Accept json
// @Produce json
// @Param request body models.ExecuteReportRequest true "执行报表请求"
// @Success 201 {object} models.CommonResponse{data=models.ReportExecution}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/reports/execute [post]
// @Security BearerAuth
func (h *ReportHandler) ExecuteReport(c *gin.Context) {
	userID := c.GetUint("user_id")
	
	var req models.ExecuteReportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind request", "error", err)
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse(err.Error()))
		return
	}

	execution, err := h.reportService.ExecuteReport(c.Request.Context(), &req, userID)
	if err != nil {
		logger.Error("Failed to execute report", "error", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, err.Error()))
		return
	}

	c.JSON(http.StatusCreated, models.SuccessResponse(execution))
}

// @Summary 获取报表执行状态
// @Description 获取报表执行状态和结果
// @Tags Reports
// @Accept json
// @Produce json
// @Param id path int true "执行ID"
// @Success 200 {object} models.CommonResponse{data=models.ReportExecution}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/reports/executions/{id} [get]
// @Security BearerAuth
func (h *ReportHandler) GetExecution(c *gin.Context) {
	userID := c.GetUint("user_id")
	
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse("Invalid execution ID"))
		return
	}

	execution, err := h.reportService.GetExecutionStatus(c.Request.Context(), uint(id), userID)
	if err != nil {
		if err.Error() == "execution not found" {
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Execution"))
			return
		}
		if err.Error() == "permission denied" {
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		logger.Error("Failed to get execution", "error", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(execution))
}

// @Summary 获取报表执行记录列表
// @Description 获取报表执行记录列表，支持分页和过滤
// @Tags Reports
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Param template_id query int false "模板ID"
// @Param status query string false "执行状态" Enums(pending,running,completed,failed)
// @Param executed_by query int false "执行者ID"
// @Success 200 {object} models.CommonResponse{data=models.PaginationResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/reports/executions [get]
// @Security BearerAuth
func (h *ReportHandler) GetExecutions(c *gin.Context) {
	userID := c.GetUint("user_id")
	
	var req models.GetReportExecutionsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query", "error", err)
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse(err.Error()))
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	result, err := h.reportService.GetExecutions(c.Request.Context(), &req, userID)
	if err != nil {
		logger.Error("Failed to get executions", "error", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// @Summary 下载报表文件
// @Description 下载已生成的报表文件
// @Tags Reports
// @Param id path int true "执行ID"
// @Success 200 {file} file "报表文件"
// @Failure 400 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 410 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/reports/executions/{id}/download [get]
// @Security BearerAuth
func (h *ReportHandler) DownloadReport(c *gin.Context) {
	userID := c.GetUint("user_id")

	// 解析执行ID
	executionIDStr := c.Param("id")
	executionID, err := strconv.ParseUint(executionIDStr, 10, 32)
	if err != nil {
		logger.Error("Invalid execution ID", "id", executionIDStr, "error", err)
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse("Invalid execution ID"))
		return
	}

	// 获取执行记录
	execution, err := h.reportService.GetExecutionStatus(c.Request.Context(), uint(executionID), userID)
	if err != nil {
		logger.Error("Failed to get execution", "execution_id", executionID, "error", err)
		if err.Error() == "execution not found" {
			c.JSON(http.StatusNotFound, models.ErrorResponse(404, "Execution not found"))
		} else if err.Error() == "permission denied" {
			c.JSON(http.StatusForbidden, models.ErrorResponse(403, "Permission denied"))
		} else {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, err.Error()))
		}
		return
	}

	// 检查执行状态
	if execution.Status != models.ExecutionStatusCompleted {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, fmt.Sprintf("Report is not ready. Current status: %s", execution.Status)))
		return
	}

	// 检查文件路径
	if execution.FilePath == "" {
		c.JSON(http.StatusNotFound, models.ErrorResponse(404, "Report file not found"))
		return
	}

	// 验证文件路径安全性
	basePath := utils.GetReportStoragePath()
	if err := utils.ValidateFilePath(execution.FilePath, basePath); err != nil {
		logger.Error("Invalid file path", "path", execution.FilePath, "error", err)
		c.JSON(http.StatusForbidden, models.ErrorResponse(403, "Invalid file path"))
		return
	}

	// 检查文件是否存在
	if _, err := os.Stat(execution.FilePath); os.IsNotExist(err) {
		logger.Error("Report file not found", "path", execution.FilePath)
		c.JSON(http.StatusGone, models.ErrorResponse(410, "Report file has expired or been removed"))
		return
	}

	// 获取文件信息
	fileInfo, err := os.Stat(execution.FilePath)
	if err != nil {
		logger.Error("Failed to get file info", "path", execution.FilePath, "error", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to access file"))
		return
	}

	// 生成文件名
	filename := h.generateDownloadFilename(execution)

	// 设置响应头
	c.Header("Content-Type", "application/octet-stream")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	c.Header("Content-Length", strconv.FormatInt(fileInfo.Size(), 10))
	c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
	c.Header("Pragma", "no-cache")
	c.Header("Expires", "0")

	// 记录下载日志
	logger.Info("Report downloaded",
		"execution_id", executionID,
		"user_id", userID,
		"filename", filename,
		"file_size", fileInfo.Size())

	// 发送文件
	c.File(execution.FilePath)
}

// generateDownloadFilename 生成下载文件名
func (h *ReportHandler) generateDownloadFilename(execution *models.ReportExecution) string {
	// 获取文件扩展名
	ext := filepath.Ext(execution.FilePath)
	if ext == "" {
		// 根据执行记录推断扩展名
		// 这里可以从Parameters中获取format信息
		ext = ".csv" // 默认为csv
	}

	// 生成友好的文件名
	templateName := "report"
	if execution.Template.ID != 0 {
		templateName = execution.Template.Name
	}

	timestamp := execution.StartTime.Format("20060102_150405")
	return fmt.Sprintf("%s_%d_%s%s", templateName, execution.ID, timestamp, ext)
}
