package handlers

import (
	"net/http"

	"db-monitor-platform/internal/middleware"
	"db-monitor-platform/internal/models"
	"db-monitor-platform/internal/services"
	"db-monitor-platform/internal/utils"
	"db-monitor-platform/pkg/logger"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// AlertHandler 告警处理器
type AlertHandler struct {
	alertService *services.AlertService
}

// NewAlertHandler 创建告警处理器
func NewAlertHandler(db *gorm.DB) *AlertHandler {
	return &AlertHandler{
		alertService: services.NewAlertService(db),
	}
}

// CreateAlertRule 创建告警规则
// @Summary 创建告警规则
// @Description 创建新的告警规则
// @Tags 告警管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.AlertRuleCreateRequest true "告警规则信息"
// @Success 201 {object} models.CommonResponse{data=models.AlertRuleResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/alerts/rules [post]
func (h *AlertHandler) CreateAlertRule(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	var req models.AlertRuleCreateRequest
	
	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind request: %v", err)
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid request data"))
		return
	}

	// 创建告警规则
	rule, err := h.alertService.CreateAlertRule(currentUser.ID, &req)
	if err != nil {
		logger.Errorf("Failed to create alert rule: %v", err)
		
		// 检查是否为验证错误
		if validationErrors := utils.FormatValidationErrors(err); len(validationErrors) > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Validation failed",
				"errors":  validationErrors,
			})
			return
		}
		
		switch err.Error() {
		case "database not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Database"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		case "conditions cannot be empty":
			c.JSON(http.StatusBadRequest, models.ErrorResponse(400, err.Error()))
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to create alert rule"))
		return
	}

	logger.Infof("Alert rule created successfully: %s by user %d", rule.Name, currentUser.ID)
	c.JSON(http.StatusCreated, models.SuccessResponse(rule))
}

// GetAlertRules 获取告警规则列表
// @Summary 获取告警规则列表
// @Description 获取告警规则列表（支持分页）
// @Tags 告警管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} models.CommonResponse{data=models.PaginationResponse}
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/alerts/rules [get]
func (h *AlertHandler) GetAlertRules(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析分页参数
	pagination := parsePaginationParams(c)

	// 获取告警规则列表
	result, err := h.alertService.GetAlertRules(currentUser.ID, currentUser.Role, pagination)
	if err != nil {
		logger.Errorf("Failed to get alert rules: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get alert rules"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// GetAlertRule 获取告警规则详情
// @Summary 获取告警规则详情
// @Description 根据ID获取告警规则详情
// @Tags 告警管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "告警规则ID"
// @Success 200 {object} models.CommonResponse{data=models.AlertRuleResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/alerts/rules/{id} [get]
func (h *AlertHandler) GetAlertRule(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析规则ID
	ruleID, err := parseUintParam(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid rule ID"))
		return
	}

	// 获取告警规则详情
	rule, err := h.alertService.GetAlertRule(currentUser.ID, ruleID, currentUser.Role)
	if err != nil {
		logger.Errorf("Failed to get alert rule: %v", err)
		
		switch err.Error() {
		case "alert rule not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Alert rule"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get alert rule"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(rule))
}

// UpdateAlertRule 更新告警规则
// @Summary 更新告警规则
// @Description 更新告警规则信息
// @Tags 告警管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "告警规则ID"
// @Param request body models.AlertRuleUpdateRequest true "更新信息"
// @Success 200 {object} models.CommonResponse{data=models.AlertRuleResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/alerts/rules/{id} [put]
func (h *AlertHandler) UpdateAlertRule(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析规则ID
	ruleID, err := parseUintParam(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid rule ID"))
		return
	}

	var req models.AlertRuleUpdateRequest
	
	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind request: %v", err)
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid request data"))
		return
	}

	// 更新告警规则
	rule, err := h.alertService.UpdateAlertRule(currentUser.ID, ruleID, currentUser.Role, &req)
	if err != nil {
		logger.Errorf("Failed to update alert rule: %v", err)
		
		// 检查是否为验证错误
		if validationErrors := utils.FormatValidationErrors(err); len(validationErrors) > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Validation failed",
				"errors":  validationErrors,
			})
			return
		}
		
		switch err.Error() {
		case "alert rule not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Alert rule"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		case "conditions cannot be empty":
			c.JSON(http.StatusBadRequest, models.ErrorResponse(400, err.Error()))
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to update alert rule"))
		return
	}

	logger.Infof("Alert rule updated successfully: %s by user %d", rule.Name, currentUser.ID)
	c.JSON(http.StatusOK, models.SuccessResponse(rule))
}

// DeleteAlertRule 删除告警规则
// @Summary 删除告警规则
// @Description 删除告警规则
// @Tags 告警管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "告警规则ID"
// @Success 200 {object} models.CommonResponse
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/alerts/rules/{id} [delete]
func (h *AlertHandler) DeleteAlertRule(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析规则ID
	ruleID, err := parseUintParam(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid rule ID"))
		return
	}

	// 删除告警规则
	err = h.alertService.DeleteAlertRule(currentUser.ID, ruleID, currentUser.Role)
	if err != nil {
		logger.Errorf("Failed to delete alert rule: %v", err)
		
		switch err.Error() {
		case "alert rule not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Alert rule"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to delete alert rule"))
		return
	}

	logger.Infof("Alert rule deleted successfully by user %d", currentUser.ID)
	c.JSON(http.StatusOK, models.SuccessResponse(gin.H{
		"message": "Alert rule deleted successfully",
	}))
}

// GetAlertEvents 获取告警事件列表
// @Summary 获取告警事件列表
// @Description 获取告警事件列表（支持分页）
// @Tags 告警管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} models.CommonResponse{data=models.PaginationResponse}
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/alerts/events [get]
func (h *AlertHandler) GetAlertEvents(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析分页参数
	pagination := parsePaginationParams(c)

	// 获取告警事件列表
	result, err := h.alertService.GetAlertEvents(currentUser.ID, currentUser.Role, pagination)
	if err != nil {
		logger.Errorf("Failed to get alert events: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get alert events"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// GetAlertEvent 获取告警事件详情
// @Summary 获取告警事件详情
// @Description 根据ID获取告警事件详情
// @Tags 告警管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "告警事件ID"
// @Success 200 {object} models.CommonResponse{data=models.AlertEventResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/alerts/events/{id} [get]
func (h *AlertHandler) GetAlertEvent(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析事件ID
	eventID, err := parseUintParam(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid event ID"))
		return
	}

	// 获取告警事件详情
	event, err := h.alertService.GetAlertEvent(currentUser.ID, eventID, currentUser.Role)
	if err != nil {
		logger.Errorf("Failed to get alert event: %v", err)
		
		switch err.Error() {
		case "alert event not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Alert event"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get alert event"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(event))
}

// ResolveAlertEvent 解决告警事件
// @Summary 解决告警事件
// @Description 标记告警事件为已解决
// @Tags 告警管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "告警事件ID"
// @Success 200 {object} models.CommonResponse
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/alerts/events/{id}/resolve [post]
func (h *AlertHandler) ResolveAlertEvent(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析事件ID
	eventID, err := parseUintParam(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid event ID"))
		return
	}

	// 解决告警事件
	err = h.alertService.ResolveAlertEvent(currentUser.ID, eventID, currentUser.Role)
	if err != nil {
		logger.Errorf("Failed to resolve alert event: %v", err)
		
		switch err.Error() {
		case "alert event not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Alert event"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		case "alert event is not active":
			c.JSON(http.StatusBadRequest, models.ErrorResponse(400, err.Error()))
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to resolve alert event"))
		return
	}

	logger.Infof("Alert event resolved successfully by user %d", currentUser.ID)
	c.JSON(http.StatusOK, models.SuccessResponse(gin.H{
		"message": "Alert event resolved successfully",
	}))
}

// GetAlertStats 获取告警统计信息
// @Summary 获取告警统计信息
// @Description 获取告警的统计信息
// @Tags 告警管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.CommonResponse{data=repository.AlertStats}
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/alerts/stats [get]
func (h *AlertHandler) GetAlertStats(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 获取告警统计信息
	stats, err := h.alertService.GetAlertStats(currentUser.ID, currentUser.Role)
	if err != nil {
		logger.Errorf("Failed to get alert stats: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get alert statistics"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(stats))
}

// SearchAlertRules 搜索告警规则
// @Summary 搜索告警规则
// @Description 根据关键词搜索告警规则
// @Tags 告警管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param q query string false "搜索关键词"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} models.CommonResponse{data=models.PaginationResponse}
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/alerts/rules/search [get]
func (h *AlertHandler) SearchAlertRules(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 获取搜索关键词
	keyword := c.Query("q")
	
	// 解析分页参数
	pagination := parsePaginationParams(c)

	// 搜索告警规则
	result, err := h.alertService.SearchAlertRules(currentUser.ID, currentUser.Role, keyword, pagination)
	if err != nil {
		logger.Errorf("Failed to search alert rules: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to search alert rules"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}
