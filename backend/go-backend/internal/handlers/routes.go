package handlers

import (
	"net/http"

	"db-monitor-platform/docs"
	"db-monitor-platform/internal/config"
	"db-monitor-platform/internal/middleware"

	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	ginSwagger "github.com/swaggo/gin-swagger"
	swaggerFiles "github.com/swaggo/files"
	"gorm.io/gorm"
)

// SetupRoutes 设置路由
func SetupRoutes(r *gin.Engine, db *gorm.DB, redisClient *redis.Client, cfg *config.Config) {
	// 初始化Swagger文档
	// 使用localhost而不是0.0.0.0，因为浏览器无法访问0.0.0.0
	swaggerHost := cfg.Server.Host
	if swaggerHost == "0.0.0.0" {
		swaggerHost = "localhost"
	}
	docs.SwaggerInfo.Host = fmt.Sprintf("%s:%d", swaggerHost, cfg.Server.Port)
	docs.SwaggerInfo.BasePath = "/"

	// Swagger文档路由（总是可用）
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"message": "Database Monitor Platform API is running",
			"version": "1.0.0",
		})
	})

	// 根路由
	r.GET("/", func(c *gin.Context) {
		status := "ok"
		if db == nil {
			status = "Documentation mode - Database not connected"
		}
		c.JSON(http.StatusOK, gin.H{
			"message": "Database Monitor Platform API",
			"version": "1.0.0",
			"docs":    "/swagger/index.html",
			"status":  status,
		})
	})

	// 创建处理器（即使数据库为nil也要创建路由用于文档）
	var authHandler *AuthHandler
	var databaseHandler *DatabaseHandler
	var metricHandler *MetricHandler
	var alertHandler *AlertHandler
	var wsHandler *WebSocketHandler
	var queryOptimizerHandler *QueryOptimizerHandler
	var backupHandler *BackupHandler
	var reportHandler *ReportHandler
	var settingHandler *SettingHandler

	if db != nil {
		authHandler = NewAuthHandler(db, cfg)
		databaseHandler = NewDatabaseHandler(db)
		metricHandler = NewMetricHandler(db)
		alertHandler = NewAlertHandler(db)
		wsHandler = NewWebSocketHandler(db, cfg)
		queryOptimizerHandler = NewQueryOptimizerHandler(db)
		backupHandler = NewBackupHandler(db)
		reportHandler = NewReportHandler(db)
		settingHandler = NewSettingHandler(db)
	} else {
		// 文档模式下创建空处理器
		authHandler = &AuthHandler{}
		databaseHandler = &DatabaseHandler{}
		metricHandler = &MetricHandler{}
		alertHandler = &AlertHandler{}
		wsHandler = &WebSocketHandler{}
		queryOptimizerHandler = &QueryOptimizerHandler{}
		backupHandler = &BackupHandler{}
		reportHandler = &ReportHandler{}
		settingHandler = &SettingHandler{}
	}

	// API版本组
	api := r.Group("/api/v1")

	// 认证路由组（无需认证）
	auth := api.Group("/auth")
	{
		auth.POST("/register", authHandler.Register)
		auth.POST("/login", authHandler.Login)
		auth.POST("/logout", authHandler.Logout)
		auth.POST("/refresh", authHandler.RefreshToken)
	}

	// 需要认证的路由组
	protected := api.Group("/")
	protected.Use(middleware.AuthMiddleware(cfg))
	{
		// 用户相关 - 修复路由路径
		auth := protected.Group("/auth")
		{
			auth.GET("/profile", authHandler.GetProfile)
			auth.PUT("/profile", authHandler.UpdateProfile)
			auth.PUT("/profile/password", authHandler.ChangePassword)
		}

		// 数据库管理
		databases := protected.Group("/databases")
		{
			databases.GET("", databaseHandler.GetDatabases)
			databases.POST("", databaseHandler.CreateDatabase)
			databases.GET("/search", databaseHandler.SearchDatabases)
			databases.GET("/stats", databaseHandler.GetDatabaseStats)
			databases.GET("/:id", databaseHandler.GetDatabase)
			databases.PUT("/:id", databaseHandler.UpdateDatabase)
			databases.DELETE("/:id", databaseHandler.DeleteDatabase)
			databases.POST("/:id/test", databaseHandler.TestConnection)
			databases.GET("/:id/metrics/real", databaseHandler.GetRealDatabaseMetrics)
		}

		// 监控数据
		metrics := protected.Group("/metrics")
		{
			metrics.POST("", metricHandler.CreateMetric)
			metrics.POST("/aggregate", metricHandler.GetAggregatedMetrics)
			metrics.GET("/realtime", metricHandler.GetRealtimeMetrics)
			metrics.GET("/:db_id", metricHandler.GetMetrics)
			metrics.GET("/:db_id/latest", metricHandler.GetLatestMetrics)
			metrics.GET("/:db_id/history", metricHandler.GetMetricsByTimeRange)
			metrics.GET("/:db_id/types", metricHandler.GetMetricTypes)
			metrics.GET("/:db_id/stats", metricHandler.GetMetricStats)
		}

		// 监控数据收集（用于监控代理，使用API Key认证）
		collect := api.Group("/metrics")
		// TODO: 添加API Key认证中间件
		// collect.Use(middleware.APIKeyMiddleware(cfg.APIKeys))
		{
			collect.POST("/collect", metricHandler.CollectMetrics)
		}

		// 告警管理
		alerts := protected.Group("/alerts")
		{
			// 告警规则
			alerts.GET("/rules", alertHandler.GetAlertRules)
			alerts.POST("/rules", alertHandler.CreateAlertRule)
			alerts.GET("/rules/search", alertHandler.SearchAlertRules)
			alerts.GET("/rules/:id", alertHandler.GetAlertRule)
			alerts.PUT("/rules/:id", alertHandler.UpdateAlertRule)
			alerts.DELETE("/rules/:id", alertHandler.DeleteAlertRule)

			// 告警事件
			alerts.GET("/events", alertHandler.GetAlertEvents)
			alerts.GET("/events/:id", alertHandler.GetAlertEvent)
			alerts.POST("/events/:id/resolve", alertHandler.ResolveAlertEvent)

			// 告警统计
			alerts.GET("/stats", alertHandler.GetAlertStats)
		}

		// 查询优化工具
		queryOptimizer := protected.Group("/query-optimizer")
		{
			// 查询分析
			queryOptimizer.POST("/analyze", queryOptimizerHandler.AnalyzeQuery)
			queryOptimizer.GET("/analyze/:id", queryOptimizerHandler.GetAnalysis)
			queryOptimizer.GET("/analyze", queryOptimizerHandler.GetAnalysisHistory)

			// 执行计划
			queryOptimizer.POST("/explain", queryOptimizerHandler.ExplainQuery)
			queryOptimizer.GET("/explain/:id", queryOptimizerHandler.GetExecutionPlan)

			// 索引推荐
			queryOptimizer.POST("/index-suggestions", queryOptimizerHandler.GetIndexSuggestions)
			queryOptimizer.GET("/index-suggestions/:analysis_id", queryOptimizerHandler.GetIndexRecommendations)

			// 优化建议
			queryOptimizer.GET("/suggestions/:analysis_id", queryOptimizerHandler.GetOptimizationSuggestions)

			// 支持的数据库类型
			queryOptimizer.GET("/databases", queryOptimizerHandler.GetSupportedDatabases)

			// 查询统计
			queryOptimizer.GET("/stats", queryOptimizerHandler.GetQueryStats)
		}

		// 维护工具 - 备份管理
		maintenance := protected.Group("/maintenance")
		{
			// 备份管理
			backup := maintenance.Group("/backup")
			{
				// 备份任务管理
				backup.GET("/tasks", backupHandler.GetBackupTasks)
				backup.POST("/tasks", backupHandler.CreateBackupTask)
				backup.GET("/tasks/stats", backupHandler.GetTaskStats)
				backup.GET("/tasks/:id", backupHandler.GetBackupTask)
				backup.PUT("/tasks/:id", backupHandler.UpdateBackupTask)
				backup.DELETE("/tasks/:id", backupHandler.DeleteBackupTask)

				// 备份执行
				backup.POST("/tasks/:id/execute", backupHandler.ExecuteBackup)
				backup.GET("/running", backupHandler.GetRunningBackups)

				// 备份历史
				backup.GET("/history", backupHandler.GetBackupHistory)
				backup.GET("/history/:id", backupHandler.GetBackupHistoryByID)
				backup.GET("/history/:id/download", backupHandler.DownloadBackupFile)
				backup.DELETE("/history/:id", backupHandler.DeleteBackupHistory)
				backup.POST("/history/:id/cancel", backupHandler.CancelBackup)

				// 备份统计
				backup.GET("/stats", backupHandler.GetBackupStats)
				backup.GET("/success-rate", backupHandler.GetSuccessRate)
			}
		}

		// 报表管理
		reports := protected.Group("/reports")
		{
			// 报表模板管理
			reports.GET("/templates", reportHandler.GetTemplates)
			reports.POST("/templates", reportHandler.CreateTemplate)
			reports.GET("/templates/:id", reportHandler.GetTemplate)
			reports.PUT("/templates/:id", reportHandler.UpdateTemplate)
			reports.DELETE("/templates/:id", reportHandler.DeleteTemplate)

			// 报表执行
			reports.POST("/execute", reportHandler.ExecuteReport)
			reports.GET("/executions", reportHandler.GetExecutions)
			reports.GET("/executions/:id", reportHandler.GetExecution)
			reports.GET("/executions/:id/download", reportHandler.DownloadReport)

			// 图表数据和统计（TODO: 实现这些方法）
			// reports.GET("/charts", reportHandler.GetChartData)
			// reports.GET("/summary", reportHandler.GetSummaryStats)
		}

		// 系统设置
		settings := protected.Group("/settings")
		{
			// 系统设置管理
			settings.GET("/system", settingHandler.GetSystemSettings)
			settings.PUT("/system", settingHandler.UpdateSystemSettings)

			// 用户偏好设置
			settings.GET("/preferences", settingHandler.GetUserPreferences)
			settings.PUT("/preferences", settingHandler.UpdateUserPreferences)
			settings.POST("/preferences/initialize", settingHandler.InitializeUserDefaults)

			// 设置元数据
			settings.GET("/categories", settingHandler.GetCategories)
			settings.GET("/validation-rules", settingHandler.GetValidationRules)
		}
	}

	// WebSocket路由
	if wsHandler != nil {
		r.GET("/ws", wsHandler.HandleWebSocket)
	} else {
		r.GET("/ws", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"message": "WebSocket endpoint - coming soon",
			})
		})
	}
}
