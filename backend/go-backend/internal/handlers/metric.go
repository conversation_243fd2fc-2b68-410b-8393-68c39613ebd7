package handlers

import (
	"net/http"
	"time"

	"db-monitor-platform/internal/middleware"
	"db-monitor-platform/internal/models"
	"db-monitor-platform/internal/services"
	"db-monitor-platform/internal/utils"
	"db-monitor-platform/pkg/logger"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// MetricHandler 监控指标处理器
type MetricHandler struct {
	metricService *services.MetricService
}

// NewMetricHandler 创建监控指标处理器
func NewMetricHandler(db *gorm.DB) *MetricHandler {
	return &MetricHandler{
		metricService: services.NewMetricService(db),
	}
}

// CreateMetric 创建监控指标
// @Summary 创建监控指标
// @Description 创建新的监控指标数据点
// @Tags 监控指标
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.MetricCreateRequest true "监控指标信息"
// @Success 201 {object} models.CommonResponse{data=models.MetricResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/metrics [post]
func (h *MetricHandler) CreateMetric(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	var req models.MetricCreateRequest
	
	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind request: %v", err)
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid request data"))
		return
	}

	// 创建监控指标
	metric, err := h.metricService.CreateMetric(currentUser.ID, &req)
	if err != nil {
		logger.Errorf("Failed to create metric: %v", err)
		
		// 检查是否为验证错误
		if validationErrors := utils.FormatValidationErrors(err); len(validationErrors) > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Validation failed",
				"errors":  validationErrors,
			})
			return
		}
		
		switch err.Error() {
		case "database not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Database"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to create metric"))
		return
	}

	c.JSON(http.StatusCreated, models.SuccessResponse(metric))
}

// GetMetrics 获取监控指标列表
// @Summary 获取监控指标列表
// @Description 获取指定数据库的监控指标列表
// @Tags 监控指标
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param db_id path int true "数据库ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} models.CommonResponse{data=models.PaginationResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/metrics/{db_id} [get]
func (h *MetricHandler) GetMetrics(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析数据库ID
	databaseID, err := parseUintParam(c, "db_id")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid database ID"))
		return
	}

	// 解析分页参数
	pagination := parsePaginationParams(c)

	// 获取监控指标列表
	result, err := h.metricService.GetMetrics(currentUser.ID, databaseID, currentUser.Role, pagination)
	if err != nil {
		logger.Errorf("Failed to get metrics: %v", err)
		
		switch err.Error() {
		case "database not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Database"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get metrics"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// GetMetricsByTimeRange 根据时间范围获取监控指标
// @Summary 根据时间范围获取监控指标
// @Description 根据时间范围获取指定数据库的监控指标
// @Tags 监控指标
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param db_id path int true "数据库ID"
// @Param metric_type query string false "指标类型"
// @Param start_time query string true "开始时间 (RFC3339格式)"
// @Param end_time query string true "结束时间 (RFC3339格式)"
// @Success 200 {object} models.CommonResponse{data=[]models.MetricResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/metrics/{db_id}/history [get]
func (h *MetricHandler) GetMetricsByTimeRange(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析数据库ID
	databaseID, err := parseUintParam(c, "db_id")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid database ID"))
		return
	}

	// 解析查询参数
	metricType := c.Query("metric_type")
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	if startTimeStr == "" || endTimeStr == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "start_time and end_time are required"))
		return
	}

	startTime, err := time.Parse(time.RFC3339, startTimeStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid start_time format"))
		return
	}

	endTime, err := time.Parse(time.RFC3339, endTimeStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid end_time format"))
		return
	}

	// 获取监控指标
	metrics, err := h.metricService.GetMetricsByTimeRange(currentUser.ID, databaseID, currentUser.Role, metricType, startTime, endTime)
	if err != nil {
		logger.Errorf("Failed to get metrics by time range: %v", err)
		
		switch err.Error() {
		case "database not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Database"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get metrics"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(metrics))
}

// GetLatestMetrics 获取最新监控指标
// @Summary 获取最新监控指标
// @Description 获取指定数据库的最新监控指标
// @Tags 监控指标
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param db_id path int true "数据库ID"
// @Success 200 {object} models.CommonResponse{data=[]models.MetricResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/metrics/{db_id}/latest [get]
func (h *MetricHandler) GetLatestMetrics(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析数据库ID
	databaseID, err := parseUintParam(c, "db_id")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid database ID"))
		return
	}

	// 获取最新监控指标
	metrics, err := h.metricService.GetLatestMetrics(currentUser.ID, databaseID, currentUser.Role)
	if err != nil {
		logger.Errorf("Failed to get latest metrics: %v", err)
		
		switch err.Error() {
		case "database not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Database"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get latest metrics"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(metrics))
}

// GetAggregatedMetrics 获取聚合监控指标
// @Summary 获取聚合监控指标
// @Description 获取聚合的监控指标数据，支持时间间隔聚合
// @Tags 监控指标
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.MetricQueryRequest true "查询参数"
// @Success 200 {object} models.CommonResponse{data=models.MetricAggregateResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/metrics/aggregate [post]
func (h *MetricHandler) GetAggregatedMetrics(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	var req models.MetricQueryRequest
	
	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind request: %v", err)
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid request data"))
		return
	}

	// 获取聚合监控指标
	result, err := h.metricService.GetAggregatedMetrics(currentUser.ID, currentUser.Role, &req)
	if err != nil {
		logger.Errorf("Failed to get aggregated metrics: %v", err)
		
		// 检查是否为验证错误
		if validationErrors := utils.FormatValidationErrors(err); len(validationErrors) > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Validation failed",
				"errors":  validationErrors,
			})
			return
		}
		
		switch err.Error() {
		case "database not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Database"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		case "end time must be after start time", "time range too large, maximum 30 days":
			c.JSON(http.StatusBadRequest, models.ErrorResponse(400, err.Error()))
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get aggregated metrics"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// GetRealtimeMetrics 获取实时监控指标
// @Summary 获取实时监控指标
// @Description 获取用户可访问的所有数据库的实时监控指标
// @Tags 监控指标
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.CommonResponse{data=[]models.RealtimeMetric}
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/metrics/realtime [get]
func (h *MetricHandler) GetRealtimeMetrics(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 获取实时监控指标
	metrics, err := h.metricService.GetRealtimeMetrics(currentUser.ID, currentUser.Role)
	if err != nil {
		logger.Errorf("Failed to get realtime metrics: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get realtime metrics"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(metrics))
}

// GetMetricTypes 获取指标类型列表
// @Summary 获取指标类型列表
// @Description 获取指定数据库的所有指标类型
// @Tags 监控指标
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param db_id path int true "数据库ID"
// @Success 200 {object} models.CommonResponse{data=[]string}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/metrics/{db_id}/types [get]
func (h *MetricHandler) GetMetricTypes(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析数据库ID
	databaseID, err := parseUintParam(c, "db_id")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid database ID"))
		return
	}

	// 获取指标类型
	metricTypes, err := h.metricService.GetMetricTypes(currentUser.ID, databaseID, currentUser.Role)
	if err != nil {
		logger.Errorf("Failed to get metric types: %v", err)
		
		switch err.Error() {
		case "database not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Database"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get metric types"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(metricTypes))
}

// GetMetricStats 获取指标统计信息
// @Summary 获取指标统计信息
// @Description 获取指定指标的统计信息
// @Tags 监控指标
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param db_id path int true "数据库ID"
// @Param metric_type query string true "指标类型"
// @Param duration query string false "统计时间范围" default("24h")
// @Success 200 {object} models.CommonResponse{data=repository.MetricStats}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/metrics/{db_id}/stats [get]
func (h *MetricHandler) GetMetricStats(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析数据库ID
	databaseID, err := parseUintParam(c, "db_id")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid database ID"))
		return
	}

	// 解析查询参数
	metricType := c.Query("metric_type")
	if metricType == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "metric_type is required"))
		return
	}

	durationStr := c.DefaultQuery("duration", "24h")
	duration, err := time.ParseDuration(durationStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid duration format"))
		return
	}

	// 获取指标统计信息
	stats, err := h.metricService.GetMetricStats(currentUser.ID, databaseID, currentUser.Role, metricType, duration)
	if err != nil {
		logger.Errorf("Failed to get metric stats: %v", err)
		
		switch err.Error() {
		case "database not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Database"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get metric statistics"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(stats))
}

// CollectMetrics 收集监控指标（用于监控代理）
// @Summary 收集监控指标
// @Description 批量收集监控指标数据（用于监控代理）
// @Tags 监控指标
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body services.MetricCollectionRequest true "指标收集请求"
// @Success 200 {object} models.CommonResponse
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/metrics/collect [post]
func (h *MetricHandler) CollectMetrics(c *gin.Context) {
	var req services.MetricCollectionRequest
	
	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind request: %v", err)
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid request data"))
		return
	}

	// 收集监控指标
	err := h.metricService.CollectMetrics(&req)
	if err != nil {
		logger.Errorf("Failed to collect metrics: %v", err)
		
		// 检查是否为验证错误
		if validationErrors := utils.FormatValidationErrors(err); len(validationErrors) > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Validation failed",
				"errors":  validationErrors,
			})
			return
		}
		
		switch err.Error() {
		case "database not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Database"))
			return
		case "database monitoring is disabled":
			c.JSON(http.StatusBadRequest, models.ErrorResponse(400, err.Error()))
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to collect metrics"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(gin.H{
		"message": "Metrics collected successfully",
	}))
}
