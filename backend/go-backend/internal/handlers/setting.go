package handlers

import (
	"net/http"

	"db-monitor-platform/internal/models"
	"db-monitor-platform/internal/services"
	"db-monitor-platform/pkg/logger"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SettingHandler 设置处理器
type SettingHandler struct {
	settingService services.SettingService
}

// NewSettingHandler 创建设置处理器实例
func NewSettingHandler(db *gorm.DB) *SettingHandler {
	return &SettingHandler{
		settingService: services.NewSettingService(db),
	}
}

// @Summary 获取系统设置
// @Description 获取系统设置，支持按分类过滤
// @Tags Settings
// @Accept json
// @Produce json
// @Param category query string false "设置分类" Enums(system,monitoring,alert,backup,security)
// @Param public query bool false "是否只获取公开设置"
// @Param search query string false "搜索关键词"
// @Success 200 {object} models.CommonResponse{data=map[string]interface{}}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/settings/system [get]
// @Security BearerAuth
func (h *SettingHandler) GetSystemSettings(c *gin.Context) {
	userID := c.GetUint("user_id")
	
	var req models.GetSystemSettingsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query", "error", err)
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse(err.Error()))
		return
	}

	settings, err := h.settingService.GetSystemSettings(c.Request.Context(), &req, userID)
	if err != nil {
		logger.Error("Failed to get system settings", "error", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(settings))
}

// @Summary 更新系统设置
// @Description 批量更新系统设置（仅管理员）
// @Tags Settings
// @Accept json
// @Produce json
// @Param request body models.UpdateSystemSettingsRequest true "更新系统设置请求"
// @Success 200 {object} models.CommonResponse
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/settings/system [put]
// @Security BearerAuth
func (h *SettingHandler) UpdateSystemSettings(c *gin.Context) {
	userID := c.GetUint("user_id")
	
	var req models.UpdateSystemSettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind request", "error", err)
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse(err.Error()))
		return
	}

	err := h.settingService.UpdateSystemSettings(c.Request.Context(), &req, userID)
	if err != nil {
		if err.Error() == "permission denied: only admin can update system settings" {
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		logger.Error("Failed to update system settings", "error", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(nil))
}

// @Summary 获取用户偏好设置
// @Description 获取当前用户的偏好设置
// @Tags Settings
// @Accept json
// @Produce json
// @Param category query string false "偏好分类" Enums(ui,notification,dashboard,report)
// @Param search query string false "搜索关键词"
// @Success 200 {object} models.CommonResponse{data=map[string]interface{}}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/settings/preferences [get]
// @Security BearerAuth
func (h *SettingHandler) GetUserPreferences(c *gin.Context) {
	userID := c.GetUint("user_id")
	
	var req models.GetUserPreferencesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query", "error", err)
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse(err.Error()))
		return
	}

	preferences, err := h.settingService.GetUserPreferences(c.Request.Context(), &req, userID)
	if err != nil {
		logger.Error("Failed to get user preferences", "error", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(preferences))
}

// @Summary 更新用户偏好设置
// @Description 批量更新当前用户的偏好设置
// @Tags Settings
// @Accept json
// @Produce json
// @Param request body models.UpdateUserPreferencesRequest true "更新用户偏好请求"
// @Success 200 {object} models.CommonResponse
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/settings/preferences [put]
// @Security BearerAuth
func (h *SettingHandler) UpdateUserPreferences(c *gin.Context) {
	userID := c.GetUint("user_id")
	
	var req models.UpdateUserPreferencesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind request", "error", err)
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse(err.Error()))
		return
	}

	err := h.settingService.UpdateUserPreferences(c.Request.Context(), &req, userID)
	if err != nil {
		logger.Error("Failed to update user preferences", "error", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(nil))
}

// @Summary 获取设置分类信息
// @Description 获取所有设置分类的信息和描述
// @Tags Settings
// @Accept json
// @Produce json
// @Success 200 {object} models.CommonResponse{data=map[string]interface{}}
// @Failure 401 {object} models.CommonResponse
// @Router /api/v1/settings/categories [get]
// @Security BearerAuth
func (h *SettingHandler) GetCategories(c *gin.Context) {
	result := map[string]interface{}{
		"system_categories":     models.SystemSettingCategories,
		"preference_categories": models.UserPreferenceCategories,
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// @Summary 获取设置验证规则
// @Description 获取设置项的验证规则
// @Tags Settings
// @Accept json
// @Produce json
// @Success 200 {object} models.CommonResponse{data=map[string]models.SettingValidationRule}
// @Failure 401 {object} models.CommonResponse
// @Router /api/v1/settings/validation-rules [get]
// @Security BearerAuth
func (h *SettingHandler) GetValidationRules(c *gin.Context) {
	c.JSON(http.StatusOK, models.SuccessResponse(models.SettingValidationRules))
}

// @Summary 初始化用户默认设置
// @Description 为当前用户初始化默认偏好设置
// @Tags Settings
// @Accept json
// @Produce json
// @Success 200 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/settings/preferences/initialize [post]
// @Security BearerAuth
func (h *SettingHandler) InitializeUserDefaults(c *gin.Context) {
	userID := c.GetUint("user_id")
	
	err := h.settingService.InitializeUserDefaults(c.Request.Context(), userID)
	if err != nil {
		logger.Error("Failed to initialize user defaults", "error", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, err.Error()))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(nil))
}
