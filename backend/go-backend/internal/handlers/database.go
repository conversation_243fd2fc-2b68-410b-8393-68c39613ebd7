package handlers

import (
	"net/http"
	"strconv"

	"db-monitor-platform/internal/middleware"
	"db-monitor-platform/internal/models"
	"db-monitor-platform/internal/services"
	"db-monitor-platform/internal/utils"
	"db-monitor-platform/pkg/logger"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// DatabaseHandler 数据库实例处理器
type DatabaseHandler struct {
	databaseService *services.DatabaseService
}

// NewDatabaseHandler 创建数据库实例处理器
func NewDatabaseHandler(db *gorm.DB) *DatabaseHandler {
	return &DatabaseHandler{
		databaseService: services.NewDatabaseService(db),
	}
}

// CreateDatabase 创建数据库实例
// @Summary 创建数据库实例
// @Description 创建新的数据库实例
// @Tags 数据库管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.DatabaseCreateRequest true "数据库实例信息"
// @Success 201 {object} models.CommonResponse{data=models.DatabaseResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 409 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/databases [post]
func (h *DatabaseHandler) CreateDatabase(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	var req models.DatabaseCreateRequest
	
	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind request: %v", err)
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid request data"))
		return
	}

	// 创建数据库实例
	database, err := h.databaseService.CreateDatabase(currentUser.ID, &req)
	if err != nil {
		logger.Errorf("Failed to create database: %v", err)
		
		// 检查是否为验证错误
		if validationErrors := utils.FormatValidationErrors(err); len(validationErrors) > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Validation failed",
				"errors":  validationErrors,
			})
			return
		}
		
		// 检查特定错误
		switch err.Error() {
		case "insufficient permissions to create database":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		case "database connection already exists":
			c.JSON(http.StatusConflict, models.ErrorResponse(409, err.Error()))
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to create database"))
		return
	}

	logger.Infof("Database created successfully: %s by user %d", database.Name, currentUser.ID)
	c.JSON(http.StatusCreated, models.SuccessResponse(database))
}

// GetDatabases 获取数据库实例列表
// @Summary 获取数据库实例列表
// @Description 获取数据库实例列表（支持分页）
// @Tags 数据库管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} models.CommonResponse{data=models.PaginationResponse}
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/databases [get]
func (h *DatabaseHandler) GetDatabases(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析分页参数
	pagination := parsePaginationParams(c)

	// 获取数据库列表
	result, err := h.databaseService.GetDatabases(currentUser.ID, currentUser.Role, pagination)
	if err != nil {
		logger.Errorf("Failed to get databases: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get databases"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// GetDatabase 获取数据库实例详情
// @Summary 获取数据库实例详情
// @Description 根据ID获取数据库实例详情
// @Tags 数据库管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "数据库实例ID"
// @Success 200 {object} models.CommonResponse{data=models.DatabaseResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/databases/{id} [get]
func (h *DatabaseHandler) GetDatabase(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析数据库ID
	databaseID, err := parseUintParam(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid database ID"))
		return
	}

	// 获取数据库详情
	database, err := h.databaseService.GetDatabase(currentUser.ID, databaseID, currentUser.Role)
	if err != nil {
		logger.Errorf("Failed to get database: %v", err)
		
		switch err.Error() {
		case "database not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Database"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get database"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(database))
}

// UpdateDatabase 更新数据库实例
// @Summary 更新数据库实例
// @Description 更新数据库实例信息
// @Tags 数据库管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "数据库实例ID"
// @Param request body models.DatabaseUpdateRequest true "更新信息"
// @Success 200 {object} models.CommonResponse{data=models.DatabaseResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/databases/{id} [put]
func (h *DatabaseHandler) UpdateDatabase(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析数据库ID
	databaseID, err := parseUintParam(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid database ID"))
		return
	}

	var req models.DatabaseUpdateRequest
	
	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind request: %v", err)
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid request data"))
		return
	}

	// 更新数据库实例
	database, err := h.databaseService.UpdateDatabase(currentUser.ID, databaseID, currentUser.Role, &req)
	if err != nil {
		logger.Errorf("Failed to update database: %v", err)
		
		// 检查是否为验证错误
		if validationErrors := utils.FormatValidationErrors(err); len(validationErrors) > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Validation failed",
				"errors":  validationErrors,
			})
			return
		}
		
		switch err.Error() {
		case "database not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Database"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to update database"))
		return
	}

	logger.Infof("Database updated successfully: %s by user %d", database.Name, currentUser.ID)
	c.JSON(http.StatusOK, models.SuccessResponse(database))
}

// DeleteDatabase 删除数据库实例
// @Summary 删除数据库实例
// @Description 删除数据库实例
// @Tags 数据库管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "数据库实例ID"
// @Success 200 {object} models.CommonResponse
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/databases/{id} [delete]
func (h *DatabaseHandler) DeleteDatabase(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析数据库ID
	databaseID, err := parseUintParam(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid database ID"))
		return
	}

	// 删除数据库实例
	err = h.databaseService.DeleteDatabase(currentUser.ID, databaseID, currentUser.Role)
	if err != nil {
		logger.Errorf("Failed to delete database: %v", err)
		
		switch err.Error() {
		case "database not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Database"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to delete database"))
		return
	}

	logger.Infof("Database deleted successfully by user %d", currentUser.ID)
	c.JSON(http.StatusOK, models.SuccessResponse(gin.H{
		"message": "Database deleted successfully",
	}))
}

// TestConnection 测试数据库连接
// @Summary 测试数据库连接
// @Description 测试数据库实例的连接状态
// @Tags 数据库管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "数据库实例ID"
// @Success 200 {object} models.CommonResponse{data=services.ConnectionTestResult}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/databases/{id}/test [post]
func (h *DatabaseHandler) TestConnection(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析数据库ID
	databaseID, err := parseUintParam(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid database ID"))
		return
	}

	// 测试连接
	result, err := h.databaseService.TestConnection(currentUser.ID, databaseID, currentUser.Role)
	if err != nil {
		logger.Errorf("Failed to test connection: %v", err)
		
		switch err.Error() {
		case "database not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Database"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to test connection"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// SearchDatabases 搜索数据库实例
// @Summary 搜索数据库实例
// @Description 根据关键词搜索数据库实例
// @Tags 数据库管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param q query string false "搜索关键词"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} models.CommonResponse{data=models.PaginationResponse}
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/databases/search [get]
func (h *DatabaseHandler) SearchDatabases(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 获取搜索关键词
	keyword := c.Query("q")
	
	// 解析分页参数
	pagination := parsePaginationParams(c)

	// 搜索数据库
	result, err := h.databaseService.SearchDatabases(currentUser.ID, currentUser.Role, keyword, pagination)
	if err != nil {
		logger.Errorf("Failed to search databases: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to search databases"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(result))
}

// GetDatabaseStats 获取数据库统计信息
// @Summary 获取数据库统计信息
// @Description 获取数据库实例的统计信息
// @Tags 数据库管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.CommonResponse{data=repository.DatabaseStats}
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/databases/stats [get]
func (h *DatabaseHandler) GetDatabaseStats(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 获取统计信息
	stats, err := h.databaseService.GetDatabaseStats(currentUser.ID, currentUser.Role)
	if err != nil {
		logger.Errorf("Failed to get database stats: %v", err)
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get database statistics"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(stats))
}

// parsePaginationParams 解析分页参数
func parsePaginationParams(c *gin.Context) *models.PaginationRequest {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}
	
	return &models.PaginationRequest{
		Page:     page,
		PageSize: pageSize,
	}
}

// parseUintParam 解析uint参数
func parseUintParam(c *gin.Context, param string) (uint, error) {
	value := c.Param(param)
	id, err := strconv.ParseUint(value, 10, 32)
	if err != nil {
		return 0, err
	}
	return uint(id), nil
}

// GetRealDatabaseMetrics 获取真实数据库监控指标
// @Summary 获取真实数据库监控指标
// @Description 获取指定数据库的真实监控指标，包括连接状态、系统信息等
// @Tags 数据库管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "数据库ID"
// @Success 200 {object} models.CommonResponse{data=services.RealDatabaseMetrics}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/databases/{id}/metrics/real [get]
func (h *DatabaseHandler) GetRealDatabaseMetrics(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析数据库ID
	databaseID, err := parseUintParam(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid database ID"))
		return
	}

	// 获取真实监控指标
	metrics, err := h.databaseService.GetRealDatabaseMetrics(currentUser.ID, databaseID, currentUser.Role)
	if err != nil {
		logger.Errorf("Failed to get real database metrics: %v", err)

		switch err.Error() {
		case "database not found":
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Database"))
			return
		case "access denied":
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}

		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get real database metrics"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(metrics))
}
