package handlers

import (
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strconv"

	"db-monitor-platform/internal/middleware"
	"db-monitor-platform/internal/models"
	"db-monitor-platform/internal/services"
	"db-monitor-platform/pkg/logger"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// BackupHandler 备份管理处理器
type BackupHandler struct {
	backupService services.BackupService
}

// NewBackupHandler 创建备份管理处理器
func NewBackupHandler(db *gorm.DB) *BackupHandler {
	return &BackupHandler{
		backupService: services.NewBackupService(db),
	}
}

// CreateBackupTask 创建备份任务
// @Summary 创建备份任务
// @Description 为指定数据库创建新的备份任务
// @Tags 备份管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.BackupTaskCreateRequest true "备份任务信息"
// @Success 201 {object} models.CommonResponse{data=models.BackupTaskResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 409 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/maintenance/backup/tasks [post]
func (h *BackupHandler) CreateBackupTask(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析请求
	var req models.BackupTaskCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind request: %v", err)
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse(err.Error()))
		return
	}

	// 创建备份任务
	task, err := h.backupService.CreateTask(&req, currentUser.ID)
	if err != nil {
		logger.Errorf("Failed to create backup task: %v", err)
		if err.Error() == "backup task with this name already exists for this database" {
			c.JSON(http.StatusConflict, models.ErrorResponse(409, err.Error()))
			return
		}
		if err.Error() == "database instance not found" || err.Error() == "permission denied: you can only create backup tasks for your own databases" {
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		c.JSON(http.StatusInternalServerError, models.InternalErrorResponse())
		return
	}

	// 返回响应
	response := task.ToResponse()
	c.JSON(http.StatusCreated, models.SuccessResponse(response))
}

// GetBackupTasks 获取备份任务列表
// @Summary 获取备份任务列表
// @Description 获取当前用户的备份任务列表，支持分页和筛选
// @Tags 备份管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param database_id query int false "数据库ID"
// @Param status query string false "任务状态" Enums(active,paused,failed)
// @Param backup_type query string false "备份类型" Enums(full,incremental,differential)
// @Param search query string false "搜索关键词"
// @Success 200 {object} models.CommonResponse{data=models.BackupTaskListResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/maintenance/backup/tasks [get]
func (h *BackupHandler) GetBackupTasks(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析查询参数
	var req models.BackupTaskListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.Errorf("Failed to bind query: %v", err)
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse(err.Error()))
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 获取备份任务列表
	tasks, total, err := h.backupService.ListTasks(&req, currentUser.ID)
	if err != nil {
		logger.Errorf("Failed to get backup tasks: %v", err)
		if err.Error() == "database instance not found" || err.Error() == "permission denied: you can only view backup tasks for your own databases" {
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		c.JSON(http.StatusInternalServerError, models.InternalErrorResponse())
		return
	}

	// 转换为响应格式
	var taskResponses []models.BackupTaskResponse
	for _, task := range tasks {
		taskResponses = append(taskResponses, *task.ToResponse())
	}

	// 创建分页响应
	response := models.NewPaginationResponse(req.Page, req.PageSize, total, taskResponses)
	c.JSON(http.StatusOK, models.SuccessResponse(response))
}

// GetBackupTask 获取备份任务详情
// @Summary 获取备份任务详情
// @Description 根据ID获取备份任务的详细信息
// @Tags 备份管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "任务ID"
// @Success 200 {object} models.CommonResponse{data=models.BackupTaskResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/maintenance/backup/tasks/{id} [get]
func (h *BackupHandler) GetBackupTask(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析任务ID
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse("invalid task ID"))
		return
	}

	// 获取备份任务
	task, err := h.backupService.GetTask(uint(taskID), currentUser.ID)
	if err != nil {
		logger.Errorf("Failed to get backup task: %v", err)
		if err.Error() == "backup task not found" {
			c.JSON(http.StatusNotFound, models.NotFoundResponse("backup task"))
			return
		}
		if err.Error() == "permission denied: you can only view your own backup tasks" {
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		c.JSON(http.StatusInternalServerError, models.InternalErrorResponse())
		return
	}

	// 返回响应
	response := task.ToResponse()
	c.JSON(http.StatusOK, models.SuccessResponse(response))
}

// UpdateBackupTask 更新备份任务
// @Summary 更新备份任务
// @Description 更新指定备份任务的配置信息
// @Tags 备份管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "任务ID"
// @Param request body models.BackupTaskUpdateRequest true "更新信息"
// @Success 200 {object} models.CommonResponse{data=models.BackupTaskResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/maintenance/backup/tasks/{id} [put]
func (h *BackupHandler) UpdateBackupTask(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析任务ID
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse("invalid task ID"))
		return
	}

	// 解析请求
	var req models.BackupTaskUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind request: %v", err)
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse(err.Error()))
		return
	}

	// 更新备份任务
	task, err := h.backupService.UpdateTask(uint(taskID), &req, currentUser.ID)
	if err != nil {
		logger.Errorf("Failed to update backup task: %v", err)
		if err.Error() == "backup task not found" {
			c.JSON(http.StatusNotFound, models.NotFoundResponse("backup task"))
			return
		}
		if err.Error() == "permission denied: you can only update your own backup tasks" {
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		c.JSON(http.StatusInternalServerError, models.InternalErrorResponse())
		return
	}

	// 返回响应
	response := task.ToResponse()
	c.JSON(http.StatusOK, models.SuccessResponse(response))
}

// DeleteBackupTask 删除备份任务
// @Summary 删除备份任务
// @Description 删除指定的备份任务及其相关历史记录
// @Tags 备份管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "任务ID"
// @Success 200 {object} models.CommonResponse
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 409 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/maintenance/backup/tasks/{id} [delete]
func (h *BackupHandler) DeleteBackupTask(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析任务ID
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse("invalid task ID"))
		return
	}

	// 删除备份任务
	err = h.backupService.DeleteTask(uint(taskID), currentUser.ID)
	if err != nil {
		logger.Errorf("Failed to delete backup task: %v", err)
		if err.Error() == "backup task not found" {
			c.JSON(http.StatusNotFound, models.NotFoundResponse("backup task"))
			return
		}
		if err.Error() == "permission denied: you can only delete your own backup tasks" {
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		if err.Error() == "cannot delete task: backup is currently running" {
			c.JSON(http.StatusConflict, models.ErrorResponse(409, err.Error()))
			return
		}
		c.JSON(http.StatusInternalServerError, models.InternalErrorResponse())
		return
	}

	// 返回成功响应
	c.JSON(http.StatusOK, models.SuccessResponse(gin.H{
		"message": "Backup task deleted successfully",
	}))
}

// ExecuteBackup 执行备份
// @Summary 执行备份任务
// @Description 手动执行指定的备份任务
// @Tags 备份管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "任务ID"
// @Param force query bool false "是否强制执行" default(false)
// @Success 200 {object} models.CommonResponse{data=models.BackupExecuteResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 409 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/maintenance/backup/tasks/{id}/execute [post]
func (h *BackupHandler) ExecuteBackup(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析任务ID
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse("invalid task ID"))
		return
	}

	// 解析force参数
	forceStr := c.DefaultQuery("force", "false")
	force, err := strconv.ParseBool(forceStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse("invalid force parameter"))
		return
	}

	// 执行备份
	history, err := h.backupService.ExecuteBackup(uint(taskID), currentUser.ID, force)
	if err != nil {
		logger.Errorf("Failed to execute backup: %v", err)
		if err.Error() == "backup task not found" {
			c.JSON(http.StatusNotFound, models.NotFoundResponse("backup task"))
			return
		}
		if err.Error() == "permission denied: you can only execute your own backup tasks" {
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		if err.Error() == "backup task is not active" || err.Error() == "backup is already running for this task" {
			c.JSON(http.StatusConflict, models.ErrorResponse(409, err.Error()))
			return
		}
		c.JSON(http.StatusInternalServerError, models.InternalErrorResponse())
		return
	}

	// 返回响应
	response := &models.BackupExecuteResponse{
		HistoryID: history.ID,
		TaskID:    history.TaskID,
		Status:    history.Status,
		Message:   "Backup execution started successfully",
		StartTime: history.StartTime,
	}
	c.JSON(http.StatusOK, models.SuccessResponse(response))
}

// GetRunningBackups 获取正在运行的备份
// @Summary 获取正在运行的备份
// @Description 获取当前用户所有正在运行的备份任务
// @Tags 备份管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.CommonResponse{data=[]models.BackupHistoryResponse}
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/maintenance/backup/running [get]
func (h *BackupHandler) GetRunningBackups(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 获取正在运行的备份
	runningBackups, err := h.backupService.GetRunningBackups(currentUser.ID)
	if err != nil {
		logger.Errorf("Failed to get running backups: %v", err)
		c.JSON(http.StatusInternalServerError, models.InternalErrorResponse())
		return
	}

	// 转换为响应格式
	var responses []models.BackupHistoryResponse
	for _, backup := range runningBackups {
		responses = append(responses, *backup.ToResponse())
	}

	c.JSON(http.StatusOK, models.SuccessResponse(responses))
}

// CancelBackup 取消备份
// @Summary 取消正在运行的备份
// @Description 取消指定的正在运行的备份任务
// @Tags 备份管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "备份历史ID"
// @Success 200 {object} models.CommonResponse
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 409 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/maintenance/backup/history/{id}/cancel [post]
func (h *BackupHandler) CancelBackup(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析历史ID
	historyIDStr := c.Param("id")
	historyID, err := strconv.ParseUint(historyIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse("invalid history ID"))
		return
	}

	// 取消备份
	err = h.backupService.CancelBackup(uint(historyID), currentUser.ID)
	if err != nil {
		logger.Errorf("Failed to cancel backup: %v", err)
		if err.Error() == "backup history not found" {
			c.JSON(http.StatusNotFound, models.NotFoundResponse("backup history"))
			return
		}
		if err.Error() == "permission denied: you can only cancel your own backups" {
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		if err.Error() == "backup is not running" {
			c.JSON(http.StatusConflict, models.ErrorResponse(409, err.Error()))
			return
		}
		c.JSON(http.StatusInternalServerError, models.InternalErrorResponse())
		return
	}

	// 返回成功响应
	c.JSON(http.StatusOK, models.SuccessResponse(gin.H{
		"message": "Backup cancelled successfully",
	}))
}

// GetBackupHistory 获取备份历史列表
// @Summary 获取备份历史列表
// @Description 获取备份历史记录，支持分页和筛选
// @Tags 备份管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param task_id query int false "任务ID"
// @Param database_id query int false "数据库ID"
// @Param status query string false "备份状态" Enums(success,failed,running)
// @Param start_date query string false "开始日期" format(date-time)
// @Param end_date query string false "结束日期" format(date-time)
// @Success 200 {object} models.CommonResponse{data=models.BackupHistoryListResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/maintenance/backup/history [get]
func (h *BackupHandler) GetBackupHistory(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析查询参数
	var req models.BackupHistoryListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.Errorf("Failed to bind query: %v", err)
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse(err.Error()))
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 获取备份历史
	histories, total, err := h.backupService.GetBackupHistory(&req, currentUser.ID)
	if err != nil {
		logger.Errorf("Failed to get backup history: %v", err)
		if err.Error() == "backup task not found" || err.Error() == "database instance not found" {
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		c.JSON(http.StatusInternalServerError, models.InternalErrorResponse())
		return
	}

	// 转换为响应格式
	var historyResponses []models.BackupHistoryResponse
	for _, history := range histories {
		historyResponses = append(historyResponses, *history.ToResponse())
	}

	// 创建分页响应
	response := models.NewPaginationResponse(req.Page, req.PageSize, total, historyResponses)
	c.JSON(http.StatusOK, models.SuccessResponse(response))
}

// GetBackupHistoryByID 获取备份历史详情
// @Summary 获取备份历史详情
// @Description 根据ID获取备份历史的详细信息
// @Tags 备份管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "历史记录ID"
// @Success 200 {object} models.CommonResponse{data=models.BackupHistoryResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/maintenance/backup/history/{id} [get]
func (h *BackupHandler) GetBackupHistoryByID(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析历史ID
	historyIDStr := c.Param("id")
	historyID, err := strconv.ParseUint(historyIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse("invalid history ID"))
		return
	}

	// 获取备份历史
	history, err := h.backupService.GetHistoryByID(uint(historyID), currentUser.ID)
	if err != nil {
		logger.Errorf("Failed to get backup history: %v", err)
		if err.Error() == "backup history not found" {
			c.JSON(http.StatusNotFound, models.NotFoundResponse("backup history"))
			return
		}
		if err.Error() == "permission denied: you can only view your own backup history" {
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		c.JSON(http.StatusInternalServerError, models.InternalErrorResponse())
		return
	}

	// 返回响应
	response := history.ToResponse()
	c.JSON(http.StatusOK, models.SuccessResponse(response))
}

// DeleteBackupHistory 删除备份历史
// @Summary 删除备份历史记录
// @Description 删除指定的备份历史记录
// @Tags 备份管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "历史记录ID"
// @Success 200 {object} models.CommonResponse
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 409 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/maintenance/backup/history/{id} [delete]
func (h *BackupHandler) DeleteBackupHistory(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析历史ID
	historyIDStr := c.Param("id")
	historyID, err := strconv.ParseUint(historyIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse("invalid history ID"))
		return
	}

	// 删除备份历史
	err = h.backupService.DeleteHistory(uint(historyID), currentUser.ID)
	if err != nil {
		logger.Errorf("Failed to delete backup history: %v", err)
		if err.Error() == "backup history not found" {
			c.JSON(http.StatusNotFound, models.NotFoundResponse("backup history"))
			return
		}
		if err.Error() == "permission denied: you can only delete your own backup history" {
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		if err.Error() == "cannot delete running backup" {
			c.JSON(http.StatusConflict, models.ErrorResponse(409, err.Error()))
			return
		}
		c.JSON(http.StatusInternalServerError, models.InternalErrorResponse())
		return
	}

	// 返回成功响应
	c.JSON(http.StatusOK, models.SuccessResponse(gin.H{
		"message": "Backup history deleted successfully",
	}))
}

// GetBackupStats 获取备份统计信息
// @Summary 获取备份统计信息
// @Description 获取备份相关的统计数据，包括成功率、存储使用等
// @Tags 备份管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param database_id query int false "数据库ID"
// @Param start_date query string false "开始日期" format(date-time)
// @Param end_date query string false "结束日期" format(date-time)
// @Param period query string false "统计周期" Enums(day,week,month,year)
// @Success 200 {object} models.CommonResponse{data=models.BackupStatsResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/maintenance/backup/stats [get]
func (h *BackupHandler) GetBackupStats(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析查询参数
	var req models.BackupStatsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.Errorf("Failed to bind query: %v", err)
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse(err.Error()))
		return
	}

	// 获取备份统计
	stats, err := h.backupService.GetBackupStats(&req, currentUser.ID)
	if err != nil {
		logger.Errorf("Failed to get backup stats: %v", err)
		if err.Error() == "database instance not found" {
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		c.JSON(http.StatusInternalServerError, models.InternalErrorResponse())
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(stats))
}

// GetTaskStats 获取任务统计信息
// @Summary 获取任务统计信息
// @Description 获取当前用户的备份任务统计信息
// @Tags 备份管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.CommonResponse{data=repository.BackupTaskStats}
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/maintenance/backup/tasks/stats [get]
func (h *BackupHandler) GetTaskStats(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 获取任务统计
	stats, err := h.backupService.GetTaskStats(currentUser.ID)
	if err != nil {
		logger.Errorf("Failed to get task stats: %v", err)
		c.JSON(http.StatusInternalServerError, models.InternalErrorResponse())
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(stats))
}

// GetSuccessRate 获取成功率
// @Summary 获取备份成功率
// @Description 获取指定时间段内的备份成功率
// @Tags 备份管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param task_id query int false "任务ID"
// @Param days query int false "天数" default(30)
// @Success 200 {object} models.CommonResponse{data=map[string]float64}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 403 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/maintenance/backup/success-rate [get]
func (h *BackupHandler) GetSuccessRate(c *gin.Context) {
	// 获取当前用户信息
	currentUser, err := middleware.GetCurrentUser(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析查询参数
	var taskID *uint
	if taskIDStr := c.Query("task_id"); taskIDStr != "" {
		id, err := strconv.ParseUint(taskIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, models.ValidationErrorResponse("invalid task ID"))
			return
		}
		taskIDUint := uint(id)
		taskID = &taskIDUint
	}

	days := 30
	if daysStr := c.Query("days"); daysStr != "" {
		d, err := strconv.Atoi(daysStr)
		if err != nil || d <= 0 {
			c.JSON(http.StatusBadRequest, models.ValidationErrorResponse("invalid days parameter"))
			return
		}
		days = d
	}

	// 获取成功率
	successRate, err := h.backupService.GetSuccessRate(taskID, days, currentUser.ID)
	if err != nil {
		logger.Errorf("Failed to get success rate: %v", err)
		if err.Error() == "backup task not found" {
			c.JSON(http.StatusForbidden, models.ForbiddenResponse())
			return
		}
		c.JSON(http.StatusInternalServerError, models.InternalErrorResponse())
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(gin.H{
		"success_rate": successRate,
		"period_days":  days,
	}))
}

// DownloadBackupFile 下载备份文件
// @Summary 下载备份文件
// @Description 根据备份历史ID下载对应的备份文件
// @Tags 备份管理
// @Accept json
// @Produce application/octet-stream
// @Param id path int true "备份历史ID"
// @Success 200 {file} file "备份文件"
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Security ApiKeyAuth
// @Router /api/v1/maintenance/backup/history/{id}/download [get]
func (h *BackupHandler) DownloadBackupFile(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 解析备份历史ID
	historyIDStr := c.Param("id")
	historyID, err := strconv.ParseUint(historyIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse("Invalid history ID"))
		return
	}

	// 获取备份历史记录
	history, err := h.backupService.GetHistoryByID(uint(historyID), userID.(uint))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, models.NotFoundResponse("Backup history not found"))
			return
		}
		logger.Errorf("Failed to get backup history: %v", err)
		c.JSON(http.StatusInternalServerError, models.InternalErrorResponse())
		return
	}

	// 检查备份是否成功且文件存在
	if history.Status != "success" || history.FilePath == "" {
		c.JSON(http.StatusBadRequest, models.ValidationErrorResponse("Backup file not available"))
		return
	}

	// 检查文件是否存在
	if _, err := os.Stat(history.FilePath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, models.NotFoundResponse("Backup file not found on disk"))
		return
	}

	// 设置响应头
	fileName := filepath.Base(history.FilePath)
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	c.Header("Content-Type", "application/octet-stream")

	// 发送文件
	c.File(history.FilePath)
}
