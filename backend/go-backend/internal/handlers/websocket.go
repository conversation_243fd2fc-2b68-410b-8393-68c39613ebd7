package handlers

import (
	"encoding/json"
	"net/http"
	"sync"
	"time"

	"db-monitor-platform/internal/config"
	"db-monitor-platform/internal/models"
	"db-monitor-platform/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"gorm.io/gorm"
)

// WebSocketHandler WebSocket处理器
type WebSocketHandler struct {
	db       *gorm.DB
	cfg      *config.Config
	upgrader websocket.Upgrader
	clients  map[*websocket.Conn]*Client
	mutex    sync.RWMutex
	hub      *Hub
}

// Client WebSocket客户端
type Client struct {
	conn   *websocket.Conn
	userID uint
	send   chan []byte
	hub    *Hub
}

// Hub WebSocket连接管理中心
type Hub struct {
	clients    map[*Client]bool
	broadcast  chan []byte
	register   chan *Client
	unregister chan *Client
	mutex      sync.RWMutex
}

// Message WebSocket消息结构
type Message struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
}

// MetricUpdate 指标更新消息
type MetricUpdate struct {
	DatabaseID uint                   `json:"database_id"`
	Metrics    []models.MetricResponse `json:"metrics"`
}

// AlertUpdate 告警更新消息
type AlertUpdate struct {
	Event models.AlertEventResponse `json:"event"`
}

// NewWebSocketHandler 创建WebSocket处理器
func NewWebSocketHandler(db *gorm.DB, cfg *config.Config) *WebSocketHandler {
	upgrader := websocket.Upgrader{
		ReadBufferSize:  cfg.WebSocket.ReadBufferSize,
		WriteBufferSize: cfg.WebSocket.WriteBufferSize,
		CheckOrigin: func(r *http.Request) bool {
			return cfg.WebSocket.CheckOrigin
		},
	}

	hub := &Hub{
		clients:    make(map[*Client]bool),
		broadcast:  make(chan []byte),
		register:   make(chan *Client),
		unregister: make(chan *Client),
	}

	handler := &WebSocketHandler{
		db:       db,
		cfg:      cfg,
		upgrader: upgrader,
		clients:  make(map[*websocket.Conn]*Client),
		hub:      hub,
	}

	// 启动Hub
	go hub.run()

	return handler
}

// HandleWebSocket 处理WebSocket连接
func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
	// 升级HTTP连接为WebSocket
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		logger.GetLogger().Errorf("Failed to upgrade connection: %v", err)
		return
	}

	// 获取用户ID（从JWT token或查询参数）
	userID := h.getUserID(c)
	if userID == 0 {
		logger.GetLogger().Warn("WebSocket connection without valid user ID")
		conn.Close()
		return
	}

	// 创建客户端
	client := &Client{
		conn:   conn,
		userID: userID,
		send:   make(chan []byte, 256),
		hub:    h.hub,
	}

	// 注册客户端
	h.hub.register <- client

	// 启动goroutines
	go client.writePump(h.cfg)
	go client.readPump(h.cfg)

	logger.GetLogger().Infof("WebSocket client connected: user_id=%d", userID)
}

// getUserID 从请求中获取用户ID
func (h *WebSocketHandler) getUserID(c *gin.Context) uint {
	// 尝试从JWT token获取
	if userInterface, exists := c.Get("user"); exists {
		if user, ok := userInterface.(*models.User); ok {
			return user.ID
		}
	}

	// 尝试从查询参数获取（用于测试）
	// 注意：生产环境中应该移除这个方法
	if token := c.Query("token"); token != "" {
		// 这里可以验证token并返回用户ID
		// 为了简化，暂时返回1
		return 1
	}

	return 0
}

// Hub运行
func (h *Hub) run() {
	for {
		select {
		case client := <-h.register:
			h.mutex.Lock()
			h.clients[client] = true
			h.mutex.Unlock()
			logger.GetLogger().Infof("Client registered, total clients: %d", len(h.clients))

		case client := <-h.unregister:
			h.mutex.Lock()
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				close(client.send)
			}
			h.mutex.Unlock()
			logger.GetLogger().Infof("Client unregistered, total clients: %d", len(h.clients))

		case message := <-h.broadcast:
			h.mutex.RLock()
			for client := range h.clients {
				select {
				case client.send <- message:
				default:
					delete(h.clients, client)
					close(client.send)
				}
			}
			h.mutex.RUnlock()
		}
	}
}

// 客户端写入泵
func (c *Client) writePump(cfg *config.Config) {
	ticker := time.NewTicker(cfg.WebSocket.PingPeriod)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(cfg.WebSocket.WriteWait))
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// 添加队列中的其他消息
			n := len(c.send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(cfg.WebSocket.WriteWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// 客户端读取泵
func (c *Client) readPump(cfg *config.Config) {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	c.conn.SetReadLimit(512)
	c.conn.SetReadDeadline(time.Now().Add(cfg.WebSocket.PongWait))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(cfg.WebSocket.PongWait))
		return nil
	})

	for {
		_, _, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				logger.GetLogger().Errorf("WebSocket error: %v", err)
			}
			break
		}
	}
}

// BroadcastMetricUpdate 广播指标更新
func (h *WebSocketHandler) BroadcastMetricUpdate(databaseID uint, metrics []models.MetricResponse) {
	message := Message{
		Type: "metric_update",
		Data: MetricUpdate{
			DatabaseID: databaseID,
			Metrics:    metrics,
		},
		Timestamp: time.Now(),
	}

	data, err := json.Marshal(message)
	if err != nil {
		logger.GetLogger().Errorf("Failed to marshal metric update: %v", err)
		return
	}

	h.hub.broadcast <- data
}

// BroadcastAlertUpdate 广播告警更新
func (h *WebSocketHandler) BroadcastAlertUpdate(event models.AlertEventResponse) {
	message := Message{
		Type: "alert_update",
		Data: AlertUpdate{
			Event: event,
		},
		Timestamp: time.Now(),
	}

	data, err := json.Marshal(message)
	if err != nil {
		logger.GetLogger().Errorf("Failed to marshal alert update: %v", err)
		return
	}

	h.hub.broadcast <- data
}

// GetConnectedClients 获取连接的客户端数量
func (h *WebSocketHandler) GetConnectedClients() int {
	h.hub.mutex.RLock()
	defer h.hub.mutex.RUnlock()
	return len(h.hub.clients)
}

// SendToUser 向特定用户发送消息
func (h *WebSocketHandler) SendToUser(userID uint, messageType string, data interface{}) {
	message := Message{
		Type:      messageType,
		Data:      data,
		Timestamp: time.Now(),
	}

	messageData, err := json.Marshal(message)
	if err != nil {
		logger.GetLogger().Errorf("Failed to marshal message: %v", err)
		return
	}

	h.hub.mutex.RLock()
	defer h.hub.mutex.RUnlock()

	for client := range h.hub.clients {
		if client.userID == userID {
			select {
			case client.send <- messageData:
			default:
				delete(h.hub.clients, client)
				close(client.send)
			}
		}
	}
}
