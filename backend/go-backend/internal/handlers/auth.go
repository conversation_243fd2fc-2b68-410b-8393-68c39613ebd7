package handlers

import (
	"net/http"

	"db-monitor-platform/internal/config"
	"db-monitor-platform/internal/models"
	"db-monitor-platform/internal/services"
	"db-monitor-platform/internal/utils"
	"db-monitor-platform/pkg/logger"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	authService *services.AuthService
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(db *gorm.DB, cfg *config.Config) *AuthHandler {
	return &AuthHandler{
		authService: services.NewAuthService(db, cfg),
	}
}

// Register 用户注册
// @Summary 用户注册
// @Description 创建新用户账户
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body models.UserCreateRequest true "注册信息"
// @Success 201 {object} models.CommonResponse{data=models.UserResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 409 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/auth/register [post]
func (h *AuthHandler) Register(c *gin.Context) {
	var req models.UserCreateRequest
	
	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind request: %v", err)
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid request data"))
		return
	}

	// 注册用户
	user, err := h.authService.Register(&req)
	if err != nil {
		logger.Errorf("Registration failed: %v", err)
		
		// 根据错误类型返回不同状态码
		if err.Error() == "email already exists" {
			c.JSON(http.StatusConflict, models.ErrorResponse(409, err.Error()))
			return
		}
		
		// 检查是否为验证错误
		if validationErrors := utils.FormatValidationErrors(err); len(validationErrors) > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Validation failed",
				"errors":  validationErrors,
			})
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Registration failed"))
		return
	}

	logger.Infof("User registered successfully: %s", user.Email)
	c.JSON(http.StatusCreated, models.SuccessResponse(user))
}

// Login 用户登录
// @Summary 用户登录
// @Description 用户登录获取访问令牌
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body models.UserLoginRequest true "登录信息"
// @Success 200 {object} models.CommonResponse{data=services.LoginResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
	// 检查是否在文档模式
	if h.authService == nil {
		c.JSON(http.StatusServiceUnavailable, models.ErrorResponse(503, "Database not connected - running in documentation mode"))
		return
	}

	var req models.UserLoginRequest

	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind request: %v", err)
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid request data"))
		return
	}

	// 用户登录
	response, err := h.authService.Login(&req)
	if err != nil {
		logger.Errorf("Login failed: %v", err)
		
		// 检查是否为验证错误
		if validationErrors := utils.FormatValidationErrors(err); len(validationErrors) > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Validation failed",
				"errors":  validationErrors,
			})
			return
		}
		
		// 认证失败
		if err.Error() == "invalid email or password" || err.Error() == "account is disabled" {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse(401, err.Error()))
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Login failed"))
		return
	}

	logger.Infof("User logged in successfully: %s", response.User.Email)
	c.JSON(http.StatusOK, models.SuccessResponse(response))
}

// GetProfile 获取用户资料
// @Summary 获取用户资料
// @Description 获取当前登录用户的资料信息
// @Tags 用户
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.CommonResponse{data=models.UserResponse}
// @Failure 401 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/profile [get]
func (h *AuthHandler) GetProfile(c *gin.Context) {
	// 从上下文获取用户ID（由认证中间件设置）
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	// 获取用户资料
	user, err := h.authService.GetProfile(userID.(uint))
	if err != nil {
		logger.Errorf("Failed to get profile: %v", err)
		
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, models.NotFoundResponse("User"))
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to get profile"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(user))
}

// UpdateProfile 更新用户资料
// @Summary 更新用户资料
// @Description 更新当前登录用户的资料信息
// @Tags 用户
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.UserUpdateRequest true "更新信息"
// @Success 200 {object} models.CommonResponse{data=models.UserResponse}
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 404 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/profile [put]
func (h *AuthHandler) UpdateProfile(c *gin.Context) {
	// 从上下文获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	var req models.UserUpdateRequest
	
	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind request: %v", err)
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid request data"))
		return
	}

	// 更新用户资料
	user, err := h.authService.UpdateProfile(userID.(uint), &req)
	if err != nil {
		logger.Errorf("Failed to update profile: %v", err)
		
		// 检查是否为验证错误
		if validationErrors := utils.FormatValidationErrors(err); len(validationErrors) > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Validation failed",
				"errors":  validationErrors,
			})
			return
		}
		
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, models.NotFoundResponse("User"))
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to update profile"))
		return
	}

	logger.Infof("User profile updated: %s", user.Email)
	c.JSON(http.StatusOK, models.SuccessResponse(user))
}

// ChangePassword 修改密码
// @Summary 修改密码
// @Description 修改当前登录用户的密码
// @Tags 用户
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body services.ChangePasswordRequest true "密码信息"
// @Success 200 {object} models.CommonResponse
// @Failure 400 {object} models.CommonResponse
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/profile/password [put]
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	// 从上下文获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
		return
	}

	var req services.ChangePasswordRequest
	
	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to bind request: %v", err)
		c.JSON(http.StatusBadRequest, models.ErrorResponse(400, "Invalid request data"))
		return
	}

	// 修改密码
	err := h.authService.ChangePassword(userID.(uint), req.OldPassword, req.NewPassword)
	if err != nil {
		logger.Errorf("Failed to change password: %v", err)
		
		// 检查是否为验证错误
		if validationErrors := utils.FormatValidationErrors(err); len(validationErrors) > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Validation failed",
				"errors":  validationErrors,
			})
			return
		}
		
		if err.Error() == "invalid old password" {
			c.JSON(http.StatusBadRequest, models.ErrorResponse(400, err.Error()))
			return
		}
		
		c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, "Failed to change password"))
		return
	}

	logger.Infof("Password changed for user ID: %d", userID)
	c.JSON(http.StatusOK, models.SuccessResponse(gin.H{
		"message": "Password changed successfully",
	}))
}

// RefreshToken 刷新令牌
// @Summary 刷新访问令牌
// @Description 使用当前令牌刷新获取新的访问令牌
// @Tags 认证
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.CommonResponse{data=utils.TokenResponse}
// @Failure 401 {object} models.CommonResponse
// @Failure 500 {object} models.CommonResponse
// @Router /api/v1/auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	// 从Authorization header获取token
	authHeader := c.GetHeader("Authorization")
	tokenString, err := utils.ExtractTokenFromHeader(authHeader)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse(401, err.Error()))
		return
	}

	// 刷新token
	tokenResponse, err := h.authService.RefreshToken(tokenString)
	if err != nil {
		logger.Errorf("Failed to refresh token: %v", err)
		c.JSON(http.StatusUnauthorized, models.ErrorResponse(401, "Invalid or expired token"))
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse(tokenResponse))
}

// Logout 用户登出
// @Summary 用户登出
// @Description 用户登出（客户端需要删除本地token）
// @Tags 认证
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.CommonResponse
// @Router /api/v1/auth/logout [post]
func (h *AuthHandler) Logout(c *gin.Context) {
	// 在无状态JWT系统中，登出主要是客户端删除token
	// 这里可以添加token黑名单逻辑（如果需要的话）
	
	logger.Info("User logged out")
	c.JSON(http.StatusOK, models.SuccessResponse(gin.H{
		"message": "Logged out successfully",
	}))
}
