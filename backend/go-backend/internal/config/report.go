package config

import (
	"os"
	"strconv"
	"time"
)

// ReportConfig 报表配置
type ReportConfig struct {
	StoragePath        string        `json:"storage_path"`         // 报表存储路径
	MaxFileSize        int64         `json:"max_file_size"`        // 最大文件大小(字节)
	RetentionDays      int           `json:"retention_days"`       // 文件保留天数
	MaxConcurrentJobs  int           `json:"max_concurrent_jobs"`  // 最大并发任务数
	GenerationTimeout  time.Duration `json:"generation_timeout"`  // 生成超时时间
	CleanupInterval    time.Duration `json:"cleanup_interval"`    // 清理间隔
	TempCleanupInterval time.Duration `json:"temp_cleanup_interval"` // 临时文件清理间隔
}

// LoadReportConfig 加载报表配置
func LoadReportConfig() *ReportConfig {
	config := &ReportConfig{
		StoragePath:         getEnvString("REPORT_STORAGE_PATH", "./reports"),
		MaxFileSize:         getEnvInt64("REPORT_MAX_FILE_SIZE", 100*1024*1024), // 100MB
		RetentionDays:       getEnvInt("REPORT_RETENTION_DAYS", 30),
		MaxConcurrentJobs:   getEnvInt("REPORT_MAX_CONCURRENT_JOBS", 5),
		GenerationTimeout:   getEnvDuration("REPORT_GENERATION_TIMEOUT", 5*time.Minute),
		CleanupInterval:     getEnvDuration("REPORT_CLEANUP_INTERVAL", 24*time.Hour),
		TempCleanupInterval: getEnvDuration("REPORT_TEMP_CLEANUP_INTERVAL", 1*time.Hour),
	}
	
	return config
}

// getEnvString 获取字符串环境变量
func getEnvString(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvInt 获取整数环境变量
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvInt64 获取64位整数环境变量
func getEnvInt64(key string, defaultValue int64) int64 {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.ParseInt(value, 10, 64); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvDuration 获取时间间隔环境变量
func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}
