package redis

import (
	"context"
	"fmt"
	"time"

	"db-monitor-platform/internal/config"

	"github.com/go-redis/redis/v8"
)

// Init 初始化Redis客户端
func Init(cfg config.RedisConfig) (*redis.Client, error) {
	// 创建Redis客户端
	client := redis.NewClient(&redis.Options{
		Addr:         fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		Password:     cfg.Password,
		DB:           cfg.DB,
		PoolSize:     cfg.PoolSize,
		MinIdleConns: cfg.MinIdleConns,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := client.Ping(ctx).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to connect to redis: %w", err)
	}

	return client, nil
}

// Close 关闭Redis连接
func Close(client *redis.Client) error {
	return client.Close()
}
