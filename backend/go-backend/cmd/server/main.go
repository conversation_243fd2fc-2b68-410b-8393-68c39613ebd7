// @title Database Monitor Platform API
// @version 1.0
// @description A comprehensive database monitoring platform API with user authentication, database management, metrics collection, and alerting capabilities.
// @termsOfService http://swagger.io/terms/

// @contact.name Database Monitor Platform API Support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name X-API-Key
// @description API Key for monitoring agents

package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"db-monitor-platform/internal/config"
	"db-monitor-platform/internal/handlers"
	"db-monitor-platform/internal/middleware"
	"db-monitor-platform/internal/utils"
	"db-monitor-platform/pkg/database"
	"db-monitor-platform/pkg/logger"
	"db-monitor-platform/pkg/redis"

	"github.com/gin-gonic/gin"
)

func main() {
	// 初始化配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	logger.Init(cfg.Log)

	// 初始化数据库（允许失败，用于文档模式）
	db, err := database.Init(cfg.Database)
	if err != nil {
		log.Printf("Warning: Failed to initialize database: %v", err)
		log.Printf("Running in documentation-only mode")
		db = nil
	}

	// 初始化Redis（允许失败）
	redisClient, err := redis.Init(cfg.Redis)
	if err != nil {
		log.Printf("Warning: Failed to initialize redis: %v", err)
		redisClient = nil
	}

	// 初始化报表存储目录
	if err := utils.InitializeReportStorage(); err != nil {
		log.Printf("Warning: Failed to initialize report storage: %v", err)
	} else {
		log.Printf("Report storage initialized at: %s", utils.GetReportStoragePath())
	}

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 创建Gin引擎
	r := gin.New()

	// 添加中间件
	r.Use(middleware.Logger())
	r.Use(middleware.Recovery())
	r.Use(middleware.CORS(cfg.CORS))

	// 设置路由
	handlers.SetupRoutes(r, db, redisClient, cfg)

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler:      r,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
	}

	// 启动服务器
	go func() {
		log.Printf("Server starting on %s:%d", cfg.Server.Host, cfg.Server.Port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// 等待中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// 设置5秒的超时时间来关闭服务器
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}

	log.Println("Server exited")
}
