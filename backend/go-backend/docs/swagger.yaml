basePath: /
definitions:
  models.AlertEvent:
    properties:
      alert_rule_id:
        type: integer
      created_at:
        type: string
      database:
        allOf:
        - $ref: '#/definitions/models.DatabaseInstance'
        description: 关联关系
      database_id:
        type: integer
      duration:
        description: 持续时间（秒）
        type: integer
      end_time:
        type: string
      id:
        type: integer
      message:
        type: string
      metric_type:
        type: string
      operator:
        type: string
      resolved_at:
        type: string
      resolved_by:
        type: integer
      resolver:
        $ref: '#/definitions/models.User'
      rule:
        $ref: '#/definitions/models.AlertRule'
      severity:
        type: string
      start_time:
        type: string
      status:
        enum:
        - active
        - resolved
        - suppressed
        type: string
      threshold:
        type: number
      updated_at:
        type: string
      value:
        type: number
    type: object
  models.AlertEventResponse:
    properties:
      alert_rule_id:
        type: integer
      created_at:
        type: string
      database:
        $ref: '#/definitions/models.DatabaseInstance'
      database_id:
        type: integer
      duration:
        type: integer
      end_time:
        type: string
      id:
        type: integer
      message:
        type: string
      metric_type:
        type: string
      operator:
        type: string
      resolved_at:
        type: string
      resolved_by:
        type: integer
      rule:
        $ref: '#/definitions/models.AlertRule'
      severity:
        type: string
      start_time:
        type: string
      status:
        type: string
      threshold:
        type: number
      updated_at:
        type: string
      value:
        type: number
    type: object
  models.AlertRule:
    properties:
      alert_events:
        items:
          $ref: '#/definitions/models.AlertEvent'
        type: array
      created_at:
        type: string
      created_by:
        type: integer
      database:
        allOf:
        - $ref: '#/definitions/models.DatabaseInstance'
        description: 关联关系
      database_id:
        type: integer
      description:
        type: string
      duration:
        description: 持续时间（秒）
        type: integer
      enabled:
        type: boolean
      id:
        type: integer
      metric_type:
        enum:
        - cpu
        - memory
        - disk
        - connections
        - qps
        - tps
        - slow_queries
        - locks
        type: string
      name:
        maxLength: 50
        minLength: 2
        type: string
      notification_channels:
        description: JSON格式存储通知渠道
        type: string
      operator:
        type: string
      severity:
        enum:
        - info
        - warning
        - error
        - critical
        type: string
      threshold:
        minimum: 0
        type: number
      updated_at:
        type: string
      user:
        $ref: '#/definitions/models.User'
    required:
    - metric_type
    - name
    - operator
    - threshold
    type: object
  models.AlertRuleCreateRequest:
    properties:
      database_id:
        type: integer
      description:
        maxLength: 500
        type: string
      duration:
        maximum: 3600
        minimum: 60
        type: integer
      enabled:
        type: boolean
      metric_type:
        enum:
        - cpu
        - memory
        - disk
        - connections
        - qps
        - tps
        - slow_queries
        - locks
        type: string
      name:
        maxLength: 50
        minLength: 2
        type: string
      notification_channels:
        type: string
      operator:
        type: string
      severity:
        enum:
        - info
        - warning
        - error
        - critical
        type: string
      threshold:
        minimum: 0
        type: number
    required:
    - database_id
    - metric_type
    - name
    - operator
    - threshold
    type: object
  models.AlertRuleResponse:
    properties:
      created_at:
        type: string
      created_by:
        type: integer
      database:
        $ref: '#/definitions/models.DatabaseInstance'
      database_id:
        type: integer
      description:
        type: string
      duration:
        type: integer
      enabled:
        type: boolean
      id:
        type: integer
      metric_type:
        type: string
      name:
        type: string
      notification_channels:
        type: string
      operator:
        type: string
      severity:
        type: string
      threshold:
        type: number
      updated_at:
        type: string
    type: object
  models.AlertRuleUpdateRequest:
    properties:
      description:
        maxLength: 500
        type: string
      duration:
        maximum: 3600
        minimum: 60
        type: integer
      enabled:
        type: boolean
      name:
        maxLength: 50
        minLength: 2
        type: string
      notification_channels:
        type: string
      operator:
        type: string
      severity:
        enum:
        - info
        - warning
        - error
        - critical
        type: string
      threshold:
        minimum: 0
        type: number
    type: object
  models.CommonResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  models.DatabaseCreateRequest:
    properties:
      database_name:
        example: production_db
        maxLength: 50
        minLength: 1
        type: string
      description:
        example: Production database server
        maxLength: 500
        type: string
      host:
        example: mysql.example.com
        type: string
      is_monitored:
        example: true
        type: boolean
      name:
        example: Production MySQL
        maxLength: 50
        minLength: 2
        type: string
      password:
        example: secure_password
        minLength: 1
        type: string
      port:
        example: 3306
        maximum: 65535
        minimum: 1
        type: integer
      tags:
        example: production,mysql,primary
        type: string
      type:
        example: mysql
        type: string
      username:
        example: db_user
        maxLength: 50
        minLength: 1
        type: string
    required:
    - database_name
    - host
    - name
    - password
    - port
    - type
    - username
    type: object
  models.DatabaseInstance:
    properties:
      alert_events:
        items:
          $ref: '#/definitions/models.AlertEvent'
        type: array
      alert_rules:
        items:
          $ref: '#/definitions/models.AlertRule'
        type: array
      created_at:
        type: string
      created_by:
        type: integer
      database_name:
        type: string
      description:
        type: string
      host:
        type: string
      id:
        type: integer
      is_monitored:
        type: boolean
      metrics:
        items:
          $ref: '#/definitions/models.Metric'
        type: array
      name:
        maxLength: 50
        minLength: 2
        type: string
      port:
        maximum: 65535
        minimum: 1
        type: integer
      status:
        enum:
        - active
        - inactive
        - error
        type: string
      tags:
        description: JSON格式存储标签
        type: string
      type:
        enum:
        - mysql
        - postgresql
        - mongodb
        - redis
        type: string
      updated_at:
        type: string
      user:
        allOf:
        - $ref: '#/definitions/models.User'
        description: 关联关系
      username:
        type: string
    required:
    - host
    - name
    - port
    - type
    type: object
  models.DatabaseResponse:
    properties:
      created_at:
        type: string
      created_by:
        type: integer
      database_name:
        type: string
      description:
        type: string
      host:
        type: string
      id:
        type: integer
      is_monitored:
        type: boolean
      name:
        type: string
      port:
        type: integer
      status:
        type: string
      tags:
        type: string
      type:
        type: string
      updated_at:
        type: string
      user:
        $ref: '#/definitions/models.User'
      username:
        type: string
    type: object
  models.DatabaseUpdateRequest:
    properties:
      database_name:
        type: string
      description:
        maxLength: 500
        type: string
      host:
        type: string
      is_monitored:
        type: boolean
      name:
        maxLength: 50
        minLength: 2
        type: string
      password:
        type: string
      port:
        maximum: 65535
        minimum: 1
        type: integer
      tags:
        type: string
      username:
        type: string
    type: object
  models.Metric:
    properties:
      created_at:
        type: string
      database:
        allOf:
        - $ref: '#/definitions/models.DatabaseInstance'
        description: 关联关系
      database_id:
        type: integer
      id:
        type: integer
      labels:
        description: JSON格式存储额外标签
        type: string
      metric_type:
        enum:
        - cpu
        - memory
        - disk
        - connections
        - qps
        - tps
        - slow_queries
        - locks
        type: string
      timestamp:
        type: string
      unit:
        type: string
      value:
        minimum: 0
        type: number
    required:
    - metric_type
    - unit
    - value
    type: object
  models.MetricAggregateResponse:
    properties:
      data_points:
        items:
          $ref: '#/definitions/models.MetricDataPoint'
        type: array
      metric_type:
        type: string
      summary:
        $ref: '#/definitions/models.MetricSummary'
      unit:
        type: string
    type: object
  models.MetricCreateRequest:
    properties:
      database_id:
        type: integer
      labels:
        type: string
      metric_type:
        enum:
        - cpu
        - memory
        - disk
        - connections
        - qps
        - tps
        - slow_queries
        - locks
        type: string
      timestamp:
        type: string
      unit:
        type: string
      value:
        minimum: 0
        type: number
    required:
    - database_id
    - metric_type
    - unit
    - value
    type: object
  models.MetricDataPoint:
    properties:
      avg:
        type: number
      count:
        type: integer
      max:
        type: number
      min:
        type: number
      timestamp:
        type: string
      value:
        type: number
    type: object
  models.MetricQueryRequest:
    properties:
      database_id:
        type: integer
      end_time:
        type: string
      interval:
        enum:
        - 1m
        - 5m
        - 15m
        - 30m
        - 1h
        - 6h
        - 12h
        - 1d
        type: string
      limit:
        maximum: 10000
        minimum: 1
        type: integer
      metric_type:
        type: string
      start_time:
        type: string
    required:
    - database_id
    - end_time
    - start_time
    type: object
  models.MetricResponse:
    properties:
      created_at:
        type: string
      database_id:
        type: integer
      id:
        type: integer
      labels:
        type: string
      metric_type:
        type: string
      timestamp:
        type: string
      unit:
        type: string
      value:
        type: number
    type: object
  models.MetricSummary:
    properties:
      avg:
        type: number
      count:
        type: integer
      current:
        type: number
      end_time:
        type: string
      max:
        type: number
      min:
        type: number
      start_time:
        type: string
    type: object
  models.MetricValue:
    properties:
      status:
        description: normal, warning, critical
        type: string
      threshold:
        type: number
      unit:
        type: string
      value:
        type: number
    type: object
  models.PaginationResponse:
    properties:
      data: {}
      page:
        type: integer
      page_size:
        type: integer
      total:
        type: integer
      total_pages:
        type: integer
    type: object
  models.RealtimeMetric:
    properties:
      database_id:
        type: integer
      database_name:
        type: string
      metrics:
        additionalProperties:
          $ref: '#/definitions/models.MetricValue'
        type: object
      status:
        type: string
      timestamp:
        type: string
    type: object
  models.User:
    properties:
      alert_rules:
        items:
          $ref: '#/definitions/models.AlertRule'
        type: array
      avatar_url:
        type: string
      created_at:
        type: string
      database_instances:
        description: 关联关系
        items:
          $ref: '#/definitions/models.DatabaseInstance'
        type: array
      email:
        type: string
      id:
        type: integer
      is_active:
        type: boolean
      last_login:
        type: string
      name:
        maxLength: 50
        minLength: 2
        type: string
      role:
        enum:
        - admin
        - user
        - viewer
        type: string
      updated_at:
        type: string
    required:
    - email
    - name
    type: object
  models.UserCreateRequest:
    properties:
      email:
        type: string
      name:
        maxLength: 50
        minLength: 2
        type: string
      password:
        minLength: 6
        type: string
      role:
        enum:
        - admin
        - user
        - viewer
        type: string
    required:
    - email
    - name
    - password
    type: object
  models.UserLoginRequest:
    properties:
      email:
        type: string
      password:
        type: string
    required:
    - email
    - password
    type: object
  models.UserResponse:
    properties:
      avatar_url:
        type: string
      created_at:
        type: string
      email:
        type: string
      id:
        type: integer
      is_active:
        type: boolean
      last_login:
        type: string
      name:
        type: string
      role:
        type: string
      updated_at:
        type: string
    type: object
  models.UserUpdateRequest:
    properties:
      avatar_url:
        type: string
      is_active:
        type: boolean
      name:
        maxLength: 50
        minLength: 2
        type: string
    type: object
  repository.AlertStats:
    properties:
      active_events:
        type: integer
      resolved_events:
        type: integer
      severity_stats:
        items:
          $ref: '#/definitions/repository.SeverityStat'
        type: array
      today_events:
        type: integer
      total_events:
        type: integer
    type: object
  repository.DatabaseStats:
    properties:
      active_count:
        type: integer
      monitored_count:
        type: integer
      total_count:
        type: integer
      type_stats:
        items:
          $ref: '#/definitions/repository.TypeStat'
        type: array
    type: object
  repository.MetricStats:
    properties:
      avg_value:
        type: number
      current_value:
        type: number
      data_points:
        type: integer
      last_updated:
        type: string
      max_value:
        type: number
      min_value:
        type: number
    type: object
  repository.SeverityStat:
    properties:
      count:
        type: integer
      severity:
        type: string
    type: object
  repository.TypeStat:
    properties:
      count:
        type: integer
      type:
        type: string
    type: object
  services.ChangePasswordRequest:
    properties:
      new_password:
        type: string
      old_password:
        type: string
    required:
    - new_password
    - old_password
    type: object
  services.ConnectionTestResult:
    properties:
      database_id:
        type: integer
      duration:
        description: 改为字符串格式，如 "150ms"
        type: string
      end_time:
        type: string
      error:
        type: string
      message:
        type: string
      start_time:
        type: string
      success:
        type: boolean
    type: object
  services.LoginResponse:
    properties:
      token:
        $ref: '#/definitions/utils.TokenResponse'
      user:
        $ref: '#/definitions/models.UserResponse'
    type: object
  services.MetricCollectionRequest:
    properties:
      database_id:
        type: integer
      metrics:
        items:
          $ref: '#/definitions/models.MetricCreateRequest'
        type: array
      timestamp:
        type: string
    required:
    - database_id
    - metrics
    type: object
  utils.TokenResponse:
    properties:
      access_token:
        type: string
      expires_at:
        type: string
      expires_in:
        type: integer
      refresh_token:
        type: string
      token_type:
        type: string
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: Database Monitor Platform API Support
  description: A comprehensive database monitoring platform API with user authentication,
    database management, metrics collection, and alerting capabilities.
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: Database Monitor Platform API
  version: "1.0"
paths:
  /api/v1/alerts/events:
    get:
      consumes:
      - application/json
      description: 获取告警事件列表（支持分页）
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.PaginationResponse'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 获取告警事件列表
      tags:
      - 告警管理
  /api/v1/alerts/events/{id}:
    get:
      consumes:
      - application/json
      description: 根据ID获取告警事件详情
      parameters:
      - description: 告警事件ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.AlertEventResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 获取告警事件详情
      tags:
      - 告警管理
  /api/v1/alerts/events/{id}/resolve:
    post:
      consumes:
      - application/json
      description: 标记告警事件为已解决
      parameters:
      - description: 告警事件ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 解决告警事件
      tags:
      - 告警管理
  /api/v1/alerts/rules:
    get:
      consumes:
      - application/json
      description: 获取告警规则列表（支持分页）
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.PaginationResponse'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 获取告警规则列表
      tags:
      - 告警管理
    post:
      consumes:
      - application/json
      description: 创建新的告警规则
      parameters:
      - description: 告警规则信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.AlertRuleCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.AlertRuleResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 创建告警规则
      tags:
      - 告警管理
  /api/v1/alerts/rules/{id}:
    delete:
      consumes:
      - application/json
      description: 删除告警规则
      parameters:
      - description: 告警规则ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 删除告警规则
      tags:
      - 告警管理
    get:
      consumes:
      - application/json
      description: 根据ID获取告警规则详情
      parameters:
      - description: 告警规则ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.AlertRuleResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 获取告警规则详情
      tags:
      - 告警管理
    put:
      consumes:
      - application/json
      description: 更新告警规则信息
      parameters:
      - description: 告警规则ID
        in: path
        name: id
        required: true
        type: integer
      - description: 更新信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.AlertRuleUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.AlertRuleResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 更新告警规则
      tags:
      - 告警管理
  /api/v1/alerts/rules/search:
    get:
      consumes:
      - application/json
      description: 根据关键词搜索告警规则
      parameters:
      - description: 搜索关键词
        in: query
        name: q
        type: string
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.PaginationResponse'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 搜索告警规则
      tags:
      - 告警管理
  /api/v1/alerts/stats:
    get:
      consumes:
      - application/json
      description: 获取告警的统计信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/repository.AlertStats'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 获取告警统计信息
      tags:
      - 告警管理
  /api/v1/auth/login:
    post:
      consumes:
      - application/json
      description: 用户登录获取访问令牌
      parameters:
      - description: 登录信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.UserLoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/services.LoginResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      summary: 用户登录
      tags:
      - 认证
  /api/v1/auth/logout:
    post:
      consumes:
      - application/json
      description: 用户登出（客户端需要删除本地token）
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 用户登出
      tags:
      - 认证
  /api/v1/auth/refresh:
    post:
      consumes:
      - application/json
      description: 使用当前令牌刷新获取新的访问令牌
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/utils.TokenResponse'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 刷新访问令牌
      tags:
      - 认证
  /api/v1/auth/register:
    post:
      consumes:
      - application/json
      description: 创建新用户账户
      parameters:
      - description: 注册信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.UserCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.UserResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      summary: 用户注册
      tags:
      - 认证
  /api/v1/databases:
    get:
      consumes:
      - application/json
      description: 获取数据库实例列表（支持分页）
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.PaginationResponse'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 获取数据库实例列表
      tags:
      - 数据库管理
    post:
      consumes:
      - application/json
      description: 创建新的数据库实例
      parameters:
      - description: 数据库实例信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.DatabaseCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.DatabaseResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 创建数据库实例
      tags:
      - 数据库管理
  /api/v1/databases/{id}:
    delete:
      consumes:
      - application/json
      description: 删除数据库实例
      parameters:
      - description: 数据库实例ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 删除数据库实例
      tags:
      - 数据库管理
    get:
      consumes:
      - application/json
      description: 根据ID获取数据库实例详情
      parameters:
      - description: 数据库实例ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.DatabaseResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 获取数据库实例详情
      tags:
      - 数据库管理
    put:
      consumes:
      - application/json
      description: 更新数据库实例信息
      parameters:
      - description: 数据库实例ID
        in: path
        name: id
        required: true
        type: integer
      - description: 更新信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.DatabaseUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.DatabaseResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 更新数据库实例
      tags:
      - 数据库管理
  /api/v1/databases/{id}/test:
    post:
      consumes:
      - application/json
      description: 测试数据库实例的连接状态
      parameters:
      - description: 数据库实例ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/services.ConnectionTestResult'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 测试数据库连接
      tags:
      - 数据库管理
  /api/v1/databases/search:
    get:
      consumes:
      - application/json
      description: 根据关键词搜索数据库实例
      parameters:
      - description: 搜索关键词
        in: query
        name: q
        type: string
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.PaginationResponse'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 搜索数据库实例
      tags:
      - 数据库管理
  /api/v1/databases/stats:
    get:
      consumes:
      - application/json
      description: 获取数据库实例的统计信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/repository.DatabaseStats'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 获取数据库统计信息
      tags:
      - 数据库管理
  /api/v1/metrics:
    post:
      consumes:
      - application/json
      description: 创建新的监控指标数据点
      parameters:
      - description: 监控指标信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.MetricCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.MetricResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 创建监控指标
      tags:
      - 监控指标
  /api/v1/metrics/{db_id}:
    get:
      consumes:
      - application/json
      description: 获取指定数据库的监控指标列表
      parameters:
      - description: 数据库ID
        in: path
        name: db_id
        required: true
        type: integer
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.PaginationResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 获取监控指标列表
      tags:
      - 监控指标
  /api/v1/metrics/{db_id}/history:
    get:
      consumes:
      - application/json
      description: 根据时间范围获取指定数据库的监控指标
      parameters:
      - description: 数据库ID
        in: path
        name: db_id
        required: true
        type: integer
      - description: 指标类型
        in: query
        name: metric_type
        type: string
      - description: 开始时间 (RFC3339格式)
        in: query
        name: start_time
        required: true
        type: string
      - description: 结束时间 (RFC3339格式)
        in: query
        name: end_time
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.MetricResponse'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 根据时间范围获取监控指标
      tags:
      - 监控指标
  /api/v1/metrics/{db_id}/latest:
    get:
      consumes:
      - application/json
      description: 获取指定数据库的最新监控指标
      parameters:
      - description: 数据库ID
        in: path
        name: db_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.MetricResponse'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 获取最新监控指标
      tags:
      - 监控指标
  /api/v1/metrics/{db_id}/stats:
    get:
      consumes:
      - application/json
      description: 获取指定指标的统计信息
      parameters:
      - description: 数据库ID
        in: path
        name: db_id
        required: true
        type: integer
      - description: 指标类型
        in: query
        name: metric_type
        required: true
        type: string
      - default: '"24h"'
        description: 统计时间范围
        in: query
        name: duration
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/repository.MetricStats'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 获取指标统计信息
      tags:
      - 监控指标
  /api/v1/metrics/{db_id}/types:
    get:
      consumes:
      - application/json
      description: 获取指定数据库的所有指标类型
      parameters:
      - description: 数据库ID
        in: path
        name: db_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  items:
                    type: string
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 获取指标类型列表
      tags:
      - 监控指标
  /api/v1/metrics/aggregate:
    post:
      consumes:
      - application/json
      description: 获取聚合的监控指标数据，支持时间间隔聚合
      parameters:
      - description: 查询参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.MetricQueryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.MetricAggregateResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 获取聚合监控指标
      tags:
      - 监控指标
  /api/v1/metrics/collect:
    post:
      consumes:
      - application/json
      description: 批量收集监控指标数据（用于监控代理）
      parameters:
      - description: 指标收集请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/services.MetricCollectionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - ApiKeyAuth: []
      summary: 收集监控指标
      tags:
      - 监控指标
  /api/v1/metrics/realtime:
    get:
      consumes:
      - application/json
      description: 获取用户可访问的所有数据库的实时监控指标
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.RealtimeMetric'
                  type: array
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 获取实时监控指标
      tags:
      - 监控指标
  /api/v1/profile:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户的资料信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.UserResponse'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 获取用户资料
      tags:
      - 用户
    put:
      consumes:
      - application/json
      description: 更新当前登录用户的资料信息
      parameters:
      - description: 更新信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.UserUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.CommonResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.UserResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 更新用户资料
      tags:
      - 用户
  /api/v1/profile/password:
    put:
      consumes:
      - application/json
      description: 修改当前登录用户的密码
      parameters:
      - description: 密码信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/services.ChangePasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.CommonResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.CommonResponse'
      security:
      - BearerAuth: []
      summary: 修改密码
      tags:
      - 用户
securityDefinitions:
  ApiKeyAuth:
    description: API Key for monitoring agents
    in: header
    name: X-API-Key
    type: apiKey
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
