// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "Database Monitor Platform API Support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/alerts/events": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取告警事件列表（支持分页）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "获取告警事件列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.PaginationResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/alerts/events/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取告警事件详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "获取告警事件详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "告警事件ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.AlertEventResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/alerts/events/{id}/resolve": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "标记告警事件为已解决",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "解决告警事件",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "告警事件ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/alerts/rules": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取告警规则列表（支持分页）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "获取告警规则列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.PaginationResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建新的告警规则",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "创建告警规则",
                "parameters": [
                    {
                        "description": "告警规则信息",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.AlertRuleCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.AlertRuleResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/alerts/rules/search": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据关键词搜索告警规则",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "搜索告警规则",
                "parameters": [
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "q",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.PaginationResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/alerts/rules/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取告警规则详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "获取告警规则详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "告警规则ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.AlertRuleResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新告警规则信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "更新告警规则",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "告警规则ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "更新信息",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.AlertRuleUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.AlertRuleResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除告警规则",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "删除告警规则",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "告警规则ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/alerts/stats": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取告警的统计信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "获取告警统计信息",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/repository.AlertStats"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/auth/login": {
            "post": {
                "description": "用户登录获取访问令牌",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "用户登录",
                "parameters": [
                    {
                        "description": "登录信息",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UserLoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.LoginResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/auth/logout": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "用户登出（客户端需要删除本地token）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "用户登出",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/auth/refresh": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "使用当前令牌刷新获取新的访问令牌",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "刷新访问令牌",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/utils.TokenResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/auth/register": {
            "post": {
                "description": "创建新用户账户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "用户注册",
                "parameters": [
                    {
                        "description": "注册信息",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UserCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/databases": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取数据库实例列表（支持分页）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "数据库管理"
                ],
                "summary": "获取数据库实例列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.PaginationResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建新的数据库实例",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "数据库管理"
                ],
                "summary": "创建数据库实例",
                "parameters": [
                    {
                        "description": "数据库实例信息",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.DatabaseCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.DatabaseResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/databases/search": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据关键词搜索数据库实例",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "数据库管理"
                ],
                "summary": "搜索数据库实例",
                "parameters": [
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "q",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.PaginationResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/databases/stats": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取数据库实例的统计信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "数据库管理"
                ],
                "summary": "获取数据库统计信息",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/repository.DatabaseStats"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/databases/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取数据库实例详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "数据库管理"
                ],
                "summary": "获取数据库实例详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "数据库实例ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.DatabaseResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新数据库实例信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "数据库管理"
                ],
                "summary": "更新数据库实例",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "数据库实例ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "更新信息",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.DatabaseUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.DatabaseResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除数据库实例",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "数据库管理"
                ],
                "summary": "删除数据库实例",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "数据库实例ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/databases/{id}/test": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "测试数据库实例的连接状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "数据库管理"
                ],
                "summary": "测试数据库连接",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "数据库实例ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/services.ConnectionTestResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/metrics": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建新的监控指标数据点",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "监控指标"
                ],
                "summary": "创建监控指标",
                "parameters": [
                    {
                        "description": "监控指标信息",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.MetricCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.MetricResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/metrics/aggregate": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取聚合的监控指标数据，支持时间间隔聚合",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "监控指标"
                ],
                "summary": "获取聚合监控指标",
                "parameters": [
                    {
                        "description": "查询参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.MetricQueryRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.MetricAggregateResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/metrics/collect": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "批量收集监控指标数据（用于监控代理）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "监控指标"
                ],
                "summary": "收集监控指标",
                "parameters": [
                    {
                        "description": "指标收集请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/services.MetricCollectionRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/metrics/realtime": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取用户可访问的所有数据库的实时监控指标",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "监控指标"
                ],
                "summary": "获取实时监控指标",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.RealtimeMetric"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/metrics/{db_id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取指定数据库的监控指标列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "监控指标"
                ],
                "summary": "获取监控指标列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "数据库ID",
                        "name": "db_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.PaginationResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/metrics/{db_id}/history": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据时间范围获取指定数据库的监控指标",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "监控指标"
                ],
                "summary": "根据时间范围获取监控指标",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "数据库ID",
                        "name": "db_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "指标类型",
                        "name": "metric_type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "开始时间 (RFC3339格式)",
                        "name": "start_time",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "结束时间 (RFC3339格式)",
                        "name": "end_time",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.MetricResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/metrics/{db_id}/latest": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取指定数据库的最新监控指标",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "监控指标"
                ],
                "summary": "获取最新监控指标",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "数据库ID",
                        "name": "db_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.MetricResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/metrics/{db_id}/stats": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取指定指标的统计信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "监控指标"
                ],
                "summary": "获取指标统计信息",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "数据库ID",
                        "name": "db_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "指标类型",
                        "name": "metric_type",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "\"24h\"",
                        "description": "统计时间范围",
                        "name": "duration",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/repository.MetricStats"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/metrics/{db_id}/types": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取指定数据库的所有指标类型",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "监控指标"
                ],
                "summary": "获取指标类型列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "数据库ID",
                        "name": "db_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "type": "string"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/profile": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前登录用户的资料信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户"
                ],
                "summary": "获取用户资料",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新当前登录用户的资料信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户"
                ],
                "summary": "更新用户资料",
                "parameters": [
                    {
                        "description": "更新信息",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UserUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.CommonResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/profile/password": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "修改当前登录用户的密码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户"
                ],
                "summary": "修改密码",
                "parameters": [
                    {
                        "description": "密码信息",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/services.ChangePasswordRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.CommonResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "models.AlertEvent": {
            "type": "object",
            "properties": {
                "alert_rule_id": {
                    "type": "integer"
                },
                "created_at": {
                    "type": "string"
                },
                "database": {
                    "description": "关联关系",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.DatabaseInstance"
                        }
                    ]
                },
                "database_id": {
                    "type": "integer"
                },
                "duration": {
                    "description": "持续时间（秒）",
                    "type": "integer"
                },
                "end_time": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                },
                "metric_type": {
                    "type": "string"
                },
                "operator": {
                    "type": "string"
                },
                "resolved_at": {
                    "type": "string"
                },
                "resolved_by": {
                    "type": "integer"
                },
                "resolver": {
                    "$ref": "#/definitions/models.User"
                },
                "rule": {
                    "$ref": "#/definitions/models.AlertRule"
                },
                "severity": {
                    "type": "string"
                },
                "start_time": {
                    "type": "string"
                },
                "status": {
                    "type": "string",
                    "enum": [
                        "active",
                        "resolved",
                        "suppressed"
                    ]
                },
                "threshold": {
                    "type": "number"
                },
                "updated_at": {
                    "type": "string"
                },
                "value": {
                    "type": "number"
                }
            }
        },
        "models.AlertEventResponse": {
            "type": "object",
            "properties": {
                "alert_rule_id": {
                    "type": "integer"
                },
                "created_at": {
                    "type": "string"
                },
                "database": {
                    "$ref": "#/definitions/models.DatabaseInstance"
                },
                "database_id": {
                    "type": "integer"
                },
                "duration": {
                    "type": "integer"
                },
                "end_time": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                },
                "metric_type": {
                    "type": "string"
                },
                "operator": {
                    "type": "string"
                },
                "resolved_at": {
                    "type": "string"
                },
                "resolved_by": {
                    "type": "integer"
                },
                "rule": {
                    "$ref": "#/definitions/models.AlertRule"
                },
                "severity": {
                    "type": "string"
                },
                "start_time": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "threshold": {
                    "type": "number"
                },
                "updated_at": {
                    "type": "string"
                },
                "value": {
                    "type": "number"
                }
            }
        },
        "models.AlertRule": {
            "type": "object",
            "required": [
                "metric_type",
                "name",
                "operator",
                "threshold"
            ],
            "properties": {
                "alert_events": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.AlertEvent"
                    }
                },
                "created_at": {
                    "type": "string"
                },
                "created_by": {
                    "type": "integer"
                },
                "database": {
                    "description": "关联关系",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.DatabaseInstance"
                        }
                    ]
                },
                "database_id": {
                    "type": "integer"
                },
                "description": {
                    "type": "string"
                },
                "duration": {
                    "description": "持续时间（秒）",
                    "type": "integer"
                },
                "enabled": {
                    "type": "boolean"
                },
                "id": {
                    "type": "integer"
                },
                "metric_type": {
                    "type": "string",
                    "enum": [
                        "cpu",
                        "memory",
                        "disk",
                        "connections",
                        "qps",
                        "tps",
                        "slow_queries",
                        "locks"
                    ]
                },
                "name": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 2
                },
                "notification_channels": {
                    "description": "JSON格式存储通知渠道",
                    "type": "string"
                },
                "operator": {
                    "type": "string"
                },
                "severity": {
                    "type": "string",
                    "enum": [
                        "info",
                        "warning",
                        "error",
                        "critical"
                    ]
                },
                "threshold": {
                    "type": "number",
                    "minimum": 0
                },
                "updated_at": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/models.User"
                }
            }
        },
        "models.AlertRuleCreateRequest": {
            "type": "object",
            "required": [
                "database_id",
                "metric_type",
                "name",
                "operator",
                "threshold"
            ],
            "properties": {
                "database_id": {
                    "type": "integer"
                },
                "description": {
                    "type": "string",
                    "maxLength": 500
                },
                "duration": {
                    "type": "integer",
                    "maximum": 3600,
                    "minimum": 60
                },
                "enabled": {
                    "type": "boolean"
                },
                "metric_type": {
                    "type": "string",
                    "enum": [
                        "cpu",
                        "memory",
                        "disk",
                        "connections",
                        "qps",
                        "tps",
                        "slow_queries",
                        "locks"
                    ]
                },
                "name": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 2
                },
                "notification_channels": {
                    "type": "string"
                },
                "operator": {
                    "type": "string"
                },
                "severity": {
                    "type": "string",
                    "enum": [
                        "info",
                        "warning",
                        "error",
                        "critical"
                    ]
                },
                "threshold": {
                    "type": "number",
                    "minimum": 0
                }
            }
        },
        "models.AlertRuleResponse": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "created_by": {
                    "type": "integer"
                },
                "database": {
                    "$ref": "#/definitions/models.DatabaseInstance"
                },
                "database_id": {
                    "type": "integer"
                },
                "description": {
                    "type": "string"
                },
                "duration": {
                    "type": "integer"
                },
                "enabled": {
                    "type": "boolean"
                },
                "id": {
                    "type": "integer"
                },
                "metric_type": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "notification_channels": {
                    "type": "string"
                },
                "operator": {
                    "type": "string"
                },
                "severity": {
                    "type": "string"
                },
                "threshold": {
                    "type": "number"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "models.AlertRuleUpdateRequest": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string",
                    "maxLength": 500
                },
                "duration": {
                    "type": "integer",
                    "maximum": 3600,
                    "minimum": 60
                },
                "enabled": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 2
                },
                "notification_channels": {
                    "type": "string"
                },
                "operator": {
                    "type": "string"
                },
                "severity": {
                    "type": "string",
                    "enum": [
                        "info",
                        "warning",
                        "error",
                        "critical"
                    ]
                },
                "threshold": {
                    "type": "number",
                    "minimum": 0
                }
            }
        },
        "models.CommonResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {},
                "message": {
                    "type": "string"
                }
            }
        },
        "models.DatabaseCreateRequest": {
            "type": "object",
            "required": [
                "database_name",
                "host",
                "name",
                "password",
                "port",
                "type",
                "username"
            ],
            "properties": {
                "database_name": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 1,
                    "example": "production_db"
                },
                "description": {
                    "type": "string",
                    "maxLength": 500,
                    "example": "Production database server"
                },
                "host": {
                    "type": "string",
                    "example": "mysql.example.com"
                },
                "is_monitored": {
                    "type": "boolean",
                    "example": true
                },
                "name": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 2,
                    "example": "Production MySQL"
                },
                "password": {
                    "type": "string",
                    "minLength": 1,
                    "example": "secure_password"
                },
                "port": {
                    "type": "integer",
                    "maximum": 65535,
                    "minimum": 1,
                    "example": 3306
                },
                "tags": {
                    "type": "string",
                    "example": "production,mysql,primary"
                },
                "type": {
                    "type": "string",
                    "example": "mysql"
                },
                "username": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 1,
                    "example": "db_user"
                }
            }
        },
        "models.DatabaseInstance": {
            "type": "object",
            "required": [
                "host",
                "name",
                "port",
                "type"
            ],
            "properties": {
                "alert_events": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.AlertEvent"
                    }
                },
                "alert_rules": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.AlertRule"
                    }
                },
                "created_at": {
                    "type": "string"
                },
                "created_by": {
                    "type": "integer"
                },
                "database_name": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "host": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "is_monitored": {
                    "type": "boolean"
                },
                "metrics": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Metric"
                    }
                },
                "name": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 2
                },
                "port": {
                    "type": "integer",
                    "maximum": 65535,
                    "minimum": 1
                },
                "status": {
                    "type": "string",
                    "enum": [
                        "active",
                        "inactive",
                        "error"
                    ]
                },
                "tags": {
                    "description": "JSON格式存储标签",
                    "type": "string"
                },
                "type": {
                    "type": "string",
                    "enum": [
                        "mysql",
                        "postgresql",
                        "mongodb",
                        "redis"
                    ]
                },
                "updated_at": {
                    "type": "string"
                },
                "user": {
                    "description": "关联关系",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.User"
                        }
                    ]
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "models.DatabaseResponse": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "created_by": {
                    "type": "integer"
                },
                "database_name": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "host": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "is_monitored": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "port": {
                    "type": "integer"
                },
                "status": {
                    "type": "string"
                },
                "tags": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/models.User"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "models.DatabaseUpdateRequest": {
            "type": "object",
            "properties": {
                "database_name": {
                    "type": "string"
                },
                "description": {
                    "type": "string",
                    "maxLength": 500
                },
                "host": {
                    "type": "string"
                },
                "is_monitored": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 2
                },
                "password": {
                    "type": "string"
                },
                "port": {
                    "type": "integer",
                    "maximum": 65535,
                    "minimum": 1
                },
                "tags": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "models.Metric": {
            "type": "object",
            "required": [
                "metric_type",
                "unit",
                "value"
            ],
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "database": {
                    "description": "关联关系",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.DatabaseInstance"
                        }
                    ]
                },
                "database_id": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "labels": {
                    "description": "JSON格式存储额外标签",
                    "type": "string"
                },
                "metric_type": {
                    "type": "string",
                    "enum": [
                        "cpu",
                        "memory",
                        "disk",
                        "connections",
                        "qps",
                        "tps",
                        "slow_queries",
                        "locks"
                    ]
                },
                "timestamp": {
                    "type": "string"
                },
                "unit": {
                    "type": "string"
                },
                "value": {
                    "type": "number",
                    "minimum": 0
                }
            }
        },
        "models.MetricAggregateResponse": {
            "type": "object",
            "properties": {
                "data_points": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.MetricDataPoint"
                    }
                },
                "metric_type": {
                    "type": "string"
                },
                "summary": {
                    "$ref": "#/definitions/models.MetricSummary"
                },
                "unit": {
                    "type": "string"
                }
            }
        },
        "models.MetricCreateRequest": {
            "type": "object",
            "required": [
                "database_id",
                "metric_type",
                "unit",
                "value"
            ],
            "properties": {
                "database_id": {
                    "type": "integer"
                },
                "labels": {
                    "type": "string"
                },
                "metric_type": {
                    "type": "string",
                    "enum": [
                        "cpu",
                        "memory",
                        "disk",
                        "connections",
                        "qps",
                        "tps",
                        "slow_queries",
                        "locks"
                    ]
                },
                "timestamp": {
                    "type": "string"
                },
                "unit": {
                    "type": "string"
                },
                "value": {
                    "type": "number",
                    "minimum": 0
                }
            }
        },
        "models.MetricDataPoint": {
            "type": "object",
            "properties": {
                "avg": {
                    "type": "number"
                },
                "count": {
                    "type": "integer"
                },
                "max": {
                    "type": "number"
                },
                "min": {
                    "type": "number"
                },
                "timestamp": {
                    "type": "string"
                },
                "value": {
                    "type": "number"
                }
            }
        },
        "models.MetricQueryRequest": {
            "type": "object",
            "required": [
                "database_id",
                "end_time",
                "start_time"
            ],
            "properties": {
                "database_id": {
                    "type": "integer"
                },
                "end_time": {
                    "type": "string"
                },
                "interval": {
                    "type": "string",
                    "enum": [
                        "1m",
                        "5m",
                        "15m",
                        "30m",
                        "1h",
                        "6h",
                        "12h",
                        "1d"
                    ]
                },
                "limit": {
                    "type": "integer",
                    "maximum": 10000,
                    "minimum": 1
                },
                "metric_type": {
                    "type": "string"
                },
                "start_time": {
                    "type": "string"
                }
            }
        },
        "models.MetricResponse": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "database_id": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "labels": {
                    "type": "string"
                },
                "metric_type": {
                    "type": "string"
                },
                "timestamp": {
                    "type": "string"
                },
                "unit": {
                    "type": "string"
                },
                "value": {
                    "type": "number"
                }
            }
        },
        "models.MetricSummary": {
            "type": "object",
            "properties": {
                "avg": {
                    "type": "number"
                },
                "count": {
                    "type": "integer"
                },
                "current": {
                    "type": "number"
                },
                "end_time": {
                    "type": "string"
                },
                "max": {
                    "type": "number"
                },
                "min": {
                    "type": "number"
                },
                "start_time": {
                    "type": "string"
                }
            }
        },
        "models.MetricValue": {
            "type": "object",
            "properties": {
                "status": {
                    "description": "normal, warning, critical",
                    "type": "string"
                },
                "threshold": {
                    "type": "number"
                },
                "unit": {
                    "type": "string"
                },
                "value": {
                    "type": "number"
                }
            }
        },
        "models.PaginationResponse": {
            "type": "object",
            "properties": {
                "data": {},
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "models.RealtimeMetric": {
            "type": "object",
            "properties": {
                "database_id": {
                    "type": "integer"
                },
                "database_name": {
                    "type": "string"
                },
                "metrics": {
                    "type": "object",
                    "additionalProperties": {
                        "$ref": "#/definitions/models.MetricValue"
                    }
                },
                "status": {
                    "type": "string"
                },
                "timestamp": {
                    "type": "string"
                }
            }
        },
        "models.User": {
            "type": "object",
            "required": [
                "email",
                "name"
            ],
            "properties": {
                "alert_rules": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.AlertRule"
                    }
                },
                "avatar_url": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "database_instances": {
                    "description": "关联关系",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.DatabaseInstance"
                    }
                },
                "email": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "is_active": {
                    "type": "boolean"
                },
                "last_login": {
                    "type": "string"
                },
                "name": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 2
                },
                "role": {
                    "type": "string",
                    "enum": [
                        "admin",
                        "user",
                        "viewer"
                    ]
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "models.UserCreateRequest": {
            "type": "object",
            "required": [
                "email",
                "name",
                "password"
            ],
            "properties": {
                "email": {
                    "type": "string"
                },
                "name": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 2
                },
                "password": {
                    "type": "string",
                    "minLength": 6
                },
                "role": {
                    "type": "string",
                    "enum": [
                        "admin",
                        "user",
                        "viewer"
                    ]
                }
            }
        },
        "models.UserLoginRequest": {
            "type": "object",
            "required": [
                "email",
                "password"
            ],
            "properties": {
                "email": {
                    "type": "string"
                },
                "password": {
                    "type": "string"
                }
            }
        },
        "models.UserResponse": {
            "type": "object",
            "properties": {
                "avatar_url": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "is_active": {
                    "type": "boolean"
                },
                "last_login": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "role": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "models.UserUpdateRequest": {
            "type": "object",
            "properties": {
                "avatar_url": {
                    "type": "string"
                },
                "is_active": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 2
                }
            }
        },
        "repository.AlertStats": {
            "type": "object",
            "properties": {
                "active_events": {
                    "type": "integer"
                },
                "resolved_events": {
                    "type": "integer"
                },
                "severity_stats": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/repository.SeverityStat"
                    }
                },
                "today_events": {
                    "type": "integer"
                },
                "total_events": {
                    "type": "integer"
                }
            }
        },
        "repository.DatabaseStats": {
            "type": "object",
            "properties": {
                "active_count": {
                    "type": "integer"
                },
                "monitored_count": {
                    "type": "integer"
                },
                "total_count": {
                    "type": "integer"
                },
                "type_stats": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/repository.TypeStat"
                    }
                }
            }
        },
        "repository.MetricStats": {
            "type": "object",
            "properties": {
                "avg_value": {
                    "type": "number"
                },
                "current_value": {
                    "type": "number"
                },
                "data_points": {
                    "type": "integer"
                },
                "last_updated": {
                    "type": "string"
                },
                "max_value": {
                    "type": "number"
                },
                "min_value": {
                    "type": "number"
                }
            }
        },
        "repository.SeverityStat": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "severity": {
                    "type": "string"
                }
            }
        },
        "repository.TypeStat": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "services.ChangePasswordRequest": {
            "type": "object",
            "required": [
                "new_password",
                "old_password"
            ],
            "properties": {
                "new_password": {
                    "type": "string"
                },
                "old_password": {
                    "type": "string"
                }
            }
        },
        "services.ConnectionTestResult": {
            "type": "object",
            "properties": {
                "database_id": {
                    "type": "integer"
                },
                "duration": {
                    "description": "改为字符串格式，如 \"150ms\"",
                    "type": "string"
                },
                "end_time": {
                    "type": "string"
                },
                "error": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                },
                "start_time": {
                    "type": "string"
                },
                "success": {
                    "type": "boolean"
                }
            }
        },
        "services.LoginResponse": {
            "type": "object",
            "properties": {
                "token": {
                    "$ref": "#/definitions/utils.TokenResponse"
                },
                "user": {
                    "$ref": "#/definitions/models.UserResponse"
                }
            }
        },
        "services.MetricCollectionRequest": {
            "type": "object",
            "required": [
                "database_id",
                "metrics"
            ],
            "properties": {
                "database_id": {
                    "type": "integer"
                },
                "metrics": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.MetricCreateRequest"
                    }
                },
                "timestamp": {
                    "type": "string"
                }
            }
        },
        "utils.TokenResponse": {
            "type": "object",
            "properties": {
                "access_token": {
                    "type": "string"
                },
                "expires_at": {
                    "type": "string"
                },
                "expires_in": {
                    "type": "integer"
                },
                "refresh_token": {
                    "type": "string"
                },
                "token_type": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "ApiKeyAuth": {
            "description": "API Key for monitoring agents",
            "type": "apiKey",
            "name": "X-API-Key",
            "in": "header"
        },
        "BearerAuth": {
            "description": "Type \"Bearer\" followed by a space and JWT token.",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8080",
	BasePath:         "/",
	Schemes:          []string{},
	Title:            "Database Monitor Platform API",
	Description:      "A comprehensive database monitoring platform API with user authentication, database management, metrics collection, and alerting capabilities.",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
