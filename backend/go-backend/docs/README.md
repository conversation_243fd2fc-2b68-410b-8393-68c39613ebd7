# API Documentation

## Overview

This directory contains the automatically generated Swagger/OpenAPI documentation for the Database Monitor Platform API.

## Files

- `docs.go` - Generated Go code for Swagger documentation
- `swagger.json` - OpenAPI specification in JSON format
- `swagger.yaml` - OpenAPI specification in YAML format
- `README.md` - This documentation file

## Accessing the API Documentation

### Swagger UI

When the server is running, you can access the interactive Swagger UI at:

```
http://localhost:8080/swagger/index.html
```

The Swagger UI provides:
- Interactive API documentation
- Ability to test API endpoints directly
- Request/response examples
- Authentication support

### API Specification Files

You can also access the raw OpenAPI specifications:

- JSON format: `http://localhost:8080/swagger/doc.json`
- YAML format: Available in the `swagger.yaml` file

## Authentication

The API uses two types of authentication:

### 1. Bearer Token (JWT)
For user authentication:
```
Authorization: Bearer <your-jwt-token>
```

### 2. API Key
For monitoring agents:
```
X-API-Key: <your-api-key>
```

## API Endpoints Overview

### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/logout` - User logout
- `POST /api/v1/auth/refresh` - Refresh JWT token

### User Management
- `GET /api/v1/profile` - Get user profile
- `PUT /api/v1/profile` - Update user profile
- `PUT /api/v1/profile/password` - Change password

### Database Management
- `GET /api/v1/databases` - List databases
- `POST /api/v1/databases` - Create database
- `GET /api/v1/databases/{id}` - Get database details
- `PUT /api/v1/databases/{id}` - Update database
- `DELETE /api/v1/databases/{id}` - Delete database
- `POST /api/v1/databases/{id}/test` - Test database connection
- `GET /api/v1/databases/search` - Search databases
- `GET /api/v1/databases/stats` - Get database statistics

### Metrics
- `POST /api/v1/metrics` - Create metric
- `GET /api/v1/metrics/{db_id}` - Get metrics for database
- `GET /api/v1/metrics/{db_id}/latest` - Get latest metrics
- `GET /api/v1/metrics/{db_id}/history` - Get historical metrics
- `GET /api/v1/metrics/{db_id}/types` - Get metric types
- `GET /api/v1/metrics/{db_id}/stats` - Get metric statistics
- `POST /api/v1/metrics/aggregate` - Get aggregated metrics
- `GET /api/v1/metrics/realtime` - Get realtime metrics
- `POST /api/v1/metrics/collect` - Collect metrics (for agents)

### Alerts
- `GET /api/v1/alerts/rules` - List alert rules
- `POST /api/v1/alerts/rules` - Create alert rule
- `GET /api/v1/alerts/rules/{id}` - Get alert rule details
- `PUT /api/v1/alerts/rules/{id}` - Update alert rule
- `DELETE /api/v1/alerts/rules/{id}` - Delete alert rule
- `GET /api/v1/alerts/rules/search` - Search alert rules
- `GET /api/v1/alerts/events` - List alert events
- `GET /api/v1/alerts/events/{id}` - Get alert event details
- `POST /api/v1/alerts/events/{id}/resolve` - Resolve alert event
- `GET /api/v1/alerts/stats` - Get alert statistics

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "code": 200,
  "message": "Success",
  "data": { ... }
}
```

### Error Response
```json
{
  "code": 400,
  "message": "Bad Request",
  "error": "Detailed error message"
}
```

### Validation Error Response
```json
{
  "code": 400,
  "message": "Validation failed",
  "errors": [
    {
      "field": "email",
      "tag": "required",
      "value": "",
      "message": "email is required"
    }
  ]
}
```

### Paginated Response
```json
{
  "code": 200,
  "data": [...],
  "meta": {
    "page": 1,
    "page_size": 20,
    "total": 100,
    "total_pages": 5
  }
}
```

## Regenerating Documentation

To regenerate the Swagger documentation after making changes to the API:

```bash
# Install swag if not already installed
go install github.com/swaggo/swag/cmd/swag@latest

# Generate documentation
swag init -g cmd/server/main.go -o docs
```

## Common HTTP Status Codes

- `200` - OK
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `500` - Internal Server Error

## Rate Limiting

API rate limiting may be implemented in the future. Check the response headers for rate limit information:

- `X-RateLimit-Limit` - Request limit per time window
- `X-RateLimit-Remaining` - Remaining requests in current window
- `X-RateLimit-Reset` - Time when the rate limit resets

## Support

For API support and questions, please contact:
- Email: <EMAIL>
- Documentation: Check the Swagger UI for detailed endpoint documentation
