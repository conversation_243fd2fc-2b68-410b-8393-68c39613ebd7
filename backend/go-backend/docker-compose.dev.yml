version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:14-alpine
    container_name: dbmonitor-postgres-dev
    environment:
      POSTGRES_DB: dbmonitor
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - dbmonitor-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: dbmonitor-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    networks:
      - dbmonitor-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # pgAdmin (可选的数据库管理工具)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: dbmonitor-pgadmin-dev
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data_dev:/var/lib/pgadmin
    networks:
      - dbmonitor-network
    depends_on:
      - postgres

volumes:
  postgres_data_dev:
    driver: local
  redis_data_dev:
    driver: local
  pgadmin_data_dev:
    driver: local

networks:
  dbmonitor-network:
    driver: bridge
