package main

import (
	"fmt"
	"log"
	"os"

	"db-monitor-platform/internal/config"
	"db-monitor-platform/internal/models"
	"db-monitor-platform/pkg/database"
	"db-monitor-platform/pkg/logger"

	"gorm.io/gorm"
)

func main() {
	// 初始化配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	logger.Init(cfg.Log)

	// 初始化数据库连接
	db, err := database.Init(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 获取底层的sql.DB对象用于关闭连接
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("Failed to get underlying sql.DB: %v", err)
	}
	defer sqlDB.Close()

	logger.Info("Starting database migration...")

	// 检查命令行参数
	if len(os.Args) > 1 {
		switch os.Args[1] {
		case "up":
			migrateUp(db)
		case "down":
			migrateDown(db)
		case "reset":
			migrateReset(db)
		case "status":
			migrateStatus(db)
		default:
			printUsage()
		}
	} else {
		// 默认执行向上迁移
		migrateUp(db)
	}
}

// migrateUp 执行向上迁移
func migrateUp(db *gorm.DB) {
	logger.Info("Running up migrations...")

	// 自动迁移所有模型
	if err := models.AutoMigrate(db); err != nil {
		log.Fatalf("Failed to auto migrate: %v", err)
	}

	// 创建额外的索引
	if err := models.CreateIndexes(db); err != nil {
		log.Fatalf("Failed to create indexes: %v", err)
	}

	logger.Info("✅ Migration completed successfully!")
	
	// 显示迁移状态
	migrateStatus(db)
}

// migrateDown 执行向下迁移（删除所有表）
func migrateDown(db *gorm.DB) {
	logger.Warn("Running down migrations (this will drop all tables)...")

	// 确认操作
	fmt.Print("Are you sure you want to drop all tables? (yes/no): ")
	var confirm string
	fmt.Scanln(&confirm)
	
	if confirm != "yes" {
		logger.Info("Migration cancelled")
		return
	}

	// 删除索引
	if err := models.DropIndexes(db); err != nil {
		logger.Errorf("Failed to drop indexes: %v", err)
	}

	// 删除所有表（按依赖关系逆序）
	tables := []string{
		"alert_events",
		"alert_rules", 
		"metrics",
		"database_instances",
		"users",
	}

	for _, table := range tables {
		if err := db.Migrator().DropTable(table); err != nil {
			logger.Errorf("Failed to drop table %s: %v", table, err)
		} else {
			logger.Infof("Dropped table: %s", table)
		}
	}

	logger.Info("✅ Down migration completed!")
}

// migrateReset 重置数据库（先down再up）
func migrateReset(db *gorm.DB) {
	logger.Info("Resetting database...")
	migrateDown(db)
	migrateUp(db)
}

// migrateStatus 显示迁移状态
func migrateStatus(db *gorm.DB) {
	logger.Info("Database migration status:")

	tables := []string{
		"users",
		"database_instances", 
		"metrics",
		"alert_rules",
		"alert_events",
	}

	fmt.Println("\n📊 Table Status:")
	fmt.Println("================")

	for _, table := range tables {
		if db.Migrator().HasTable(table) {
			// 获取表的记录数
			var count int64
			db.Table(table).Count(&count)
			fmt.Printf("✅ %-20s (records: %d)\n", table, count)
		} else {
			fmt.Printf("❌ %-20s (not exists)\n", table)
		}
	}

	// 检查索引
	fmt.Println("\n📈 Index Status:")
	fmt.Println("================")
	
	indexes := []string{
		"idx_metrics_database_time",
		"idx_metrics_type_time", 
		"idx_metrics_database_type_time",
		"idx_alert_events_database_status",
		"idx_alert_events_rule_time",
		"idx_database_instances_user_status",
	}

	for _, index := range indexes {
		var exists bool
		err := db.Raw(`
			SELECT EXISTS (
				SELECT 1 FROM pg_indexes 
				WHERE indexname = ?
			)
		`, index).Scan(&exists).Error
		
		if err != nil {
			fmt.Printf("❓ %-35s (check failed)\n", index)
		} else if exists {
			fmt.Printf("✅ %-35s\n", index)
		} else {
			fmt.Printf("❌ %-35s\n", index)
		}
	}

	fmt.Println()
}

// printUsage 打印使用说明
func printUsage() {
	fmt.Println("Database Migration Tool")
	fmt.Println("=======================")
	fmt.Println("Usage: go run scripts/migrate.go [command]")
	fmt.Println("")
	fmt.Println("Commands:")
	fmt.Println("  up      Run up migrations (default)")
	fmt.Println("  down    Run down migrations (drop all tables)")
	fmt.Println("  reset   Reset database (down + up)")
	fmt.Println("  status  Show migration status")
	fmt.Println("")
	fmt.Println("Examples:")
	fmt.Println("  go run scripts/migrate.go")
	fmt.Println("  go run scripts/migrate.go up")
	fmt.Println("  go run scripts/migrate.go status")
	fmt.Println("  go run scripts/migrate.go reset")
}
