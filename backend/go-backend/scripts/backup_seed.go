package main

import (
	"fmt"
	"log"
	"time"

	"db-monitor-platform/internal/config"
	"db-monitor-platform/internal/models"
	"db-monitor-platform/pkg/database"
	"db-monitor-platform/pkg/logger"
	"gorm.io/gorm"
)

func main() {
	// 初始化配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	logger.Init(cfg.Log)

	// 初始化数据库连接
	db, err := database.Init(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 获取底层的sql.DB对象用于关闭连接
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("Failed to get underlying sql.DB: %v", err)
	}
	defer sqlDB.Close()

	logger.Info("Creating backup seed data...")

	// 创建备份任务种子数据
	if err := createBackupTaskSeeds(db); err != nil {
		log.Fatalf("Failed to create backup task seeds: %v", err)
	}

	// 创建备份历史种子数据
	if err := createBackupHistorySeeds(db); err != nil {
		log.Fatalf("Failed to create backup history seeds: %v", err)
	}

	logger.Info("✅ Backup seed data created successfully!")
}

func createBackupTaskSeeds(db *gorm.DB) error {
	// 获取第一个用户和数据库实例
	var user models.User
	if err := db.First(&user).Error; err != nil {
		return err
	}

	var databases []models.DatabaseInstance
	if err := db.Limit(3).Find(&databases).Error; err != nil {
		return err
	}

	if len(databases) == 0 {
		logger.Warn("No database instances found, skipping backup task seeds")
		return nil
	}

	// 创建备份任务
	backupTasks := []models.BackupTask{
		{
			DatabaseID:    databases[0].ID,
			TaskName:      "PostgreSQL Daily Full Backup",
			BackupType:    "full",
			Schedule:      "0 2 * * *", // 每日凌晨2点
			RetentionDays: 30,
			Compression:   true,
			Encryption:    true,
			Status:        "active",
			BackupSize:    2147483648, // 2GB
			CreatedBy:     user.ID,
		},
		{
			DatabaseID:    databases[0].ID,
			TaskName:      "PostgreSQL Hourly Incremental",
			BackupType:    "incremental",
			Schedule:      "0 * * * *", // 每小时
			RetentionDays: 7,
			Compression:   true,
			Encryption:    false,
			Status:        "active",
			BackupSize:    104857600, // 100MB
			CreatedBy:     user.ID,
		},
	}

	// 如果有第二个数据库，添加更多任务
	if len(databases) > 1 {
		backupTasks = append(backupTasks, models.BackupTask{
			DatabaseID:    databases[1].ID,
			TaskName:      "MySQL Weekly Full Backup",
			BackupType:    "full",
			Schedule:      "0 3 * * 0", // 每周日凌晨3点
			RetentionDays: 90,
			Compression:   true,
			Encryption:    true,
			Status:        "active",
			BackupSize:    1073741824, // 1GB
			CreatedBy:     user.ID,
		})
	}

	// 如果有第三个数据库，添加暂停的任务
	if len(databases) > 2 {
		backupTasks = append(backupTasks, models.BackupTask{
			DatabaseID:    databases[2].ID,
			TaskName:      "MongoDB Test Backup",
			BackupType:    "differential",
			Schedule:      "0 4 * * 1-5", // 工作日凌晨4点
			RetentionDays: 14,
			Compression:   false,
			Encryption:    false,
			Status:        "paused",
			BackupSize:    0,
			CreatedBy:     user.ID,
		})
	}

	// 设置时间
	now := time.Now()
	for i := range backupTasks {
		backupTasks[i].CreatedAt = now.Add(-time.Duration(i*24) * time.Hour)
		backupTasks[i].UpdatedAt = backupTasks[i].CreatedAt
		
		// 设置最后备份时间和下次备份时间
		if backupTasks[i].Status == "active" {
			lastBackup := now.Add(-time.Duration(i+1) * time.Hour)
			backupTasks[i].LastBackup = &lastBackup
			
			nextBackup := now.Add(time.Duration(24-i) * time.Hour)
			backupTasks[i].NextBackup = &nextBackup
		}
	}

	// 批量创建
	for _, task := range backupTasks {
		if err := db.Create(&task).Error; err != nil {
			logger.Errorf("Failed to create backup task: %v", err)
			continue
		}
		logger.Infof("Created backup task: %s", task.TaskName)
	}

	return nil
}

func createBackupHistorySeeds(db *gorm.DB) error {
	// 获取所有备份任务
	var tasks []models.BackupTask
	if err := db.Find(&tasks).Error; err != nil {
		return err
	}

	if len(tasks) == 0 {
		logger.Warn("No backup tasks found, skipping backup history seeds")
		return nil
	}

	now := time.Now()
	
	// 为每个任务创建历史记录
	for _, task := range tasks {
		// 创建成功的备份历史
		for i := 0; i < 5; i++ {
			startTime := now.Add(-time.Duration(i*24+i) * time.Hour)
			endTime := startTime.Add(time.Duration(30+i*10) * time.Minute)
			
			history := models.BackupHistory{
				TaskID:       task.ID,
				Status:       "success",
				StartTime:    startTime,
				EndTime:      &endTime,
				BackupSize:   task.BackupSize + int64(i*1024*1024), // 增加一些变化
				FilePath:     fmt.Sprintf("/backups/%s_%s.sql.gz", task.TaskName, startTime.Format("20060102_150405")),
				Duration:     int(endTime.Sub(startTime).Seconds()),
				CreatedAt:    startTime,
				UpdatedAt:    endTime,
			}
			
			if err := db.Create(&history).Error; err != nil {
				logger.Errorf("Failed to create backup history: %v", err)
				continue
			}
		}
		
		// 创建一个失败的备份历史
		if task.Status == "active" {
			startTime := now.Add(-time.Duration(2) * time.Hour)
			endTime := startTime.Add(5 * time.Minute)
			
			failedHistory := models.BackupHistory{
				TaskID:       task.ID,
				Status:       "failed",
				StartTime:    startTime,
				EndTime:      &endTime,
				BackupSize:   0,
				FilePath:     "",
				ErrorMessage: "Connection timeout: unable to connect to database server",
				Duration:     int(endTime.Sub(startTime).Seconds()),
				CreatedAt:    startTime,
				UpdatedAt:    endTime,
			}
			
			if err := db.Create(&failedHistory).Error; err != nil {
				logger.Errorf("Failed to create failed backup history: %v", err)
			}
		}
		
		logger.Infof("Created backup histories for task: %s", task.TaskName)
	}

	return nil
}
