#!/bin/bash

# 数据库监控平台开发环境设置脚本

set -e

echo "🚀 Setting up development environment for DB Monitor Platform..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker is not installed. Please install Docker first.${NC}"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose is not installed. Please install Docker Compose first.${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Docker and Docker Compose are installed${NC}"
}

# 检查Go是否安装
check_go() {
    if ! command -v go &> /dev/null; then
        echo -e "${RED}❌ Go is not installed. Please install Go 1.21+ first.${NC}"
        exit 1
    fi
    
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    echo -e "${GREEN}✅ Go ${GO_VERSION} is installed${NC}"
}

# 启动数据库服务
start_databases() {
    echo -e "${BLUE}🐳 Starting database services...${NC}"
    
    # 停止可能存在的容器
    docker-compose -f docker-compose.dev.yml down 2>/dev/null || true
    
    # 启动服务
    docker-compose -f docker-compose.dev.yml up -d
    
    echo -e "${YELLOW}⏳ Waiting for databases to be ready...${NC}"
    
    # 等待PostgreSQL就绪
    echo -n "Waiting for PostgreSQL"
    until docker-compose -f docker-compose.dev.yml exec -T postgres pg_isready -U postgres &>/dev/null; do
        echo -n "."
        sleep 1
    done
    echo -e " ${GREEN}✅${NC}"
    
    # 等待Redis就绪
    echo -n "Waiting for Redis"
    until docker-compose -f docker-compose.dev.yml exec -T redis redis-cli ping &>/dev/null; do
        echo -n "."
        sleep 1
    done
    echo -e " ${GREEN}✅${NC}"
    
    echo -e "${GREEN}✅ Database services are ready!${NC}"
}

# 安装Go依赖
install_dependencies() {
    echo -e "${BLUE}📦 Installing Go dependencies...${NC}"
    go mod download
    go mod tidy
    echo -e "${GREEN}✅ Dependencies installed${NC}"
}

# 安装开发工具
install_dev_tools() {
    echo -e "${BLUE}🔧 Installing development tools...${NC}"
    
    # 安装Air (热重载工具)
    if ! command -v air &> /dev/null; then
        echo "Installing Air..."
        go install github.com/cosmtrek/air@latest
    fi
    
    # 安装golangci-lint (代码检查工具)
    if ! command -v golangci-lint &> /dev/null; then
        echo "Installing golangci-lint..."
        curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.54.2
    fi
    
    # 安装swag (API文档生成工具)
    if ! command -v swag &> /dev/null; then
        echo "Installing swag..."
        go install github.com/swaggo/swag/cmd/swag@latest
    fi
    
    echo -e "${GREEN}✅ Development tools installed${NC}"
}

# 创建必要的目录
create_directories() {
    echo -e "${BLUE}📁 Creating necessary directories...${NC}"
    mkdir -p logs
    mkdir -p tmp
    mkdir -p build
    echo -e "${GREEN}✅ Directories created${NC}"
}

# 显示服务信息
show_services_info() {
    echo -e "\n${GREEN}🎉 Development environment setup completed!${NC}\n"
    echo -e "${BLUE}📋 Service Information:${NC}"
    echo -e "  PostgreSQL: localhost:5432"
    echo -e "    Database: dbmonitor"
    echo -e "    Username: postgres"
    echo -e "    Password: password"
    echo -e ""
    echo -e "  Redis: localhost:6379"
    echo -e ""
    echo -e "  pgAdmin: http://localhost:5050"
    echo -e "    Email: <EMAIL>"
    echo -e "    Password: admin"
    echo -e ""
    echo -e "${BLUE}🚀 Quick Start Commands:${NC}"
    echo -e "  Start development server: ${YELLOW}air${NC} or ${YELLOW}go run cmd/server/main.go${NC}"
    echo -e "  Run tests: ${YELLOW}go test ./...${NC}"
    echo -e "  Build application: ${YELLOW}go build -o build/app cmd/server/main.go${NC}"
    echo -e "  Stop databases: ${YELLOW}docker-compose -f docker-compose.dev.yml down${NC}"
    echo -e ""
}

# 主函数
main() {
    echo -e "${BLUE}🔍 Checking prerequisites...${NC}"
    check_docker
    check_go
    
    create_directories
    start_databases
    install_dependencies
    install_dev_tools
    show_services_info
}

# 运行主函数
main "$@"
