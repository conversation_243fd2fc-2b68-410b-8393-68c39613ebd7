package main

import (
	"fmt"
	"log"
	"math/rand"
	"time"

	"db-monitor-platform/internal/config"
	"db-monitor-platform/internal/models"
	"db-monitor-platform/pkg/database"
	"db-monitor-platform/pkg/logger"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

func main() {
	// 初始化配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	logger.Init(cfg.Log)

	// 初始化数据库连接
	db, err := database.Init(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 获取底层的sql.DB对象用于关闭连接
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("Failed to get underlying sql.DB: %v", err)
	}
	defer sqlDB.Close()

	logger.Info("Starting database seeding...")

	// 创建种子数据
	if err := seedData(db); err != nil {
		log.Fatalf("Failed to seed data: %v", err)
	}

	logger.Info("✅ Database seeding completed successfully!")
}

// seedData 创建种子数据
func seedData(db *gorm.DB) error {
	// 创建用户
	users, err := createUsers(db)
	if err != nil {
		return fmt.Errorf("failed to create users: %w", err)
	}

	// 创建数据库实例
	databases, err := createDatabaseInstances(db, users)
	if err != nil {
		return fmt.Errorf("failed to create database instances: %w", err)
	}

	// 创建告警规则
	alertRules, err := createAlertRules(db, users, databases)
	if err != nil {
		return fmt.Errorf("failed to create alert rules: %w", err)
	}

	// 创建监控指标
	if err := createMetrics(db, databases); err != nil {
		return fmt.Errorf("failed to create metrics: %w", err)
	}

	// 创建告警事件
	if err := createAlertEvents(db, alertRules, databases); err != nil {
		return fmt.Errorf("failed to create alert events: %w", err)
	}

	return nil
}

// createUsers 创建用户
func createUsers(db *gorm.DB) ([]models.User, error) {
	logger.Info("Creating users...")

	// 检查是否已存在用户
	var count int64
	db.Model(&models.User{}).Count(&count)
	if count > 0 {
		logger.Info("Users already exist, skipping...")
		var users []models.User
		db.Find(&users)
		return users, nil
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte("password123"), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	users := []models.User{
		{
			Email:    "<EMAIL>",
			Password: string(hashedPassword),
			Name:     "系统管理员",
			Role:     "admin",
			IsActive: true,
		},
		{
			Email:    "<EMAIL>", 
			Password: string(hashedPassword),
			Name:     "普通用户",
			Role:     "user",
			IsActive: true,
		},
		{
			Email:    "<EMAIL>",
			Password: string(hashedPassword),
			Name:     "只读用户",
			Role:     "viewer",
			IsActive: true,
		},
	}

	for i := range users {
		if err := db.Create(&users[i]).Error; err != nil {
			return nil, err
		}
		logger.Infof("Created user: %s (%s)", users[i].Name, users[i].Email)
	}

	return users, nil
}

// createDatabaseInstances 创建数据库实例
func createDatabaseInstances(db *gorm.DB, users []models.User) ([]models.DatabaseInstance, error) {
	logger.Info("Creating database instances...")

	// 检查是否已存在数据库实例
	var count int64
	db.Model(&models.DatabaseInstance{}).Count(&count)
	if count > 0 {
		logger.Info("Database instances already exist, skipping...")
		var databases []models.DatabaseInstance
		db.Find(&databases)
		return databases, nil
	}

	adminUser := users[0] // 管理员用户

	databases := []models.DatabaseInstance{
		{
			Name:         "生产环境PostgreSQL",
			Type:         "postgresql",
			Host:         "prod-pg.example.com",
			Port:         5432,
			DatabaseName: "production",
			Username:     "postgres",
			Status:       "active",
			Description:  "生产环境主数据库",
			Tags:         `["production", "postgresql", "primary"]`,
			IsMonitored:  true,
			CreatedBy:    adminUser.ID,
		},
		{
			Name:         "开发环境MySQL",
			Type:         "mysql",
			Host:         "dev-mysql.example.com",
			Port:         3306,
			DatabaseName: "development",
			Username:     "root",
			Status:       "active",
			Description:  "开发环境数据库",
			Tags:         `["development", "mysql"]`,
			IsMonitored:  true,
			CreatedBy:    adminUser.ID,
		},
		{
			Name:         "缓存Redis",
			Type:         "redis",
			Host:         "redis.example.com",
			Port:         6379,
			Username:     "",
			Status:       "active",
			Description:  "Redis缓存服务器",
			Tags:         `["cache", "redis"]`,
			IsMonitored:  true,
			CreatedBy:    adminUser.ID,
		},
		{
			Name:         "MongoDB文档库",
			Type:         "mongodb",
			Host:         "mongo.example.com",
			Port:         27017,
			DatabaseName: "documents",
			Username:     "admin",
			Status:       "active",
			Description:  "MongoDB文档数据库",
			Tags:         `["mongodb", "documents"]`,
			IsMonitored:  true,
			CreatedBy:    adminUser.ID,
		},
	}

	for i := range databases {
		if err := db.Create(&databases[i]).Error; err != nil {
			return nil, err
		}
		logger.Infof("Created database instance: %s (%s)", databases[i].Name, databases[i].Type)
	}

	return databases, nil
}

// createAlertRules 创建告警规则
func createAlertRules(db *gorm.DB, users []models.User, databases []models.DatabaseInstance) ([]models.AlertRule, error) {
	logger.Info("Creating alert rules...")

	// 检查是否已存在告警规则
	var count int64
	db.Model(&models.AlertRule{}).Count(&count)
	if count > 0 {
		logger.Info("Alert rules already exist, skipping...")
		var rules []models.AlertRule
		db.Find(&rules)
		return rules, nil
	}

	adminUser := users[0]
	
	var alertRules []models.AlertRule
	
	// 为每个数据库创建基础告警规则
	for _, database := range databases {
		rules := []models.AlertRule{
			{
				Name:                 fmt.Sprintf("%s - CPU使用率告警", database.Name),
				DatabaseID:           database.ID,
				MetricType:           "cpu",
				Operator:             ">",
				Threshold:            80.0,
				Duration:             300,
				Severity:             "warning",
				Enabled:              true,
				NotificationChannels: `["email", "webhook"]`,
				Description:          "CPU使用率超过80%时触发告警",
				CreatedBy:            adminUser.ID,
			},
			{
				Name:                 fmt.Sprintf("%s - 内存使用率告警", database.Name),
				DatabaseID:           database.ID,
				MetricType:           "memory",
				Operator:             ">",
				Threshold:            85.0,
				Duration:             300,
				Severity:             "error",
				Enabled:              true,
				NotificationChannels: `["email"]`,
				Description:          "内存使用率超过85%时触发告警",
				CreatedBy:            adminUser.ID,
			},
		}
		
		alertRules = append(alertRules, rules...)
	}

	for i := range alertRules {
		if err := db.Create(&alertRules[i]).Error; err != nil {
			return nil, err
		}
		logger.Infof("Created alert rule: %s", alertRules[i].Name)
	}

	return alertRules, nil
}

// createMetrics 创建监控指标
func createMetrics(db *gorm.DB, databases []models.DatabaseInstance) error {
	logger.Info("Creating metrics...")

	// 检查是否已存在监控指标
	var count int64
	db.Model(&models.Metric{}).Count(&count)
	if count > 0 {
		logger.Info("Metrics already exist, skipping...")
		return nil
	}

	// 为每个数据库生成最近24小时的监控数据
	now := time.Now()
	startTime := now.Add(-24 * time.Hour)

	metricTypes := []string{"cpu", "memory", "disk", "connections"}
	
	for _, database := range databases {
		for _, metricType := range metricTypes {
			// 每5分钟一个数据点
			for t := startTime; t.Before(now); t = t.Add(5 * time.Minute) {
				value := generateRandomMetricValue(metricType)
				unit := models.GetMetricUnit(metricType)
				
				metric := models.Metric{
					DatabaseID: database.ID,
					MetricType: metricType,
					Value:      value,
					Unit:       unit,
					Labels:     "{}",  // 空的JSON对象
					Timestamp:  t,
				}
				
				if err := db.Create(&metric).Error; err != nil {
					return err
				}
			}
		}
		logger.Infof("Created metrics for database: %s", database.Name)
	}

	return nil
}

// createAlertEvents 创建告警事件
func createAlertEvents(db *gorm.DB, alertRules []models.AlertRule, databases []models.DatabaseInstance) error {
	logger.Info("Creating alert events...")

	// 检查是否已存在告警事件
	var count int64
	db.Model(&models.AlertEvent{}).Count(&count)
	if count > 0 {
		logger.Info("Alert events already exist, skipping...")
		return nil
	}

	// 创建一些示例告警事件
	now := time.Now()
	
	for i, rule := range alertRules[:3] { // 只为前3个规则创建事件
		startTime := now.Add(-time.Duration(i+1) * time.Hour)
		
		event := models.AlertEvent{
			AlertRuleID: rule.ID,
			DatabaseID:  rule.DatabaseID,
			MetricType:  rule.MetricType,
			Value:       rule.Threshold + 10, // 超过阈值
			Threshold:   rule.Threshold,
			Operator:    rule.Operator,
			Severity:    rule.Severity,
			Status:      "active",
			Message:     fmt.Sprintf("%s 指标值 %.2f 超过阈值 %.2f", rule.MetricType, rule.Threshold+10, rule.Threshold),
			StartTime:   startTime,
		}
		
		if err := db.Create(&event).Error; err != nil {
			return err
		}
		logger.Infof("Created alert event for rule: %s", rule.Name)
	}

	return nil
}

// generateRandomMetricValue 生成随机指标值
func generateRandomMetricValue(metricType string) float64 {
	switch metricType {
	case "cpu", "memory", "disk":
		// 百分比指标 0-100
		return rand.Float64() * 100
	case "connections":
		// 连接数 0-1000
		return rand.Float64() * 1000
	case "qps":
		// 每秒查询数 0-10000
		return rand.Float64() * 10000
	case "tps":
		// 每秒事务数 0-5000
		return rand.Float64() * 5000
	default:
		return rand.Float64() * 100
	}
}
