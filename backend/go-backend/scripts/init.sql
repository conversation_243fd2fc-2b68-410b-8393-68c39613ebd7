-- 数据库监控平台初始化脚本

-- 创建数据库（如果不存在）
-- CREATE DATABASE dbmonitor;

-- 连接到数据库
\c dbmonitor;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建用户角色枚举
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('admin', 'user', 'viewer');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建数据库类型枚举
DO $$ BEGIN
    CREATE TYPE database_type AS ENUM ('mysql', 'postgresql', 'mongodb', 'redis');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建告警严重级别枚举
DO $$ BEGIN
    CREATE TYPE alert_severity AS ENUM ('info', 'warning', 'error', 'critical');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建告警状态枚举
DO $$ BEGIN
    CREATE TYPE alert_status AS ENUM ('active', 'resolved', 'suppressed');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建索引函数
CREATE OR REPLACE FUNCTION create_index_if_not_exists(index_name text, table_name text, index_definition text)
RETURNS void AS $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE indexname = index_name
    ) THEN
        EXECUTE 'CREATE INDEX ' || index_name || ' ON ' || table_name || ' ' || index_definition;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 输出初始化完成信息
DO $$
BEGIN
    RAISE NOTICE 'Database initialization completed successfully!';
    RAISE NOTICE 'Database: dbmonitor';
    RAISE NOTICE 'Extensions: uuid-ossp, pg_stat_statements';
    RAISE NOTICE 'Timezone: Asia/Shanghai';
    RAISE NOTICE 'Custom types: user_role, database_type, alert_severity, alert_status';
END $$;
