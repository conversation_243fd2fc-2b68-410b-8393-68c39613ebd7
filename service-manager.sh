#!/bin/bash

# 数据库监控平台服务管理脚本
# 用于启动和停止开发环境中的所有服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
BACKEND_DIR="$PROJECT_ROOT/backend/go-backend"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # 端口被占用
    else
        return 1  # 端口未被占用
    fi
}

# 等待端口可用
wait_for_port() {
    local host=$1
    local port=$2
    local timeout=${3:-30}
    local count=0
    
    log_info "等待 $host:$port 可用..."
    
    while [ $count -lt $timeout ]; do
        if nc -z $host $port 2>/dev/null; then
            log_success "$host:$port 已可用"
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done
    
    log_error "等待 $host:$port 超时"
    return 1
}

# 启动Docker服务
start_docker() {
    log_info "启动Docker服务..."
    
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker未运行，请先启动Docker"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
    docker-compose up -d
    
    # 等待数据库服务启动
    wait_for_port localhost 5432 30
    wait_for_port localhost 6379 30
    wait_for_port localhost 3306 30
    
    log_success "Docker服务启动完成"
}

# 停止Docker服务
stop_docker() {
    log_info "停止Docker服务..."
    cd "$PROJECT_ROOT"
    docker-compose down
    log_success "Docker服务已停止"
}

# 启动后端服务
start_backend() {
    log_info "启动后端服务..."
    
    if check_port 8080; then
        log_warning "端口8080已被占用，跳过后端启动"
        return 0
    fi
    
    cd "$BACKEND_DIR"
    
    # 检查Go环境
    check_command go
    
    # 启动后端服务（后台运行）
    nohup go run cmd/server/main.go > backend.log 2>&1 &
    echo $! > backend.pid
    
    # 等待后端服务启动
    wait_for_port localhost 8080 30
    
    log_success "后端服务启动完成 (PID: $(cat backend.pid))"
}

# 停止后端服务
stop_backend() {
    log_info "停止后端服务..."
    
    cd "$BACKEND_DIR"
    
    if [ -f backend.pid ]; then
        local pid=$(cat backend.pid)
        if kill -0 $pid 2>/dev/null; then
            kill $pid
            rm -f backend.pid
            log_success "后端服务已停止"
        else
            log_warning "后端服务进程不存在"
            rm -f backend.pid
        fi
    else
        log_warning "未找到后端服务PID文件"
    fi
}

# 启动前端服务
start_frontend() {
    log_info "启动前端服务..."
    
    if check_port 5173; then
        log_warning "端口5173已被占用，跳过前端启动"
        return 0
    fi
    
    cd "$FRONTEND_DIR"
    
    # 检查Node.js环境
    check_command npm
    
    # 检查依赖是否安装
    if [ ! -d "node_modules" ]; then
        log_info "安装前端依赖..."
        npm install
    fi
    
    # 启动前端服务（后台运行）
    nohup npm run dev > frontend.log 2>&1 &
    echo $! > frontend.pid
    
    # 等待前端服务启动
    wait_for_port localhost 5173 30
    
    log_success "前端服务启动完成 (PID: $(cat frontend.pid))"
}

# 停止前端服务
stop_frontend() {
    log_info "停止前端服务..."
    
    cd "$FRONTEND_DIR"
    
    if [ -f frontend.pid ]; then
        local pid=$(cat frontend.pid)
        if kill -0 $pid 2>/dev/null; then
            kill $pid
            rm -f frontend.pid
            log_success "前端服务已停止"
        else
            log_warning "前端服务进程不存在"
            rm -f frontend.pid
        fi
    else
        log_warning "未找到前端服务PID文件"
    fi
}

# 检查服务状态
check_status() {
    log_info "检查服务状态..."
    
    echo ""
    echo "=== Docker 服务状态 ==="
    docker-compose ps
    
    echo ""
    echo "=== 端口占用情况 ==="
    echo "PostgreSQL (5432): $(check_port 5432 && echo "✅ 运行中" || echo "❌ 未运行")"
    echo "Redis (6379): $(check_port 6379 && echo "✅ 运行中" || echo "❌ 未运行")"
    echo "MySQL (3306): $(check_port 3306 && echo "✅ 运行中" || echo "❌ 未运行")"
    echo "后端API (8080): $(check_port 8080 && echo "✅ 运行中" || echo "❌ 未运行")"
    echo "前端Dev (5173): $(check_port 5173 && echo "✅ 运行中" || echo "❌ 未运行")"
    
    echo ""
    echo "=== 服务访问地址 ==="
    echo "前端应用: http://localhost:5173"
    echo "后端API: http://localhost:8080"
    echo "API文档: http://localhost:8080/swagger/index.html"
}

# 启动所有服务
start_all() {
    log_info "启动所有服务..."
    
    start_docker
    sleep 3  # 等待数据库完全启动
    start_backend
    sleep 2  # 等待后端启动
    start_frontend
    
    echo ""
    log_success "所有服务启动完成！"
    check_status
}

# 停止所有服务
stop_all() {
    log_info "停止所有服务..."
    
    stop_frontend
    stop_backend
    stop_docker
    
    log_success "所有服务已停止"
}

# 重启所有服务
restart_all() {
    log_info "重启所有服务..."
    stop_all
    sleep 2
    start_all
}

# 显示帮助信息
show_help() {
    echo "数据库监控平台服务管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start          启动所有服务 (Docker + 后端 + 前端)"
    echo "  stop           停止所有服务"
    echo "  restart        重启所有服务"
    echo "  status         检查服务状态"
    echo ""
    echo "  start-docker   仅启动Docker服务"
    echo "  stop-docker    仅停止Docker服务"
    echo "  start-backend  仅启动后端服务"
    echo "  stop-backend   仅停止后端服务"
    echo "  start-frontend 仅启动前端服务"
    echo "  stop-frontend  仅停止前端服务"
    echo ""
    echo "  help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start       # 启动所有服务"
    echo "  $0 stop        # 停止所有服务"
    echo "  $0 status      # 查看服务状态"
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            start_all
            ;;
        stop)
            stop_all
            ;;
        restart)
            restart_all
            ;;
        status)
            check_status
            ;;
        start-docker)
            start_docker
            ;;
        stop-docker)
            stop_docker
            ;;
        start-backend)
            start_backend
            ;;
        stop-backend)
            stop_backend
            ;;
        start-frontend)
            start_frontend
            ;;
        stop-frontend)
            stop_frontend
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
