#!/bin/bash

echo "=== 测试删除模板功能 ==="

# 获取访问token
echo "1. 获取访问token..."
TOKEN=$(curl -s -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}' | \
  python3 -c "import sys, json; data = json.load(sys.stdin); print(data['data']['token']['access_token'])" 2>/dev/null)

if [ -z "$TOKEN" ]; then
    echo "❌ 获取token失败"
    exit 1
fi

echo "✅ Token获取成功: ${TOKEN:0:30}..."

# 先获取模板列表
echo ""
echo "2. 获取当前模板列表..."
curl -s -X GET "http://localhost:8080/api/v1/reports/templates?page=1&page_size=10" \
  -H "Authorization: Bearer $TOKEN" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if 'data' in data and 'items' in data['data']:
        print('当前模板列表:')
        for item in data['data']['items']:
            print(f'   - ID: {item[\"id\"]}, 名称: {item[\"name\"]}, 创建者ID: {item[\"created_by\"]}')
    else:
        print('获取失败:', data)
except Exception as e:
    print('解析响应失败:', e)
"

# 创建一个测试模板用于删除
echo ""
echo "3. 创建测试模板..."
CREATE_RESULT=$(curl -s -X POST http://localhost:8080/api/v1/reports/templates \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "name": "待删除测试模板",
    "description": "这个模板将被删除",
    "type": "usage",
    "config": {"chart_type": "bar", "metrics": ["cpu_usage"]}
  }')

TEMPLATE_ID=$(echo "$CREATE_RESULT" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if 'data' in data:
        print('✅ 测试模板创建成功, ID:', data['data']['id'])
        print(data['data']['id'])
    else:
        print('❌ 创建失败:', data)
        print('0')
except Exception as e:
    print('❌ 解析响应失败:', e)
    print('0')
" | tail -1)

if [ "$TEMPLATE_ID" = "0" ]; then
    echo "❌ 无法创建测试模板，退出"
    exit 1
fi

# 测试删除模板
echo ""
echo "4. 测试删除模板 ID: $TEMPLATE_ID"
DELETE_RESPONSE=$(curl -s -w "HTTP_CODE:%{http_code}" -X DELETE "http://localhost:8080/api/v1/reports/templates/$TEMPLATE_ID" \
  -H "Authorization: Bearer $TOKEN")

HTTP_CODE=$(echo "$DELETE_RESPONSE" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
RESPONSE_BODY=$(echo "$DELETE_RESPONSE" | sed 's/HTTP_CODE:[0-9]*$//')

echo "HTTP状态码: $HTTP_CODE"
echo "响应内容: $RESPONSE_BODY"

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ 删除请求成功"
else
    echo "❌ 删除请求失败，状态码: $HTTP_CODE"
fi

# 再次获取模板列表确认删除
echo ""
echo "5. 确认删除后的模板列表..."
curl -s -X GET "http://localhost:8080/api/v1/reports/templates?page=1&page_size=10" \
  -H "Authorization: Bearer $TOKEN" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if 'data' in data and 'items' in data['data']:
        print('删除后模板列表:')
        found_deleted = False
        for item in data['data']['items']:
            if item['id'] == $TEMPLATE_ID:
                found_deleted = True
            print(f'   - ID: {item[\"id\"]}, 名称: {item[\"name\"]}')
        if not found_deleted:
            print('✅ 模板已成功删除')
        else:
            print('❌ 模板仍然存在')
    else:
        print('获取失败:', data)
except Exception as e:
    print('解析响应失败:', e)
"

echo ""
echo "🎉 删除功能测试完成！"
