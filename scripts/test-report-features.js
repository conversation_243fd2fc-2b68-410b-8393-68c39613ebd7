const { chromium } = require('playwright');

async function testReportFeatures() {
  console.log('🚀 开始测试报表管理功能...');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1000 // 减慢操作速度以便观察
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // 1. 导航到登录页面
    console.log('📝 步骤1: 导航到登录页面');
    await page.goto('http://localhost:5183');
    await page.waitForTimeout(2000);
    
    // 2. 登录
    console.log('🔐 步骤2: 执行登录');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);
    
    // 3. 导航到报表管理页面
    console.log('📊 步骤3: 导航到报表管理页面');
    await page.click('text=报表管理');
    await page.waitForTimeout(2000);
    
    // 4. 测试创建模板功能
    console.log('➕ 步骤4: 测试创建模板功能');
    await page.click('button:has-text("创建模板")');
    await page.waitForTimeout(1000);
    
    // 检查创建模板模态框是否出现
    const createModal = await page.locator('text=创建报表模板').isVisible();
    if (createModal) {
      console.log('✅ 创建模板模态框正常显示');
      
      // 填写表单
      await page.fill('input[placeholder="请输入模板名称"]', 'Playwright测试模板');
      await page.fill('textarea[placeholder="请输入模板描述"]', '这是通过Playwright自动化测试创建的模板');
      await page.selectOption('select', 'usage');
      
      // 提交表单
      await page.click('button:has-text("创建")');
      await page.waitForTimeout(3000);
      
      console.log('✅ 模板创建请求已发送');
    } else {
      console.log('❌ 创建模板模态框未显示');
    }
    
    // 5. 测试查看功能
    console.log('👁️ 步骤5: 测试查看模板功能');
    const viewButtons = await page.locator('button[title="查看详情"]').count();
    if (viewButtons > 0) {
      await page.locator('button[title="查看详情"]').first().click();
      await page.waitForTimeout(1000);
      
      const viewModal = await page.locator('text=模板详情').isVisible();
      if (viewModal) {
        console.log('✅ 查看模板模态框正常显示');
        
        // 关闭模态框
        await page.click('button:has-text("关闭")');
        await page.waitForTimeout(1000);
      } else {
        console.log('❌ 查看模板模态框未显示');
      }
    } else {
      console.log('⚠️ 没有找到查看按钮');
    }
    
    // 6. 测试执行报表功能
    console.log('▶️ 步骤6: 测试执行报表功能');
    const executeButtons = await page.locator('button[title="执行报表"]').count();
    if (executeButtons > 0) {
      await page.locator('button[title="执行报表"]').first().click();
      await page.waitForTimeout(1000);
      
      const executeModal = await page.locator('text=执行报表').isVisible();
      if (executeModal) {
        console.log('✅ 执行报表模态框正常显示');
        
        // 设置时间范围
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 7);
        const endDate = new Date();
        
        await page.fill('input[type="datetime-local"]', startDate.toISOString().slice(0, 16));
        await page.fill('input[type="datetime-local"]', endDate.toISOString().slice(0, 16));
        
        // 执行报表
        await page.click('button:has-text("执行")');
        await page.waitForTimeout(3000);
        
        console.log('✅ 报表执行请求已发送');
      } else {
        console.log('❌ 执行报表模态框未显示');
      }
    } else {
      console.log('⚠️ 没有找到执行按钮');
    }
    
    // 7. 切换到执行记录标签页
    console.log('📋 步骤7: 切换到执行记录标签页');
    await page.click('text=执行记录');
    await page.waitForTimeout(2000);
    
    // 8. 检查下载功能
    console.log('⬇️ 步骤8: 检查下载功能');
    const downloadButtons = await page.locator('button:has-text("下载")').count();
    if (downloadButtons > 0) {
      console.log('✅ 找到下载按钮');
      
      // 设置下载处理
      const downloadPromise = page.waitForEvent('download');
      await page.locator('button:has-text("下载")').first().click();
      
      try {
        const download = await downloadPromise;
        console.log('✅ 文件下载成功:', download.suggestedFilename());
      } catch (error) {
        console.log('⚠️ 下载可能需要更多时间或文件未准备好');
      }
    } else {
      console.log('⚠️ 没有找到下载按钮（可能没有已完成的报表）');
    }
    
    // 9. 测试删除功能（谨慎测试）
    console.log('🗑️ 步骤9: 测试删除功能');
    await page.click('text=报表模板');
    await page.waitForTimeout(1000);
    
    const deleteButtons = await page.locator('button[title="删除模板"]').count();
    if (deleteButtons > 0) {
      console.log('✅ 找到删除按钮');
      
      // 监听确认对话框
      page.on('dialog', async dialog => {
        console.log('📝 确认对话框:', dialog.message());
        await dialog.dismiss(); // 取消删除以避免实际删除数据
      });
      
      await page.locator('button[title="删除模板"]').first().click();
      await page.waitForTimeout(1000);
      
      console.log('✅ 删除确认对话框正常显示');
    } else {
      console.log('⚠️ 没有找到删除按钮');
    }
    
    console.log('🎉 所有测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  } finally {
    await page.waitForTimeout(3000); // 等待3秒以便观察结果
    await browser.close();
  }
}

// 运行测试
testReportFeatures().catch(console.error);
