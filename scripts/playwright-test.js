const { chromium } = require('playwright');

async function testFrontendIntegration() {
    console.log('🎭 启动Playwright自动化测试...');
    
    const browser = await chromium.launch({ 
        headless: false,  // 显示浏览器窗口以便观察
        slowMo: 1000      // 减慢操作速度以便观察
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
        // 监听网络请求
        page.on('request', request => {
            if (request.url().includes('/api/')) {
                console.log(`📤 API请求: ${request.method()} ${request.url()}`);
                if (request.postData()) {
                    console.log(`📝 请求数据: ${request.postData()}`);
                }
            }
        });
        
        page.on('response', response => {
            if (response.url().includes('/api/')) {
                console.log(`📥 API响应: ${response.status()} ${response.url()}`);
            }
        });
        
        // 监听控制台错误
        page.on('console', msg => {
            if (msg.type() === 'error') {
                console.log(`❌ 控制台错误: ${msg.text()}`);
            }
        });
        
        console.log('🔗 访问前端应用...');
        await page.goto('http://localhost:5178');
        
        // 等待页面加载
        await page.waitForTimeout(2000);
        
        console.log('🔐 执行登录...');
        // 填写登录表单
        await page.fill('input[type="email"]', '<EMAIL>');
        await page.fill('input[type="password"]', 'admin123');
        await page.click('button[type="submit"]');
        
        // 等待登录完成
        await page.waitForTimeout(3000);
        
        console.log('📊 测试报表管理页面...');
        // 点击报表生成菜单
        await page.click('text=报表生成');
        await page.waitForTimeout(2000);
        
        console.log('📋 检查报表模板列表...');
        // 等待模板列表加载
        await page.waitForSelector('[data-testid="template-list"], .grid', { timeout: 10000 });
        
        // 检查是否有模板
        const templates = await page.locator('.grid > div').count();
        console.log(`📝 找到 ${templates} 个报表模板`);
        
        if (templates > 0) {
            console.log('🎯 测试报表执行...');
            // 点击第一个模板的执行按钮
            await page.click('.grid > div:first-child button[title="执行报表"]');
            await page.waitForTimeout(1000);
            
            // 检查模态框是否出现
            const modal = await page.locator('.fixed.inset-0').isVisible();
            if (modal) {
                console.log('✅ 执行模态框已打开');
                
                // 填写执行参数
                await page.selectOption('select', 'pdf');
                
                // 设置时间范围
                const now = new Date();
                const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                
                const formatDateTime = (date) => {
                    return date.toISOString().slice(0, 16);
                };
                
                await page.fill('input[type="datetime-local"]:first-of-type', formatDateTime(weekAgo));
                await page.fill('input[type="datetime-local"]:last-of-type', formatDateTime(now));
                
                console.log('🚀 提交报表执行...');
                await page.click('button[type="submit"]');
                
                // 等待响应
                await page.waitForTimeout(3000);
                
            } else {
                console.log('❌ 执行模态框未出现');
            }
        }
        
        console.log('⚙️ 测试系统设置页面...');
        // 点击系统设置菜单
        await page.click('text=系统设置');
        await page.waitForTimeout(2000);
        
        console.log('👤 测试用户偏好设置...');
        // 确保在用户偏好标签页
        await page.click('text=用户偏好');
        await page.waitForTimeout(2000);
        
        // 检查设置是否加载
        const settingsLoaded = await page.locator('.bg-white.rounded-lg').count();
        console.log(`⚙️ 找到 ${settingsLoaded} 个设置分类`);
        
        if (settingsLoaded > 0) {
            console.log('🔧 测试设置修改...');
            // 尝试修改第一个设置
            const firstSetting = page.locator('.bg-white.rounded-lg').first();
            const inputs = await firstSetting.locator('input, select').count();
            
            if (inputs > 0) {
                // 修改第一个输入框
                const firstInput = firstSetting.locator('input, select').first();
                const inputType = await firstInput.getAttribute('type');
                
                if (inputType === 'checkbox') {
                    await firstInput.click();
                } else if (inputType === 'text') {
                    await firstInput.fill('测试值');
                } else {
                    console.log(`📝 输入类型: ${inputType}`);
                }
                
                // 检查是否有保存按钮出现
                await page.waitForTimeout(1000);
                const saveButton = await page.locator('text=保存更改').isVisible();
                if (saveButton) {
                    console.log('💾 点击保存更改...');
                    await page.click('text=保存更改');
                    await page.waitForTimeout(2000);
                }
            }
        }
        
        console.log('🧪 测试初始化默认设置...');
        const initButton = await page.locator('text=初始化默认设置').isVisible();
        if (initButton) {
            await page.click('text=初始化默认设置');
            await page.waitForTimeout(2000);
        }
        
        console.log('✅ 测试完成！');
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
    } finally {
        // 保持浏览器打开以便查看结果
        console.log('🔍 浏览器将保持打开状态，请手动关闭...');
        // await browser.close();
    }
}

// 运行测试
testFrontendIntegration().catch(console.error);
