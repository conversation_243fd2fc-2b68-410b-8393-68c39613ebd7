#!/bin/bash

echo "=== 测试报表管理API功能 ==="

# 获取访问token
echo "1. 获取访问token..."
TOKEN=$(curl -s -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}' | \
  python3 -c "import sys, json; data = json.load(sys.stdin); print(data['data']['token']['access_token'])" 2>/dev/null)

if [ -z "$TOKEN" ]; then
    echo "❌ 获取token失败"
    exit 1
fi

echo "✅ Token获取成功: ${TOKEN:0:20}..."

# 测试创建报表模板
echo ""
echo "2. 测试创建报表模板..."
CREATE_RESULT=$(curl -s -X POST http://localhost:8080/api/v1/reports/templates \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "name": "API测试模板",
    "description": "通过API创建的测试模板",
    "type": "usage",
    "config": {"chart_type": "bar", "metrics": ["cpu_usage", "memory_usage"]}
  }')

echo "$CREATE_RESULT" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if 'data' in data:
        print('✅ 模板创建成功')
        print('   ID:', data['data']['id'])
        print('   名称:', data['data']['name'])
        print('   类型:', data['data']['type'])
        print('   创建者:', data['data']['creator']['name'])
    else:
        print('❌ 创建失败:', data)
except Exception as e:
    print('❌ 解析响应失败:', e)
"

# 测试获取模板列表
echo ""
echo "3. 测试获取模板列表..."
curl -s -X GET "http://localhost:8080/api/v1/reports/templates?page=1&page_size=10" \
  -H "Authorization: Bearer $TOKEN" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if 'data' in data and 'items' in data['data']:
        print('✅ 获取模板列表成功')
        print('   总数:', data['data']['total'])
        print('   模板:')
        for item in data['data']['items']:
            print(f'   - ID: {item[\"id\"]}, 名称: {item[\"name\"]}, 类型: {item[\"type\"]}')
    else:
        print('❌ 获取失败:', data)
except Exception as e:
    print('❌ 解析响应失败:', e)
"

# 测试获取单个模板详情
echo ""
echo "4. 测试获取模板详情..."
curl -s -X GET "http://localhost:8080/api/v1/reports/templates/1" \
  -H "Authorization: Bearer $TOKEN" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if 'data' in data:
        print('✅ 获取模板详情成功')
        print('   ID:', data['data']['id'])
        print('   名称:', data['data']['name'])
        print('   描述:', data['data']['description'])
        print('   配置:', data['data']['config'])
    else:
        print('❌ 获取失败:', data)
except Exception as e:
    print('❌ 解析响应失败:', e)
"

# 测试执行报表
echo ""
echo "5. 测试执行报表..."
EXECUTE_RESULT=$(curl -s -X POST http://localhost:8080/api/v1/reports/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "template_id": 1,
    "format": "CSV",
    "time_range": {
      "start_time": "2025-07-13T00:00:00Z",
      "end_time": "2025-07-20T23:59:59Z"
    },
    "database_ids": [12, 13],
    "parameters": {"granularity": "hour"}
  }')

EXECUTION_ID=$(echo "$EXECUTE_RESULT" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if 'data' in data:
        print('✅ 报表执行成功')
        print('   执行ID:', data['data']['id'])
        print('   状态:', data['data']['status'])
        print(data['data']['id'])
    else:
        print('❌ 执行失败:', data)
        print('0')
except Exception as e:
    print('❌ 解析响应失败:', e)
    print('0')
" | tail -1)

# 等待报表生成完成
if [ "$EXECUTION_ID" != "0" ]; then
    echo ""
    echo "6. 等待报表生成完成..."
    sleep 3
    
    # 检查执行状态
    curl -s -X GET "http://localhost:8080/api/v1/reports/executions/$EXECUTION_ID" \
      -H "Authorization: Bearer $TOKEN" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if 'data' in data:
        print('✅ 获取执行状态成功')
        print('   执行ID:', data['data']['id'])
        print('   状态:', data['data']['status'])
        print('   文件路径:', data['data']['file_path'])
        print('   文件大小:', data['data']['file_size'], 'bytes')
        if data['data']['status'] == 'completed':
            print('✅ 报表生成完成，可以下载')
        else:
            print('⏳ 报表还在生成中...')
    else:
        print('❌ 获取状态失败:', data)
except Exception as e:
    print('❌ 解析响应失败:', e)
"
fi

echo ""
echo "🎉 API测试完成！"
