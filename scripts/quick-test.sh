#!/bin/bash

# 快速API测试脚本
# 使用方法: ./quick-test.sh

BASE_URL="http://localhost:8080"

echo "🚀 快速API测试..."

# 1. 健康检查
echo "1. 健康检查..."
curl -s $BASE_URL/health | jq '.' 2>/dev/null || curl -s $BASE_URL/health

# 2. 登录
echo -e "\n2. 登录..."
LOGIN_RESPONSE=$(curl -s -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}' \
  $BASE_URL/api/v1/auth/login)

# 尝试用jq解析，如果失败则用grep
if command -v jq &> /dev/null; then
  TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.token.access_token' 2>/dev/null)
else
  TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
fi

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
  echo "❌ 登录失败"
  echo "响应: $LOGIN_RESPONSE"
  exit 1
fi

echo "✅ 登录成功: ${TOKEN:0:20}..."

# 3. 数据库列表
echo -e "\n3. 数据库列表..."
curl -s -H "Authorization: Bearer $TOKEN" \
  $BASE_URL/api/v1/databases | jq '.data.total' 2>/dev/null || echo "获取成功"

# 4. 创建测试数据库
echo -e "\n4. 创建测试数据库..."
DB_ID=$(curl -s -X POST \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "快速测试DB",
    "type": "postgresql",
    "host": "localhost",
    "port": 9999,
    "username": "test",
    "password": "test",
    "database_name": "quicktest",
    "description": "快速测试"
  }' \
  $BASE_URL/api/v1/databases | \
  jq -r '.data.id' 2>/dev/null)

if [ "$DB_ID" = "null" ] || [ -z "$DB_ID" ]; then
  echo "❌ 创建失败"
else
  echo "✅ 创建成功，ID: $DB_ID"
  
  # 5. 测试连接
  echo -e "\n5. 测试连接..."
  curl -s -X POST -H "Authorization: Bearer $TOKEN" \
    $BASE_URL/api/v1/databases/$DB_ID/test | \
    jq '.data.success' 2>/dev/null || echo "测试完成"
  
  # 6. 删除测试数据库
  echo -e "\n6. 清理测试数据..."
  curl -s -X DELETE -H "Authorization: Bearer $TOKEN" \
    $BASE_URL/api/v1/databases/$DB_ID | \
    jq '.message' 2>/dev/null || echo "删除完成"
fi

echo -e "\n✅ 快速测试完成！"
