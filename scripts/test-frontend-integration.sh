#!/bin/bash

# 前端集成功能测试脚本
# 测试新的报表管理和系统设置API集成

echo "🧪 前端集成功能测试"
echo "===================="

# 配置
API_BASE="http://localhost:8080/api/v1"
FRONTEND_URL="http://localhost:5173"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
test_api() {
    local name="$1"
    local method="$2"
    local url="$3"
    local data="$4"
    local expected_status="$5"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "测试 $name... "
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $TOKEN" "$API_BASE$url")
    elif [ "$method" = "POST" ]; then
        response=$(curl -s -w "%{http_code}" -X POST -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN" -d "$data" "$API_BASE$url")
    elif [ "$method" = "PUT" ]; then
        response=$(curl -s -w "%{http_code}" -X PUT -H "Content-Type: application/json" -H "Authorization: Bearer $TOKEN" -d "$data" "$API_BASE$url")
    fi
    
    status_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ 通过${NC} (状态码: $status_code)"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo -e "${RED}❌ 失败${NC} (期望: $expected_status, 实际: $status_code)"
        echo "响应: $response_body"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 检查服务状态
echo "🔍 检查服务状态..."
if ! curl -s "$FRONTEND_URL" > /dev/null; then
    echo -e "${RED}❌ 前端服务未启动 ($FRONTEND_URL)${NC}"
    exit 1
fi

if ! curl -s "$API_BASE/health" > /dev/null; then
    echo -e "${RED}❌ 后端服务未启动 ($API_BASE)${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 所有服务正常运行${NC}"
echo ""

# 1. 登录获取token
echo "🔐 1. 用户认证测试"
echo "-------------------"

login_response=$(curl -s -X POST -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"admin123"}' \
    "$API_BASE/auth/login")

if echo "$login_response" | grep -q '"code":200'; then
    # 提取access_token
    TOKEN=$(echo "$login_response" | sed -n 's/.*"access_token":"\([^"]*\)".*/\1/p')

    if [ -n "$TOKEN" ]; then
        echo -e "${GREEN}✅ 登录成功${NC}"
        echo "Token: ${TOKEN:0:20}..."
    else
        echo -e "${RED}❌ Token提取失败${NC}"
        echo "响应: $login_response"
        exit 1
    fi
else
    echo -e "${RED}❌ 登录失败${NC}"
    echo "响应: $login_response"
    exit 1
fi
echo ""

# 2. 测试设置API
echo "⚙️  2. 系统设置API测试"
echo "--------------------"

test_api "获取设置分类" "GET" "/settings/categories" "" "200"
test_api "获取验证规则" "GET" "/settings/validation-rules" "" "200"
test_api "获取系统设置" "GET" "/settings/system" "" "200"
test_api "获取用户偏好" "GET" "/settings/preferences" "" "200"
test_api "初始化用户默认设置" "POST" "/settings/preferences/initialize" "" "200"

echo ""

# 3. 测试报表API
echo "📊 3. 报表管理API测试"
echo "-------------------"

test_api "获取报表模板列表" "GET" "/reports/templates" "" "200"

# 创建测试报表模板
template_data='{
    "name": "前端测试报表",
    "description": "用于前端集成测试的报表模板",
    "type": "performance",
    "config": {
        "chart_type": "line",
        "metrics": ["cpu_usage", "memory_usage"],
        "time_range": "1h"
    }
}'

test_api "创建报表模板" "POST" "/reports/templates" "$template_data" "200"

# 获取模板列表以获取新创建的模板ID
templates_response=$(curl -s -H "Authorization: Bearer $TOKEN" "$API_BASE/reports/templates")
if echo "$templates_response" | grep -q '"name":"前端测试报表"'; then
    template_id=$(echo "$templates_response" | grep -A 10 '"name":"前端测试报表"' | grep -o '"id":[0-9]*' | cut -d':' -f2)
    echo -e "${BLUE}📝 创建的模板ID: $template_id${NC}"
    
    test_api "获取报表模板详情" "GET" "/reports/templates/$template_id" "" "200"
    
    # 执行报表
    execute_data='{
        "template_id": '$template_id',
        "format": "pdf",
        "time_range": {
            "start_time": "2025-07-19T00:00:00Z",
            "end_time": "2025-07-20T00:00:00Z"
        },
        "parameters": {}
    }'
    
    test_api "执行报表" "POST" "/reports/execute" "$execute_data" "200"
fi

test_api "获取报表执行记录" "GET" "/reports/executions" "" "200"

echo ""

# 4. 前端界面测试指南
echo "🖥️  4. 前端界面测试指南"
echo "----------------------"
echo -e "${BLUE}请在浏览器中测试以下功能：${NC}"
echo ""
echo "📍 前端地址: $FRONTEND_URL"
echo ""
echo "🔐 登录信息:"
echo "   邮箱: <EMAIL>"
echo "   密码: admin123"
echo ""
echo "📋 测试清单:"
echo ""
echo "✅ 系统设置页面测试:"
echo "   1. 点击侧边栏 '系统设置' 菜单"
echo "   2. 测试 '用户偏好' 标签页:"
echo "      - 查看各分类设置 (UI偏好、通知偏好等)"
echo "      - 修改设置值 (主题、语言等)"
echo "      - 点击 '保存更改' 按钮"
echo "      - 测试 '初始化默认设置' 按钮"
echo "   3. 测试 '系统设置' 标签页:"
echo "      - 查看系统级设置"
echo "      - 修改系统配置"
echo "      - 保存更改"
echo ""
echo "✅ 报表管理页面测试:"
echo "   1. 点击侧边栏 '报表生成' 菜单"
echo "   2. 测试 '报表模板' 标签页:"
echo "      - 查看模板列表 (应该看到 '前端测试报表')"
echo "      - 使用搜索框搜索模板"
echo "      - 使用类型过滤器"
echo "      - 点击 '执行' 按钮测试报表执行"
echo "      - 点击 '查看' 按钮查看模板详情"
echo "   3. 测试 '执行记录' 标签页:"
echo "      - 查看执行历史记录"
echo "      - 查看执行状态和时间"
echo "      - 点击 '查看' 查看执行详情"
echo ""
echo "🎯 重点测试项目:"
echo "   - 所有API调用是否正常 (检查浏览器开发者工具网络标签)"
echo "   - 加载状态是否正确显示"
echo "   - 错误处理是否友好"
echo "   - 界面响应是否流畅"
echo "   - 数据更新是否实时"
echo ""

# 5. 测试结果统计
echo "📊 5. 测试结果统计"
echo "-----------------"
echo "总测试数: $TOTAL_TESTS"
echo -e "通过: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n🎉 ${GREEN}所有API测试通过！${NC}"
    echo -e "✅ 后端API集成正常"
    echo -e "🚀 可以开始前端界面测试"
else
    echo -e "\n⚠️  ${YELLOW}部分测试失败，请检查后端服务${NC}"
fi

echo ""
echo "🔗 有用的链接:"
echo "   前端应用: $FRONTEND_URL"
echo "   API文档: http://localhost:8080/swagger/index.html"
echo "   后端健康检查: $API_BASE/health"
echo ""
echo "📝 测试完成！请继续在浏览器中进行界面测试。"
