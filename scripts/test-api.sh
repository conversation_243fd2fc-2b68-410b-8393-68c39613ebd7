#!/bin/bash

# 数据库监控平台 API 测试脚本
# 创建时间: 2025-01-13
# 使用方法: ./test-api.sh

set -e  # 遇到错误立即退出

# 配置
BASE_URL="http://localhost:8080"
EMAIL="<EMAIL>"
PASSWORD="admin123"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装，请先安装 curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warning "jq 未安装，输出将不会格式化"
        JQ_AVAILABLE=false
    else
        JQ_AVAILABLE=true
    fi
}

# 格式化JSON输出
format_json() {
    if [ "$JQ_AVAILABLE" = true ]; then
        echo "$1" | jq '.'
    else
        echo "$1"
    fi
}

# 检查服务状态
check_service() {
    log_info "检查服务健康状态..."
    
    HEALTH_RESPONSE=$(curl -s $BASE_URL/health 2>/dev/null || echo "")
    
    if [ -z "$HEALTH_RESPONSE" ]; then
        log_error "无法连接到服务器 $BASE_URL"
        log_error "请确保后端服务正在运行"
        exit 1
    fi
    
    log_success "服务健康检查通过"
    format_json "$HEALTH_RESPONSE"
}

# 用户登录
login() {
    log_info "用户登录..."
    
    LOGIN_RESPONSE=$(curl -s -H "Content-Type: application/json" \
        -d "{\"email\":\"$EMAIL\",\"password\":\"$PASSWORD\"}" \
        $BASE_URL/api/v1/auth/login)
    
    if [ "$JQ_AVAILABLE" = true ]; then
        TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.data.token.access_token // empty')
        LOGIN_CODE=$(echo $LOGIN_RESPONSE | jq -r '.code // 0')
    else
        # 简单的文本提取（不够健壮，建议安装jq）
        TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
        LOGIN_CODE=200
    fi
    
    if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ]; then
        log_error "登录失败"
        format_json "$LOGIN_RESPONSE"
        exit 1
    fi
    
    log_success "登录成功，Token: ${TOKEN:0:20}..."
}

# 测试数据库列表
test_database_list() {
    log_info "测试获取数据库列表..."
    
    LIST_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
        $BASE_URL/api/v1/databases)
    
    if [ "$JQ_AVAILABLE" = true ]; then
        TOTAL=$(echo $LIST_RESPONSE | jq -r '.data.total // 0')
        log_success "获取数据库列表成功，共 $TOTAL 个数据库"
    else
        log_success "获取数据库列表成功"
    fi
    
    format_json "$LIST_RESPONSE"
}

# 测试创建数据库
test_create_database() {
    log_info "测试创建数据库..."
    
    CREATE_RESPONSE=$(curl -s -X POST \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "name": "API测试数据库",
            "type": "postgresql",
            "host": "localhost",
            "port": 5433,
            "username": "testuser",
            "password": "testpass",
            "database_name": "api_test_db",
            "description": "通过API测试脚本创建的数据库"
        }' \
        $BASE_URL/api/v1/databases)
    
    if [ "$JQ_AVAILABLE" = true ]; then
        DB_ID=$(echo $CREATE_RESPONSE | jq -r '.data.id // empty')
        CREATE_CODE=$(echo $CREATE_RESPONSE | jq -r '.code // 0')
    else
        # 简单提取ID
        DB_ID=$(echo $CREATE_RESPONSE | grep -o '"id":[0-9]*' | cut -d':' -f2)
        CREATE_CODE=200
    fi
    
    if [ -z "$DB_ID" ] || [ "$DB_ID" = "null" ]; then
        log_error "创建数据库失败"
        format_json "$CREATE_RESPONSE"
        return 1
    fi
    
    log_success "创建数据库成功，ID: $DB_ID"
    format_json "$CREATE_RESPONSE"
    
    # 导出DB_ID供其他函数使用
    export TEST_DB_ID=$DB_ID
}

# 测试更新数据库
test_update_database() {
    if [ -z "$TEST_DB_ID" ]; then
        log_warning "跳过更新测试：没有可用的测试数据库ID"
        return
    fi
    
    log_info "测试更新数据库 (ID: $TEST_DB_ID)..."
    
    UPDATE_RESPONSE=$(curl -s -X PUT \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "name": "API测试数据库(已更新)",
            "description": "通过API测试脚本更新的数据库"
        }' \
        $BASE_URL/api/v1/databases/$TEST_DB_ID)
    
    log_success "更新数据库成功"
    format_json "$UPDATE_RESPONSE"
}

# 测试连接测试
test_connection() {
    if [ -z "$TEST_DB_ID" ]; then
        log_warning "跳过连接测试：没有可用的测试数据库ID"
        return
    fi
    
    log_info "测试数据库连接 (ID: $TEST_DB_ID)..."
    
    TEST_RESPONSE=$(curl -s -X POST \
        -H "Authorization: Bearer $TOKEN" \
        $BASE_URL/api/v1/databases/$TEST_DB_ID/test)
    
    if [ "$JQ_AVAILABLE" = true ]; then
        SUCCESS=$(echo $TEST_RESPONSE | jq -r '.data.success // false')
        if [ "$SUCCESS" = "true" ]; then
            log_success "数据库连接测试成功"
        else
            log_warning "数据库连接测试失败（这是预期的，因为测试端口不存在）"
        fi
    else
        log_success "连接测试完成"
    fi
    
    format_json "$TEST_RESPONSE"
}

# 测试实时指标
test_realtime_metrics() {
    log_info "测试获取实时指标..."
    
    METRICS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
        $BASE_URL/api/v1/metrics/realtime)
    
    log_success "获取实时指标成功"
    format_json "$METRICS_RESPONSE"
}

# 测试告警统计（已知问题）
test_alert_stats() {
    log_info "测试告警统计（已知问题）..."
    
    ALERT_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
        $BASE_URL/api/v1/alerts/stats)
    
    if [ "$JQ_AVAILABLE" = true ]; then
        ALERT_CODE=$(echo $ALERT_RESPONSE | jq -r '.code // 0')
        if [ "$ALERT_CODE" = "500" ]; then
            log_warning "告警统计API返回500错误（已知问题）"
        else
            log_success "告警统计API正常"
        fi
    else
        log_success "告警统计测试完成"
    fi
    
    format_json "$ALERT_RESPONSE"
}

# 清理测试数据
cleanup() {
    if [ -z "$TEST_DB_ID" ]; then
        log_info "没有需要清理的测试数据"
        return
    fi
    
    log_info "清理测试数据 (删除数据库 ID: $TEST_DB_ID)..."
    
    DELETE_RESPONSE=$(curl -s -X DELETE \
        -H "Authorization: Bearer $TOKEN" \
        $BASE_URL/api/v1/databases/$TEST_DB_ID)
    
    log_success "测试数据清理完成"
    format_json "$DELETE_RESPONSE"
}

# 主函数
main() {
    echo "🚀 数据库监控平台 API 测试开始..."
    echo "================================================"
    
    check_dependencies
    check_service
    login
    
    echo ""
    echo "📊 开始功能测试..."
    echo "================================================"
    
    test_database_list
    echo ""
    
    test_create_database
    echo ""
    
    test_update_database
    echo ""
    
    test_connection
    echo ""
    
    test_realtime_metrics
    echo ""
    
    test_alert_stats
    echo ""
    
    echo "🧹 清理测试数据..."
    echo "================================================"
    cleanup
    
    echo ""
    echo "✅ API测试完成！"
    echo "================================================"
    
    # 最终验证
    log_info "最终验证：再次获取数据库列表"
    test_database_list
}

# 运行主函数
main "$@"
