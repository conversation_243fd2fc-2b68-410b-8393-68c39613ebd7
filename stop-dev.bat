@echo off
REM Windows批处理版本的开发环境停止脚本

echo 🛑 停止数据库监控平台开发环境...

REM 1. 停止前端和后端服务（通过端口）
echo 🎨 停止前端服务...
for /f "tokens=5" %%a in ('netstat -aon ^| find ":5173"') do taskkill /f /pid %%a >nul 2>&1

echo 🔧 停止后端服务...
for /f "tokens=5" %%a in ('netstat -aon ^| find ":8080"') do taskkill /f /pid %%a >nul 2>&1

REM 2. 停止Docker服务
echo 📦 停止Docker服务...
docker-compose down

echo.
echo ✅ 所有服务已停止！
echo.
echo 📝 日志文件保留：
echo    后端日志: backend.log
echo    前端日志: frontend.log

pause
