# Go后端技术规格说明

## 🚀 技术栈选择理由

### 为什么选择Go
1. **性能优势**: 编译型语言，接近C/C++的性能
2. **并发优势**: Goroutine天然适合实时数据处理
3. **部署简便**: 单一二进制文件，无依赖部署
4. **内存效率**: 优秀的垃圾回收，资源占用少
5. **开发效率**: 语法简洁，配合AI辅助开发效率高

### 技术栈对比
| 特性 | Go | Node.js | Java | Python |
|------|----|---------|----- |--------|
| 性能 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| 并发 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ |
| 部署 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |
| 开发效率 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

## 🛠️ 核心技术栈

### Web框架: Gin
```go
// 选择理由
- 性能优秀 (比标准库快40倍)
- 中间件丰富
- 路由功能强大
- 社区活跃
- 学习成本低
```

### ORM: GORM
```go
// 特性
- 自动迁移
- 关联关系
- 钩子函数
- 事务支持
- 软删除
- 批量操作
```

### 数据库驱动
```go
// PostgreSQL
"gorm.io/driver/postgres"
"gorm.io/gorm"

// Redis
"github.com/go-redis/redis/v8"
```

### WebSocket: Gorilla WebSocket
```go
// 优势
- 标准库兼容
- 性能优秀
- 功能完整
- 文档详细
```

## 📁 项目结构

```
backend/
├── cmd/
│   └── server/
│       └── main.go              # 应用入口
├── internal/
│   ├── config/
│   │   └── config.go            # 配置管理
│   ├── models/
│   │   ├── user.go              # 用户模型
│   │   ├── database.go          # 数据库实例模型
│   │   ├── metric.go            # 监控指标模型
│   │   └── alert.go             # 告警模型
│   ├── handlers/
│   │   ├── auth.go              # 认证处理器
│   │   ├── database.go          # 数据库管理
│   │   ├── metric.go            # 监控数据
│   │   └── websocket.go         # WebSocket处理
│   ├── middleware/
│   │   ├── auth.go              # JWT认证中间件
│   │   ├── cors.go              # CORS中间件
│   │   └── logger.go            # 日志中间件
│   ├── services/
│   │   ├── auth.go              # 认证服务
│   │   ├── database.go          # 数据库服务
│   │   ├── metric.go            # 监控服务
│   │   └── alert.go             # 告警服务
│   ├── repository/
│   │   ├── user.go              # 用户数据访问
│   │   ├── database.go          # 数据库数据访问
│   │   └── metric.go            # 监控数据访问
│   └── utils/
│       ├── jwt.go               # JWT工具
│       ├── hash.go              # 密码加密
│       └── validator.go         # 数据验证
├── pkg/
│   ├── database/
│   │   └── postgres.go          # 数据库连接
│   ├── redis/
│   │   └── client.go            # Redis客户端
│   └── logger/
│       └── logger.go            # 日志配置
├── api/
│   └── swagger/                 # API文档
├── scripts/
│   ├── migrate.go               # 数据库迁移
│   └── seed.go                  # 种子数据
├── tests/
│   ├── unit/                    # 单元测试
│   └── integration/             # 集成测试
├── configs/
│   ├── config.yaml              # 配置文件
│   └── config.example.yaml      # 配置示例
├── docker/
│   ├── Dockerfile               # Docker镜像
│   └── docker-compose.yml       # 开发环境
├── go.mod                       # Go模块
├── go.sum                       # 依赖校验
├── Makefile                     # 构建脚本
└── README.md                    # 项目说明
```

## 🔧 核心依赖

### go.mod 依赖列表
```go
module db-monitor-platform

go 1.21

require (
    github.com/gin-gonic/gin v1.9.1
    github.com/golang-jwt/jwt/v5 v5.0.0
    github.com/gorilla/websocket v1.5.0
    github.com/go-redis/redis/v8 v8.11.5
    github.com/spf13/viper v1.16.0
    github.com/sirupsen/logrus v1.9.3
    github.com/stretchr/testify v1.8.4
    golang.org/x/crypto v0.12.0
    gorm.io/gorm v1.25.4
    gorm.io/driver/postgres v1.5.2
    github.com/go-playground/validator/v10 v10.15.1
    github.com/swaggo/gin-swagger v1.6.0
    github.com/swaggo/swag v1.16.1
)
```

## 🏗️ 核心模型设计

### 用户模型
```go
type User struct {
    ID        uint      `json:"id" gorm:"primaryKey"`
    Email     string    `json:"email" gorm:"uniqueIndex;not null"`
    Password  string    `json:"-" gorm:"not null"`
    Name      string    `json:"name" gorm:"not null"`
    Role      string    `json:"role" gorm:"default:user"`
    AvatarURL string    `json:"avatar_url"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}
```

### 数据库实例模型
```go
type DatabaseInstance struct {
    ID           uint      `json:"id" gorm:"primaryKey"`
    Name         string    `json:"name" gorm:"not null"`
    Type         string    `json:"type" gorm:"not null"` // mysql, postgresql, mongodb
    Host         string    `json:"host" gorm:"not null"`
    Port         int       `json:"port" gorm:"not null"`
    DatabaseName string    `json:"database_name"`
    Username     string    `json:"username"`
    Password     string    `json:"-"` // 加密存储
    Status       string    `json:"status" gorm:"default:active"`
    CreatedBy    uint      `json:"created_by"`
    User         User      `json:"user" gorm:"foreignKey:CreatedBy"`
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
}
```

### 监控指标模型
```go
type Metric struct {
    ID         uint      `json:"id" gorm:"primaryKey"`
    DatabaseID uint      `json:"database_id" gorm:"index"`
    Database   DatabaseInstance `json:"database" gorm:"foreignKey:DatabaseID"`
    MetricType string    `json:"metric_type" gorm:"index"` // cpu, memory, connections, qps
    Value      float64   `json:"value"`
    Unit       string    `json:"unit"`
    Timestamp  time.Time `json:"timestamp" gorm:"index"`
}
```

### 告警规则模型
```go
type AlertRule struct {
    ID                   uint      `json:"id" gorm:"primaryKey"`
    Name                 string    `json:"name" gorm:"not null"`
    DatabaseID           uint      `json:"database_id"`
    Database             DatabaseInstance `json:"database" gorm:"foreignKey:DatabaseID"`
    MetricType           string    `json:"metric_type"`
    Operator             string    `json:"operator"` // >, <, >=, <=, =
    Threshold            float64   `json:"threshold"`
    Severity             string    `json:"severity" gorm:"default:warning"`
    Enabled              bool      `json:"enabled" gorm:"default:true"`
    NotificationChannels string    `json:"notification_channels" gorm:"type:jsonb"`
    CreatedBy            uint      `json:"created_by"`
    User                 User      `json:"user" gorm:"foreignKey:CreatedBy"`
    CreatedAt            time.Time `json:"created_at"`
    UpdatedAt            time.Time `json:"updated_at"`
}
```

## 🔌 API路由设计

### 路由组织
```go
func SetupRoutes(r *gin.Engine) {
    api := r.Group("/api/v1")
    
    // 认证路由
    auth := api.Group("/auth")
    {
        auth.POST("/register", handlers.Register)
        auth.POST("/login", handlers.Login)
        auth.POST("/logout", handlers.Logout)
    }
    
    // 需要认证的路由
    protected := api.Group("/")
    protected.Use(middleware.AuthMiddleware())
    {
        // 用户相关
        protected.GET("/profile", handlers.GetProfile)
        protected.PUT("/profile", handlers.UpdateProfile)
        
        // 数据库管理
        databases := protected.Group("/databases")
        {
            databases.GET("", handlers.GetDatabases)
            databases.POST("", handlers.CreateDatabase)
            databases.GET("/:id", handlers.GetDatabase)
            databases.PUT("/:id", handlers.UpdateDatabase)
            databases.DELETE("/:id", handlers.DeleteDatabase)
            databases.POST("/:id/test", handlers.TestConnection)
        }
        
        // 监控数据
        metrics := protected.Group("/metrics")
        {
            metrics.GET("/:db_id", handlers.GetMetrics)
            metrics.GET("/:db_id/realtime", handlers.GetRealtimeMetrics)
            metrics.GET("/:db_id/history", handlers.GetHistoryMetrics)
        }
        
        // 告警管理
        alerts := protected.Group("/alerts")
        {
            alerts.GET("", handlers.GetAlerts)
            alerts.POST("", handlers.CreateAlert)
            alerts.PUT("/:id", handlers.UpdateAlert)
            alerts.DELETE("/:id", handlers.DeleteAlert)
            alerts.GET("/history", handlers.GetAlertHistory)
            alerts.POST("/:id/resolve", handlers.ResolveAlert)
        }
    }
    
    // WebSocket
    r.GET("/ws", handlers.WebSocketHandler)
}
```

## 🔒 安全设计

### JWT认证
```go
type Claims struct {
    UserID uint   `json:"user_id"`
    Email  string `json:"email"`
    Role   string `json:"role"`
    jwt.RegisteredClaims
}

func GenerateToken(user *models.User) (string, error) {
    claims := &Claims{
        UserID: user.ID,
        Email:  user.Email,
        Role:   user.Role,
        RegisteredClaims: jwt.RegisteredClaims{
            ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
            IssuedAt:  jwt.NewNumericDate(time.Now()),
            NotBefore: jwt.NewNumericDate(time.Now()),
        },
    }
    
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(config.JWTSecret))
}
```

### 密码加密
```go
func HashPassword(password string) (string, error) {
    bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
    return string(bytes), err
}

func CheckPassword(password, hash string) bool {
    err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
    return err == nil
}
```

## 📊 性能优化

### 数据库优化
```go
// 连接池配置
func SetupDatabase() *gorm.DB {
    db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
    if err != nil {
        log.Fatal("Failed to connect to database")
    }
    
    sqlDB, _ := db.DB()
    sqlDB.SetMaxIdleConns(10)
    sqlDB.SetMaxOpenConns(100)
    sqlDB.SetConnMaxLifetime(time.Hour)
    
    return db
}

// 索引优化
func (m *Metric) BeforeCreate(tx *gorm.DB) error {
    // 自动创建复合索引
    tx.Exec("CREATE INDEX IF NOT EXISTS idx_metrics_db_time ON metrics(database_id, timestamp)")
    tx.Exec("CREATE INDEX IF NOT EXISTS idx_metrics_type_time ON metrics(metric_type, timestamp)")
    return nil
}
```

### Redis缓存
```go
type CacheService struct {
    client *redis.Client
}

func (c *CacheService) SetMetrics(dbID uint, metrics []models.Metric) error {
    key := fmt.Sprintf("metrics:%d", dbID)
    data, _ := json.Marshal(metrics)
    return c.client.Set(context.Background(), key, data, 5*time.Minute).Err()
}

func (c *CacheService) GetMetrics(dbID uint) ([]models.Metric, error) {
    key := fmt.Sprintf("metrics:%d", dbID)
    data, err := c.client.Get(context.Background(), key).Result()
    if err != nil {
        return nil, err
    }
    
    var metrics []models.Metric
    err = json.Unmarshal([]byte(data), &metrics)
    return metrics, err
}
```

## 🚀 部署配置

### Dockerfile
```dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o main cmd/server/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/main .
COPY --from=builder /app/configs ./configs

EXPOSE 8080
CMD ["./main"]
```

### docker-compose.yml
```yaml
version: '3.8'
services:
  backend:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=postgres
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis
    volumes:
      - ./configs:/app/configs

  postgres:
    image: postgres:14-alpine
    environment:
      POSTGRES_DB: dbmonitor
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

---

*文档版本: v1.0*  
*最后更新: 2024-12-28*
