# Phase 4 剩余API开发 - 详细设计方案

> **设计时间**: 2025-07-19  
> **实施计划**: 按照 phase4_remaining_apis.md 执行  
> **架构原则**: 保持与现有系统的一致性和扩展性  

## 🎯 **设计概览**

### 🏗️ **架构设计原则**
1. **分层架构**: 数据模型 → 仓储层 → 服务层 → 处理器层
2. **一致性**: 与查询优化工具、备份管理系统保持相同的设计模式
3. **扩展性**: 为未来功能扩展预留接口
4. **安全性**: 完整的权限控制和数据验证

### 📊 **技术栈选择**
- **数据模型**: GORM + PostgreSQL
- **API框架**: Gin + Swagger文档
- **权限控制**: JWT中间件
- **文件处理**: 支持CSV/Excel导出
- **数据聚合**: SQL聚合查询 + Redis缓存

## 🔧 **任务1: 报表生成系统API设计**

### **1.1 数据模型设计**

#### **ReportTemplate 模型**
```go
type ReportTemplate struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    Name        string    `json:"name" gorm:"size:100;not null"`
    Description string    `json:"description" gorm:"size:500"`
    Type        string    `json:"type" gorm:"size:50;not null"` // performance, usage, alert
    Config      string    `json:"config" gorm:"type:text"`      // JSON配置
    CreatedBy   uint      `json:"created_by" gorm:"not null"`
    IsActive    bool      `json:"is_active" gorm:"default:true"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
    
    // 关联关系
    Creator     User      `json:"creator" gorm:"foreignKey:CreatedBy"`
}
```

#### **ReportExecution 模型**
```go
type ReportExecution struct {
    ID           uint      `json:"id" gorm:"primaryKey"`
    TemplateID   uint      `json:"template_id" gorm:"not null"`
    ExecutedBy   uint      `json:"executed_by" gorm:"not null"`
    Status       string    `json:"status" gorm:"size:20;default:'pending'"` // pending, running, completed, failed
    Parameters   string    `json:"parameters" gorm:"type:text"`             // JSON参数
    FilePath     string    `json:"file_path" gorm:"size:500"`
    FileSize     int64     `json:"file_size" gorm:"default:0"`
    ErrorMessage string    `json:"error_message" gorm:"type:text"`
    StartTime    time.Time `json:"start_time"`
    EndTime      *time.Time `json:"end_time"`
    CreatedAt    time.Time `json:"created_at"`
    
    // 关联关系
    Template     ReportTemplate `json:"template" gorm:"foreignKey:TemplateID"`
    Executor     User          `json:"executor" gorm:"foreignKey:ExecutedBy"`
}
```

### **1.2 API端点设计**

#### **报表模板管理**
```go
// GET /api/v1/reports/templates - 获取报表模板列表
type GetReportTemplatesRequest struct {
    Page     int    `form:"page" binding:"min=1"`
    PageSize int    `form:"page_size" binding:"min=1,max=100"`
    Type     string `form:"type"`
    Search   string `form:"search"`
}

// POST /api/v1/reports/templates - 创建报表模板
type CreateReportTemplateRequest struct {
    Name        string                 `json:"name" binding:"required,max=100"`
    Description string                 `json:"description" binding:"max=500"`
    Type        string                 `json:"type" binding:"required,oneof=performance usage alert"`
    Config      map[string]interface{} `json:"config" binding:"required"`
}
```

#### **报表执行**
```go
// POST /api/v1/reports/execute - 执行报表生成
type ExecuteReportRequest struct {
    TemplateID  uint                   `json:"template_id" binding:"required"`
    Parameters  map[string]interface{} `json:"parameters"`
    Format      string                 `json:"format" binding:"required,oneof=csv excel json"`
    TimeRange   TimeRangeRequest       `json:"time_range" binding:"required"`
    DatabaseIDs []uint                 `json:"database_ids"`
}

type TimeRangeRequest struct {
    StartTime time.Time `json:"start_time" binding:"required"`
    EndTime   time.Time `json:"end_time" binding:"required"`
}
```

#### **图表数据API**
```go
// GET /api/v1/reports/charts - 获取图表数据
type GetChartDataRequest struct {
    ChartType   string    `form:"chart_type" binding:"required,oneof=line bar pie area"`
    MetricType  string    `form:"metric_type" binding:"required"`
    DatabaseID  uint      `form:"database_id"`
    StartTime   time.Time `form:"start_time" binding:"required"`
    EndTime     time.Time `form:"end_time" binding:"required"`
    Granularity string    `form:"granularity" binding:"oneof=minute hour day week month"`
}

type ChartDataResponse struct {
    Labels []string      `json:"labels"`
    Series []ChartSeries `json:"series"`
}

type ChartSeries struct {
    Name string        `json:"name"`
    Data []interface{} `json:"data"`
}
```

### **1.3 服务层设计**

#### **ReportService 接口**
```go
type ReportService interface {
    // 模板管理
    CreateTemplate(ctx context.Context, req *CreateReportTemplateRequest, userID uint) (*ReportTemplate, error)
    GetTemplates(ctx context.Context, req *GetReportTemplatesRequest, userID uint) (*PaginationResponse, error)
    UpdateTemplate(ctx context.Context, id uint, req *UpdateReportTemplateRequest, userID uint) error
    DeleteTemplate(ctx context.Context, id uint, userID uint) error
    
    // 报表执行
    ExecuteReport(ctx context.Context, req *ExecuteReportRequest, userID uint) (*ReportExecution, error)
    GetExecutionStatus(ctx context.Context, executionID uint, userID uint) (*ReportExecution, error)
    DownloadReport(ctx context.Context, executionID uint, userID uint) (string, error)
    
    // 图表数据
    GetChartData(ctx context.Context, req *GetChartDataRequest, userID uint) (*ChartDataResponse, error)
    GetSummaryStats(ctx context.Context, req *GetSummaryStatsRequest, userID uint) (*SummaryStatsResponse, error)
}
```

## 🔧 **任务2: 系统设置管理API设计**

### **2.1 数据模型设计**

#### **SystemSetting 模型**
```go
type SystemSetting struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    Category    string    `json:"category" gorm:"size:50;not null;index"` // system, monitoring, alert, backup
    Key         string    `json:"key" gorm:"size:100;not null;uniqueIndex:idx_category_key"`
    Value       string    `json:"value" gorm:"type:text"`
    ValueType   string    `json:"value_type" gorm:"size:20;default:'string'"` // string, number, boolean, json
    Description string    `json:"description" gorm:"size:500"`
    IsPublic    bool      `json:"is_public" gorm:"default:false"`  // 是否对普通用户可见
    UpdatedBy   uint      `json:"updated_by"`
    UpdatedAt   time.Time `json:"updated_at"`
    CreatedAt   time.Time `json:"created_at"`
    
    // 关联关系
    Updater     User      `json:"updater" gorm:"foreignKey:UpdatedBy"`
}
```

#### **UserPreference 模型**
```go
type UserPreference struct {
    ID        uint      `json:"id" gorm:"primaryKey"`
    UserID    uint      `json:"user_id" gorm:"not null;index"`
    Category  string    `json:"category" gorm:"size:50;not null"` // ui, notification, dashboard
    Key       string    `json:"key" gorm:"size:100;not null"`
    Value     string    `json:"value" gorm:"type:text"`
    UpdatedAt time.Time `json:"updated_at"`
    CreatedAt time.Time `json:"created_at"`
    
    // 关联关系
    User      User      `json:"user" gorm:"foreignKey:UserID"`
    
    // 复合唯一索引
    _ struct{} `gorm:"uniqueIndex:idx_user_category_key,priority:1"`
}
```

### **2.2 API端点设计**

#### **系统设置管理**
```go
// GET /api/v1/settings/system - 获取系统设置
type GetSystemSettingsRequest struct {
    Category string `form:"category"`
    Public   *bool  `form:"public"`
}

// PUT /api/v1/settings/system - 批量更新系统设置
type UpdateSystemSettingsRequest struct {
    Settings []SystemSettingUpdate `json:"settings" binding:"required,dive"`
}

type SystemSettingUpdate struct {
    Category string      `json:"category" binding:"required"`
    Key      string      `json:"key" binding:"required"`
    Value    interface{} `json:"value" binding:"required"`
}
```

#### **用户偏好设置**
```go
// GET /api/v1/settings/preferences - 获取用户偏好
type GetUserPreferencesRequest struct {
    Category string `form:"category"`
}

// PUT /api/v1/settings/preferences - 更新用户偏好
type UpdateUserPreferencesRequest struct {
    Preferences []UserPreferenceUpdate `json:"preferences" binding:"required,dive"`
}

type UserPreferenceUpdate struct {
    Category string      `json:"category" binding:"required"`
    Key      string      `json:"key" binding:"required"`
    Value    interface{} `json:"value" binding:"required"`
}
```

## 🔧 **任务3: 监控告警API完善设计**

### **3.1 增强现有模型**

#### **AlertRule 模型增强**
```go
// 在现有AlertRule基础上增加字段
type AlertRule struct {
    // ... 现有字段 ...
    
    // 新增字段
    Expression    string `json:"expression" gorm:"type:text"`      // 自定义表达式
    Dependencies  string `json:"dependencies" gorm:"type:text"`    // 依赖规则JSON
    Channels      string `json:"channels" gorm:"type:text"`        // 通知渠道JSON
    Escalation    string `json:"escalation" gorm:"type:text"`      // 升级策略JSON
    Metadata      string `json:"metadata" gorm:"type:text"`        // 元数据JSON
}
```

#### **NotificationChannel 模型**
```go
type NotificationChannel struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    Name        string    `json:"name" gorm:"size:100;not null"`
    Type        string    `json:"type" gorm:"size:50;not null"`    // email, sms, slack, webhook
    Config      string    `json:"config" gorm:"type:text"`         // JSON配置
    IsActive    bool      `json:"is_active" gorm:"default:true"`
    TestStatus  string    `json:"test_status" gorm:"size:20"`      // untested, success, failed
    LastTested  *time.Time `json:"last_tested"`
    CreatedBy   uint      `json:"created_by" gorm:"not null"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
    
    // 关联关系
    Creator     User      `json:"creator" gorm:"foreignKey:CreatedBy"`
}
```

### **3.2 API端点设计**

#### **通知渠道管理**
```go
// POST /api/v1/alerts/channels - 创建通知渠道
type CreateNotificationChannelRequest struct {
    Name   string                 `json:"name" binding:"required,max=100"`
    Type   string                 `json:"type" binding:"required,oneof=email sms slack webhook"`
    Config map[string]interface{} `json:"config" binding:"required"`
}

// POST /api/v1/alerts/channels/:id/test - 测试通知渠道
type TestNotificationChannelRequest struct {
    Message string `json:"message" binding:"max=500"`
}
```

#### **告警分析API**
```go
// GET /api/v1/alerts/analytics - 获取告警分析数据
type GetAlertAnalyticsRequest struct {
    TimeRange   TimeRangeRequest `form:"time_range" binding:"required"`
    DatabaseIDs []uint           `form:"database_ids"`
    RuleIDs     []uint           `form:"rule_ids"`
    Granularity string           `form:"granularity" binding:"oneof=hour day week month"`
}

type AlertAnalyticsResponse struct {
    Summary      AlertSummary      `json:"summary"`
    Trends       []AlertTrend      `json:"trends"`
    TopRules     []AlertRuleStats  `json:"top_rules"`
    Distribution AlertDistribution `json:"distribution"`
}
```

## 🏗️ **实施架构设计**

### **分层架构实施**

#### **1. 数据层 (Models)**
```
backend/go-backend/internal/models/
├── report_template.go      # 报表模板模型
├── report_execution.go     # 报表执行模型
├── system_setting.go       # 系统设置模型
├── user_preference.go      # 用户偏好模型
├── notification_channel.go # 通知渠道模型
└── report_requests.go      # API请求响应模型
```

#### **2. 仓储层 (Repository)**
```
backend/go-backend/internal/repository/
├── report_template.go      # 报表模板仓储
├── report_execution.go     # 报表执行仓储
├── system_setting.go       # 系统设置仓储
├── user_preference.go      # 用户偏好仓储
└── notification_channel.go # 通知渠道仓储
```

#### **3. 服务层 (Services)**
```
backend/go-backend/internal/services/
├── report.go               # 报表服务
├── setting.go              # 设置服务
└── notification.go         # 通知服务
```

#### **4. 处理器层 (Handlers)**
```
backend/go-backend/internal/handlers/
├── report.go               # 报表处理器
├── setting.go              # 设置处理器
└── notification.go         # 通知处理器
```

## 🔧 **技术实施细节**

### **文件导出实现**
```go
// CSV导出
func (s *reportService) ExportToCSV(data [][]string) (string, error) {
    filename := fmt.Sprintf("report_%d.csv", time.Now().Unix())
    filepath := path.Join("/tmp/reports", filename)
    
    file, err := os.Create(filepath)
    if err != nil {
        return "", err
    }
    defer file.Close()
    
    writer := csv.NewWriter(file)
    defer writer.Flush()
    
    for _, record := range data {
        if err := writer.Write(record); err != nil {
            return "", err
        }
    }
    
    return filepath, nil
}
```

### **权限控制实现**
```go
// 系统设置权限检查
func (s *settingService) checkSystemSettingPermission(userID uint, category string) error {
    user, err := s.userRepo.GetByID(userID)
    if err != nil {
        return err
    }
    
    // 只有管理员可以修改系统设置
    if user.Role != "admin" {
        return errors.New("insufficient permissions")
    }
    
    return nil
}
```

### **数据聚合查询**
```go
// 性能指标聚合
func (s *reportService) aggregateMetrics(req *GetChartDataRequest) (*ChartDataResponse, error) {
    query := `
        SELECT 
            DATE_TRUNC(?, collected_at) as time_bucket,
            AVG(cpu_usage) as avg_cpu,
            MAX(memory_usage) as max_memory,
            COUNT(*) as sample_count
        FROM metrics 
        WHERE database_id = ? 
        AND collected_at BETWEEN ? AND ?
        GROUP BY time_bucket
        ORDER BY time_bucket
    `
    
    // 执行查询并格式化结果
    // ...
}
```

## 📊 **开发优先级和时间安排**

### **第1个周末 (6小时)**
1. **报表系统API** (4小时)
   - 数据模型创建和迁移
   - 基础CRUD API实现
   - 文件导出功能

2. **系统设置API** (2小时)
   - 数据模型创建
   - 基础设置管理API

### **第2个周末 (6小时)**
1. **监控告警完善** (4小时)
   - 通知渠道管理
   - 告警分析API
   - 高级规则管理

2. **前端集成和测试** (2小时)
   - API集成测试
   - 文档更新
   - 验收测试

## 🔍 **设计亮点和创新点**

### **1. 报表系统设计亮点**
- **模板化设计**: 支持自定义报表模板，提高复用性
- **异步执行**: 大型报表异步生成，避免请求超时
- **多格式导出**: 支持CSV、Excel、JSON多种格式
- **缓存优化**: 相同参数的报表结果缓存，提高性能

### **2. 系统设置设计亮点**
- **分层权限**: 系统设置vs用户偏好分离管理
- **类型安全**: 强类型值验证，避免配置错误
- **热更新**: 部分设置支持热更新，无需重启
- **审计日志**: 完整的设置变更记录

### **3. 告警系统设计亮点**
- **多渠道通知**: 统一的通知渠道管理
- **智能分析**: 告警趋势分析和异常检测
- **依赖管理**: 支持告警规则依赖关系
- **升级策略**: 自动告警升级机制

## 🚀 **性能优化策略**

### **数据库优化**
```sql
-- 报表执行表索引
CREATE INDEX idx_report_execution_status_time ON report_executions(status, created_at);
CREATE INDEX idx_report_execution_user_template ON report_executions(executed_by, template_id);

-- 系统设置表索引
CREATE UNIQUE INDEX idx_system_setting_category_key ON system_settings(category, key);

-- 用户偏好表索引
CREATE UNIQUE INDEX idx_user_preference_user_category_key ON user_preferences(user_id, category, key);
```

### **缓存策略**
```go
// Redis缓存键设计
const (
    CacheKeySystemSettings = "system:settings:%s"           // category
    CacheKeyUserPreferences = "user:preferences:%d:%s"      // userID, category
    CacheKeyReportData = "report:data:%s"                   // hash(parameters)
    CacheKeyChartData = "chart:data:%s"                     // hash(request)
)

// 缓存TTL设置
var CacheTTL = map[string]time.Duration{
    "system_settings":   24 * time.Hour,    // 系统设置缓存24小时
    "user_preferences":  1 * time.Hour,     // 用户偏好缓存1小时
    "report_data":       30 * time.Minute,  // 报表数据缓存30分钟
    "chart_data":        10 * time.Minute,  // 图表数据缓存10分钟
}
```

## 🔒 **安全性设计**

### **权限控制矩阵**
```
功能模块          | 普通用户 | 管理员 | 超级管理员
报表模板查看      |    ✅    |   ✅   |     ✅
报表模板创建      |    ✅    |   ✅   |     ✅
报表模板删除      |   自己   |   ✅   |     ✅
报表执行         |    ✅    |   ✅   |     ✅
系统设置查看      |   公开   |   ✅   |     ✅
系统设置修改      |    ❌    |   ✅   |     ✅
用户偏好管理      |   自己   |  自己  |     ✅
通知渠道管理      |    ❌    |   ✅   |     ✅
告警规则管理      |    ✅    |   ✅   |     ✅
```

### **数据验证规则**
```go
// 报表参数验证
func validateReportParameters(params map[string]interface{}) error {
    // 时间范围验证
    if startTime, ok := params["start_time"].(time.Time); ok {
        if startTime.After(time.Now()) {
            return errors.New("start_time cannot be in the future")
        }
        if time.Since(startTime) > 365*24*time.Hour {
            return errors.New("start_time cannot be more than 1 year ago")
        }
    }

    // 数据库ID验证
    if dbIDs, ok := params["database_ids"].([]uint); ok {
        if len(dbIDs) > 50 {
            return errors.New("too many databases selected (max: 50)")
        }
    }

    return nil
}
```

## 📈 **监控和日志设计**

### **关键指标监控**
```go
// Prometheus指标定义
var (
    ReportExecutionDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "report_execution_duration_seconds",
            Help: "Time spent executing reports",
        },
        []string{"template_type", "format"},
    )

    SettingUpdateCounter = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "setting_updates_total",
            Help: "Total number of setting updates",
        },
        []string{"category", "user_role"},
    )

    NotificationSendCounter = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "notifications_sent_total",
            Help: "Total number of notifications sent",
        },
        []string{"channel_type", "status"},
    )
)
```

### **结构化日志**
```go
// 操作日志记录
func (s *settingService) logSettingChange(userID uint, category, key string, oldValue, newValue interface{}) {
    logger.WithFields(logrus.Fields{
        "user_id":   userID,
        "category":  category,
        "key":       key,
        "old_value": oldValue,
        "new_value": newValue,
        "action":    "setting_update",
    }).Info("System setting updated")
}
```

## 🧪 **测试策略**

### **单元测试覆盖**
```go
// 报表服务测试示例
func TestReportService_ExecuteReport(t *testing.T) {
    tests := []struct {
        name    string
        request *ExecuteReportRequest
        want    *ReportExecution
        wantErr bool
    }{
        {
            name: "valid performance report",
            request: &ExecuteReportRequest{
                TemplateID: 1,
                Format:     "csv",
                TimeRange: TimeRangeRequest{
                    StartTime: time.Now().Add(-24 * time.Hour),
                    EndTime:   time.Now(),
                },
            },
            wantErr: false,
        },
        {
            name: "invalid time range",
            request: &ExecuteReportRequest{
                TemplateID: 1,
                Format:     "csv",
                TimeRange: TimeRangeRequest{
                    StartTime: time.Now(),
                    EndTime:   time.Now().Add(-24 * time.Hour),
                },
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 测试实现
        })
    }
}
```

### **集成测试计划**
1. **API端点测试**: 所有端点的正常和异常情况
2. **权限测试**: 不同角色的权限验证
3. **性能测试**: 大数据量报表生成性能
4. **并发测试**: 多用户同时操作的并发安全性

## 📋 **实施检查清单**

### **开发前准备**
- [ ] 确认数据库迁移脚本
- [ ] 准备测试数据
- [ ] 配置开发环境
- [ ] 创建功能分支

### **开发过程检查**
- [ ] 数据模型创建和验证
- [ ] 仓储层实现和测试
- [ ] 服务层实现和测试
- [ ] 处理器层实现和测试
- [ ] Swagger文档更新
- [ ] 单元测试编写

### **完成后验证**
- [ ] API功能测试
- [ ] 权限控制验证
- [ ] 性能基准测试
- [ ] 前端集成测试
- [ ] 文档更新完成

这个设计方案提供了完整的技术实施路径，确保代码质量和系统稳定性。准备好开始实施了吗？
