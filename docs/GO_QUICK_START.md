# Go后端快速启动指南

## 🚀 第一个周末：Go后端基础搭建

### 环境准备 (30分钟)

#### 1. 确认Go环境
```bash
# 检查Go版本 (需要1.21+)
go version

# 如果没有安装Go，请访问: https://golang.org/dl/
```

#### 2. 安装开发工具
```bash
# 安装Air (热重载工具)
go install github.com/cosmtrek/air@latest

# 安装Swag (API文档生成)
go install github.com/swaggo/swag/cmd/swag@latest
```

### 项目初始化 (1小时)

#### 1. 创建项目结构
```bash
# 创建后端目录
mkdir backend
cd backend

# 初始化Go模块
go mod init db-monitor-platform

# 创建项目结构
mkdir -p cmd/server
mkdir -p internal/{config,models,handlers,middleware,services,repository,utils}
mkdir -p pkg/{database,redis,logger}
mkdir -p api/swagger
mkdir -p configs
mkdir -p tests/{unit,integration}
mkdir -p docker
```

#### 2. 安装核心依赖
```bash
# Web框架
go get github.com/gin-gonic/gin

# 数据库
go get gorm.io/gorm
go get gorm.io/driver/postgres

# Redis
go get github.com/go-redis/redis/v8

# JWT认证
go get github.com/golang-jwt/jwt/v5

# 密码加密
go get golang.org/x/crypto/bcrypt

# WebSocket
go get github.com/gorilla/websocket

# 配置管理
go get github.com/spf13/viper

# 日志
go get github.com/sirupsen/logrus

# 验证器
go get github.com/go-playground/validator/v10

# 测试
go get github.com/stretchr/testify

# CORS
go get github.com/gin-contrib/cors

# Swagger
go get github.com/swaggo/gin-swagger
go get github.com/swaggo/files
```

### 核心文件创建 (2小时)

#### 1. 主入口文件 `cmd/server/main.go`
```go
package main

import (
    "log"
    "db-monitor-platform/internal/config"
    "db-monitor-platform/internal/handlers"
    "db-monitor-platform/pkg/database"
    "db-monitor-platform/pkg/logger"
    "github.com/gin-gonic/gin"
)

func main() {
    // 加载配置
    cfg := config.Load()
    
    // 初始化日志
    logger.Init(cfg.LogLevel)
    
    // 连接数据库
    db := database.Connect(cfg.DatabaseURL)
    
    // 自动迁移
    database.Migrate(db)
    
    // 创建Gin引擎
    r := gin.Default()
    
    // 设置路由
    handlers.SetupRoutes(r, db)
    
    // 启动服务器
    log.Printf("Server starting on port %s", cfg.Port)
    log.Fatal(r.Run(":" + cfg.Port))
}
```

#### 2. 配置管理 `internal/config/config.go`
```go
package config

import (
    "github.com/spf13/viper"
    "log"
)

type Config struct {
    Port        string `mapstructure:"PORT"`
    DatabaseURL string `mapstructure:"DATABASE_URL"`
    RedisURL    string `mapstructure:"REDIS_URL"`
    JWTSecret   string `mapstructure:"JWT_SECRET"`
    LogLevel    string `mapstructure:"LOG_LEVEL"`
}

func Load() *Config {
    viper.SetConfigName("config")
    viper.SetConfigType("yaml")
    viper.AddConfigPath("./configs")
    viper.AddConfigPath(".")
    
    // 设置默认值
    viper.SetDefault("PORT", "8080")
    viper.SetDefault("LOG_LEVEL", "info")
    viper.SetDefault("JWT_SECRET", "your-secret-key")
    
    // 读取环境变量
    viper.AutomaticEnv()
    
    if err := viper.ReadInConfig(); err != nil {
        log.Printf("Config file not found, using defaults: %v", err)
    }
    
    var config Config
    if err := viper.Unmarshal(&config); err != nil {
        log.Fatal("Unable to decode config:", err)
    }
    
    return &config
}
```

#### 3. 数据库连接 `pkg/database/postgres.go`
```go
package database

import (
    "log"
    "gorm.io/driver/postgres"
    "gorm.io/gorm"
    "db-monitor-platform/internal/models"
)

func Connect(databaseURL string) *gorm.DB {
    db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{})
    if err != nil {
        log.Fatal("Failed to connect to database:", err)
    }
    
    // 配置连接池
    sqlDB, _ := db.DB()
    sqlDB.SetMaxIdleConns(10)
    sqlDB.SetMaxOpenConns(100)
    
    return db
}

func Migrate(db *gorm.DB) {
    err := db.AutoMigrate(
        &models.User{},
        &models.DatabaseInstance{},
        &models.Metric{},
        &models.AlertRule{},
    )
    if err != nil {
        log.Fatal("Failed to migrate database:", err)
    }
}
```

#### 4. 用户模型 `internal/models/user.go`
```go
package models

import (
    "time"
    "gorm.io/gorm"
)

type User struct {
    ID        uint           `json:"id" gorm:"primaryKey"`
    Email     string         `json:"email" gorm:"uniqueIndex;not null"`
    Password  string         `json:"-" gorm:"not null"`
    Name      string         `json:"name" gorm:"not null"`
    Role      string         `json:"role" gorm:"default:user"`
    AvatarURL string         `json:"avatar_url"`
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

type UserRequest struct {
    Email    string `json:"email" binding:"required,email"`
    Password string `json:"password" binding:"required,min=6"`
    Name     string `json:"name" binding:"required"`
}

type LoginRequest struct {
    Email    string `json:"email" binding:"required,email"`
    Password string `json:"password" binding:"required"`
}

type UserResponse struct {
    ID        uint      `json:"id"`
    Email     string    `json:"email"`
    Name      string    `json:"name"`
    Role      string    `json:"role"`
    AvatarURL string    `json:"avatar_url"`
    CreatedAt time.Time `json:"created_at"`
}
```

#### 5. 认证处理器 `internal/handlers/auth.go`
```go
package handlers

import (
    "net/http"
    "db-monitor-platform/internal/models"
    "db-monitor-platform/internal/utils"
    "github.com/gin-gonic/gin"
    "gorm.io/gorm"
)

type AuthHandler struct {
    db *gorm.DB
}

func NewAuthHandler(db *gorm.DB) *AuthHandler {
    return &AuthHandler{db: db}
}

// @Summary 用户注册
// @Description 创建新用户账户
// @Tags auth
// @Accept json
// @Produce json
// @Param user body models.UserRequest true "用户信息"
// @Success 201 {object} models.UserResponse
// @Router /api/v1/auth/register [post]
func (h *AuthHandler) Register(c *gin.Context) {
    var req models.UserRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // 检查用户是否已存在
    var existingUser models.User
    if err := h.db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
        c.JSON(http.StatusConflict, gin.H{"error": "用户已存在"})
        return
    }
    
    // 加密密码
    hashedPassword, err := utils.HashPassword(req.Password)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "密码加密失败"})
        return
    }
    
    // 创建用户
    user := models.User{
        Email:    req.Email,
        Password: hashedPassword,
        Name:     req.Name,
    }
    
    if err := h.db.Create(&user).Error; err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "创建用户失败"})
        return
    }
    
    // 返回用户信息
    response := models.UserResponse{
        ID:        user.ID,
        Email:     user.Email,
        Name:      user.Name,
        Role:      user.Role,
        AvatarURL: user.AvatarURL,
        CreatedAt: user.CreatedAt,
    }
    
    c.JSON(http.StatusCreated, response)
}

// @Summary 用户登录
// @Description 用户登录获取JWT token
// @Tags auth
// @Accept json
// @Produce json
// @Param credentials body models.LoginRequest true "登录凭据"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
    var req models.LoginRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // 查找用户
    var user models.User
    if err := h.db.Where("email = ?", req.Email).First(&user).Error; err != nil {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "邮箱或密码错误"})
        return
    }
    
    // 验证密码
    if !utils.CheckPassword(req.Password, user.Password) {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "邮箱或密码错误"})
        return
    }
    
    // 生成JWT token
    token, err := utils.GenerateToken(&user)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "生成token失败"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "token": token,
        "user": models.UserResponse{
            ID:        user.ID,
            Email:     user.Email,
            Name:      user.Name,
            Role:      user.Role,
            AvatarURL: user.AvatarURL,
            CreatedAt: user.CreatedAt,
        },
    })
}
```

### 配置文件 (15分钟)

#### 1. 创建 `configs/config.yaml`
```yaml
# 服务器配置
port: "8080"
log_level: "info"

# 数据库配置
database_url: "postgres://postgres:password@localhost:5432/dbmonitor?sslmode=disable"

# Redis配置
redis_url: "redis://localhost:6379"

# JWT配置
jwt_secret: "your-super-secret-jwt-key"
```

#### 2. 创建 `.air.toml` (热重载配置)
```toml
root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
  args_bin = []
  bin = "./tmp/main"
  cmd = "go build -o ./tmp/main ./cmd/server"
  delay = 1000
  exclude_dir = ["assets", "tmp", "vendor", "testdata"]
  exclude_file = []
  exclude_regex = ["_test.go"]
  exclude_unchanged = false
  follow_symlink = false
  full_bin = ""
  include_dir = []
  include_ext = ["go", "tpl", "tmpl", "html"]
  kill_delay = "0s"
  log = "build-errors.log"
  send_interrupt = false
  stop_on_root = false

[color]
  app = ""
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"

[log]
  time = false

[misc]
  clean_on_exit = false
```

### 启动项目 (15分钟)

```bash
# 启动PostgreSQL和Redis (使用Docker)
docker run -d --name postgres -e POSTGRES_PASSWORD=password -e POSTGRES_DB=dbmonitor -p 5432:5432 postgres:14
docker run -d --name redis -p 6379:6379 redis:7-alpine

# 启动Go服务器 (热重载)
air

# 或者直接运行
go run cmd/server/main.go
```

### 测试API (15分钟)

```bash
# 测试用户注册
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "Admin User"
  }'

# 测试用户登录
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

## 🎯 第一个周末目标完成检查

- [ ] Go项目成功初始化
- [ ] 数据库连接正常
- [ ] 用户注册API工作正常
- [ ] 用户登录API工作正常
- [ ] JWT token生成和验证
- [ ] 热重载开发环境配置完成

## 📝 下周末预告

下个周末我们将实现：
- 数据库实例管理API
- 监控指标收集API
- WebSocket实时数据推送
- 前端API集成

---

*准备好开始了吗？让我们用Go构建一个高性能的监控系统！🚀*
