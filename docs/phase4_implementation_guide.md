# Phase 4 剩余API实施指南

> **基于**: phase4_remaining_apis_design.md  
> **目标**: 提供清晰的实施步骤和代码模板  
> **时间**: 2个周末完成  

## 🚀 **快速开始**

### **第1步: 环境准备**
```bash
# 1. 创建功能分支
git checkout -b feature/phase4-remaining-apis

# 2. 确保数据库运行
docker-compose up -d postgres redis

# 3. 备份当前数据库
pg_dump -h localhost -U dbmonitor db_monitor > backup_before_phase4.sql
```

### **第2步: 数据模型创建**
按照以下顺序创建模型文件：

1. `backend/go-backend/internal/models/report_template.go`
2. `backend/go-backend/internal/models/report_execution.go`
3. `backend/go-backend/internal/models/system_setting.go`
4. `backend/go-backend/internal/models/user_preference.go`
5. `backend/go-backend/internal/models/notification_channel.go`

## 📋 **第1个周末实施计划**

### **周六上午 (3小时): 报表系统基础**

#### **任务1.1: 创建报表数据模型 (45分钟)**
```go
// backend/go-backend/internal/models/report_template.go
package models

import (
    "time"
    "gorm.io/gorm"
)

type ReportTemplate struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    Name        string    `json:"name" gorm:"size:100;not null"`
    Description string    `json:"description" gorm:"size:500"`
    Type        string    `json:"type" gorm:"size:50;not null"`
    Config      string    `json:"config" gorm:"type:text"`
    CreatedBy   uint      `json:"created_by" gorm:"not null"`
    IsActive    bool      `json:"is_active" gorm:"default:true"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
    
    Creator     User      `json:"creator" gorm:"foreignKey:CreatedBy"`
}

type ReportExecution struct {
    ID           uint      `json:"id" gorm:"primaryKey"`
    TemplateID   uint      `json:"template_id" gorm:"not null"`
    ExecutedBy   uint      `json:"executed_by" gorm:"not null"`
    Status       string    `json:"status" gorm:"size:20;default:'pending'"`
    Parameters   string    `json:"parameters" gorm:"type:text"`
    FilePath     string    `json:"file_path" gorm:"size:500"`
    FileSize     int64     `json:"file_size" gorm:"default:0"`
    ErrorMessage string    `json:"error_message" gorm:"type:text"`
    StartTime    time.Time `json:"start_time"`
    EndTime      *time.Time `json:"end_time"`
    CreatedAt    time.Time `json:"created_at"`
    
    Template     ReportTemplate `json:"template" gorm:"foreignKey:TemplateID"`
    Executor     User          `json:"executor" gorm:"foreignKey:ExecutedBy"`
}
```

#### **任务1.2: 创建报表请求模型 (30分钟)**
```go
// backend/go-backend/internal/models/report_requests.go
package models

import "time"

type CreateReportTemplateRequest struct {
    Name        string                 `json:"name" binding:"required,max=100"`
    Description string                 `json:"description" binding:"max=500"`
    Type        string                 `json:"type" binding:"required,oneof=performance usage alert"`
    Config      map[string]interface{} `json:"config" binding:"required"`
}

type ExecuteReportRequest struct {
    TemplateID  uint                   `json:"template_id" binding:"required"`
    Parameters  map[string]interface{} `json:"parameters"`
    Format      string                 `json:"format" binding:"required,oneof=csv excel json"`
    TimeRange   TimeRangeRequest       `json:"time_range" binding:"required"`
    DatabaseIDs []uint                 `json:"database_ids"`
}

type TimeRangeRequest struct {
    StartTime time.Time `json:"start_time" binding:"required"`
    EndTime   time.Time `json:"end_time" binding:"required"`
}

type GetChartDataRequest struct {
    ChartType   string    `form:"chart_type" binding:"required,oneof=line bar pie area"`
    MetricType  string    `form:"metric_type" binding:"required"`
    DatabaseID  uint      `form:"database_id"`
    StartTime   time.Time `form:"start_time" binding:"required"`
    EndTime     time.Time `form:"end_time" binding:"required"`
    Granularity string    `form:"granularity" binding:"oneof=minute hour day week month"`
}

type ChartDataResponse struct {
    Labels []string      `json:"labels"`
    Series []ChartSeries `json:"series"`
}

type ChartSeries struct {
    Name string        `json:"name"`
    Data []interface{} `json:"data"`
}
```

#### **任务1.3: 数据库迁移 (30分钟)**
```go
// 在 backend/go-backend/internal/models/models.go 中添加
func AutoMigrate(db *gorm.DB) error {
    return db.AutoMigrate(
        // 现有模型...
        &ReportTemplate{},
        &ReportExecution{},
        // 其他新模型...
    )
}
```

#### **任务1.4: 创建报表仓储 (75分钟)**
```go
// backend/go-backend/internal/repository/report_template.go
package repository

import (
    "context"
    "db-monitor-platform/internal/models"
    "gorm.io/gorm"
)

type ReportTemplateRepository interface {
    Create(ctx context.Context, template *models.ReportTemplate) error
    GetByID(ctx context.Context, id uint) (*models.ReportTemplate, error)
    GetByUserID(ctx context.Context, userID uint, page, pageSize int) ([]*models.ReportTemplate, int64, error)
    Update(ctx context.Context, template *models.ReportTemplate) error
    Delete(ctx context.Context, id uint) error
}

type reportTemplateRepository struct {
    db *gorm.DB
}

func NewReportTemplateRepository(db *gorm.DB) ReportTemplateRepository {
    return &reportTemplateRepository{db: db}
}

func (r *reportTemplateRepository) Create(ctx context.Context, template *models.ReportTemplate) error {
    return r.db.WithContext(ctx).Create(template).Error
}

func (r *reportTemplateRepository) GetByID(ctx context.Context, id uint) (*models.ReportTemplate, error) {
    var template models.ReportTemplate
    err := r.db.WithContext(ctx).Preload("Creator").First(&template, id).Error
    return &template, err
}

// 实现其他方法...
```

### **周六下午 (3小时): 系统设置基础**

#### **任务2.1: 创建设置数据模型 (45分钟)**
```go
// backend/go-backend/internal/models/system_setting.go
package models

import "time"

type SystemSetting struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    Category    string    `json:"category" gorm:"size:50;not null;index"`
    Key         string    `json:"key" gorm:"size:100;not null"`
    Value       string    `json:"value" gorm:"type:text"`
    ValueType   string    `json:"value_type" gorm:"size:20;default:'string'"`
    Description string    `json:"description" gorm:"size:500"`
    IsPublic    bool      `json:"is_public" gorm:"default:false"`
    UpdatedBy   uint      `json:"updated_by"`
    UpdatedAt   time.Time `json:"updated_at"`
    CreatedAt   time.Time `json:"created_at"`
    
    Updater     User      `json:"updater" gorm:"foreignKey:UpdatedBy"`
}

type UserPreference struct {
    ID        uint      `json:"id" gorm:"primaryKey"`
    UserID    uint      `json:"user_id" gorm:"not null;index"`
    Category  string    `json:"category" gorm:"size:50;not null"`
    Key       string    `json:"key" gorm:"size:100;not null"`
    Value     string    `json:"value" gorm:"type:text"`
    UpdatedAt time.Time `json:"updated_at"`
    CreatedAt time.Time `json:"created_at"`
    
    User      User      `json:"user" gorm:"foreignKey:UserID"`
}
```

#### **任务2.2: 创建设置服务 (90分钟)**
```go
// backend/go-backend/internal/services/setting.go
package services

import (
    "context"
    "encoding/json"
    "db-monitor-platform/internal/models"
    "db-monitor-platform/internal/repository"
)

type SettingService interface {
    GetSystemSettings(ctx context.Context, category string, userID uint) (map[string]interface{}, error)
    UpdateSystemSettings(ctx context.Context, settings []models.SystemSettingUpdate, userID uint) error
    GetUserPreferences(ctx context.Context, userID uint, category string) (map[string]interface{}, error)
    UpdateUserPreferences(ctx context.Context, preferences []models.UserPreferenceUpdate, userID uint) error
}

type settingService struct {
    systemSettingRepo repository.SystemSettingRepository
    userPrefRepo      repository.UserPreferenceRepository
    userRepo          repository.UserRepository
}

func NewSettingService(
    systemSettingRepo repository.SystemSettingRepository,
    userPrefRepo repository.UserPreferenceRepository,
    userRepo repository.UserRepository,
) SettingService {
    return &settingService{
        systemSettingRepo: systemSettingRepo,
        userPrefRepo:      userPrefRepo,
        userRepo:          userRepo,
    }
}

// 实现接口方法...
```

#### **任务2.3: 创建设置API处理器 (45分钟)**
```go
// backend/go-backend/internal/handlers/setting.go
package handlers

import (
    "net/http"
    "db-monitor-platform/internal/models"
    "db-monitor-platform/internal/services"
    "github.com/gin-gonic/gin"
)

type SettingHandler struct {
    settingService services.SettingService
}

func NewSettingHandler(settingService services.SettingService) *SettingHandler {
    return &SettingHandler{
        settingService: settingService,
    }
}

// @Summary 获取系统设置
// @Tags Settings
// @Accept json
// @Produce json
// @Param category query string false "设置分类"
// @Success 200 {object} models.CommonResponse
// @Router /api/v1/settings/system [get]
func (h *SettingHandler) GetSystemSettings(c *gin.Context) {
    userID := c.GetUint("user_id")
    category := c.Query("category")
    
    settings, err := h.settingService.GetSystemSettings(c.Request.Context(), category, userID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, models.ErrorResponse(500, err.Error()))
        return
    }
    
    c.JSON(http.StatusOK, models.SuccessResponse(settings))
}

// 实现其他处理器方法...
```

## 📋 **第2个周末实施计划**

### **周日上午 (3小时): 监控告警完善**

#### **任务3.1: 通知渠道模型 (45分钟)**
#### **任务3.2: 告警分析API (90分钟)**
#### **任务3.3: 通知渠道管理 (45分钟)**

### **周日下午 (3小时): 集成和测试**

#### **任务4.1: 路由配置 (30分钟)**
```go
// 在 backend/go-backend/internal/handlers/routes.go 中添加
func setupReportRoutes(api *gin.RouterGroup, reportHandler *ReportHandler) {
    reports := api.Group("/reports")
    reports.Use(middleware.AuthRequired())
    {
        // 报表模板
        reports.GET("/templates", reportHandler.GetTemplates)
        reports.POST("/templates", reportHandler.CreateTemplate)
        reports.PUT("/templates/:id", reportHandler.UpdateTemplate)
        reports.DELETE("/templates/:id", reportHandler.DeleteTemplate)
        
        // 报表执行
        reports.POST("/execute", reportHandler.ExecuteReport)
        reports.GET("/executions/:id", reportHandler.GetExecution)
        reports.GET("/executions/:id/download", reportHandler.DownloadReport)
        
        // 图表数据
        reports.GET("/charts", reportHandler.GetChartData)
        reports.GET("/summary", reportHandler.GetSummary)
    }
}
```

#### **任务4.2: 前端API集成 (90分钟)**
#### **任务4.3: 测试和验证 (60分钟)**

## ✅ **验收检查清单**

### **功能验收**
- [ ] 报表模板CRUD操作正常
- [ ] 报表执行和文件下载功能正常
- [ ] 系统设置管理功能正常
- [ ] 用户偏好设置功能正常
- [ ] 通知渠道管理功能正常
- [ ] 告警分析API正常响应

### **技术验收**
- [ ] 所有API端点有Swagger文档
- [ ] 权限控制正确实施
- [ ] 错误处理完善
- [ ] 日志记录完整
- [ ] 性能指标达标

### **集成验收**
- [ ] 前端页面完全集成
- [ ] 无模拟数据残留
- [ ] 用户体验流畅
- [ ] 错误提示友好

完成这个实施指南后，您就可以按照清晰的步骤完成Phase 4的剩余API开发了！
