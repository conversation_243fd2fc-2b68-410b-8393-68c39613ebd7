# API 测试指南

> **创建时间**: 2025-01-13  
> **最后更新**: 2025-01-13  
> **测试环境**: 开发环境 (localhost)

## 📋 概述

本文档记录了数据库监控平台API的完整测试流程，包括认证、数据库管理、监控指标等核心功能的测试方法。

## 🚀 环境准备

### 1. 启动服务

```bash
# 1. 启动数据库服务
cd /path/to/db-monitor-platform
docker-compose up -d postgres redis

# 2. 启动后端服务
cd backend/go-backend
./server
# 或者
go run cmd/server/main.go

# 3. 启动前端服务 (可选)
cd frontend
npm run dev
```

### 2. 验证服务状态

```bash
# 检查后端健康状态
curl -s http://localhost:8080/health

# 预期响应:
# {"message":"Database Monitor Platform API is running","status":"ok","version":"1.0.0"}
```

## 🔐 认证测试

### 1. 用户登录

```bash
curl -s -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}' \
  http://localhost:8080/api/v1/auth/login
```

**预期响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "user": {
      "id": 5,
      "email": "<EMAIL>",
      "name": "Admin User",
      "role": "user"
    },
    "token": {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "token_type": "Bearer",
      "expires_in": 86400
    }
  }
}
```

**重要**: 保存 `access_token` 用于后续API调用

### 2. 设置认证头

```bash
# 将token设置为环境变量 (推荐)
export TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

# 或者在每个请求中使用完整的Authorization头
# -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

## 🗄️ 数据库管理API测试

### 1. 获取数据库列表

```bash
curl -s -H "Authorization: Bearer $TOKEN" \
  http://localhost:8080/api/v1/databases
```

**预期响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "page": 1,
    "page_size": 20,
    "total": 1,
    "total_pages": 1,
    "data": [
      {
        "id": 5,
        "name": "db_monitor",
        "type": "postgresql",
        "host": "localhost",
        "port": 5432,
        "database_name": "db_monitor",
        "username": "dbmonitor",
        "status": "active",
        "description": "",
        "is_monitored": true,
        "created_by": 5,
        "created_at": "2025-07-13T13:33:35.332194+08:00",
        "updated_at": "2025-07-13T20:50:30.502248+08:00"
      }
    ]
  }
}
```

### 2. 创建数据库

```bash
curl -s -X POST \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试数据库",
    "type": "postgresql",
    "host": "localhost",
    "port": 5433,
    "username": "testuser",
    "password": "testpass",
    "database_name": "testdb",
    "description": "这是一个测试数据库"
  }' \
  http://localhost:8080/api/v1/databases
```

**预期响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 6,
    "name": "测试数据库",
    "type": "postgresql",
    "host": "localhost",
    "port": 5433,
    "database_name": "testdb",
    "username": "testuser",
    "status": "active",
    "description": "这是一个测试数据库",
    "is_monitored": true,
    "created_by": 5,
    "created_at": "2025-07-13T20:51:09.710131339+08:00",
    "updated_at": "2025-07-13T20:51:09.710131339+08:00"
  }
}
```

### 3. 更新数据库

```bash
# 使用上一步创建的数据库ID (例如: 6)
curl -s -X PUT \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "更新后的测试数据库",
    "description": "这是一个更新后的测试数据库"
  }' \
  http://localhost:8080/api/v1/databases/6
```

**预期响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 6,
    "name": "更新后的测试数据库",
    "type": "postgresql",
    "host": "localhost",
    "port": 5433,
    "database_name": "testdb",
    "username": "testuser",
    "status": "active",
    "description": "这是一个更新后的测试数据库",
    "is_monitored": true,
    "created_by": 5,
    "created_at": "2025-07-13T20:51:09.710131+08:00",
    "updated_at": "2025-07-13T20:51:34.957773575+08:00"
  }
}
```

### 4. 测试数据库连接

```bash
curl -s -X POST \
  -H "Authorization: Bearer $TOKEN" \
  http://localhost:8080/api/v1/databases/6/test
```

**预期响应** (连接失败示例):
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "database_id": 6,
    "success": false,
    "error": "Network connection failed: dial tcp 127.0.0.1:5433: connect: connection refused",
    "start_time": "2025-07-13T20:51:55.018262331+08:00",
    "end_time": "2025-07-13T20:51:55.018863713+08:00",
    "duration": "601.391µs"
  }
}
```

**预期响应** (连接成功示例):
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "database_id": 5,
    "success": true,
    "message": "Connection successful",
    "start_time": "2025-07-13T20:51:55.018262331+08:00",
    "end_time": "2025-07-13T20:51:55.018863713+08:00",
    "duration": "1.2ms"
  }
}
```

### 5. 删除数据库

```bash
curl -s -X DELETE \
  -H "Authorization: Bearer $TOKEN" \
  http://localhost:8080/api/v1/databases/6
```

**预期响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "Database deleted successfully"
  }
}
```

### 6. 验证删除结果

```bash
# 再次获取数据库列表，确认删除成功
curl -s -H "Authorization: Bearer $TOKEN" \
  http://localhost:8080/api/v1/databases
```

## 📊 监控指标API测试

### 1. 获取实时指标

```bash
curl -s -H "Authorization: Bearer $TOKEN" \
  http://localhost:8080/api/v1/metrics/realtime
```

**预期响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "database_id": 5,
      "database_name": "db_monitor",
      "status": "active",
      "metrics": {},
      "timestamp": "2025-07-13T20:52:54.87580911+08:00"
    }
  ]
}
```

### 2. 获取数据库统计

```bash
curl -s -H "Authorization: Bearer $TOKEN" \
  http://localhost:8080/api/v1/databases/stats
```

## 🚨 告警系统API测试

### 1. 获取告警统计 (已知问题)

```bash
curl -s -H "Authorization: Bearer $TOKEN" \
  http://localhost:8080/api/v1/alerts/stats
```

**当前响应** (有问题):
```json
{
  "code": 500,
  "message": "Failed to get alert statistics"
}
```

**注意**: 此API目前有SQL错误，需要修复。

## 🔧 常见问题排查

### 1. 认证失败 (401)

```bash
# 检查token是否过期
curl -s -H "Authorization: Bearer $TOKEN" \
  http://localhost:8080/api/v1/profile

# 如果返回401，重新登录获取新token
```

### 2. 服务不可用 (连接拒绝)

```bash
# 检查服务是否运行
curl -s http://localhost:8080/health

# 检查端口是否被占用
netstat -tlnp | grep 8080
```

### 3. 数据库连接问题

```bash
# 检查PostgreSQL是否运行
docker ps | grep postgres

# 检查数据库连接
docker exec -it db-monitor-postgres psql -U dbmonitor -d db_monitor
```

## 📝 测试检查清单

### ✅ 基础功能测试
- [ ] 服务健康检查
- [ ] 用户登录认证
- [ ] 获取数据库列表
- [ ] 创建数据库
- [ ] 更新数据库
- [ ] 测试数据库连接
- [ ] 删除数据库
- [ ] 获取实时指标

### ⚠️ 已知问题
- [ ] 告警统计API (500错误)

### 🎯 性能测试
- [ ] 并发请求测试
- [ ] 大量数据测试
- [ ] 长时间运行测试

## 🔄 自动化测试脚本

### 完整测试脚本

创建文件 `test-api.sh`:

```bash
#!/bin/bash

# API测试脚本
# 使用方法: ./test-api.sh

BASE_URL="http://localhost:8080"
EMAIL="<EMAIL>"
PASSWORD="admin123"

echo "🚀 开始API测试..."

# 1. 健康检查
echo "1. 检查服务健康状态..."
curl -s $BASE_URL/health | jq '.'

# 2. 登录获取token
echo -e "\n2. 用户登录..."
LOGIN_RESPONSE=$(curl -s -H "Content-Type: application/json" \
  -d "{\"email\":\"$EMAIL\",\"password\":\"$PASSWORD\"}" \
  $BASE_URL/api/v1/auth/login)

TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.data.token.access_token')
echo "Token获取成功: ${TOKEN:0:20}..."

# 3. 获取数据库列表
echo -e "\n3. 获取数据库列表..."
curl -s -H "Authorization: Bearer $TOKEN" \
  $BASE_URL/api/v1/databases | jq '.'

# 4. 创建测试数据库
echo -e "\n4. 创建测试数据库..."
CREATE_RESPONSE=$(curl -s -X POST \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "API测试数据库",
    "type": "postgresql",
    "host": "localhost",
    "port": 5433,
    "username": "testuser",
    "password": "testpass",
    "database_name": "api_test_db",
    "description": "通过API测试脚本创建"
  }' \
  $BASE_URL/api/v1/databases)

DB_ID=$(echo $CREATE_RESPONSE | jq -r '.data.id')
echo "创建的数据库ID: $DB_ID"
echo $CREATE_RESPONSE | jq '.'

# 5. 更新数据库
echo -e "\n5. 更新数据库..."
curl -s -X PUT \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "API测试数据库(已更新)",
    "description": "通过API测试脚本更新"
  }' \
  $BASE_URL/api/v1/databases/$DB_ID | jq '.'

# 6. 测试连接
echo -e "\n6. 测试数据库连接..."
curl -s -X POST \
  -H "Authorization: Bearer $TOKEN" \
  $BASE_URL/api/v1/databases/$DB_ID/test | jq '.'

# 7. 获取实时指标
echo -e "\n7. 获取实时指标..."
curl -s -H "Authorization: Bearer $TOKEN" \
  $BASE_URL/api/v1/metrics/realtime | jq '.'

# 8. 删除测试数据库
echo -e "\n8. 删除测试数据库..."
curl -s -X DELETE \
  -H "Authorization: Bearer $TOKEN" \
  $BASE_URL/api/v1/databases/$DB_ID | jq '.'

# 9. 验证删除
echo -e "\n9. 验证删除结果..."
curl -s -H "Authorization: Bearer $TOKEN" \
  $BASE_URL/api/v1/databases | jq '.data.total'

echo -e "\n✅ API测试完成!"
```

### 使用方法

```bash
# 1. 创建测试脚本
chmod +x test-api.sh

# 2. 安装jq (用于JSON格式化)
# Ubuntu/Debian:
sudo apt-get install jq
# macOS:
brew install jq

# 3. 运行测试
./test-api.sh
```

## 🎯 压力测试

### 并发创建数据库测试

```bash
#!/bin/bash
# 并发测试脚本

TOKEN="your_token_here"
BASE_URL="http://localhost:8080"

# 并发创建10个数据库
for i in {1..10}; do
  (
    curl -s -X POST \
      -H "Authorization: Bearer $TOKEN" \
      -H "Content-Type: application/json" \
      -d "{
        \"name\": \"并发测试DB$i\",
        \"type\": \"postgresql\",
        \"host\": \"localhost\",
        \"port\": $((5432 + i)),
        \"username\": \"user$i\",
        \"password\": \"pass$i\",
        \"database_name\": \"testdb$i\",
        \"description\": \"并发测试数据库$i\"
      }" \
      $BASE_URL/api/v1/databases
  ) &
done

wait
echo "并发测试完成"
```

## 📊 测试结果记录

### 2025-01-13 测试结果

| API端点 | 状态 | 响应时间 | 备注 |
|---------|------|----------|------|
| GET /health | ✅ | <10ms | 正常 |
| POST /auth/login | ✅ | ~100ms | 正常 |
| GET /databases | ✅ | ~50ms | 正常 |
| POST /databases | ✅ | ~200ms | 正常 |
| PUT /databases/:id | ✅ | ~150ms | 正常 |
| POST /databases/:id/test | ✅ | ~600μs | 正常(连接失败) |
| DELETE /databases/:id | ✅ | ~100ms | 正常 |
| GET /metrics/realtime | ✅ | ~80ms | 正常 |
| GET /alerts/stats | ❌ | ~50ms | 500错误 |

### 发现的问题

1. **告警统计API错误**
   - 端点: `GET /api/v1/alerts/stats`
   - 错误: 500 Internal Server Error
   - 原因: SQL查询错误
   - 状态: 待修复

## 📚 相关文档

- [API文档](../backend/go-backend/docs/swagger.yaml)
- [数据库列表流程详解](./database-list-flow.md)
- [前端学习指南](./frontend-learning-guide.md)
- [项目状态](../PROJECT_STATUS.md)
