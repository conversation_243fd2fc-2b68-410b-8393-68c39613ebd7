# 报表生成功能设计文档

## 📋 概述

### 问题描述
当前数据库监控平台的报表系统已实现模板管理、执行记录管理和前端界面，但缺少核心的**报表生成功能**。用户点击"执行报表"后，系统只创建pending状态的执行记录，没有实际生成报表文件。

### 目标
实现完整的报表生成功能，支持多种格式（CSV、PDF、Excel、JSON），基于真实监控数据生成可下载的报表文件。

## 🏗️ 系统架构设计

### 核心组件架构
```
报表生成系统
├── ReportGenerator (报表生成器接口)
│   ├── CSVGenerator (CSV生成器)
│   ├── PDFGenerator (PDF生成器)
│   ├── ExcelGenerator (Excel生成器)
│   └── JSONGenerator (JSON生成器)
├── FileManager (文件管理器)
├── DataQueryService (数据查询服务)
├── AsyncTaskProcessor (异步任务处理器)
└── DownloadAPI (文件下载API)
```

### 数据流程图
```
用户请求执行报表
    ↓
创建pending状态执行记录
    ↓
启动异步生成任务 (goroutine)
    ↓
查询监控数据 (metrics表)
    ↓
数据处理和格式化
    ↓
生成报表文件 (CSV/PDF/Excel/JSON)
    ↓
更新执行记录状态为completed
    ↓
用户下载生成的文件
```

### 文件存储结构
```
reports/
├── 2025/
│   ├── 01/                          # 按年月组织
│   │   ├── report_1_20250120_143022.csv
│   │   ├── report_2_20250120_143055.pdf
│   │   └── report_3_20250120_144010.xlsx
│   └── 02/
├── temp/                            # 临时文件
└── archive/                         # 归档文件
```

## 📅 实施计划

### 阶段1：MVP版本（优先级：高）
**目标：实现基本的CSV报表生成功能**

| 任务 | 预估时间 | 负责模块 | 描述 |
|------|----------|----------|------|
| 1. 报表生成器框架 | 30分钟 | Backend | 创建ReportGenerator接口和CSVGenerator实现 |
| 2. 文件管理工具 | 20分钟 | Backend | 文件存储、路径生成、清理机制 |
| 3. 数据查询服务 | 40分钟 | Backend | 从metrics表查询真实监控数据 |
| 4. 修改执行逻辑 | 30分钟 | Backend | 在ExecuteReport中添加异步生成逻辑 |
| 5. 文件下载API | 20分钟 | Backend | 实现文件下载路由和权限验证 |
| 6. 前端下载功能 | 15分钟 | Frontend | 添加下载按钮和请求处理 |

**总计：约2.5小时**

### 阶段2：完整版本（优先级：中）
**目标：支持多种格式和高级功能**

| 功能 | 预估时间 | 描述 |
|------|----------|------|
| PDF生成器 | 2小时 | 使用go-pdf库生成PDF报表 |
| Excel生成器 | 1.5小时 | 使用excelize库生成Excel报表 |
| 异步任务队列 | 3小时 | 实现Redis/内存队列处理大量报表任务 |
| 进度跟踪 | 1小时 | WebSocket实时更新生成进度 |
| 文件清理机制 | 1小时 | 定时清理过期文件 |

### 阶段3：高级功能（优先级：低）
**目标：企业级功能**

| 功能 | 预估时间 | 描述 |
|------|----------|------|
| 报表模板自定义 | 4小时 | 可视化报表设计器 |
| 图表生成 | 3小时 | 集成图表库生成可视化报表 |
| 邮件发送 | 2小时 | 自动发送报表到指定邮箱 |
| 定时报表 | 2小时 | 支持定时生成和发送报表 |

## 🔧 技术选型

### 后端技术栈
| 组件 | 技术选择 | 版本 | 理由 |
|------|----------|------|------|
| CSV生成 | Go标准库 `encoding/csv` | - | 简单可靠，无额外依赖 |
| PDF生成 | `github.com/jung-kurt/gofpdf` | v2.17.2 | 轻量级，功能完整 |
| Excel生成 | `github.com/xuri/excelize` | v2.8.0 | 功能强大，性能优秀 |
| 文件存储 | 本地文件系统 | - | MVP版本足够，后续可扩展到云存储 |
| 异步处理 | Goroutine + Channel | - | 轻量级，易于实现和调试 |
| 数据查询 | 现有MetricRepository | - | 复用现有代码，保持一致性 |

### 前端技术栈
| 组件 | 技术选择 | 理由 |
|------|----------|------|
| 文件下载 | Axios + Blob API | 支持大文件下载和进度显示 |
| 进度显示 | React Progress Bar | 用户体验友好 |
| 状态管理 | React Query | 自动刷新执行状态 |

## 📊 核心接口设计

### 1. 报表生成器接口
```go
type ReportGenerator interface {
    Generate(data *ReportData, format string) (filePath string, err error)
    Validate(template *models.ReportTemplate) error
    GetSupportedFormats() []string
}

type ReportData struct {
    Template    *models.ReportTemplate      // 报表模板
    TimeRange   models.TimeRangeRequest     // 时间范围
    Metrics     []MetricData                // 监控数据
    Databases   []DatabaseInfo              // 数据库信息
    Parameters  map[string]interface{}      // 自定义参数
    UserInfo    *models.User                // 执行用户信息
}

type MetricData struct {
    DatabaseID   uint      `json:"database_id"`
    DatabaseName string    `json:"database_name"`
    MetricType   string    `json:"metric_type"`
    Value        float64   `json:"value"`
    Unit         string    `json:"unit"`
    Timestamp    time.Time `json:"timestamp"`
}
```

### 2. 文件管理器接口
```go
type FileManager interface {
    GenerateFilePath(executionID uint, format string) string
    EnsureDirectory(path string) error
    CleanupOldFiles(olderThan time.Duration) error
    GetFileInfo(filePath string) (*FileInfo, error)
}

type FileInfo struct {
    Path         string    `json:"path"`
    Size         int64     `json:"size"`
    CreatedAt    time.Time `json:"created_at"`
    IsAccessible bool      `json:"is_accessible"`
}
```

### 3. 数据查询服务接口
```go
type DataQueryService interface {
    QueryMetrics(req *MetricQueryRequest) ([]MetricData, error)
    QueryDatabases(databaseIDs []uint) ([]DatabaseInfo, error)
    AggregateData(data []MetricData, granularity string) ([]MetricData, error)
}

type MetricQueryRequest struct {
    DatabaseIDs []uint                 `json:"database_ids"`
    MetricTypes []string               `json:"metric_types"`
    TimeRange   models.TimeRangeRequest `json:"time_range"`
    Granularity string                 `json:"granularity"` // minute, hour, day
}
```

## 🔒 安全考虑

### 文件访问控制
1. **权限验证**：只有执行者和管理员可以下载报表
2. **文件路径验证**：防止路径遍历攻击
3. **文件大小限制**：防止生成过大文件占用磁盘空间
4. **访问日志**：记录文件下载行为

### 数据安全
1. **数据脱敏**：敏感数据在报表中脱敏处理
2. **访问控制**：基于用户角色限制数据访问范围
3. **审计日志**：记录报表生成和下载操作

## 📈 性能优化

### 数据查询优化
1. **索引优化**：确保metrics表有合适的时间和数据库ID索引
2. **分页查询**：大数据量时使用分页避免内存溢出
3. **缓存机制**：常用查询结果缓存

### 文件生成优化
1. **流式处理**：大数据量时使用流式写入
2. **并发控制**：限制同时生成的报表数量
3. **资源清理**：及时释放内存和文件句柄

## 🧪 测试策略

### 单元测试
- [ ] ReportGenerator各实现类测试
- [ ] FileManager功能测试
- [ ] DataQueryService查询测试

### 集成测试
- [ ] 端到端报表生成流程测试
- [ ] 文件下载功能测试
- [ ] 异步任务处理测试

### 性能测试
- [ ] 大数据量报表生成测试
- [ ] 并发报表生成测试
- [ ] 文件存储压力测试

## 📝 配置项

### 环境变量
```bash
# 报表存储配置
REPORT_STORAGE_PATH=/app/reports
REPORT_MAX_FILE_SIZE=100MB
REPORT_RETENTION_DAYS=30

# 并发控制
REPORT_MAX_CONCURRENT_JOBS=5
REPORT_GENERATION_TIMEOUT=300s

# 文件清理
REPORT_CLEANUP_INTERVAL=24h
REPORT_TEMP_CLEANUP_INTERVAL=1h
```

## 🚀 部署考虑

### 存储需求
- 预估每个报表文件大小：1-10MB
- 每日报表生成量：50-100个
- 存储保留期：30天
- **总存储需求**：约15-30GB

### 监控指标
- 报表生成成功率
- 平均生成时间
- 文件存储使用量
- 下载次数统计

---

## 📋 实施检查清单

### MVP版本完成标准
- [ ] 用户可以成功生成CSV格式报表
- [ ] 报表包含真实的监控数据
- [ ] 执行状态正确更新（pending → running → completed）
- [ ] 用户可以下载生成的文件
- [ ] 基本的错误处理和日志记录

### 验收测试用例
1. **正常流程**：选择模板 → 设置参数 → 执行 → 下载
2. **数据验证**：报表数据与数据库数据一致
3. **权限控制**：非执行者无法下载他人报表
4. **错误处理**：网络异常、磁盘满等异常情况处理
5. **性能测试**：大时间范围数据的报表生成

---

**文档版本**：v1.0  
**创建日期**：2025-01-20  
**最后更新**：2025-01-20  
**负责人**：开发团队
