# 数据库列表获取流程详解

## 📋 概述

本文档详细介绍了数据库监控平台中获取数据库列表功能的完整前后端实现流程，包括技术栈、代码实现、数据流向等。

## 🏗️ 技术架构

```
前端 (React + TypeScript)
    ↓ HTTP请求
后端 (Go + Gin)
    ↓ SQL查询
数据库 (PostgreSQL)
```

### 技术栈
- **前端**: React 18, TypeScript, React Query, Tailwind CSS
- **后端**: Go 1.21, Gin, GORM, JWT
- **数据库**: PostgreSQL
- **缓存**: React Query自动缓存

## 🔄 完整数据流程

### 1. 用户操作触发
```
用户访问页面 → React组件挂载 → 自动调用数据获取Hook
```

### 2. 前端数据获取
```
useDatabases() → React Query → API Service → HTTP请求
```

### 3. 后端处理
```
HTTP请求 → Gin路由 → JWT验证 → Handler → Service → Repository → 数据库查询
```

### 4. 数据返回
```
数据库结果 → Repository → Service → Handler → JSON响应 → 前端缓存 → 组件渲染
```

## 📱 前端实现

### 1. 数据获取Hook (React Query)

```typescript
// frontend/src/queries/index.ts
export const useDatabases = (
  params?: any,
  options?: UseQueryOptions<ApiResponse<PaginatedResponse<DatabaseInstance>>>
) => {
  return useQuery({
    queryKey: queryKeys.databases.list(params),
    queryFn: () => apiService.getDatabases(params),
    ...options,
  });
};
```

**功能特点**:
- 自动缓存API响应
- 自动重试失败请求
- 后台数据更新
- 统一错误处理

### 2. 组件使用

```typescript
// frontend/src/components/database/DatabaseManagement.tsx
export const DatabaseManagement: React.FC = ({ onDatabaseSelect }) => {
  // 使用React Query获取数据
  const { data: databasesResponse, isLoading, error, refetch } = useDatabases();
  
  // 解析数据结构
  const databases = (databasesResponse?.data?.data || []) as DatabaseInstance[];

  // 渲染逻辑
  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  
  return (
    <div className="space-y-6">
      {/* 头部操作栏 */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">数据库管理</h1>
        <button onClick={() => refetch()}>刷新</button>
      </div>
      
      {/* 数据库列表 */}
      <div className="grid gap-6">
        {databases.map(database => (
          <DatabaseCard key={database.id} database={database} />
        ))}
      </div>
    </div>
  );
};
```

### 3. API服务层

```typescript
// frontend/src/services/api.ts
class ApiService {
  async getDatabases(params?: any): Promise<ApiResponse<PaginatedResponse<DatabaseInstance>>> {
    const response = await api.get('/databases', { params });
    return response.data;
  }
}

export const apiService = new ApiService();
```

## 🔧 后端实现

### 1. HTTP处理器 (Gin Handler)

```go
// backend/go-backend/internal/handlers/database.go
func (h *DatabaseHandler) GetDatabases(c *gin.Context) {
    // 1. JWT身份验证
    currentUser, err := middleware.GetCurrentUser(c)
    if err != nil {
        c.JSON(http.StatusUnauthorized, models.UnauthorizedResponse())
        return
    }

    // 2. 解析分页参数
    pagination := parsePaginationParams(c)

    // 3. 调用业务逻辑层
    result, err := h.databaseService.GetDatabases(
        currentUser.ID, 
        currentUser.Role, 
        pagination
    )
    if err != nil {
        logger.Errorf("Failed to get databases: %v", err)
        c.JSON(http.StatusInternalServerError, 
            models.ErrorResponse(500, "Failed to get databases"))
        return
    }

    // 4. 返回成功响应
    c.JSON(http.StatusOK, models.SuccessResponse(result))
}
```

### 2. 业务逻辑层 (Service)

```go
// backend/go-backend/internal/services/database.go
func (s *DatabaseService) GetDatabases(userID uint, userRole string, pagination *models.PaginationRequest) (*models.PaginationResponse, error) {
    var databases []models.DatabaseInstance
    var total int64
    var err error

    // 权限控制：根据用户角色获取不同数据
    if userRole == "admin" {
        // 管理员可以查看所有数据库
        databases, total, err = s.databaseRepo.GetAll(pagination)
    } else {
        // 普通用户只能查看自己创建的数据库
        databases, total, err = s.databaseRepo.GetByUserID(userID, pagination)
    }

    if err != nil {
        logger.Errorf("Failed to get databases: %v", err)
        return nil, errors.New("failed to get databases")
    }

    // 数据转换：内部模型 → API响应格式
    var responses []models.DatabaseResponse
    for _, db := range databases {
        response := db.ToResponse()
        if db.User.ID > 0 {
            response.User = &db.User
        }
        responses = append(responses, *response)
    }

    // 构建分页响应
    return &models.PaginationResponse{
        Items:      responses,
        Total:      total,
        Page:       pagination.Page,
        PageSize:   pagination.PageSize,
        TotalPages: int((total + int64(pagination.PageSize) - 1) / int64(pagination.PageSize)),
    }, nil
}
```

### 3. 数据访问层 (Repository)

```go
// backend/go-backend/internal/repository/database.go
func (r *databaseRepository) GetAll(pagination *models.PaginationRequest) ([]models.DatabaseInstance, int64, error) {
    var databases []models.DatabaseInstance
    var total int64

    // 获取总数
    if err := r.db.Model(&models.DatabaseInstance{}).Count(&total).Error; err != nil {
        return nil, 0, err
    }

    // 分页查询
    offset := pagination.CalculateOffset()
    err := r.db.Preload("User").  // 预加载关联用户信息
        Offset(offset).
        Limit(pagination.PageSize).
        Order("created_at DESC").  // 按创建时间倒序
        Find(&databases).Error

    return databases, total, err
}

func (r *databaseRepository) GetByUserID(userID uint, pagination *models.PaginationRequest) ([]models.DatabaseInstance, int64, error) {
    var databases []models.DatabaseInstance
    var total int64

    query := r.db.Model(&models.DatabaseInstance{}).Where("created_by = ?", userID)

    // 获取总数
    if err := query.Count(&total).Error; err != nil {
        return nil, 0, err
    }

    // 分页查询
    offset := pagination.CalculateOffset()
    err := query.Preload("User").
        Offset(offset).
        Limit(pagination.PageSize).
        Order("created_at DESC").
        Find(&databases).Error

    return databases, total, err
}
```

## 🗄️ 数据模型

### 1. 数据库实例模型

```go
// backend/go-backend/internal/models/database.go
type DatabaseInstance struct {
    ID           uint           `json:"id" gorm:"primaryKey"`
    Name         string         `json:"name" gorm:"not null;size:100"`
    Type         string         `json:"type" gorm:"not null;size:20"`
    Host         string         `json:"host" gorm:"not null;size:255"`
    Port         int            `json:"port" gorm:"not null"`
    DatabaseName string         `json:"database_name" gorm:"size:100"`
    Username     string         `json:"username" gorm:"size:100"`
    Password     string         `json:"-" gorm:"size:255"` // 不返回给前端
    Status       string         `json:"status" gorm:"default:active;size:20"`
    Description  string         `json:"description" gorm:"size:500"`
    IsMonitored  bool           `json:"is_monitored" gorm:"default:true"`
    CreatedBy    uint           `json:"created_by" gorm:"not null"`
    CreatedAt    time.Time      `json:"created_at"`
    UpdatedAt    time.Time      `json:"updated_at"`
    DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

    // 关联关系
    User        User         `json:"user,omitempty" gorm:"foreignKey:CreatedBy"`
    Metrics     []Metric     `json:"metrics,omitempty" gorm:"foreignKey:DatabaseID"`
    AlertRules  []AlertRule  `json:"alert_rules,omitempty" gorm:"foreignKey:DatabaseID"`
}
```

### 2. 分页模型

```go
// backend/go-backend/internal/models/pagination.go
type PaginationRequest struct {
    Page     int `json:"page" form:"page" binding:"min=1"`
    PageSize int `json:"page_size" form:"page_size" binding:"min=1,max=100"`
}

type PaginationResponse struct {
    Items      interface{} `json:"items"`
    Total      int64       `json:"total"`
    Page       int         `json:"page"`
    PageSize   int         `json:"page_size"`
    TotalPages int         `json:"total_pages"`
}
```

## 🔐 权限控制

### JWT认证流程
1. 用户登录获取JWT token
2. 前端在请求头中携带token
3. 后端中间件验证token
4. 提取用户信息用于权限判断

### 权限规则
- **管理员**: 可以查看所有数据库实例
- **普通用户**: 只能查看自己创建的数据库实例

## 📊 API接口规范

### 请求
```http
GET /api/v1/databases?page=1&page_size=20
Authorization: Bearer <jwt_token>
```

### 响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [
      {
        "id": 1,
        "name": "生产环境MySQL",
        "type": "mysql",
        "host": "*************",
        "port": 3306,
        "status": "active",
        "description": "主要业务数据库",
        "is_monitored": true,
        "created_at": "2024-01-01T10:00:00Z",
        "user": {
          "id": 1,
          "name": "管理员",
          "email": "<EMAIL>"
        }
      }
    ],
    "total": 50,
    "page": 1,
    "page_size": 20,
    "total_pages": 3
  }
}
```

## 🚀 性能优化

### 前端优化
1. **React Query缓存**: 避免重复请求
2. **分页加载**: 减少单次数据量
3. **虚拟滚动**: 处理大量数据时使用

### 后端优化
1. **数据库索引**: 在查询字段上建立索引
2. **预加载**: 使用GORM的Preload减少N+1查询
3. **分页查询**: 使用OFFSET和LIMIT控制数据量

### 数据库优化
```sql
-- 创建索引提升查询性能
CREATE INDEX idx_database_instances_created_by ON database_instances(created_by);
CREATE INDEX idx_database_instances_created_at ON database_instances(created_at);
CREATE INDEX idx_database_instances_status ON database_instances(status);
```

## 🔍 错误处理

### 前端错误处理
- React Query自动重试失败请求
- 统一错误提示组件
- 网络错误时显示友好提示

### 后端错误处理
- 统一错误响应格式
- 详细的错误日志记录
- 不同错误类型的HTTP状态码

## 🧪 测试验证

### 前端测试
```typescript
// 在浏览器F12控制台中测试
const token = localStorage.getItem('access_token');
console.log('Token:', token);

// 手动测试API
fetch('/api/v1/databases', {
    headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    }
})
.then(res => res.json())
.then(data => console.log('数据库列表:', data));
```

### 后端测试
```bash
# 使用curl测试API
curl -X GET "http://localhost:8080/api/v1/databases?page=1&page_size=10" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json"
```

## 🐛 常见问题

### 1. 前端问题

**问题**: `useDatabases()` 返回空数据
```typescript
// 检查数据结构
const { data } = useDatabases();
console.log('完整响应:', data);
console.log('数据库列表:', data?.data?.data);
```

**解决**: 确认API响应格式和数据解析路径

**问题**: React Query缓存过期
```typescript
// 手动刷新数据
const { refetch } = useDatabases();
refetch();

// 或者清除缓存
queryClient.invalidateQueries(['databases']);
```

### 2. 后端问题

**问题**: JWT认证失败
```go
// 检查token格式
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**问题**: 权限不足
```go
// 检查用户角色
if userRole != "admin" && database.CreatedBy != userID {
    return nil, errors.New("access denied")
}
```

### 3. 数据库问题

**问题**: 查询性能慢
```sql
-- 检查是否有索引
EXPLAIN ANALYZE SELECT * FROM database_instances WHERE created_by = 1;

-- 创建缺失的索引
CREATE INDEX IF NOT EXISTS idx_database_instances_created_by
ON database_instances(created_by);
```

## 🔧 开发调试

### 1. 前端调试
```typescript
// 开启React Query开发工具
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

function App() {
  return (
    <>
      <YourApp />
      <ReactQueryDevtools initialIsOpen={false} />
    </>
  );
}
```

### 2. 后端调试
```go
// 添加详细日志
logger.Infof("Getting databases for user %d with role %s", userID, userRole)
logger.Debugf("Pagination: page=%d, size=%d", pagination.Page, pagination.PageSize)
logger.Debugf("Found %d databases, total=%d", len(databases), total)
```

### 3. 网络调试
- 使用浏览器开发者工具的Network标签
- 检查请求头、响应状态码、响应时间
- 验证JWT token是否正确传递

## 📚 相关文档

- [React Query官方文档](https://tanstack.com/query/latest)
- [Gin框架文档](https://gin-gonic.com/docs/)
- [GORM文档](https://gorm.io/docs/)
- [JWT认证指南](./jwt-authentication-guide.md)
- [前端学习指南](./frontend-learning-guide.md)

## 📝 总结

这个数据库列表获取功能展示了现代Web应用的标准架构：

1. **前端**: 使用React Query简化数据获取和状态管理
2. **后端**: 采用分层架构，职责清晰
3. **安全**: JWT认证和基于角色的权限控制
4. **性能**: 缓存、分页、索引等优化措施
5. **可维护**: 代码结构清晰，易于扩展

通过这个流程，我们实现了一个高效、安全、用户友好的数据库管理界面。

### 关键学习点

1. **React Query的威力**: 一行代码搞定复杂的数据获取逻辑
2. **解构赋值语法**: 让代码更简洁易读
3. **分层架构**: Handler → Service → Repository 的清晰职责分工
4. **权限控制**: 基于JWT和用户角色的安全机制
5. **性能优化**: 从前端缓存到数据库索引的全方位优化

这个文档可以作为团队开发的参考标准，确保代码质量和架构一致性。
