# 报表生成API规范文档

## 📋 概述

本文档定义了报表生成功能的API接口规范，包括现有API的扩展和新增的文件下载API。

## 🔄 现有API扩展

### 1. 执行报表生成 (已存在，需扩展)

**接口**: `POST /api/v1/reports/execute`

#### 请求参数
```json
{
  "template_id": 1,
  "format": "csv",                    // 支持: csv, pdf, excel, json
  "time_range": {
    "start_time": "2025-01-13T00:00:00Z",
    "end_time": "2025-01-20T00:00:00Z"
  },
  "parameters": {
    "granularity": "hour",            // minute, hour, day
    "include_charts": false,          // 是否包含图表
    "data_aggregation": "avg"         // avg, sum, max, min
  },
  "database_ids": [1, 2, 3]          // 可选，指定数据库
}
```

#### 响应格式
```json
{
  "code": 201,
  "message": "success",
  "data": {
    "id": 123,
    "template_id": 1,
    "executed_by": 7,
    "status": "pending",              // pending, running, completed, failed
    "parameters": "{\"granularity\":\"hour\"}",
    "file_path": "",                  // 生成后填充
    "file_size": 0,                   // 生成后填充
    "error_message": "",
    "start_time": "2025-01-20T10:40:43Z",
    "end_time": null,
    "duration": 0,                    // 毫秒
    "created_at": "2025-01-20T10:40:43Z",
    "template": {
      "id": 1,
      "name": "前端测试报表",
      "description": "用于前端集成测试的报表模板",
      "type": "performance"
    },
    "executor": {
      "id": 7,
      "name": "Administrator",
      "email": "<EMAIL>"
    }
  }
}
```

### 2. 获取执行状态 (已存在，响应扩展)

**接口**: `GET /api/v1/reports/executions/{id}`

#### 响应格式扩展
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 123,
    "template_id": 1,
    "executed_by": 7,
    "status": "completed",
    "parameters": "{\"granularity\":\"hour\"}",
    "file_path": "/reports/2025/01/report_123_20250120_104043.csv",
    "file_size": 2048576,             // 字节
    "error_message": "",
    "start_time": "2025-01-20T10:40:43Z",
    "end_time": "2025-01-20T10:41:15Z",
    "duration": 32000,                // 32秒
    "created_at": "2025-01-20T10:40:43Z",
    "template": { /* ... */ },
    "executor": { /* ... */ },
    "download_url": "/api/v1/reports/executions/123/download",  // 新增
    "is_downloadable": true           // 新增
  }
}
```

## 🆕 新增API接口

### 1. 下载报表文件

**接口**: `GET /api/v1/reports/executions/{id}/download`

#### 请求参数
- **路径参数**: `id` (integer) - 执行记录ID
- **请求头**: `Authorization: Bearer {token}`

#### 响应格式
**成功响应 (200)**:
```
Content-Type: application/octet-stream
Content-Disposition: attachment; filename="report_123_20250120_104043.csv"
Content-Length: 2048576

[文件二进制内容]
```

**错误响应**:
```json
{
  "code": 404,
  "message": "Report file not found",
  "data": null
}
```

#### 错误码说明
| 状态码 | 错误码 | 说明 |
|--------|--------|------|
| 400 | 400 | 无效的执行ID |
| 403 | 403 | 无权限下载此报表 |
| 404 | 404 | 执行记录不存在 |
| 404 | 404 | 报表文件不存在 |
| 410 | 410 | 报表文件已过期 |
| 500 | 500 | 文件读取失败 |

### 2. 获取报表生成进度 (可选)

**接口**: `GET /api/v1/reports/executions/{id}/progress`

#### 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "execution_id": 123,
    "status": "running",
    "progress": 65,                   // 百分比 0-100
    "current_step": "generating_file", // querying_data, processing_data, generating_file
    "estimated_remaining": 15,        // 预计剩余秒数
    "processed_records": 6500,
    "total_records": 10000
  }
}
```

### 3. 取消报表生成 (可选)

**接口**: `POST /api/v1/reports/executions/{id}/cancel`

#### 响应格式
```json
{
  "code": 200,
  "message": "Report generation cancelled successfully",
  "data": {
    "execution_id": 123,
    "status": "cancelled",
    "cancelled_at": "2025-01-20T10:42:30Z"
  }
}
```

## 📊 数据模型扩展

### ReportExecution 模型扩展
```go
type ReportExecution struct {
    ID           uint       `json:"id" gorm:"primaryKey"`
    TemplateID   uint       `json:"template_id"`
    ExecutedBy   uint       `json:"executed_by"`
    Status       string     `json:"status"`        // pending, running, completed, failed, cancelled
    Parameters   string     `json:"parameters"`
    FilePath     string     `json:"file_path"`     // 新增：文件存储路径
    FileSize     int64      `json:"file_size"`     // 新增：文件大小(字节)
    ErrorMessage string     `json:"error_message"`
    StartTime    time.Time  `json:"start_time"`
    EndTime      *time.Time `json:"end_time"`
    Duration     int64      `json:"duration"`      // 新增：执行时长(毫秒)
    CreatedAt    time.Time  `json:"created_at"`
    UpdatedAt    time.Time  `json:"updated_at"`
    
    // 关联关系
    Template     ReportTemplate `json:"template"`
    Executor     User          `json:"executor"`
    
    // 计算字段
    DownloadURL     string `json:"download_url" gorm:"-"`
    IsDownloadable  bool   `json:"is_downloadable" gorm:"-"`
}
```

### 新增状态常量
```go
const (
    ExecutionStatusPending   = "pending"    // 等待执行
    ExecutionStatusRunning   = "running"    // 执行中
    ExecutionStatusCompleted = "completed"  // 已完成
    ExecutionStatusFailed    = "failed"     // 执行失败
    ExecutionStatusCancelled = "cancelled"  // 已取消
)
```

## 🔒 权限控制

### 下载权限规则
1. **执行者权限**: 用户只能下载自己执行的报表
2. **管理员权限**: 管理员可以下载所有报表
3. **文件有效期**: 报表文件保留30天，过期后无法下载
4. **访问日志**: 记录所有下载行为

### 权限验证流程
```go
func validateDownloadPermission(execution *ReportExecution, userID uint, userRole string) error {
    // 1. 检查执行记录是否存在
    // 2. 检查用户权限（执行者或管理员）
    // 3. 检查文件是否存在
    // 4. 检查文件是否过期
    // 5. 记录访问日志
}
```

## 📈 性能考虑

### 文件下载优化
1. **流式传输**: 大文件使用流式传输，避免内存占用
2. **断点续传**: 支持HTTP Range请求
3. **缓存控制**: 设置适当的缓存头
4. **并发限制**: 限制同时下载的文件数量

### 响应头设置
```go
// 文件下载响应头
c.Header("Content-Type", "application/octet-stream")
c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
c.Header("Content-Length", strconv.FormatInt(fileSize, 10))
c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
c.Header("Pragma", "no-cache")
c.Header("Expires", "0")
```

## 🧪 API测试用例

### 1. 报表执行测试
```bash
# 执行报表生成
curl -X POST http://localhost:8080/api/v1/reports/execute \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "template_id": 1,
    "format": "csv",
    "time_range": {
      "start_time": "2025-01-13T00:00:00Z",
      "end_time": "2025-01-20T00:00:00Z"
    },
    "parameters": {}
  }'
```

### 2. 状态查询测试
```bash
# 查询执行状态
curl -X GET http://localhost:8080/api/v1/reports/executions/123 \
  -H "Authorization: Bearer {token}"
```

### 3. 文件下载测试
```bash
# 下载报表文件
curl -X GET http://localhost:8080/api/v1/reports/executions/123/download \
  -H "Authorization: Bearer {token}" \
  -o report.csv
```

## 📝 错误处理规范

### 标准错误响应格式
```json
{
  "code": 400,
  "message": "Invalid request parameters",
  "data": {
    "error_type": "validation_error",
    "details": [
      {
        "field": "template_id",
        "message": "Template ID is required"
      }
    ]
  }
}
```

### 常见错误场景
1. **模板不存在**: 404 - Template not found
2. **权限不足**: 403 - Permission denied
3. **文件生成失败**: 500 - Report generation failed
4. **文件不存在**: 404 - Report file not found
5. **文件已过期**: 410 - Report file expired

## 🔄 版本兼容性

### API版本控制
- 当前版本: `v1`
- 向后兼容: 现有API保持兼容
- 新增字段: 使用可选字段，不影响现有客户端

### 迁移指南
1. 现有的报表执行API无需修改
2. 新增的下载功能为可选功能
3. 前端可以渐进式升级下载功能

---

**API版本**: v1.1  
**文档版本**: 1.0  
**最后更新**: 2025-01-20
