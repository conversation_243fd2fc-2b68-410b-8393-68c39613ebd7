# 报表生成功能实现指南

## 🎯 实现目标

将当前只有管理功能的报表系统，扩展为具备完整报表生成能力的系统。用户执行报表后能够获得真实的、可下载的报表文件。

## 📁 文件结构规划

### 新增文件
```
backend/go-backend/
├── internal/
│   ├── services/
│   │   ├── report_generator.go      # 报表生成器服务
│   │   └── data_query.go           # 数据查询服务
│   ├── utils/
│   │   └── file_manager.go         # 文件管理工具
│   └── handlers/
│       └── download.go             # 文件下载处理器
├── reports/                        # 报表文件存储目录
│   ├── 2025/
│   └── temp/
└── configs/
    └── report_config.go            # 报表配置
```

### 修改文件
```
backend/go-backend/
├── internal/
│   ├── services/report.go          # 修改ExecuteReport方法
│   └── handlers/report.go          # 添加下载路由
└── cmd/server/main.go              # 添加报表目录初始化
```

## 🔧 实现步骤详解

### 步骤1：创建报表生成器框架 (30分钟)

#### 1.1 创建报表生成器接口
```go
// internal/services/report_generator.go
package services

type ReportGenerator interface {
    Generate(data *ReportData, format string) (filePath string, err error)
    Validate(template *models.ReportTemplate) error
    GetSupportedFormats() []string
}

type ReportData struct {
    Template    *models.ReportTemplate
    TimeRange   models.TimeRangeRequest
    Metrics     []MetricData
    Databases   []DatabaseInfo
    Parameters  map[string]interface{}
    UserInfo    *models.User
}
```

#### 1.2 实现CSV生成器
```go
type CSVGenerator struct {
    fileManager FileManager
}

func (g *CSVGenerator) Generate(data *ReportData, format string) (string, error) {
    // 1. 生成文件路径
    // 2. 创建CSV文件
    // 3. 写入表头
    // 4. 写入数据行
    // 5. 返回文件路径
}
```

#### 1.3 创建数据结构
```go
type MetricData struct {
    DatabaseID   uint      `json:"database_id"`
    DatabaseName string    `json:"database_name"`
    MetricType   string    `json:"metric_type"`
    Value        float64   `json:"value"`
    Unit         string    `json:"unit"`
    Timestamp    time.Time `json:"timestamp"`
}
```

### 步骤2：实现文件管理工具 (20分钟)

#### 2.1 创建文件管理器
```go
// internal/utils/file_manager.go
package utils

type FileManager interface {
    GenerateFilePath(executionID uint, format string) string
    EnsureDirectory(path string) error
    CleanupOldFiles(olderThan time.Duration) error
}

type fileManager struct {
    basePath string
}

func (fm *fileManager) GenerateFilePath(executionID uint, format string) string {
    now := time.Now()
    year := now.Format("2006")
    month := now.Format("01")
    timestamp := now.Format("20060102_150405")
    
    filename := fmt.Sprintf("report_%d_%s.%s", executionID, timestamp, format)
    return filepath.Join(fm.basePath, year, month, filename)
}
```

#### 2.2 目录结构管理
```go
func (fm *fileManager) EnsureDirectory(path string) error {
    dir := filepath.Dir(path)
    return os.MkdirAll(dir, 0755)
}
```

### 步骤3：实现数据查询服务 (40分钟)

#### 3.1 创建数据查询服务
```go
// internal/services/data_query.go
package services

type DataQueryService interface {
    QueryMetrics(req *MetricQueryRequest) ([]MetricData, error)
    QueryDatabases(databaseIDs []uint) ([]DatabaseInfo, error)
}

type dataQueryService struct {
    metricRepo   repository.MetricRepository
    databaseRepo repository.DatabaseRepository
}
```

#### 3.2 实现指标数据查询
```go
func (s *dataQueryService) QueryMetrics(req *MetricQueryRequest) ([]MetricData, error) {
    // 1. 构建查询条件
    // 2. 从metrics表查询数据
    // 3. 数据格式转换
    // 4. 按时间聚合（如果需要）
    // 5. 返回结构化数据
}
```

#### 3.3 处理空数据情况
```go
func (s *dataQueryService) generateMockData(req *MetricQueryRequest) []MetricData {
    // 如果数据库中没有足够数据，生成模拟数据用于演示
}
```

### 步骤4：修改报表执行逻辑 (30分钟)

#### 4.1 修改ExecuteReport方法
```go
// internal/services/report.go
func (s *reportService) ExecuteReport(ctx context.Context, req *models.ExecuteReportRequest, userID uint) (*models.ReportExecution, error) {
    // ... 现有验证逻辑 ...
    
    // 创建执行记录
    execution := &models.ReportExecution{
        TemplateID:  req.TemplateID,
        ExecutedBy:  userID,
        Status:      models.ExecutionStatusPending,
        Parameters:  string(parametersJSON),
        StartTime:   time.Now(),
    }
    
    if err := s.executionRepo.Create(ctx, execution); err != nil {
        return nil, fmt.Errorf("failed to create execution: %w", err)
    }
    
    // 启动异步报表生成任务
    go s.generateReportAsync(execution.ID, req, userID)
    
    return s.executionRepo.GetByID(ctx, execution.ID)
}
```

#### 4.2 实现异步生成逻辑
```go
func (s *reportService) generateReportAsync(executionID uint, req *models.ExecuteReportRequest, userID uint) {
    // 1. 更新状态为running
    // 2. 查询数据
    // 3. 生成报表文件
    // 4. 更新执行记录（文件路径、大小、状态）
    // 5. 错误处理
}
```

### 步骤5：实现文件下载API (20分钟)

#### 5.1 添加下载路由
```go
// internal/handlers/report.go
// @Summary 下载报表文件
// @Description 下载已生成的报表文件
// @Tags Reports
// @Param id path int true "执行ID"
// @Success 200 {file} file "报表文件"
// @Failure 404 {object} models.CommonResponse
// @Router /api/v1/reports/executions/{id}/download [get]
// @Security BearerAuth
func (h *ReportHandler) DownloadReport(c *gin.Context) {
    // 1. 验证执行ID
    // 2. 检查权限
    // 3. 验证文件存在
    // 4. 设置响应头
    // 5. 返回文件内容
}
```

#### 5.2 权限验证
```go
func (h *ReportHandler) validateDownloadPermission(execution *models.ReportExecution, userID uint) error {
    // 只有执行者和管理员可以下载
}
```

### 步骤6：前端下载功能 (15分钟)

#### 6.1 添加下载按钮
```tsx
// frontend/src/components/reports/ReportGenerator.tsx
const DownloadButton: React.FC<{execution: ReportExecution}> = ({execution}) => {
  const handleDownload = async () => {
    try {
      const response = await api.get(`/reports/executions/${execution.id}/download`, {
        responseType: 'blob'
      });
      
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.download = `report_${execution.id}.${getFileExtension(execution.format)}`;
      link.click();
    } catch (error) {
      console.error('下载失败:', error);
    }
  };
  
  return (
    <button onClick={handleDownload} disabled={execution.status !== 'completed'}>
      下载报表
    </button>
  );
};
```

## 🔍 关键实现细节

### 错误处理策略
```go
func (s *reportService) handleGenerationError(executionID uint, err error) {
    // 1. 记录错误日志
    // 2. 更新执行记录状态为failed
    // 3. 保存错误信息
    // 4. 清理临时文件
}
```

### 文件路径安全
```go
func validateFilePath(filePath string) error {
    // 1. 检查路径遍历攻击
    // 2. 验证文件扩展名
    // 3. 检查文件大小
    return nil
}
```

### 并发控制
```go
var (
    reportGenerationSemaphore = make(chan struct{}, 5) // 最多5个并发任务
)

func (s *reportService) generateReportAsync(executionID uint, req *models.ExecuteReportRequest, userID uint) {
    reportGenerationSemaphore <- struct{}{} // 获取信号量
    defer func() { <-reportGenerationSemaphore }() // 释放信号量
    
    // 生成逻辑...
}
```

## 📊 数据库变更

### 执行记录表更新
```sql
-- 添加文件相关字段（如果还没有）
ALTER TABLE report_executions 
ADD COLUMN file_path VARCHAR(500) COMMENT '生成文件路径',
ADD COLUMN file_size BIGINT DEFAULT 0 COMMENT '文件大小(字节)',
ADD COLUMN duration BIGINT DEFAULT 0 COMMENT '执行时长(毫秒)';
```

## 🧪 测试用例

### 单元测试
```go
func TestCSVGenerator_Generate(t *testing.T) {
    // 测试CSV生成功能
}

func TestFileManager_GenerateFilePath(t *testing.T) {
    // 测试文件路径生成
}

func TestDataQueryService_QueryMetrics(t *testing.T) {
    // 测试数据查询
}
```

### 集成测试
```go
func TestReportGeneration_EndToEnd(t *testing.T) {
    // 1. 创建测试模板
    // 2. 执行报表生成
    // 3. 验证文件生成
    // 4. 测试文件下载
    // 5. 清理测试数据
}
```

## 🚀 部署配置

### 环境变量
```bash
# .env
REPORT_STORAGE_PATH=/app/reports
REPORT_MAX_FILE_SIZE=100MB
REPORT_RETENTION_DAYS=30
REPORT_MAX_CONCURRENT_JOBS=5
```

### Docker配置
```dockerfile
# 确保报表目录存在
RUN mkdir -p /app/reports && chmod 755 /app/reports

# 挂载卷
VOLUME ["/app/reports"]
```

## ✅ 验收标准

### 功能验收
- [ ] 用户可以成功执行报表生成
- [ ] 执行状态正确更新（pending → running → completed）
- [ ] 生成的CSV文件包含正确的数据
- [ ] 用户可以下载生成的文件
- [ ] 权限控制正常工作

### 性能验收
- [ ] 单个报表生成时间 < 30秒（正常数据量）
- [ ] 支持并发生成多个报表
- [ ] 文件下载速度正常

### 安全验收
- [ ] 文件路径安全验证
- [ ] 下载权限控制
- [ ] 错误信息不泄露敏感数据

---

**实现优先级**：MVP版本 → 完整版本 → 高级功能  
**预计完成时间**：2.5小时（MVP版本）  
**负责人**：后端开发团队
