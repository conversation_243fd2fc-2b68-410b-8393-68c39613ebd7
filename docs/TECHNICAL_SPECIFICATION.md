# 技术规格说明书

## 📋 系统概述

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (React)   │    │   后端 (Go)      │    │  数据库 (PG)     │
│                 │    │                 │    │                 │
│ - React 18      │◄──►│ - Gin Framework │◄──►│ - PostgreSQL    │
│ - TypeScript    │    │ - GORM          │    │ - Redis (缓存)   │
│ - Tailwind CSS  │    │ - Gorilla WS    │    │                 │
│ - React Query   │    │ - Go Routines   │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心功能模块
1. **用户认证模块**: 注册、登录、权限管理
2. **数据库监控模块**: 实时指标收集和展示
3. **告警管理模块**: 规则配置、告警发送
4. **数据分析模块**: 历史数据查询、报告生成
5. **系统管理模块**: 配置管理、用户管理

## 🛠️ 技术栈详细说明

### 前端技术栈

#### 核心框架
- **React 18.2+**: 使用最新的并发特性和Hooks
- **TypeScript 5.0+**: 严格类型检查，提升代码质量
- **Vite 5.0+**: 快速构建工具，支持HMR

#### UI框架和样式
- **Tailwind CSS 3.4+**: 原子化CSS框架
- **Headless UI**: 无样式组件库
- **Heroicons**: 图标库
- **Recharts**: 图表库

#### 状态管理
- **React Query (TanStack Query)**: 服务端状态管理
- **Zustand**: 客户端状态管理
- **React Hook Form**: 表单状态管理

#### 工具库
- **Axios**: HTTP客户端
- **Socket.io-client**: WebSocket客户端
- **date-fns**: 日期处理
- **zod**: 数据验证

### 后端技术栈

#### 核心框架
- **Go 1.21+**: 编程语言
- **Gin 1.9+**: Web框架
- **GORM 1.25+**: ORM框架

#### 数据库和缓存
- **PostgreSQL 14+**: 主数据库
- **Redis 7.0+**: 缓存和会话存储
- **go-redis**: Redis客户端

#### 认证和安全
- **golang-jwt**: JWT token生成和验证
- **bcrypt**: 密码加密
- **gin-cors**: 跨域资源共享
- **validator**: 数据验证

#### 实时通信
- **Gorilla WebSocket**: WebSocket服务
- **Go Routines**: 并发处理

#### 工具库
- **Viper**: 配置管理
- **Logrus/Zap**: 日志记录
- **Testify**: 测试框架
- **Air**: 热重载开发工具

### 开发工具

#### 代码质量
- **ESLint**: 代码检查
- **Prettier**: 代码格式化
- **Husky**: Git hooks
- **lint-staged**: 暂存文件检查

#### 测试框架
- **Testify**: Go单元测试
- **Gin Test**: API测试
- **Playwright**: E2E测试
- **Testing Library**: React组件测试

#### 构建和部署
- **Docker**: 容器化
- **GitHub Actions**: CI/CD
- **Nginx**: 反向代理
- **PM2**: 进程管理

## 🗄️ 数据库设计

### 核心表结构

#### 用户表 (users)
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(100) NOT NULL,
  role VARCHAR(20) DEFAULT 'user',
  avatar_url VARCHAR(500),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 数据库实例表 (database_instances)
```sql
CREATE TABLE database_instances (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  type VARCHAR(20) NOT NULL, -- mysql, postgresql, mongodb
  host VARCHAR(255) NOT NULL,
  port INTEGER NOT NULL,
  database_name VARCHAR(100),
  username VARCHAR(100),
  password_encrypted TEXT,
  status VARCHAR(20) DEFAULT 'active',
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 监控指标表 (metrics)
```sql
CREATE TABLE metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  database_id UUID REFERENCES database_instances(id),
  metric_type VARCHAR(50) NOT NULL, -- cpu, memory, connections, qps
  value DECIMAL(10,2) NOT NULL,
  unit VARCHAR(20),
  timestamp TIMESTAMP DEFAULT NOW(),
  INDEX idx_database_timestamp (database_id, timestamp),
  INDEX idx_metric_type_timestamp (metric_type, timestamp)
);
```

#### 告警规则表 (alert_rules)
```sql
CREATE TABLE alert_rules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  database_id UUID REFERENCES database_instances(id),
  metric_type VARCHAR(50) NOT NULL,
  operator VARCHAR(10) NOT NULL, -- >, <, >=, <=, =
  threshold DECIMAL(10,2) NOT NULL,
  severity VARCHAR(20) DEFAULT 'warning', -- info, warning, error, critical
  enabled BOOLEAN DEFAULT true,
  notification_channels JSONB, -- email, sms, webhook
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 告警历史表 (alert_history)
```sql
CREATE TABLE alert_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  rule_id UUID REFERENCES alert_rules(id),
  database_id UUID REFERENCES database_instances(id),
  metric_value DECIMAL(10,2) NOT NULL,
  threshold DECIMAL(10,2) NOT NULL,
  severity VARCHAR(20) NOT NULL,
  status VARCHAR(20) DEFAULT 'active', -- active, resolved, ignored
  message TEXT,
  resolved_at TIMESTAMP,
  resolved_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 索引策略
- 时间序列数据按时间分区
- 查询频繁的字段添加复合索引
- 使用部分索引优化特定查询

## 🔌 API设计

### RESTful API规范

#### 认证相关
```
POST   /api/auth/register     # 用户注册
POST   /api/auth/login        # 用户登录
POST   /api/auth/logout       # 用户登出
GET    /api/auth/profile      # 获取用户信息
PUT    /api/auth/profile      # 更新用户信息
```

#### 数据库实例管理
```
GET    /api/databases         # 获取数据库列表
POST   /api/databases         # 创建数据库实例
GET    /api/databases/:id     # 获取数据库详情
PUT    /api/databases/:id     # 更新数据库实例
DELETE /api/databases/:id     # 删除数据库实例
POST   /api/databases/:id/test # 测试数据库连接
```

#### 监控数据
```
GET    /api/metrics/:db_id    # 获取监控指标
GET    /api/metrics/:db_id/realtime # 获取实时数据
GET    /api/metrics/:db_id/history  # 获取历史数据
```

#### 告警管理
```
GET    /api/alerts            # 获取告警列表
POST   /api/alerts            # 创建告警规则
PUT    /api/alerts/:id        # 更新告警规则
DELETE /api/alerts/:id        # 删除告警规则
GET    /api/alerts/history    # 获取告警历史
POST   /api/alerts/:id/resolve # 处理告警
```

### WebSocket事件

#### 客户端 → 服务端
```javascript
// 订阅数据库监控数据
socket.emit('subscribe', { databaseId: 'uuid' });

// 取消订阅
socket.emit('unsubscribe', { databaseId: 'uuid' });
```

#### 服务端 → 客户端
```javascript
// 实时监控数据推送
socket.emit('metrics:update', {
  databaseId: 'uuid',
  metrics: { cpu: 45, memory: 68, connections: 127 }
});

// 告警通知
socket.emit('alert:new', {
  id: 'uuid',
  severity: 'warning',
  message: 'CPU使用率过高'
});
```

## 🔒 安全设计

### 认证和授权
- JWT token认证，有效期24小时
- Refresh token机制，有效期7天
- 基于角色的访问控制 (RBAC)
- API接口权限验证

### 数据安全
- 敏感数据加密存储 (AES-256)
- 数据库连接密码加密
- HTTPS强制使用
- SQL注入防护

### 安全头设置
```javascript
// helmet配置
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true
  }
}));
```

## 📊 性能优化

### 前端优化
- 代码分割和懒加载
- 图片压缩和WebP格式
- CDN静态资源加速
- Service Worker缓存

### 后端优化
- Redis缓存热点数据
- 数据库连接池
- API响应压缩
- 分页查询优化

### 数据库优化
- 索引优化
- 查询优化
- 连接池配置
- 读写分离 (可选)

## 🚀 部署架构

### 开发环境
```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
  
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=development
    volumes:
      - ./backend:/app
  
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: dbmonitor
      POSTGRES_USER: dev
      POSTGRES_PASSWORD: dev123
    ports:
      - "5432:5432"
  
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
```

### 生产环境
- Nginx反向代理
- PM2进程管理
- 数据库主从复制
- 自动备份策略

## 📈 监控和日志

### 应用监控
- 性能指标收集 (响应时间、吞吐量)
- 错误率监控
- 资源使用监控
- 用户行为分析

### 日志管理
- 结构化日志格式
- 日志级别分类
- 日志轮转和归档
- 集中化日志收集

---

*文档版本: v1.0*  
*最后更新: 2024-12-28*
