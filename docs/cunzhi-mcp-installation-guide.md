# 寸止 MCP 工具安装使用指南

> **文档版本**: v1.0  
> **创建时间**: 2025-01-13  
> **适用环境**: WSL (Windows Subsystem for Linux)  
> **项目**: 数据库监控平台

## 📋 概述

寸止（cunzhi）是一个智能代码审查工具，支持通过 MCP (Model Context Protocol) 协议与 Augment 集成。本文档记录了在 WSL 环境中安装和配置寸止 MCP 工具的完整过程。

## 🛠️ 安装过程

### 1. WSL 环境准备和依赖安装

#### 检查 WSL 环境
```bash
# 检查 WSL 版本
wsl --version

# 确认当前发行版
lsb_release -a
```

#### 安装必要依赖包
```bash
# 更新包管理器
sudo apt update

# 安装基础依赖（根据实际需要调整）
sudo apt install -y \
    libc6 \
    libgcc-s1 \
    libstdc++6 \
    libssl3 \
    libcurl4 \
    libfontconfig1 \
    libfreetype6 \
    libx11-6 \
    libxext6 \
    libxrender1 \
    libxtst6 \
    libxi6

# 如果是图形界面相关依赖
sudo apt install -y \
    libgtk-3-0 \
    libgdk-pixbuf2.0-0 \
    libcairo-gobject2 \
    libpango-1.0-0 \
    libatk1.0-0

# 检查依赖是否满足
ldd 寸止  # 运行前检查依赖
```

### 2. 下载和安装

#### 下载安装包
```bash
# 进入下载目录
cd ~/Downloads

# 下载寸止工具（从官方获取下载链接）
# 解压安装包
tar -xzf cunzhi-linux.tar.gz  # 或相应的压缩包名称

# 解压后得到的实际文件：
# 寸止 - 主程序
# 等一下 - 设置程序
```

### 3. 权限配置和别名设置

```bash
# 确保可执行权限
chmod +x 寸止
chmod +x 等一下

# 移动到用户本地路径
mkdir -p ~/.local/bin
mv 寸止 ~/.local/bin/
mv 等一下 ~/.local/bin/

# 创建英文别名（方便使用）
echo 'alias cunzhi="寸止"' >> ~/.bashrc
echo 'alias cunzhi-setup="等一下"' >> ~/.bashrc
```

### 4. 环境变量设置

```bash
# 添加到 PATH
echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc

# 验证安装
which 寸止
which 等一下
# 验证别名
cunzhi --help
cunzhi-setup --help
```

## ⚙️ 配置过程

### 1. GUI 设置界面问题

#### 问题描述
运行 `等一下`（或别名 `cunzhi-setup`）时出现 GUI 界面乱码，显示方块字符，无法正常使用。

#### 解决方案
```bash
# 跳过 GUI 设置，直接使用命令行配置
# GUI 乱码不影响核心 MCP 功能

# 查看可用选项
等一下 --help
# 或使用别名
cunzhi-setup --help
```

#### GUI 设置输出示例
```
寸止 - 智能代码审查工具

用法:
  等一下                    启动设置界面
  等一下 --mcp-request <文件>  处理 MCP 请求
  等一下 --help             显示此帮助信息
  等一下 --version          显示版本信息
```

### 2. MCP 服务器配置

#### 启动 MCP 服务器
```bash
# 直接启动 MCP 服务器
寸止
# 或使用别名
cunzhi

# 输出示例：
# 2025-07-13 02:48:47.175 [INFO] [寸止] 启动 MCP 服务器
```

#### 验证服务器运行
```bash
# 检查进程
ps aux | grep 寸止

# 检查端口占用（如果有固定端口）
netstat -tlnp | grep 寸止
```

### 3. Augment 集成配置

#### 在 Augment 中添加 MCP 服务器

1. **打开 Augment MCP 设置**
   - 进入 Augment 设置界面
   - 选择 "MCP" 选项
   - 点击 "New Stdio MCP Server"

2. **配置服务器参数**
   ```
   Name: cunzhi
   Command: /home/<USER>/.local/bin/寸止
   Environment Variables:
     PWD = /home/<USER>/projects/cursor-pj/app-aug/db-monitor-platform
   ```

3. **保存并测试连接**
   - 点击 "Add" 保存配置
   - 验证连接状态（无错误提示即为成功）

### 4. 工作目录设置

#### 环境变量配置
```bash
# 设置项目工作目录
export MCP_WORKING_DIR="/home/<USER>/projects/cursor-pj/app-aug/db-monitor-platform"

# 或者在 Augment MCP 配置中添加环境变量：
# Variable Name: PWD
# Value: /home/<USER>/projects/cursor-pj/app-aug/db-monitor-platform
```

## 🔧 问题和解决方案

### 1. WSL 依赖包不足问题 ⭐ **最重要**

**问题**: 运行 `寸止` 时提示缺少依赖库

**常见错误信息**:
```bash
./寸止: error while loading shared libraries: libxxx.so.x: cannot open shared object file: No such file or directory
```

**解决方案**:
```bash
# 1. 检查缺少的依赖
ldd 寸止

# 2. 根据缺少的库安装对应包
# 常见缺失依赖和解决方案：

# 如果缺少 libssl
sudo apt install libssl3

# 如果缺少 libcurl
sudo apt install libcurl4

# 如果缺少图形界面库
sudo apt install libgtk-3-0 libx11-6

# 如果缺少字体库
sudo apt install libfontconfig1 libfreetype6

# 3. 一次性安装常用依赖
sudo apt install -y libc6 libgcc-s1 libstdc++6 libssl3 libcurl4 libfontconfig1 libfreetype6 libx11-6 libxext6 libxrender1 libxtst6 libxi6 libgtk-3-0
```

### 2. GUI 乱码问题

**问题**: GUI 界面显示方块字符，无法正常使用

**原因**: WSL 环境中字体和显示支持问题

**解决方案**:
- 跳过 GUI 配置，直接使用 MCP 功能
- GUI 乱码不影响核心 MCP 服务器功能
- 可以通过命令行直接启动 MCP 服务器

### 3. 路径配置问题

**问题**: Augment 提示 "cunzhi: not found"

**错误信息**: 
```
Failed to start the MCP server. {"command":"cunzhi","args":[],"error":"MCP error -32000: Connection closed","stderr":"/bin/sh: 1: cunzhi: not found\n"}
```

**解决方案**:
```bash
# 使用完整路径指向实际文件
Command: /home/<USER>/.local/bin/寸止

# 确认路径
which 寸止  # 确认实际文件路径
which cunzhi  # 确认别名是否生效
```

### 4. 连接测试

```bash
# 测试程序能否运行
寸止 --help

# 在 Augment 中测试
# 发送：请帮我列出当前项目目录下的所有文件
```

## 📝 总结

### 关键步骤
1. **安装依赖包** - 解决 WSL 环境缺失的系统库
2. **设置别名** - 方便使用中文程序名
3. **配置 Augment MCP** - 使用绝对路径连接
4. **跳过 GUI** - 直接使用 MCP 功能

### 常见问题
- **依赖缺失** → 安装对应的 apt 包
- **GUI 乱码** → 跳过设置界面
- **路径错误** → 使用完整路径

---

> 基于 WSL 环境实际安装经验整理
