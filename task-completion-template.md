# 任务完成助手模板

> 🎯 **用途**: 当完成任务时，AI助手使用此模板确保完整的状态同步

## 📋 任务完成检查清单

### ✅ 功能验证
- [ ] 使用 `web-fetch` 检查页面状态
- [ ] 使用 `open-browser` 进行视觉确认
- [ ] 运行 `npm run build` 检查编译
- [ ] 验证新功能正常工作
- [ ] 确认没有破坏现有功能

### 📊 任务状态更新
- [ ] 使用 `update_tasks` 标记任务完成
- [ ] 添加新发现的子任务（如有）
- [ ] 更新任务进度百分比
- [ ] 记录完成时间

### 📄 文档同步更新
- [ ] 更新 `PROJECT_STATUS.md`
- [ ] 更新 `project-status.json`
- [ ] 更新 `quick-status-check.md`
- [ ] 记录最新成果和影响

### 🎯 下一步规划
- [ ] 明确下一个优先任务
- [ ] 识别技术债务
- [ ] 评估整体项目进度
- [ ] 确认开发重点

## 🤖 AI助手执行模板

### Step 1: 功能验证
```bash
# 检查页面状态
web-fetch http://localhost:5173

# 如果需要，打开浏览器确认
open-browser http://localhost:5173

# 检查编译状态
launch-process "npm run build" --cwd frontend
```

### Step 2: 询问用户确认
```
🎉 看起来 [功能名称] 已经完成了！

我看到：
- ✅ [具体功能点1]
- ✅ [具体功能点2]

请确认：
1. 这个功能是否已经完全完成？
2. 是否还有其他需要测试的方面？
3. 是否发现了新的子任务或问题？
```

### Step 3: 更新任务状态
```bash
# 标记任务完成
update_tasks [{"task_id": "[任务ID]", "state": "COMPLETE"}]

# 如果有新任务，添加到清单
add_tasks [{"name": "[新任务]", "description": "[描述]"}]
```

### Step 4: 更新项目文档
```bash
# 更新主状态文档
str-replace-editor PROJECT_STATUS.md

# 更新JSON状态
str-replace-editor project-status.json

# 更新快速检查
str-replace-editor quick-status-check.md
```

### Step 5: 确认下一步
```
📋 项目状态已更新！

✅ 刚完成：[功能名称]
🎯 下一步：[下一个任务]
📊 整体进度：[百分比]

是否要继续开发下一个功能？
```

## 📝 文档更新模板

### PROJECT_STATUS.md 更新模板
```markdown
### 🔔 [功能名称] ([完成日期] 完成)
**位置**: [功能在系统中的位置]  
**功能**:
- [功能描述1]
- [功能描述2]

**技术实现**:
- [技术要点1]
- [技术要点2]

**用户价值**: [对用户的价值和影响]

**集成位置**: [在哪个文件/组件中集成]
```

### project-status.json 更新模板
```json
{
  "recentAchievements": [
    {
      "date": "[YYYY-MM-DD]",
      "achievement": "[功能名称]",
      "impact": "[用户价值描述]"
    }
  ],
  "currentTasks": {
    "completed": [
      {
        "id": "[task-id]",
        "name": "[任务名称]",
        "status": "completed",
        "progress": 100,
        "completedDate": "[YYYY-MM-DD]"
      }
    ]
  }
}
```

## 🎯 常见任务完成场景

### 场景1: 新功能开发完成
```
用户说："告警铃铛功能完成了"

AI应该：
1. 验证功能：web-fetch + open-browser
2. 确认细节：询问具体实现的功能点
3. 更新状态：update_tasks + 文档更新
4. 规划下一步：确认下一个开发重点
```

### 场景2: Bug修复完成
```
用户说："页面加载问题修复了"

AI应该：
1. 验证修复：检查页面是否正常
2. 确认影响：询问修复的具体问题
3. 记录解决方案：更新技术债务状态
4. 预防措施：建议相关的改进
```

### 场景3: 阶段性里程碑达成
```
用户说："告警管理系统基本完成了"

AI应该：
1. 全面验证：检查所有相关功能
2. 评估完整性：确认是否达到预期目标
3. 更新进度：调整阶段进度百分比
4. 规划下一阶段：明确下一个开发重点
```

## ⚠️ 注意事项

### 必须避免的错误
- ❌ 不验证功能就标记完成
- ❌ 不询问用户确认就更新状态
- ❌ 不更新文档就开始下一个任务
- ❌ 不明确下一步就结束对话

### 质量保证要求
- ✅ 每次完成都要验证功能
- ✅ 每次更新都要保持文档一致性
- ✅ 每次规划都要明确优先级
- ✅ 每次记录都要包含用户价值

---

## 🎉 使用此模板的效果

通过使用这个模板，可以确保：
- **零遗漏**：所有完成的任务都被正确记录
- **状态同步**：文档始终反映最新进度
- **连续性**：新Thread可以完美接续
- **质量保证**：每个功能都经过验证
