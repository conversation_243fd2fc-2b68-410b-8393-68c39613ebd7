#!/bin/bash

# 快速启动开发环境脚本
# 解决服务启动顺序问题：Docker -> 后端 -> 前端

echo "🚀 启动数据库监控平台开发环境..."

# 1. 启动Docker服务
echo "📦 启动Docker服务..."
docker-compose up -d

# 等待数据库启动
echo "⏳ 等待数据库启动..."
sleep 5

# 2. 启动后端服务
echo "🔧 启动后端服务..."
cd backend/go-backend
nohup go run cmd/server/main.go > ../../backend.log 2>&1 &
echo $! > ../../backend.pid
cd ../..

# 等待后端启动
echo "⏳ 等待后端启动..."
sleep 3

# 3. 启动前端服务
echo "🎨 启动前端服务..."
cd frontend
nohup npm run dev > ../frontend.log 2>&1 &
echo $! > ../frontend.pid
cd ..

echo ""
echo "✅ 所有服务启动完成！"
echo ""
echo "📍 服务地址："
echo "   前端应用: http://localhost:5173"
echo "   后端API: http://localhost:8080"
echo "   API文档: http://localhost:8080/swagger/index.html"
echo ""
echo "📋 管理命令："
echo "   查看状态: ./service-manager.sh status"
echo "   停止服务: ./stop-dev.sh"
echo ""
echo "📝 日志文件："
echo "   后端日志: backend.log"
echo "   前端日志: frontend.log"
