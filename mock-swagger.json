{"swagger": "2.0", "info": {"title": "数据库监控平台 API", "description": "企业级数据库监控平台的RESTful API", "version": "1.0.0"}, "host": "localhost:8080", "basePath": "/api/v1", "schemes": ["http"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/health": {"get": {"summary": "健康检查", "description": "检查API服务状态", "responses": {"200": {"description": "服务正常", "schema": {"type": "object", "properties": {"status": {"type": "string"}, "timestamp": {"type": "string"}}}}}}}, "/auth/register": {"post": {"summary": "用户注册", "description": "创建新用户账户", "parameters": [{"in": "body", "name": "user", "required": true, "schema": {"type": "object", "required": ["email", "password", "name"], "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string", "minLength": 6}, "name": {"type": "string", "minLength": 2}}}}], "responses": {"201": {"description": "注册成功", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "email": {"type": "string"}, "name": {"type": "string"}}}}}}, "400": {"description": "请求参数错误"}, "409": {"description": "邮箱已存在"}}}}, "/auth/login": {"post": {"summary": "用户登录", "description": "用户身份验证", "parameters": [{"in": "body", "name": "credentials", "required": true, "schema": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string"}}}}], "responses": {"200": {"description": "登录成功", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"token": {"type": "string"}, "user": {"type": "object", "properties": {"id": {"type": "integer"}, "email": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}}}}}}}}, "401": {"description": "认证失败"}}}}, "/databases": {"get": {"summary": "获取数据库列表", "description": "获取用户的数据库实例列表", "parameters": [{"in": "query", "name": "page", "type": "integer", "default": 1}, {"in": "query", "name": "page_size", "type": "integer", "default": 20}], "responses": {"200": {"description": "获取成功", "schema": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"page": {"type": "integer"}, "page_size": {"type": "integer"}, "total": {"type": "integer"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "type": {"type": "string", "enum": ["mysql", "postgresql", "mongodb", "redis"]}, "host": {"type": "string"}, "port": {"type": "integer"}, "status": {"type": "string", "enum": ["active", "inactive", "error"]}}}}}}}}}}}, "post": {"summary": "创建数据库实例", "description": "添加新的数据库实例", "parameters": [{"in": "body", "name": "database", "required": true, "schema": {"type": "object", "required": ["name", "type", "host", "port"], "properties": {"name": {"type": "string"}, "type": {"type": "string", "enum": ["mysql", "postgresql", "mongodb", "redis"]}, "host": {"type": "string"}, "port": {"type": "integer", "minimum": 1, "maximum": 65535}, "database_name": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}, "description": {"type": "string"}}}}], "responses": {"201": {"description": "创建成功"}, "400": {"description": "请求参数错误"}}}}, "/databases/{id}": {"get": {"summary": "获取数据库详情", "description": "获取指定数据库实例的详细信息", "parameters": [{"in": "path", "name": "id", "required": true, "type": "integer"}], "responses": {"200": {"description": "获取成功"}, "404": {"description": "数据库实例不存在"}}}, "put": {"summary": "更新数据库实例", "description": "更新数据库实例信息", "parameters": [{"in": "path", "name": "id", "required": true, "type": "integer"}, {"in": "body", "name": "database", "required": true, "schema": {"type": "object", "properties": {"name": {"type": "string"}, "host": {"type": "string"}, "port": {"type": "integer"}, "description": {"type": "string"}}}}], "responses": {"200": {"description": "更新成功"}, "404": {"description": "数据库实例不存在"}}}, "delete": {"summary": "删除数据库实例", "description": "删除指定的数据库实例", "parameters": [{"in": "path", "name": "id", "required": true, "type": "integer"}], "responses": {"200": {"description": "删除成功"}, "404": {"description": "数据库实例不存在"}}}}, "/metrics/{db_id}": {"get": {"summary": "获取监控指标", "description": "获取指定数据库的监控指标数据", "parameters": [{"in": "path", "name": "db_id", "required": true, "type": "integer"}, {"in": "query", "name": "metric_type", "type": "string", "enum": ["cpu", "memory", "disk", "connections", "qps", "tps"]}, {"in": "query", "name": "start_time", "type": "string", "format": "date-time"}, {"in": "query", "name": "end_time", "type": "string", "format": "date-time"}], "responses": {"200": {"description": "获取成功"}, "404": {"description": "数据库实例不存在"}}}}, "/alerts/rules": {"get": {"summary": "获取告警规则", "description": "获取告警规则列表", "responses": {"200": {"description": "获取成功"}}}, "post": {"summary": "创建告警规则", "description": "创建新的告警规则", "parameters": [{"in": "body", "name": "rule", "required": true, "schema": {"type": "object", "required": ["name", "database_id", "metric_type", "operator", "threshold"], "properties": {"name": {"type": "string"}, "database_id": {"type": "integer"}, "metric_type": {"type": "string", "enum": ["cpu", "memory", "disk", "connections"]}, "operator": {"type": "string", "enum": [">", "<", ">=", "<=", "=", "!="]}, "threshold": {"type": "number"}, "severity": {"type": "string", "enum": ["info", "warning", "error", "critical"]}}}}], "responses": {"201": {"description": "创建成功"}, "400": {"description": "请求参数错误"}}}}}}