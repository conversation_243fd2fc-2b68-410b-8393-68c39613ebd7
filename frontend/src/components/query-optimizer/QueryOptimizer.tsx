import React, { useState, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import {
  PlayIcon,
  ChartBarIcon,
  LightBulbIcon,
  ClockIcon,
  CpuChipIcon,
  CircleStackIcon,
  MagnifyingGlassIcon,
  CodeBracketIcon,
  CheckCircleIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import {
  useAnalyzeQuery,
  useDatabases
} from '../../queries';
import { apiService } from '../../services/api';

// SQL查询分析结果接口 - 匹配后端QueryAnalysisResponse
interface QueryAnalysisResult {
  id: number;
  database_id: number;
  original_query: string;
  formatted_query: string;
  query_type: string;
  estimated_cost: number;
  complexity_score: number;
  execution_time: number;
  rows_returned: number;
  rows_examined: number;
  status: string;
  error_message?: string;
  analysis_timestamp: string;
  execution_plan?: ExecutionPlan;
  optimization_suggestions?: OptimizationSuggestion[];
  index_recommendations?: IndexRecommendation[];
}

// 执行计划接口
interface ExecutionPlan {
  plan_id: string;
  total_cost: number;
  execution_time: number;
  nodes: PlanNode[];
  statistics: PlanStatistics;
}

interface PlanNode {
  id: string;
  node_type: string;
  operation: string;
  table_name?: string;
  index_name?: string;
  cost: number;
  rows: number;
  width: number;
  actual_time: number;
  children: PlanNode[];
  condition?: string;
}

interface PlanStatistics {
  total_rows_processed: number;
  total_time: number;
  buffer_hits: number;
  buffer_reads: number;
  temp_files: number;
  temp_size: number;
}



// 优化建议接口
interface OptimizationSuggestion {
  id: string;
  type: 'index' | 'query_rewrite' | 'schema' | 'configuration';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  before_query?: string;
  after_query?: string;
  estimated_improvement: number;
  implementation_effort: 'easy' | 'medium' | 'hard';
  impact_areas: string[];
}

// 索引推荐接口
interface IndexRecommendation {
  id: string;
  table_name: string;
  columns: string[];
  index_type: 'btree' | 'hash' | 'gin' | 'gist';
  create_statement: string;
  estimated_size: string;
  estimated_improvement: number;
  usage_frequency: number;
  maintenance_cost: 'low' | 'medium' | 'high';
}

interface QueryOptimizerProps {
  onClose?: () => void;
}

export const QueryOptimizer: React.FC<QueryOptimizerProps> = ({ onClose }) => {
  const [currentTab, setCurrentTab] = useState<'analyzer' | 'plan' | 'compare' | 'recommendations'>('analyzer');
  const [sqlQuery, setSqlQuery] = useState('');
  const [analysisResult, setAnalysisResult] = useState<QueryAnalysisResult | null>(null);
  const [selectedDatabaseId, setSelectedDatabaseId] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [analysisId, setAnalysisId] = useState<number | null>(null);
  const [isPolling, setIsPolling] = useState(false);

  // API hooks
  const analyzeQueryMutation = useAnalyzeQuery({
    onSuccess: (response) => {
      if (response.data && response.data.id) {
        // 开始轮询获取分析结果
        setAnalysisId(response.data.id);
        setIsPolling(true);
        setError(null);
      }
    },
    onError: (error: any) => {
      console.error('分析查询失败:', error);
      setError(error.response?.data?.message || '分析查询失败，请稍后重试');
      setIsPolling(false);
    }
  });

  // 轮询获取分析结果
  React.useEffect(() => {
    if (!analysisId || !isPolling) return;

    const pollAnalysis = async () => {
      try {
        const response = await apiService.getAnalysis(analysisId);
        if (response.data) {
          const result = response.data;
          // 如果分析完成，停止轮询并设置结果
          if (result.status === 'completed') {
            setAnalysisResult(result);
            setIsPolling(false);
          } else if (result.status === 'failed') {
            setError(result.error_message || '分析失败');
            setIsPolling(false);
          }
        }
      } catch (error: any) {
        console.error('轮询分析结果失败:', error);
        setError('获取分析结果失败');
        setIsPolling(false);
      }
    };

    // 立即执行一次
    pollAnalysis();

    // 设置轮询间隔
    const interval = setInterval(pollAnalysis, 1000);

    return () => clearInterval(interval);
  }, [analysisId, isPolling]);

  // 获取queryClient用于手动刷新缓存
  const queryClient = useQueryClient();

  // 获取数据库列表
  const { data: databasesResponse, isLoading: isDatabasesLoading, error: databasesError, refetch: refetchDatabases } = useDatabases();
  const databases = databasesResponse?.data?.items || [];

  // 强制刷新数据库列表的函数
  const forceRefreshDatabases = async () => {
    // 清除数据库相关的所有缓存
    await queryClient.invalidateQueries({ queryKey: ['databases'] });
    // 重新获取数据
    await refetchDatabases();
  };

  // 添加详细调试信息
  console.log('=== QueryOptimizer 数据库加载调试 ===');
  console.log('isDatabasesLoading:', isDatabasesLoading);
  console.log('databasesError:', databasesError);
  console.log('databasesResponse:', databasesResponse);
  console.log('databases array:', databases);
  console.log('databases length:', databases.length);
  console.log('selectedDatabaseId:', selectedDatabaseId);

  // 检查localStorage中的token
  const token = localStorage.getItem('access_token');
  console.log('localStorage token:', token ? 'exists' : 'missing');

  // 如果有错误，显示详细信息
  if (databasesError) {
    console.error('数据库API错误详情:', databasesError);
  }

  // 暂时注释掉未使用的hooks，后续会在其他标签页中使用
  // const { data: suggestionsResponse } = useOptimizationSuggestions(currentAnalysisId || 0, {
  //   enabled: !!currentAnalysisId,
  //   staleTime: 5 * 60 * 1000 // 5分钟
  // });

  // const { data: indexRecommendationsResponse } = useIndexRecommendations(currentAnalysisId || 0, {
  //   enabled: !!currentAnalysisId,
  //   staleTime: 5 * 60 * 1000 // 5分钟
  // });

  // 组件挂载时强制刷新数据库列表
  useEffect(() => {
    forceRefreshDatabases();
  }, []); // 只在组件挂载时执行一次

  // 设置默认数据库
  useEffect(() => {
    if (databases.length > 0 && !selectedDatabaseId) {
      setSelectedDatabaseId(databases[0].id);
    }
  }, [databases, selectedDatabaseId]);

  // 示例SQL查询
  const exampleQueries = [
    {
      name: '用户订单查询',
      query: `SELECT u.id, u.name, u.email, 
       COUNT(o.id) as order_count,
       SUM(o.total_amount) as total_spent
FROM users u 
LEFT JOIN orders o ON u.id = o.user_id 
WHERE u.created_at > '2024-01-01'
  AND u.status = 'active'
GROUP BY u.id, u.name, u.email
ORDER BY total_spent DESC
LIMIT 100;`
    },
    {
      name: '产品库存分析',
      query: `SELECT category,
       COUNT(*) as product_count,
       AVG(price) as avg_price,
       MIN(price) as min_price,
       MAX(price) as max_price,
       SUM(stock_quantity) as total_stock
FROM products
WHERE stock_quantity > 0
GROUP BY category
ORDER BY avg_price DESC;`
    },
    {
      name: '慢查询示例',
      query: `SELECT DISTINCT u.id, u.name, u.email,
       (SELECT COUNT(*) FROM orders WHERE user_id = u.id) as order_count,
       (SELECT MAX(created_at) FROM orders WHERE user_id = u.id) as last_order
FROM users u
WHERE EXISTS (
  SELECT 1 FROM orders o 
  WHERE o.user_id = u.id 
    AND o.total_amount > 1000
)
ORDER BY u.created_at DESC;`
    }
  ];

  // 分析SQL查询
  const analyzeQuery = async () => {
    if (!sqlQuery.trim()) {
      setError('请输入SQL查询语句');
      return;
    }

    if (!selectedDatabaseId) {
      setError('请选择数据库');
      return;
    }

    setError(null);

    // 调用真实API
    analyzeQueryMutation.mutate({
      database_id: selectedDatabaseId,
      sql_query: sqlQuery.trim(),
      options: {
        include_execution_plan: true,
        include_suggestions: true,
        include_index_recommendations: true,
        run_explain_analyze: false
      }
    });
  };



  // 暂时保留格式化SQL函数，后续可能会用到
  // const formatSQL = (sql: string): string => {
  //   return sql
  //     .replace(/\s+/g, ' ')
  //     .replace(/\bSELECT\b/gi, '\nSELECT')
  //     .replace(/\bFROM\b/gi, '\nFROM')
  //     .replace(/\bWHERE\b/gi, '\nWHERE')
  //     .replace(/\bJOIN\b/gi, '\nJOIN')
  //     .replace(/\bLEFT JOIN\b/gi, '\nLEFT JOIN')
  //     .replace(/\bRIGHT JOIN\b/gi, '\nRIGHT JOIN')
  //     .replace(/\bINNER JOIN\b/gi, '\nINNER JOIN')
  //     .replace(/\bGROUP BY\b/gi, '\nGROUP BY')
  //     .replace(/\bORDER BY\b/gi, '\nORDER BY')
  //     .replace(/\bHAVING\b/gi, '\nHAVING')
  //     .replace(/\bLIMIT\b/gi, '\nLIMIT')
  //     .trim();
  // };

  // 暂时注释掉mock函数，后续可能作为fallback使用
  // const generateMockExecutionPlan = (): ExecutionPlan => {
  //   return {
  //     plan_id: 'plan_' + Date.now(),
  //     total_cost: Math.random() * 1000 + 50,
  //     execution_time: Math.random() * 500 + 10,
  //     nodes: [...],
  //     statistics: {...}
  //   };
  // };

  // 其他mock函数已注释，后续可能作为fallback使用

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取性能等级颜色
  const getPerformanceColor = (score: number) => {
    if (score >= 8) return 'text-green-600';
    if (score >= 6) return 'text-yellow-600';
    if (score >= 4) return 'text-orange-600';
    return 'text-red-600';
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* 页面头部 */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">SQL查询优化工具</h1>
            <p className="mt-2 text-gray-600">分析SQL查询性能，生成优化建议和执行计划可视化</p>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              返回
            </button>
          )}
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="mb-6">
        <nav className="flex space-x-8">
          {[
            { key: 'analyzer', label: 'SQL分析器', icon: MagnifyingGlassIcon },
            { key: 'plan', label: '执行计划', icon: ChartBarIcon },
            { key: 'compare', label: '性能对比', icon: ArrowPathIcon },
            { key: 'recommendations', label: '优化建议', icon: LightBulbIcon }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.key}
                onClick={() => setCurrentTab(tab.key as any)}
                className={`
                  flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors
                  ${currentTab === tab.key
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                  }
                `}
              >
                <Icon className="h-5 w-5 mr-2" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* SQL分析器标签页 */}
      {currentTab === 'analyzer' && (
        <div className="space-y-6">
          {/* 查询输入区域 */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">SQL查询分析</h2>
              <div className="flex items-center space-x-4">
                <select
                  value={selectedDatabaseId || ''}
                  onChange={(e) => setSelectedDatabaseId(Number(e.target.value))}
                  className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  disabled={isDatabasesLoading}
                >
                  <option value="">
                    {isDatabasesLoading ? '加载中...' : '选择数据库'}
                  </option>
                  {databases.map((db) => (
                    <option key={db.id} value={db.id}>
                      {db.name} ({db.type})
                    </option>
                  ))}
                </select>
                <button
                  onClick={forceRefreshDatabases}
                  disabled={isDatabasesLoading}
                  className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  title="刷新数据库列表"
                >
                  🔄
                </button>
                {databasesError && (
                  <span className="text-xs text-red-600 ml-2">
                    加载失败
                  </span>
                )}
                <button
                  onClick={analyzeQuery}
                  disabled={analyzeQueryMutation.isPending || isPolling || !sqlQuery.trim() || !selectedDatabaseId}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {analyzeQueryMutation.isPending ? (
                    <>
                      <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                      提交分析...
                    </>
                  ) : isPolling ? (
                    <>
                      <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                      分析中...
                    </>
                  ) : (
                    <>
                      <PlayIcon className="h-4 w-4 mr-2" />
                      分析查询
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* 错误提示 */}
            {error && (
              <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mr-2" />
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            )}

            {/* SQL输入框 */}
            <div className="space-y-4">
              <textarea
                value={sqlQuery}
                onChange={(e) => setSqlQuery(e.target.value)}
                placeholder="请输入您要分析的SQL查询语句..."
                className="w-full h-40 px-4 py-3 border border-gray-300 rounded-lg font-mono text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              />

              {/* 示例查询 */}
              <div>
                <p className="text-sm font-medium text-gray-700 mb-2">示例查询：</p>
                <div className="flex flex-wrap gap-2">
                  {exampleQueries.map((example, index) => (
                    <button
                      key={index}
                      onClick={() => setSqlQuery(example.query)}
                      className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
                    >
                      {example.name}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* 分析结果 */}
          {analysisResult && (
            <div className="space-y-6">
              {/* 性能概览卡片 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center">
                    <ClockIcon className="h-8 w-8 text-blue-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500">执行时间</p>
                      <p className="text-2xl font-semibold text-gray-900">
                        {analysisResult.execution_time.toFixed(1)}ms
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center">
                    <CpuChipIcon className="h-8 w-8 text-green-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500">复杂度评分</p>
                      <p className={`text-2xl font-semibold ${getPerformanceColor(analysisResult.complexity_score)}`}>
                        {analysisResult.complexity_score}/10
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center">
                    <CircleStackIcon className="h-8 w-8 text-purple-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500">扫描行数</p>
                      <p className="text-2xl font-semibold text-gray-900">
                        {analysisResult.rows_examined.toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center">
                    <LightBulbIcon className="h-8 w-8 text-yellow-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500">优化建议</p>
                      <p className="text-2xl font-semibold text-gray-900">
                        {analysisResult.optimization_suggestions?.length || 0}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* 格式化的SQL */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <CodeBracketIcon className="h-5 w-5 mr-2" />
                  格式化SQL
                </h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <pre className="text-sm font-mono text-gray-800 whitespace-pre-wrap">
                    {analysisResult.formatted_query}
                  </pre>
                </div>
              </div>

              {/* 性能指标详情 */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">性能指标详情</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-700">查询信息</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">查询类型:</span>
                        <span className="font-medium uppercase">{analysisResult.query_type}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">执行时间:</span>
                        <span className="font-medium">{analysisResult.execution_time.toFixed(1)}ms</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">状态:</span>
                        <span className={`font-medium ${analysisResult.status === 'completed' ? 'text-green-600' : analysisResult.status === 'failed' ? 'text-red-600' : 'text-yellow-600'}`}>
                          {analysisResult.status === 'completed' ? '已完成' : analysisResult.status === 'failed' ? '失败' : '运行中'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-700">数据指标</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">返回行数:</span>
                        <span className="font-medium">{analysisResult.rows_returned.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">扫描行数:</span>
                        <span className="font-medium">{analysisResult.rows_examined.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">估算成本:</span>
                        <span className="font-medium">{analysisResult.estimated_cost.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-700">分析结果</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">复杂度评分:</span>
                        <span className="font-medium">{analysisResult.complexity_score}/10</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">优化建议:</span>
                        <span className="font-medium">{analysisResult.optimization_suggestions?.length || 0}条</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">索引推荐:</span>
                        <span className="font-medium">{analysisResult.index_recommendations?.length || 0}条</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 执行计划标签页 */}
      {currentTab === 'plan' && analysisResult && (
        <div className="space-y-6">
          {analysisResult.execution_plan ? (
            <>
              {/* 执行计划概览 */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">执行计划概览</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <ChartBarIcon className="h-6 w-6 text-blue-600" />
                      <div className="ml-3">
                        <p className="text-sm font-medium text-blue-900">总成本</p>
                        <p className="text-xl font-bold text-blue-600">
                          {analysisResult.execution_plan.total_cost?.toFixed(2) || 'N/A'}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-green-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <ClockIcon className="h-6 w-6 text-green-600" />
                      <div className="ml-3">
                        <p className="text-sm font-medium text-green-900">执行时间</p>
                        <p className="text-xl font-bold text-green-600">
                          {analysisResult.execution_plan.execution_time?.toFixed(1) || 'N/A'}ms
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-purple-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <CircleStackIcon className="h-6 w-6 text-purple-600" />
                      <div className="ml-3">
                        <p className="text-sm font-medium text-purple-900">处理行数</p>
                        <p className="text-xl font-bold text-purple-600">
                          {analysisResult.execution_plan.statistics?.total_rows_processed?.toLocaleString() || 'N/A'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </>
          ) : (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="text-center py-8">
                <ExclamationTriangleIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">执行计划不可用</h3>
                <p className="text-gray-500">当前分析结果中没有执行计划信息</p>
              </div>
            </div>
          )}

              {/* 执行计划树 */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">执行计划树</h3>
                <div className="space-y-4">
                  {analysisResult.execution_plan?.nodes?.map((node, index) => (
                    <div key={node.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-sm font-medium text-blue-600">{index + 1}</span>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900">{node.operation}</h4>
                            <p className="text-sm text-gray-500">{node.node_type}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-gray-900">成本: {node.cost?.toFixed(2) || 'N/A'}</p>
                          <p className="text-xs text-gray-500">时间: {node.actual_time?.toFixed(1) || 'N/A'}ms</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">表名:</span>
                          <span className="ml-2 font-medium">{node.table_name || 'N/A'}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">行数:</span>
                          <span className="ml-2 font-medium">{node.rows?.toLocaleString() || 'N/A'}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">宽度:</span>
                          <span className="ml-2 font-medium">{node.width || 'N/A'} bytes</span>
                        </div>
                      </div>

                      {node.condition && (
                        <div className="mt-3 p-3 bg-gray-50 rounded">
                          <p className="text-sm text-gray-600">条件:</p>
                          <p className="text-sm font-mono text-gray-800">{node.condition}</p>
                        </div>
                      )}
                    </div>
                  )) || (
                    <div className="text-center py-4 text-gray-500">
                      没有可用的执行计划节点
                    </div>
                  )}
                </div>
              </div>

              {/* 执行统计 */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">执行统计</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-700">缓冲区统计</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">缓冲区命中:</span>
                        <span className="font-medium">{analysisResult.execution_plan?.statistics?.buffer_hits?.toLocaleString() || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">缓冲区读取:</span>
                        <span className="font-medium">{analysisResult.execution_plan?.statistics?.buffer_reads?.toLocaleString() || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">命中率:</span>
                        <span className="font-medium">
                          {analysisResult.execution_plan?.statistics?.buffer_hits && analysisResult.execution_plan?.statistics?.buffer_reads
                            ? ((analysisResult.execution_plan.statistics.buffer_hits /
                                (analysisResult.execution_plan.statistics.buffer_hits + analysisResult.execution_plan.statistics.buffer_reads)) * 100).toFixed(1) + '%'
                            : 'N/A'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-700">临时文件</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">临时文件数:</span>
                        <span className="font-medium">{analysisResult.execution_plan?.statistics?.temp_files || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">临时文件大小:</span>
                        <span className="font-medium">{analysisResult.execution_plan?.statistics?.temp_size?.toFixed(1) || 'N/A'}KB</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-700">总体性能</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">总时间:</span>
                        <span className="font-medium">{analysisResult.execution_plan?.statistics?.total_time?.toFixed(1) || 'N/A'}ms</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">处理行数:</span>
                        <span className="font-medium">{analysisResult.execution_plan?.statistics?.total_rows_processed?.toLocaleString() || 'N/A'}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
        </div>
      )}

      {/* 性能对比标签页 */}
      {currentTab === 'compare' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">查询性能对比</h2>
            <div className="text-center py-12">
              <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">性能对比功能</h3>
              <p className="mt-1 text-sm text-gray-500">
                此功能允许您对比不同SQL查询的性能表现
              </p>
              <p className="mt-2 text-xs text-gray-400">
                功能开发中，敬请期待...
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 优化建议标签页 */}
      {currentTab === 'recommendations' && analysisResult && (
        <div className="space-y-6">
          {/* 优化建议列表 */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-900">优化建议</h2>
            {analysisResult.optimization_suggestions?.map((suggestion) => (
              <div key={suggestion.id} className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-3">
                      <LightBulbIcon className="h-6 w-6 text-yellow-500" />
                      <h3 className="text-lg font-semibold text-gray-900">{suggestion.title}</h3>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(suggestion.priority)}`}>
                        {suggestion.priority === 'high' ? '高优先级' : suggestion.priority === 'medium' ? '中优先级' : '低优先级'}
                      </span>
                    </div>

                    <p className="text-gray-700 mb-4">{suggestion.description}</p>

                    {suggestion.before_query && suggestion.after_query && (
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 mb-2">优化前:</h4>
                          <div className="bg-red-50 border border-red-200 rounded p-3">
                            <code className="text-sm text-red-800">{suggestion.before_query}</code>
                          </div>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 mb-2">优化后:</h4>
                          <div className="bg-green-50 border border-green-200 rounded p-3">
                            <code className="text-sm text-green-800">{suggestion.after_query}</code>
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="flex items-center space-x-6 text-sm text-gray-600">
                      <div className="flex items-center">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mr-1" />
                        预期提升: {suggestion.estimated_improvement}%
                      </div>
                      <div className="flex items-center">
                        <CpuChipIcon className="h-4 w-4 text-blue-500 mr-1" />
                        实施难度: {suggestion.implementation_effort === 'easy' ? '简单' : suggestion.implementation_effort === 'medium' ? '中等' : '困难'}
                      </div>
                    </div>

                    <div className="mt-3">
                      <p className="text-sm text-gray-600">影响领域:</p>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {suggestion.impact_areas.map((area, index) => (
                          <span key={index} className="inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                            {area}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 索引推荐 */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-900">索引推荐</h2>
            {analysisResult.index_recommendations?.map((recommendation) => (
              <div key={recommendation.id} className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-3">
                      <CircleStackIcon className="h-6 w-6 text-blue-500" />
                      <h3 className="text-lg font-semibold text-gray-900">
                        {recommendation.table_name} 表索引优化
                      </h3>
                      <span className="inline-flex px-2 py-1 text-xs font-semibold bg-blue-100 text-blue-800 rounded-full">
                        {recommendation.index_type.toUpperCase()}
                      </span>
                    </div>

                    <div className="bg-gray-50 rounded p-4 mb-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">创建语句:</h4>
                      <code className="text-sm font-mono text-gray-800">{recommendation.create_statement}</code>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">预期大小:</span>
                        <span className="ml-2 font-medium">{recommendation.estimated_size}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">性能提升:</span>
                        <span className="ml-2 font-medium text-green-600">{recommendation.estimated_improvement}%</span>
                      </div>
                      <div>
                        <span className="text-gray-600">使用频率:</span>
                        <span className="ml-2 font-medium">{recommendation.usage_frequency}%</span>
                      </div>
                    </div>

                    <div className="mt-3 flex items-center space-x-4 text-sm">
                      <div className="flex items-center">
                        <span className="text-gray-600">维护成本:</span>
                        <span className={`ml-2 font-medium ${
                          recommendation.maintenance_cost === 'low' ? 'text-green-600' :
                          recommendation.maintenance_cost === 'medium' ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {recommendation.maintenance_cost === 'low' ? '低' :
                           recommendation.maintenance_cost === 'medium' ? '中' : '高'}
                        </span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-gray-600">涉及字段:</span>
                        <span className="ml-2 font-medium">{recommendation.columns.join(', ')}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
