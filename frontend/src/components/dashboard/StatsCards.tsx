import React from 'react';

interface StatsCardsProps {
  databaseStats?: {
    total: number;
    active: number;
    inactive: number;
    by_type: Record<string, number>;
  };
  alertStats?: {
    total_rules: number;
    active_rules: number;
    total_events: number;
    active_events: number;
    resolved_events: number;
    by_severity: Record<string, number>;
    by_status: Record<string, number>;
  };
  loading: boolean;
}

export const StatsCards: React.FC<StatsCardsProps> = ({ 
  databaseStats, 
  alertStats, 
  loading 
}) => {
  if (loading) {
    return (
      <>
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="bg-white rounded-lg shadow p-6">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        ))}
      </>
    );
  }

  const cards = [
    {
      title: '数据库总数',
      value: databaseStats?.total || 0,
      subtitle: `${databaseStats?.active || 0} 个在线`,
      color: 'blue',
      icon: '🗄️',
    },
    {
      title: '活跃告警',
      value: alertStats?.active_events || 0,
      subtitle: `${alertStats?.total_events || 0} 个总告警`,
      color: alertStats?.active_events ? 'red' : 'green',
      icon: '🚨',
    },
    {
      title: '告警规则',
      value: alertStats?.active_rules || 0,
      subtitle: `${alertStats?.total_rules || 0} 个总规则`,
      color: 'purple',
      icon: '📋',
    },
    {
      title: '系统状态',
      value: databaseStats?.active === databaseStats?.total ? '正常' : '异常',
      subtitle: `${databaseStats?.inactive || 0} 个离线`,
      color: databaseStats?.active === databaseStats?.total ? 'green' : 'yellow',
      icon: '💚',
    },
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'text-blue-600 bg-blue-50',
      red: 'text-red-600 bg-red-50',
      green: 'text-green-600 bg-green-50',
      purple: 'text-purple-600 bg-purple-50',
      yellow: 'text-yellow-600 bg-yellow-50',
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  return (
    <>
      {cards.map((card, index) => (
        <div key={index} className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className={`p-3 rounded-lg ${getColorClasses(card.color)}`}>
              <span className="text-2xl">{card.icon}</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">{card.title}</p>
              <p className="text-2xl font-semibold text-gray-900">{card.value}</p>
              <p className="text-sm text-gray-500">{card.subtitle}</p>
            </div>
          </div>
        </div>
      ))}
    </>
  );
};
