import React, { useState, useEffect } from 'react';
import {
  ClockIcon,
  ServerIcon,
  CircleStackIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowPathIcon,
  CloudArrowUpIcon,
  DocumentDuplicateIcon,
  WifiIcon,
  BoltIcon,
  CogIcon
} from '@heroicons/react/24/outline';

interface SystemStatus {
  uptime: {
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  };
  storage: {
    total: number;
    used: number;
    available: number;
    usage_percentage: number;
  };
  backup: {
    last_backup: string;
    status: 'success' | 'failed' | 'running' | 'pending';
    next_backup: string;
    backup_size: string;
  };
  system: {
    cpu_cores: number;
    total_memory: string;
    os_version: string;
    database_version: string;
  };
  network: {
    status: 'connected' | 'disconnected' | 'unstable';
    latency: number;
    throughput: string;
  };
  security: {
    ssl_status: 'valid' | 'expired' | 'warning';
    ssl_expires: string;
    firewall_status: 'active' | 'inactive';
    last_security_scan: string;
  };
}

interface SystemStatusPanelProps {
  onRefresh?: () => void;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export const SystemStatusPanel: React.FC<SystemStatusPanelProps> = ({
  onRefresh,
  autoRefresh = true,
  refreshInterval = 30000 // 30秒刷新
}) => {
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    uptime: { days: 15, hours: 8, minutes: 32, seconds: 45 },
    storage: {
      total: 1000,
      used: 650,
      available: 350,
      usage_percentage: 65
    },
    backup: {
      last_backup: '2025-07-11 02:00:00',
      status: 'success',
      next_backup: '2025-07-12 02:00:00',
      backup_size: '2.3 GB'
    },
    system: {
      cpu_cores: 8,
      total_memory: '32 GB',
      os_version: 'Ubuntu 22.04 LTS',
      database_version: 'PostgreSQL 15.2'
    },
    network: {
      status: 'connected',
      latency: 24,
      throughput: '1.2 Gbps'
    },
    security: {
      ssl_status: 'valid',
      ssl_expires: '2025-12-31',
      firewall_status: 'active',
      last_security_scan: '2025-07-11 10:30:00'
    }
  });

  const [loading, setLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  // 模拟实时更新系统状态
  const updateSystemStatus = () => {
    setSystemStatus(prev => ({
      ...prev,
      uptime: {
        ...prev.uptime,
        seconds: (prev.uptime.seconds + 1) % 60,
        minutes: prev.uptime.seconds === 59 ? (prev.uptime.minutes + 1) % 60 : prev.uptime.minutes,
        hours: prev.uptime.minutes === 59 && prev.uptime.seconds === 59 ? (prev.uptime.hours + 1) % 24 : prev.uptime.hours,
        days: prev.uptime.hours === 23 && prev.uptime.minutes === 59 && prev.uptime.seconds === 59 ? prev.uptime.days + 1 : prev.uptime.days
      },
      storage: {
        ...prev.storage,
        used: prev.storage.used + (Math.random() - 0.5) * 0.1,
        usage_percentage: Math.round(((prev.storage.used + (Math.random() - 0.5) * 0.1) / prev.storage.total) * 100)
      },
      network: {
        ...prev.network,
        latency: Math.max(5, Math.min(50, prev.network.latency + (Math.random() - 0.5) * 5))
      }
    }));
    setLastUpdate(new Date());
  };

  // 自动刷新
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(updateSystemStatus, 1000); // 每秒更新运行时间
    const statusInterval = setInterval(() => {
      updateSystemStatus();
      onRefresh?.();
    }, refreshInterval);

    return () => {
      clearInterval(interval);
      clearInterval(statusInterval);
    };
  }, [autoRefresh, refreshInterval, onRefresh]);

  // 手动刷新
  const handleRefresh = async () => {
    setLoading(true);
    setTimeout(() => {
      updateSystemStatus();
      setLoading(false);
      onRefresh?.();
    }, 1000);
  };

  // 获取状态颜色和图标
  const getStatusInfo = (status: string, type: 'backup' | 'network' | 'security' | 'general') => {
    switch (type) {
      case 'backup':
        switch (status) {
          case 'success':
            return { color: 'text-green-600', bgColor: 'bg-green-100', icon: CheckCircleIcon, text: '成功' };
          case 'failed':
            return { color: 'text-red-600', bgColor: 'bg-red-100', icon: XCircleIcon, text: '失败' };
          case 'running':
            return { color: 'text-blue-600', bgColor: 'bg-blue-100', icon: ArrowPathIcon, text: '进行中' };
          case 'pending':
            return { color: 'text-yellow-600', bgColor: 'bg-yellow-100', icon: ClockIcon, text: '等待中' };
          default:
            return { color: 'text-gray-600', bgColor: 'bg-gray-100', icon: ClockIcon, text: '未知' };
        }
      case 'network':
        switch (status) {
          case 'connected':
            return { color: 'text-green-600', bgColor: 'bg-green-100', icon: WifiIcon, text: '已连接' };
          case 'disconnected':
            return { color: 'text-red-600', bgColor: 'bg-red-100', icon: XCircleIcon, text: '断开' };
          case 'unstable':
            return { color: 'text-yellow-600', bgColor: 'bg-yellow-100', icon: ExclamationTriangleIcon, text: '不稳定' };
          default:
            return { color: 'text-gray-600', bgColor: 'bg-gray-100', icon: WifiIcon, text: '未知' };
        }
      case 'security':
        switch (status) {
          case 'valid':
            return { color: 'text-green-600', bgColor: 'bg-green-100', icon: ShieldCheckIcon, text: '有效' };
          case 'expired':
            return { color: 'text-red-600', bgColor: 'bg-red-100', icon: XCircleIcon, text: '已过期' };
          case 'warning':
            return { color: 'text-yellow-600', bgColor: 'bg-yellow-100', icon: ExclamationTriangleIcon, text: '警告' };
          case 'active':
            return { color: 'text-green-600', bgColor: 'bg-green-100', icon: ShieldCheckIcon, text: '活跃' };
          case 'inactive':
            return { color: 'text-red-600', bgColor: 'bg-red-100', icon: XCircleIcon, text: '未激活' };
          default:
            return { color: 'text-gray-600', bgColor: 'bg-gray-100', icon: ShieldCheckIcon, text: '未知' };
        }
      default:
        return { color: 'text-gray-600', bgColor: 'bg-gray-100', icon: CheckCircleIcon, text: '正常' };
    }
  };

  // 格式化时间
  const formatTime = (timeStr: string) => {
    return new Date(timeStr).toLocaleString('zh-CN');
  };

  // 格式化运行时间
  const formatUptime = (uptime: SystemStatus['uptime']) => {
    return `${uptime.days}天 ${uptime.hours.toString().padStart(2, '0')}:${uptime.minutes.toString().padStart(2, '0')}:${uptime.seconds.toString().padStart(2, '0')}`;
  };

  // 获取存储使用率颜色
  const getStorageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600';
    if (percentage >= 80) return 'text-yellow-600';
    return 'text-green-600';
  };

  const backupStatusInfo = getStatusInfo(systemStatus.backup.status, 'backup');
  const networkStatusInfo = getStatusInfo(systemStatus.network.status, 'network');
  const sslStatusInfo = getStatusInfo(systemStatus.security.ssl_status, 'security');
  const firewallStatusInfo = getStatusInfo(systemStatus.security.firewall_status, 'security');

  return (
    <div className="space-y-6">
      {/* 头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">系统状态概览</h3>
          <p className="text-sm text-gray-500 mt-1">
            最后更新: {lastUpdate.toLocaleTimeString('zh-CN')}
          </p>
        </div>
        <button
          onClick={handleRefresh}
          disabled={loading}
          className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
        >
          <ArrowPathIcon className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
          刷新
        </button>
      </div>

      {/* 系统运行时间 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <ClockIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h4 className="text-lg font-medium text-gray-900">系统运行时间</h4>
              <p className="text-sm text-gray-500">系统持续运行状态</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-blue-600">
              {formatUptime(systemStatus.uptime)}
            </p>
            <p className="text-sm text-gray-500">稳定运行</p>
          </div>
        </div>
        
        <div className="grid grid-cols-4 gap-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <p className="text-2xl font-bold text-gray-900">{systemStatus.uptime.days}</p>
            <p className="text-xs text-gray-500">天</p>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <p className="text-2xl font-bold text-gray-900">{systemStatus.uptime.hours}</p>
            <p className="text-xs text-gray-500">小时</p>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <p className="text-2xl font-bold text-gray-900">{systemStatus.uptime.minutes}</p>
            <p className="text-xs text-gray-500">分钟</p>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <p className="text-2xl font-bold text-gray-900">{systemStatus.uptime.seconds}</p>
            <p className="text-xs text-gray-500">秒</p>
          </div>
        </div>
      </div>

      {/* 存储使用情况 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <CircleStackIcon className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <h4 className="text-lg font-medium text-gray-900">存储使用情况</h4>
              <p className="text-sm text-gray-500">磁盘空间使用状态</p>
            </div>
          </div>
          <div className="text-right">
            <p className={`text-2xl font-bold ${getStorageColor(systemStatus.storage.usage_percentage)}`}>
              {systemStatus.storage.usage_percentage}%
            </p>
            <p className="text-sm text-gray-500">已使用</p>
          </div>
        </div>

        {/* 存储进度条 */}
        <div className="mb-4">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>已使用: {systemStatus.storage.used.toFixed(1)} GB</span>
            <span>可用: {systemStatus.storage.available.toFixed(1)} GB</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className={`h-3 rounded-full transition-all duration-300 ${
                systemStatus.storage.usage_percentage >= 90 ? 'bg-red-500' :
                systemStatus.storage.usage_percentage >= 80 ? 'bg-yellow-500' : 'bg-green-500'
              }`}
              style={{ width: `${systemStatus.storage.usage_percentage}%` }}
            ></div>
          </div>
        </div>

        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <p className="text-lg font-semibold text-gray-900">{systemStatus.storage.total} GB</p>
            <p className="text-xs text-gray-500">总容量</p>
          </div>
          <div>
            <p className="text-lg font-semibold text-gray-900">{systemStatus.storage.used.toFixed(1)} GB</p>
            <p className="text-xs text-gray-500">已使用</p>
          </div>
          <div>
            <p className="text-lg font-semibold text-gray-900">{systemStatus.storage.available.toFixed(1)} GB</p>
            <p className="text-xs text-gray-500">可用空间</p>
          </div>
        </div>
      </div>

      {/* 备份状态和系统信息 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 备份状态 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <CloudArrowUpIcon className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h4 className="text-lg font-medium text-gray-900">备份状态</h4>
                <p className="text-sm text-gray-500">数据备份情况</p>
              </div>
            </div>
            <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${backupStatusInfo.bgColor} ${backupStatusInfo.color}`}>
              <backupStatusInfo.icon className="h-3 w-3 mr-1" />
              {backupStatusInfo.text}
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">最后备份</span>
              <span className="text-sm font-medium text-gray-900">
                {formatTime(systemStatus.backup.last_backup)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">下次备份</span>
              <span className="text-sm font-medium text-gray-900">
                {formatTime(systemStatus.backup.next_backup)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">备份大小</span>
              <span className="text-sm font-medium text-gray-900">
                {systemStatus.backup.backup_size}
              </span>
            </div>
          </div>
        </div>

        {/* 系统信息 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-gray-100 rounded-lg">
              <ServerIcon className="h-6 w-6 text-gray-600" />
            </div>
            <div>
              <h4 className="text-lg font-medium text-gray-900">系统信息</h4>
              <p className="text-sm text-gray-500">硬件和软件配置</p>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">CPU核心</span>
              <span className="text-sm font-medium text-gray-900">
                {systemStatus.system.cpu_cores} 核
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">总内存</span>
              <span className="text-sm font-medium text-gray-900">
                {systemStatus.system.total_memory}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">操作系统</span>
              <span className="text-sm font-medium text-gray-900">
                {systemStatus.system.os_version}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">数据库版本</span>
              <span className="text-sm font-medium text-gray-900">
                {systemStatus.system.database_version}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 网络和安全状态 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 网络状态 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <WifiIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h4 className="text-lg font-medium text-gray-900">网络状态</h4>
                <p className="text-sm text-gray-500">网络连接情况</p>
              </div>
            </div>
            <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${networkStatusInfo.bgColor} ${networkStatusInfo.color}`}>
              <networkStatusInfo.icon className="h-3 w-3 mr-1" />
              {networkStatusInfo.text}
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">延迟</span>
              <span className={`text-sm font-medium ${
                systemStatus.network.latency > 30 ? 'text-red-600' :
                systemStatus.network.latency > 20 ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {systemStatus.network.latency.toFixed(1)} ms
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">吞吐量</span>
              <span className="text-sm font-medium text-gray-900">
                {systemStatus.network.throughput}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">连接状态</span>
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  systemStatus.network.status === 'connected' ? 'bg-green-500 animate-pulse' :
                  systemStatus.network.status === 'unstable' ? 'bg-yellow-500' : 'bg-red-500'
                }`}></div>
                <span className="text-sm font-medium text-gray-900">
                  {networkStatusInfo.text}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 安全状态 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-red-100 rounded-lg">
              <ShieldCheckIcon className="h-6 w-6 text-red-600" />
            </div>
            <div>
              <h4 className="text-lg font-medium text-gray-900">安全状态</h4>
              <p className="text-sm text-gray-500">系统安全情况</p>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">SSL证书</span>
              <div className="flex items-center space-x-2">
                <div className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${sslStatusInfo.bgColor} ${sslStatusInfo.color}`}>
                  <sslStatusInfo.icon className="h-3 w-3 mr-1" />
                  {sslStatusInfo.text}
                </div>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">证书到期</span>
              <span className="text-sm font-medium text-gray-900">
                {systemStatus.security.ssl_expires}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">防火墙</span>
              <div className="flex items-center space-x-2">
                <div className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${firewallStatusInfo.bgColor} ${firewallStatusInfo.color}`}>
                  <firewallStatusInfo.icon className="h-3 w-3 mr-1" />
                  {firewallStatusInfo.text}
                </div>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">安全扫描</span>
              <span className="text-sm font-medium text-gray-900">
                {formatTime(systemStatus.security.last_security_scan)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 快速操作面板 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-medium text-gray-900">快速操作</h4>
          <span className="text-xs text-gray-500">一键执行常用管理任务</span>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button
            onClick={() => console.log('执行立即备份')}
            className="group flex flex-col items-center p-4 bg-blue-50 rounded-xl hover:bg-blue-100 transition-all duration-200 hover:scale-105 hover:shadow-md"
          >
            <CloudArrowUpIcon className="h-8 w-8 text-blue-600 mb-2 group-hover:scale-110 transition-transform" />
            <span className="text-sm font-medium text-blue-900">立即备份</span>
            <span className="text-xs text-blue-600 mt-1">数据安全</span>
          </button>
          <button
            onClick={() => console.log('重启服务')}
            className="group flex flex-col items-center p-4 bg-green-50 rounded-xl hover:bg-green-100 transition-all duration-200 hover:scale-105 hover:shadow-md"
          >
            <ArrowPathIcon className="h-8 w-8 text-green-600 mb-2 group-hover:scale-110 transition-transform" />
            <span className="text-sm font-medium text-green-900">重启服务</span>
            <span className="text-xs text-green-600 mt-1">服务管理</span>
          </button>
          <button
            onClick={() => console.log('查看日志')}
            className="group flex flex-col items-center p-4 bg-purple-50 rounded-xl hover:bg-purple-100 transition-all duration-200 hover:scale-105 hover:shadow-md"
          >
            <DocumentDuplicateIcon className="h-8 w-8 text-purple-600 mb-2 group-hover:scale-110 transition-transform" />
            <span className="text-sm font-medium text-purple-900">查看日志</span>
            <span className="text-xs text-purple-600 mt-1">故障排查</span>
          </button>
          <button
            onClick={() => console.log('性能优化')}
            className="group flex flex-col items-center p-4 bg-orange-50 rounded-xl hover:bg-orange-100 transition-all duration-200 hover:scale-105 hover:shadow-md"
          >
            <BoltIcon className="h-8 w-8 text-orange-600 mb-2 group-hover:scale-110 transition-transform" />
            <span className="text-sm font-medium text-orange-900">性能优化</span>
            <span className="text-xs text-orange-600 mt-1">系统调优</span>
          </button>
        </div>

        {/* 高级操作 */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <h5 className="text-sm font-medium text-gray-700 mb-3">高级操作</h5>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            <button className="flex items-center px-3 py-2 text-sm text-gray-700 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <ServerIcon className="h-4 w-4 mr-2" />
              健康检查
            </button>
            <button className="flex items-center px-3 py-2 text-sm text-gray-700 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <ShieldCheckIcon className="h-4 w-4 mr-2" />
              安全扫描
            </button>
            <button className="flex items-center px-3 py-2 text-sm text-gray-700 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <CogIcon className="h-4 w-4 mr-2" />
              系统设置
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
