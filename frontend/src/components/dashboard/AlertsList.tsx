import React from 'react';
import { useResolveAlertEvent } from '../../queries';

interface AlertsListProps {
  alerts: any[];
  loading: boolean;
}

export const AlertsList: React.FC<AlertsListProps> = ({ alerts, loading }) => {
  const resolveAlertMutation = useResolveAlertEvent();

  const handleResolveAlert = async (alertId: number) => {
    try {
      await resolveAlertMutation.mutateAsync({ id: alertId });
    } catch (error) {
      console.error('Failed to resolve alert:', error);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'text-red-600 bg-red-100';
      case 'warning':
        return 'text-yellow-600 bg-yellow-100';
      case 'info':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return '🚨';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      default:
        return '📢';
    }
  };

  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="space-y-3">
        {[1, 2, 3].map((i) => (
          <div key={i} className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    );
  }

  if (alerts.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-4xl mb-2">✅</div>
        <p className="text-gray-500">暂无活跃告警</p>
        <p className="text-sm text-gray-400 mt-1">系统运行正常</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {alerts.map((alert) => (
        <div
          key={alert.id}
          className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
        >
          {/* 告警头部 */}
          <div className="flex items-start justify-between mb-2">
            <div className="flex items-center">
              <span className="text-lg mr-2">
                {getSeverityIcon(alert.severity)}
              </span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(alert.severity)}`}>
                {alert.severity}
              </span>
            </div>
            <span className="text-xs text-gray-500">
              {formatTime(alert.start_time)}
            </span>
          </div>

          {/* 告警消息 */}
          <p className="text-sm text-gray-900 mb-2 line-clamp-2">
            {alert.message}
          </p>

          {/* 数据库信息 */}
          {alert.database && (
            <p className="text-xs text-gray-600 mb-3">
              数据库: {alert.database.name} ({alert.database.type})
            </p>
          )}

          {/* 告警详情 */}
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="space-x-4">
              <span>指标: {alert.metric_type}</span>
              <span>阈值: {alert.threshold}</span>
              <span>当前值: {alert.value}</span>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="mt-3 flex justify-end">
            <button
              onClick={() => handleResolveAlert(alert.id)}
              disabled={resolveAlertMutation.isPending}
              className="text-xs bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-3 py-1 rounded transition-colors"
            >
              {resolveAlertMutation.isPending ? '处理中...' : '标记已解决'}
            </button>
          </div>
        </div>
      ))}

      {/* 查看更多 */}
      {alerts.length >= 10 && (
        <div className="text-center pt-4">
          <button className="text-sm text-blue-600 hover:text-blue-700">
            查看所有告警 →
          </button>
        </div>
      )}
    </div>
  );
};
