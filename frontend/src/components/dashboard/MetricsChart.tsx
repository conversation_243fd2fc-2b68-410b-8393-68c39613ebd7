import React from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';
import { useMetricHistory } from '../../queries';

interface MetricsChartProps {
  databaseId?: number;
  metrics?: any[];
  loading?: boolean;
}

export const MetricsChart: React.FC<MetricsChartProps> = ({
  databaseId,
  metrics: _metrics = [],
  loading = false,
}) => {
  // 获取历史数据（最近1小时）
  const endTime = new Date();
  const startTime = new Date(endTime.getTime() - 60 * 60 * 1000); // 1小时前

  const {
    data: historyResponse,
    isLoading: historyLoading
  } = useMetricHistory(databaseId || 0, {
    start_time: startTime.toISOString(),
    end_time: endTime.toISOString(),
    interval: '5m', // 5分钟间隔
  }, {
    enabled: !!databaseId,
    queryKey: ['metric-history', databaseId, startTime.toISOString(), endTime.toISOString()],
  });

  const historyData = historyResponse?.data || [];

  // 处理图表数据
  const processChartData = () => {
    if (!historyData.length) {
      // 如果没有历史数据，生成模拟数据用于演示
      const data = [];
      const now = new Date();

      for (let i = 11; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 5 * 60 * 1000); // 5分钟间隔
        data.push({
          time: time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
          cpu_usage: Math.random() * 80 + 10,
          memory_usage: Math.random() * 70 + 20,
          connections: Math.floor(Math.random() * 100 + 50),
          qps: Math.floor(Math.random() * 1000 + 200),
        });
      }

      return data;
    }

    // 按时间戳分组
    const groupedData: Record<string, any> = {};

    historyData.forEach((metric: any) => {
      const timestamp = new Date(metric.timestamp).toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
      });

      if (!groupedData[timestamp]) {
        groupedData[timestamp] = { time: timestamp };
      }

      groupedData[timestamp][metric.metric_type] = parseFloat(metric.value);
    });

    return Object.values(groupedData).sort((a, b) =>
      new Date(`2000/01/01 ${a.time}`).getTime() - new Date(`2000/01/01 ${b.time}`).getTime()
    );
  };

  const data = processChartData();

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-xl shadow-lg">
          <p className="text-sm font-medium text-gray-900 mb-2">{`时间: ${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {`${entry.name}: ${entry.value.toFixed(1)}${
                entry.dataKey.includes('usage') ? '%' :
                entry.dataKey === 'connections' ? '' :
                entry.dataKey === 'qps' ? ' QPS' : ''
              }`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (loading || historyLoading) {
    return (
      <div className="h-80 flex justify-center items-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
        >
          <defs>
            <linearGradient id="cpuGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.1}/>
              <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
            </linearGradient>
            <linearGradient id="memoryGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#10b981" stopOpacity={0.1}/>
              <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
            </linearGradient>
            <linearGradient id="connectionsGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#f59e0b" stopOpacity={0.1}/>
              <stop offset="95%" stopColor="#f59e0b" stopOpacity={0}/>
            </linearGradient>
          </defs>
          <CartesianGrid
            strokeDasharray="3 3"
            stroke="#f3f4f6"
            horizontal={true}
            vertical={false}
          />
          <XAxis
            dataKey="time"
            tick={{ fontSize: 12, fill: '#6b7280' }}
            tickLine={false}
            axisLine={false}
            interval="preserveStartEnd"
          />
          <YAxis
            tick={{ fontSize: 12, fill: '#6b7280' }}
            tickLine={false}
            axisLine={false}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend
            wrapperStyle={{ paddingTop: '20px' }}
            iconType="circle"
          />
          {/* CPU使用率 */}
          {data.some(d => d.cpu_usage !== undefined) && (
            <Line
              type="monotone"
              dataKey="cpu_usage"
              stroke="#3b82f6"
              strokeWidth={3}
              name="CPU使用率"
              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2, fill: '#ffffff' }}
            />
          )}

          {/* 内存使用率 */}
          {data.some(d => d.memory_usage !== undefined) && (
            <Line
              type="monotone"
              dataKey="memory_usage"
              stroke="#10b981"
              strokeWidth={3}
              name="内存使用率"
              dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: '#10b981', strokeWidth: 2, fill: '#ffffff' }}
            />
          )}

          {/* 连接数 */}
          {data.some(d => d.connections !== undefined) && (
            <Line
              type="monotone"
              dataKey="connections"
              stroke="#f59e0b"
              strokeWidth={3}
              name="连接数"
              dot={{ fill: '#f59e0b', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: '#f59e0b', strokeWidth: 2, fill: '#ffffff' }}
            />
          )}

          {/* QPS */}
          {data.some(d => d.qps !== undefined) && (
            <Line
              type="monotone"
              dataKey="qps"
              stroke="#ef4444"
              strokeWidth={3}
              name="QPS"
              dot={{ fill: '#ef4444', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: '#ef4444', strokeWidth: 2, fill: '#ffffff' }}
            />
          )}
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};
