import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  <PERSON>Axis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  AreaChart,
  Area,
  BarChart,
  Bar
} from 'recharts';
import {
  PlayIcon,
  PauseIcon,
  ArrowPathIcon,
  ChartBarIcon,
  CpuChipIcon,
  CircleStackIcon,
  ClockIcon,
  ServerIcon,
  BoltIcon
} from '@heroicons/react/24/outline';

interface MetricDataPoint {
  timestamp: string;
  time: string;
  cpu_usage: number;
  memory_usage: number;
  connections: number;
  response_time: number;
  qps: number;
  disk_usage: number;
  network_io: number;
  disk_io: number;
  cache_hit_rate: number;
  slow_queries: number;
}

interface RealtimeMonitoringChartsProps {
  databaseId?: number;
  autoRefresh?: boolean;
  refreshInterval?: number;
  onRefresh?: () => void;
  selectedMetric?: 'overview' | 'cpu' | 'memory' | 'connections' | 'performance' | 'io' | 'cache';

}

export const RealtimeMonitoringCharts: React.FC<RealtimeMonitoringChartsProps> = ({
  databaseId,
  autoRefresh = true,
  refreshInterval = 5000,
  onRefresh,
  selectedMetric: propSelectedMetric,
}) => {
  const [data, setData] = useState<MetricDataPoint[]>([]);
  const [isRealtime, setIsRealtime] = useState(autoRefresh);
  const [selectedMetric, setSelectedMetric] = useState<'overview' | 'cpu' | 'memory' | 'connections' | 'performance' | 'io' | 'cache'>(propSelectedMetric || 'overview');
  const [loading, setLoading] = useState(false);

  // 生成模拟实时数据
  const generateRealtimeData = (): MetricDataPoint => {
    const now = new Date();
    const baseTime = now.getTime();

    // 模拟一些波动的数据 - 更真实的企业级数据模式
    const cpuBase = 45 + Math.sin(baseTime / 60000) * 20; // 基于时间的正弦波动
    const memoryBase = 60 + Math.cos(baseTime / 80000) * 15;
    const connectionsBase = 80 + Math.sin(baseTime / 40000) * 30;
    const responseTimeBase = 50 + Math.cos(baseTime / 30000) * 20;
    const networkBase = 30 + Math.sin(baseTime / 45000) * 25;
    const diskIOBase = 40 + Math.cos(baseTime / 55000) * 20;
    const cacheBase = 85 + Math.sin(baseTime / 70000) * 10; // 缓存命中率通常较高

    return {
      timestamp: now.toISOString(),
      time: now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', second: '2-digit' }),
      cpu_usage: Math.max(0, Math.min(100, cpuBase + (Math.random() - 0.5) * 10)),
      memory_usage: Math.max(0, Math.min(100, memoryBase + (Math.random() - 0.5) * 8)),
      connections: Math.max(0, connectionsBase + (Math.random() - 0.5) * 20),
      response_time: Math.max(0, responseTimeBase + (Math.random() - 0.5) * 15),
      qps: Math.max(0, 100 + (Math.random() - 0.5) * 50),
      disk_usage: Math.max(0, Math.min(100, 70 + (Math.random() - 0.5) * 5)),
      network_io: Math.max(0, Math.min(100, networkBase + (Math.random() - 0.5) * 12)),
      disk_io: Math.max(0, Math.min(100, diskIOBase + (Math.random() - 0.5) * 15)),
      cache_hit_rate: Math.max(0, Math.min(100, cacheBase + (Math.random() - 0.5) * 8)),
      slow_queries: Math.max(0, Math.floor(Math.random() * 5)) // 慢查询数量
    };
  };

  // 初始化数据
  useEffect(() => {
    const initialData: MetricDataPoint[] = [];
    const now = new Date();
    
    // 生成最近30个数据点（2.5分钟的历史数据）
    for (let i = 29; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 5000); // 每5秒一个点
      const baseTime = time.getTime();
      
      const cpuBase = 45 + Math.sin(baseTime / 60000) * 20;
      const memoryBase = 60 + Math.cos(baseTime / 80000) * 15;
      const connectionsBase = 80 + Math.sin(baseTime / 40000) * 30;
      const responseTimeBase = 50 + Math.cos(baseTime / 30000) * 20;
      const networkBase = 30 + Math.sin(baseTime / 45000) * 25;
      const diskIOBase = 40 + Math.cos(baseTime / 55000) * 20;
      const cacheBase = 85 + Math.sin(baseTime / 70000) * 10;

      initialData.push({
        timestamp: time.toISOString(),
        time: time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', second: '2-digit' }),
        cpu_usage: Math.max(0, Math.min(100, cpuBase + (Math.random() - 0.5) * 10)),
        memory_usage: Math.max(0, Math.min(100, memoryBase + (Math.random() - 0.5) * 8)),
        connections: Math.max(0, connectionsBase + (Math.random() - 0.5) * 20),
        response_time: Math.max(0, responseTimeBase + (Math.random() - 0.5) * 15),
        qps: Math.max(0, 100 + (Math.random() - 0.5) * 50),
        disk_usage: Math.max(0, Math.min(100, 70 + (Math.random() - 0.5) * 5)),
        network_io: Math.max(0, Math.min(100, networkBase + (Math.random() - 0.5) * 12)),
        disk_io: Math.max(0, Math.min(100, diskIOBase + (Math.random() - 0.5) * 15)),
        cache_hit_rate: Math.max(0, Math.min(100, cacheBase + (Math.random() - 0.5) * 8)),
        slow_queries: Math.max(0, Math.floor(Math.random() * 5))
      });
    }
    
    setData(initialData);
  }, [databaseId]);

  // 实时数据更新
  useEffect(() => {
    if (!isRealtime) return;

    const interval = setInterval(() => {
      setData(prevData => {
        const newData = [...prevData];
        newData.push(generateRealtimeData());
        
        // 保持最多30个数据点
        if (newData.length > 30) {
          newData.shift();
        }
        
        return newData;
      });
      
      onRefresh?.();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [isRealtime, refreshInterval, onRefresh]);

  // 手动刷新
  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      setData(prevData => {
        const newData = [...prevData];
        newData.push(generateRealtimeData());
        
        if (newData.length > 30) {
          newData.shift();
        }
        
        return newData;
      });
      setLoading(false);
      onRefresh?.();
    }, 500);
  };

  // 切换实时模式
  const toggleRealtime = () => {
    setIsRealtime(!isRealtime);
  };

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900 mb-2">{`时间: ${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {`${entry.name}: ${entry.value.toFixed(1)}${
                entry.dataKey.includes('usage') || entry.dataKey.includes('rate') ? '%' :
                entry.dataKey === 'connections' ? '' :
                entry.dataKey === 'response_time' ? 'ms' :
                entry.dataKey === 'qps' ? ' QPS' :
                entry.dataKey === 'slow_queries' ? ' 个' :
                entry.dataKey.includes('io') ? ' MB/s' : ''
              }`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };



  // 指标选项
  const metricOptions = [
    { key: 'overview', label: '总览', icon: ChartBarIcon },
    { key: 'cpu', label: 'CPU', icon: CpuChipIcon },
    { key: 'memory', label: '内存', icon: CircleStackIcon },
    { key: 'connections', label: '连接', icon: CircleStackIcon },
    { key: 'performance', label: '性能', icon: ClockIcon },
    { key: 'io', label: 'I/O', icon: ServerIcon },
    { key: 'cache', label: '缓存', icon: BoltIcon }
  ];

  return (
    <div className="space-y-6">
      {/* 控制面板 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {databaseId && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                数据库 #{databaseId}
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-3">
            {/* 指标选择 */}
            <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
              {metricOptions.map((option) => {
                const Icon = option.icon;
                return (
                  <button
                    key={option.key}
                    onClick={() => setSelectedMetric(option.key as any)}
                    className={`
                      inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-md transition-colors
                      ${selectedMetric === option.key
                        ? 'bg-white text-blue-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                      }
                    `}
                  >
                    <Icon className="h-4 w-4 mr-1" />
                    {option.label}
                  </button>
                );
              })}
            </div>

            {/* 控制按钮 */}
            <div className="flex items-center space-x-2">
              <button
                onClick={toggleRealtime}
                className={`
                  inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-lg transition-colors
                  ${isRealtime
                    ? 'bg-green-100 text-green-700 hover:bg-green-200'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }
                `}
              >
                {isRealtime ? (
                  <>
                    <PauseIcon className="h-4 w-4 mr-1" />
                    暂停
                  </>
                ) : (
                  <>
                    <PlayIcon className="h-4 w-4 mr-1" />
                    开始
                  </>
                )}
              </button>
              
              <button
                onClick={handleRefresh}
                disabled={loading}
                className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
              >
                <ArrowPathIcon className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
                刷新
              </button>
            </div>
          </div>
        </div>

        {/* 实时状态指示 */}
        {isRealtime && (
          <div className="mt-3 flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-green-600 font-medium">实时监控中</span>
            <span className="text-xs text-gray-500">每{refreshInterval/1000}秒更新</span>
          </div>
        )}
      </div>

      {/* DataDog风格实时指标概览 - 移除，因为已在顶部指标条展示 */}

      {/* DataDog风格图表区域 - 主要展示空间 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        {selectedMetric === 'overview' && (
          <div className="space-y-4">
            <div className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                  <defs>
                    <linearGradient id="cpuGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.1}/>
                      <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                    </linearGradient>
                    <linearGradient id="memoryGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#10b981" stopOpacity={0.1}/>
                      <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
                  <XAxis
                    dataKey="time"
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    tickLine={false}
                    axisLine={false}
                    interval="preserveStartEnd"
                  />
                  <YAxis
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    tickLine={false}
                    axisLine={false}
                    domain={[0, 100]}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="cpu_usage"
                    stroke="#3b82f6"
                    strokeWidth={2}
                    name="CPU使用率"
                    dot={false}
                    activeDot={{ r: 4, stroke: '#3b82f6', strokeWidth: 2, fill: '#ffffff' }}
                  />
                  <Line
                    type="monotone"
                    dataKey="memory_usage"
                    stroke="#10b981"
                    strokeWidth={2}
                    name="内存使用率"
                    dot={false}
                    activeDot={{ r: 4, stroke: '#10b981', strokeWidth: 2, fill: '#ffffff' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}

        {selectedMetric === 'cpu' && (
          <div className="space-y-4">
            <div className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                  <defs>
                    <linearGradient id="cpuAreaGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                      <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
                  <XAxis
                    dataKey="time"
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    tickLine={false}
                    axisLine={false}
                    domain={[0, 100]}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Area
                    type="monotone"
                    dataKey="cpu_usage"
                    stroke="#3b82f6"
                    strokeWidth={3}
                    fill="url(#cpuAreaGradient)"
                    name="CPU使用率"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}

        {selectedMetric === 'memory' && (
          <div className="space-y-4">
            <div className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                  <defs>
                    <linearGradient id="memoryAreaGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#10b981" stopOpacity={0.3}/>
                      <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
                  <XAxis
                    dataKey="time"
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    tickLine={false}
                    axisLine={false}
                    domain={[0, 100]}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Area
                    type="monotone"
                    dataKey="memory_usage"
                    stroke="#10b981"
                    strokeWidth={3}
                    fill="url(#memoryAreaGradient)"
                    name="内存使用率"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}

        {selectedMetric === 'connections' && (
          <div className="space-y-4">
            <div className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
                  <XAxis
                    dataKey="time"
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar
                    dataKey="connections"
                    fill="#8b5cf6"
                    name="活跃连接"
                    radius={[2, 2, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}

        {selectedMetric === 'performance' && (
          <div className="space-y-4">
            <div className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
                  <XAxis
                    dataKey="time"
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    yAxisId="left"
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    yAxisId="right"
                    orientation="right"
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Line
                    yAxisId="left"
                    type="monotone"
                    dataKey="response_time"
                    stroke="#f59e0b"
                    strokeWidth={2}
                    name="响应时间(ms)"
                    dot={false}
                    activeDot={{ r: 4, stroke: '#f59e0b', strokeWidth: 2, fill: '#ffffff' }}
                  />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="qps"
                    stroke="#ef4444"
                    strokeWidth={2}
                    name="QPS"
                    dot={false}
                    activeDot={{ r: 4, stroke: '#ef4444', strokeWidth: 2, fill: '#ffffff' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}

        {selectedMetric === 'io' && (
          <div className="space-y-4">
            <div className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                  <defs>
                    <linearGradient id="networkGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#06b6d4" stopOpacity={0.1}/>
                      <stop offset="95%" stopColor="#06b6d4" stopOpacity={0}/>
                    </linearGradient>
                    <linearGradient id="diskGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.1}/>
                      <stop offset="95%" stopColor="#8b5cf6" stopOpacity={0}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
                  <XAxis
                    dataKey="time"
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    tickLine={false}
                    axisLine={false}
                    domain={[0, 100]}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="network_io"
                    stroke="#06b6d4"
                    strokeWidth={2}
                    name="网络I/O"
                    dot={false}
                    activeDot={{ r: 4, stroke: '#06b6d4', strokeWidth: 2, fill: '#ffffff' }}
                  />
                  <Line
                    type="monotone"
                    dataKey="disk_io"
                    stroke="#8b5cf6"
                    strokeWidth={2}
                    name="磁盘I/O"
                    dot={false}
                    activeDot={{ r: 4, stroke: '#8b5cf6', strokeWidth: 2, fill: '#ffffff' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}

        {selectedMetric === 'cache' && (
          <div className="space-y-4">
            <div className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
                  <XAxis
                    dataKey="time"
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    yAxisId="left"
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    tickLine={false}
                    axisLine={false}
                    domain={[0, 100]}
                  />
                  <YAxis
                    yAxisId="right"
                    orientation="right"
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    tickLine={false}
                    axisLine={false}
                    domain={[0, 10]}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Line
                    yAxisId="left"
                    type="monotone"
                    dataKey="cache_hit_rate"
                    stroke="#10b981"
                    strokeWidth={3}
                    name="缓存命中率"
                    dot={false}
                    activeDot={{ r: 4, stroke: '#10b981', strokeWidth: 2, fill: '#ffffff' }}
                  />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="slow_queries"
                    stroke="#f59e0b"
                    strokeWidth={2}
                    name="慢查询数"
                    dot={false}
                    activeDot={{ r: 4, stroke: '#f59e0b', strokeWidth: 2, fill: '#ffffff' }}
                  />
                  {/* 添加告警阈值线 */}
                  <Line
                    yAxisId="left"
                    type="monotone"
                    dataKey={() => 80}
                    stroke="#ef4444"
                    strokeWidth={1}
                    strokeDasharray="5 5"
                    name="告警阈值"
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// 简化版图表组件，用于响应式网格
export const CompactRealtimeChart: React.FC<{
  selectedMetric: 'overview' | 'cpu' | 'memory' | 'connections' | 'performance' | 'io' | 'cache';
  databaseId?: number;
}> = ({ selectedMetric, databaseId }) => {
  const [data, setData] = useState<MetricDataPoint[]>([]);

  // 生成模拟数据
  useEffect(() => {
    const generateData = () => {
      const now = new Date();
      return Array.from({ length: 20 }, (_, i) => {
        const time = new Date(now.getTime() - (19 - i) * 60000);
        return {
          timestamp: time.toISOString(),
          time: time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
          cpu_usage: Math.max(0, Math.min(100, 45 + (Math.random() - 0.5) * 20)),
          memory_usage: Math.max(0, Math.min(100, 67 + (Math.random() - 0.5) * 15)),
          connections: Math.max(0, Math.floor(156 + (Math.random() - 0.5) * 40)),
          response_time: Math.max(0, 45 + (Math.random() - 0.5) * 30),
          qps: Math.max(0, Math.floor(1200 + (Math.random() - 0.5) * 400)),
          disk_usage: Math.max(0, Math.min(100, 23 + (Math.random() - 0.5) * 10)),
          network_io: Math.max(0, 50 + (Math.random() - 0.5) * 30),
          disk_io: Math.max(0, 30 + (Math.random() - 0.5) * 20),
          cache_hit_rate: Math.max(0, Math.min(100, 85 + (Math.random() - 0.5) * 10)),
          slow_queries: Math.max(0, Math.floor(Math.random() * 5))
        };
      });
    };

    setData(generateData());
  }, [databaseId]);

  const renderCompactChart = () => {
    switch (selectedMetric) {
      case 'overview':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ top: 10, right: 10, left: 10, bottom: 10 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
              <XAxis dataKey="time" tick={{ fontSize: 10 }} />
              <YAxis tick={{ fontSize: 10 }} />
              <Tooltip />
              <Line type="monotone" dataKey="cpu_usage" stroke="#3b82f6" strokeWidth={2} dot={false} />
              <Line type="monotone" dataKey="memory_usage" stroke="#10b981" strokeWidth={2} dot={false} />
            </LineChart>
          </ResponsiveContainer>
        );
      case 'cpu':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ top: 10, right: 10, left: 10, bottom: 10 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
              <XAxis dataKey="time" tick={{ fontSize: 10 }} />
              <YAxis tick={{ fontSize: 10 }} domain={[0, 100]} />
              <Tooltip />
              <Line type="monotone" dataKey="cpu_usage" stroke="#3b82f6" strokeWidth={3} dot={false} />
            </LineChart>
          </ResponsiveContainer>
        );
      case 'memory':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ top: 10, right: 10, left: 10, bottom: 10 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
              <XAxis dataKey="time" tick={{ fontSize: 10 }} />
              <YAxis tick={{ fontSize: 10 }} domain={[0, 100]} />
              <Tooltip />
              <Line type="monotone" dataKey="memory_usage" stroke="#10b981" strokeWidth={3} dot={false} />
            </LineChart>
          </ResponsiveContainer>
        );
      case 'connections':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data} margin={{ top: 10, right: 10, left: 10, bottom: 10 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
              <XAxis dataKey="time" tick={{ fontSize: 10 }} />
              <YAxis tick={{ fontSize: 10 }} />
              <Tooltip />
              <Bar dataKey="connections" fill="#8b5cf6" />
            </BarChart>
          </ResponsiveContainer>
        );
      default:
        return (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ top: 10, right: 10, left: 10, bottom: 10 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
              <XAxis dataKey="time" tick={{ fontSize: 10 }} />
              <YAxis tick={{ fontSize: 10 }} />
              <Tooltip />
              <Line type="monotone" dataKey="cpu_usage" stroke="#3b82f6" strokeWidth={2} dot={false} />
            </LineChart>
          </ResponsiveContainer>
        );
    }
  };

  return (
    <div className="h-full">
      {renderCompactChart()}
    </div>
  );
};
