import React from 'react';

interface DatabaseCardProps {
  database: {
    id: number;
    name: string;
    type: string;
    host: string;
    port: number;
    status: string;
    description?: string;
  };
  metrics: any[];
  isSelected: boolean;
  onClick: () => void;
}

export const DatabaseCard: React.FC<DatabaseCardProps> = ({
  database,
  metrics,
  isSelected,
  onClick,
}) => {
  // 获取最新指标
  const getLatestMetric = (type: string) => {
    const metric = metrics.find(m => m.metric_type === type);
    return metric ? parseFloat(metric.value) : 0;
  };

  const cpuUsage = getLatestMetric('cpu_usage');
  const memoryUsage = getLatestMetric('memory_usage');
  const connections = getLatestMetric('connections');

  // 状态颜色
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
      case 'online':
        return 'text-green-600 bg-green-100';
      case 'inactive':
      case 'offline':
        return 'text-red-600 bg-red-100';
      case 'warning':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // 数据库类型图标
  const getTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'postgresql':
        return '🐘';
      case 'mysql':
        return '🐬';
      case 'redis':
        return '🔴';
      case 'mongodb':
        return '🍃';
      default:
        return '🗄️';
    }
  };

  return (
    <div
      className={`
        border rounded-lg p-4 cursor-pointer transition-all duration-200
        ${isSelected 
          ? 'border-blue-500 bg-blue-50 shadow-md' 
          : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'
        }
      `}
      onClick={onClick}
    >
      {/* 头部信息 */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          <span className="text-2xl mr-2">{getTypeIcon(database.type)}</span>
          <div>
            <h3 className="font-medium text-gray-900">{database.name}</h3>
            <p className="text-sm text-gray-500">
              {database.host}:{database.port}
            </p>
          </div>
        </div>
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(database.status)}`}>
          {database.status}
        </span>
      </div>

      {/* 描述 */}
      {database.description && (
        <p className="text-sm text-gray-600 mb-3">{database.description}</p>
      )}

      {/* 指标信息 */}
      <div className="grid grid-cols-3 gap-3">
        <div className="text-center">
          <p className="text-xs text-gray-500">CPU</p>
          <p className={`text-sm font-medium ${cpuUsage > 80 ? 'text-red-600' : cpuUsage > 60 ? 'text-yellow-600' : 'text-green-600'}`}>
            {cpuUsage.toFixed(1)}%
          </p>
        </div>
        <div className="text-center">
          <p className="text-xs text-gray-500">内存</p>
          <p className={`text-sm font-medium ${memoryUsage > 80 ? 'text-red-600' : memoryUsage > 60 ? 'text-yellow-600' : 'text-green-600'}`}>
            {memoryUsage.toFixed(1)}%
          </p>
        </div>
        <div className="text-center">
          <p className="text-xs text-gray-500">连接数</p>
          <p className="text-sm font-medium text-blue-600">
            {connections}
          </p>
        </div>
      </div>

      {/* 选中指示器 */}
      {isSelected && (
        <div className="mt-3 text-center">
          <span className="text-xs text-blue-600 font-medium">
            点击查看详细监控 ↓
          </span>
        </div>
      )}
    </div>
  );
};
