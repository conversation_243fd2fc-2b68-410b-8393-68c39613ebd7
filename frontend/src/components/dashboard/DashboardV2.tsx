import React, { useState } from 'react';
import { 
  useDatabases, 
  useDatabaseStats, 
  useAlertEvents, 
  useAlertStats,
  useRealtimeMetrics 
} from '../../queries';
import { MetricsChart } from './MetricsChart';
import { AlertsList } from './AlertsList';
import { CoreMetricsCards } from './CoreMetricsCards';
import { EnhancedDatabaseList } from './EnhancedDatabaseList';

interface DashboardV2Props {
  user: {
    name: string;
    email: string;
  };
  onLogout: () => void;
}

export const DashboardV2: React.FC<DashboardV2Props> = ({ user, onLogout }) => {
  const [selectedDatabase, setSelectedDatabase] = useState<number | null>(null);

  // 使用React Query获取数据
  const {
    data: databasesResponse,
    isLoading: databasesLoading,
    refetch: refetchDatabases
  } = useDatabases({ page: 1, page_size: 20 });

  const { 
    data: statsResponse, 
    isLoading: statsLoading 
  } = useDatabaseStats();

  const { 
    data: alertEventsResponse, 
    isLoading: alertEventsLoading 
  } = useAlertEvents({ page: 1, page_size: 10, status: 'active' });

  const { 
    data: alertStatsResponse 
  } = useAlertStats();

  const { 
    data: realtimeMetricsResponse,
    isLoading: metricsLoading 
  } = useRealtimeMetrics();

  // 提取数据
  const databases = databasesResponse?.data?.items || [];
  const databaseStats = statsResponse?.data;
  const alertEvents = alertEventsResponse?.data?.items || [];
  const alertStats = alertStatsResponse?.data;
  const realtimeMetrics = realtimeMetricsResponse?.data || [];

  // 处理数据库选择
  const handleDatabaseSelect = (dbId: number) => {
    setSelectedDatabase(dbId === selectedDatabase ? null : dbId);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部导航 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                数据库监控平台
              </h1>
              <span className="ml-4 px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">
                实时数据
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">
                欢迎, {user.name}
              </span>
              <button
                onClick={onLogout}
                className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md text-sm"
              >
                退出登录
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 核心指标卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <CoreMetricsCards
            databaseStats={databaseStats}
            alertStats={alertStats}
            performanceStats={{
              total_connections: 156,
              avg_response_time: 45,
              connections_trend: 'up',
              response_time_trend: 'up',
              connections_change: 12,
              response_time_change: 5
            }}
            loading={statsLoading}
          />
        </div>

        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 数据库列表 */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow p-6">
              <EnhancedDatabaseList
                databases={databases.map((db: any) => ({
                  id: db.id,
                  name: db.name,
                  type: db.type,
                  host: db.host,
                  port: db.port,
                  status: db.status,
                  description: db.description,
                  version: db.version,
                  cpu: realtimeMetrics.find((m: any) => m.database_id === db.id && m.metric_type === 'cpu_usage')?.value || 0,
                  memory: realtimeMetrics.find((m: any) => m.database_id === db.id && m.metric_type === 'memory_usage')?.value || 0,
                  connections: realtimeMetrics.find((m: any) => m.database_id === db.id && m.metric_type === 'connections')?.value || 0,
                  maxConnections: 200,
                  uptime: '15天',
                  lastUpdate: '刚刚'
                }))}
                loading={databasesLoading}
                onDatabaseSelect={(database) => {
                  handleDatabaseSelect(database.id as number);
                }}
                onQuickAction={(action, database) => {
                  console.log('数据库快速操作:', action, database.name);
                  switch (action) {
                    case 'view':
                      handleDatabaseSelect(database.id as number);
                      break;
                    case 'monitor':
                      handleDatabaseSelect(database.id as number);
                      break;
                    case 'settings':
                      console.log('打开数据库设置:', database.name);
                      break;
                    case 'add':
                      console.log('添加新数据库');
                      break;
                    default:
                      console.log('未知操作:', action);
                  }
                }}
                onRefresh={refetchDatabases}
              />
            </div>

            {/* 指标图表 */}
            {selectedDatabase && (
              <div className="mt-8 bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-medium text-gray-900">
                    实时监控指标
                  </h2>
                </div>
                <div className="p-6">
                  <MetricsChart
                    databaseId={selectedDatabase}
                    metrics={realtimeMetrics.filter((m: any) => m.database_id === selectedDatabase)}
                    loading={metricsLoading}
                  />
                </div>
              </div>
            )}
          </div>

          {/* 告警列表 */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">
                  活跃告警
                </h2>
              </div>
              <div className="p-6">
                <AlertsList
                  alerts={alertEvents}
                  loading={alertEventsLoading}
                />
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};
