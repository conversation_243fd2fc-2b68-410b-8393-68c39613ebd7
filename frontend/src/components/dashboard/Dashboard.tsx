import React from 'react';
// import { useQuery } from '@tanstack/react-query';
import {
  ChartBarIcon,
  ServerIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { MetricsChart } from './MetricsChart';
import { DatabaseList } from './DatabaseList';
import { AlertPanel } from './AlertPanel';
import { DatabaseDetail } from '../database/DatabaseDetail';
// import { apiService } from '../../services/api';

interface DashboardProps {
  user: {
    name: string;
    email: string;
  };
  onLogout?: () => void;
}

export const Dashboard: React.FC<DashboardProps> = ({ user, onLogout }) => {
  // 页面状态管理
  const [currentView, setCurrentView] = React.useState<'dashboard' | 'database-detail'>('dashboard');
  const [selectedDatabase, setSelectedDatabase] = React.useState<any>(null);

  // 处理卡片点击事件
  const handleCardClick = (cardType: string) => {
    console.log(`点击了${cardType}卡片`);
    if (cardType === '数据库实例') {
      // 模拟选择第一个数据库
      const mockDatabase = {
        id: 'db-1',
        name: 'MySQL-prod',
        type: 'MySQL',
        status: 'running',
        host: '*************',
        port: 3306,
        version: '8.0.32'
      };
      setSelectedDatabase(mockDatabase);
      setCurrentView('database-detail');
    } else {
      alert(`您点击了${cardType}卡片，将跳转到详情页面`);
    }
  };

  // 处理时间范围切换
  const [selectedTimeRange, setSelectedTimeRange] = React.useState('24小时');
  const handleTimeRangeChange = (range: string) => {
    setSelectedTimeRange(range);
    console.log(`切换到${range}视图`);
  };

  // 处理刷新操作
  const [isRefreshing, setIsRefreshing] = React.useState(false);
  const handleRefresh = async () => {
    setIsRefreshing(true);
    console.log('刷新数据库列表...');
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsRefreshing(false);
    alert('数据库列表已刷新！');
  };

  // 处理添加数据库
  const handleAddDatabase = () => {
    console.log('添加新数据库...');
    alert('将打开添加数据库对话框');
    // 这里可以打开模态框或跳转到添加页面
  };

  // 返回仪表板
  const handleBackToDashboard = () => {
    setCurrentView('dashboard');
    setSelectedDatabase(null);
  };
  // 使用模拟数据进行MVP演示
  const mockDatabases = [
    {
      id: 'mysql-prod-001',
      name: 'MySQL-prod',
      type: 'MySQL',
      host: '*************',
      port: 3306,
      database: 'production',
      status: 'active'
    },
    {
      id: 'postgres-main-001',
      name: 'PostgreSQL-main',
      type: 'PostgreSQL',
      host: '*************',
      port: 5432,
      database: 'maindb',
      status: 'active'
    },
    {
      id: 'mysql-test-001',
      name: 'MySQL-test',
      type: 'MySQL',
      host: '*************',
      port: 3306,
      database: 'testdb',
      status: 'active'
    }
  ];

  const mockMetrics = {
    'mysql-prod-001': {
      instance_id: 'mysql-prod-001',
      timestamp: new Date().toISOString(),
      cpu: 75.2,
      memory: 68.5,
      connections: 85,
      qps: 450,
      slow_queries: 2
    },
    'postgres-main-001': {
      instance_id: 'postgres-main-001',
      timestamp: new Date().toISOString(),
      cpu: 45.8,
      memory: 82.1,
      connections: 200,
      qps: 280,
      slow_queries: 0
    },
    'mysql-test-001': {
      instance_id: 'mysql-test-001',
      timestamp: new Date().toISOString(),
      cpu: 25.3,
      memory: 35.7,
      connections: 12,
      qps: 120,
      slow_queries: 0
    }
  };

  const databases = mockDatabases;
  const metrics = mockMetrics;

  // 计算统计数据
  const totalConnections = Object.values(metrics).reduce((sum, metric) => sum + metric.connections, 0);
  // const _activeInstances = databases.filter(db => db.status === 'active').length;
  const alertCount = Object.values(metrics).filter(metric => metric.cpu > 80 || metric.memory > 80).length;
  const healthStatus = alertCount === 0 ? '良好' : alertCount < 3 ? '警告' : '严重';

  const stats = [
    {
      name: '数据库实例',
      value: databases.length.toString(),
      icon: ServerIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      name: '活跃连接',
      value: totalConnections.toString(),
      icon: ChartBarIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      name: '告警数量',
      value: alertCount.toString(),
      icon: ExclamationTriangleIcon,
      color: alertCount > 0 ? 'text-red-600' : 'text-green-600',
      bgColor: alertCount > 0 ? 'bg-red-100' : 'bg-green-100',
    },
    {
      name: '健康状态',
      value: healthStatus,
      icon: CheckCircleIcon,
      color: healthStatus === '良好' ? 'text-green-600' : 'text-yellow-600',
      bgColor: healthStatus === '良好' ? 'bg-green-100' : 'bg-yellow-100',
    },
  ];

  // 移除加载状态，因为我们使用模拟数据

  // 如果选择了数据库详情，显示详情页面
  if (currentView === 'database-detail' && selectedDatabase) {
    return (
      <DatabaseDetail
        database={selectedDatabase}
        onBack={handleBackToDashboard}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <header className="nav-header backdrop-blur-sm bg-white/95 sticky top-0 z-50">
        <div className="nav-container">
          <div className="nav-content">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
                  <ServerIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                    数据库监控平台
                  </h1>
                  <p className="text-sm text-gray-500">欢迎回来，{user.name}</p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={handleAddDatabase}
                className="btn-primary shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
              >
                <ServerIcon className="h-4 w-4 mr-2" />
                添加数据库
              </button>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                  <span className="text-white text-sm font-semibold">
                    {user.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className="hidden md:block">
                  <p className="text-sm font-medium text-gray-900">{user.name}</p>
                  <p className="text-xs text-gray-500">{user.email}</p>
                </div>
                <button
                  onClick={onLogout}
                  className="btn-secondary text-sm py-2 px-3 ml-2"
                  title="登出"
                >
                  登出
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div
              key={stat.name}
              className="stat-card group cursor-pointer transform hover:scale-105 transition-all duration-200"
              onClick={() => handleCardClick(stat.name)}
            >
              <div className="stat-card-content">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={`stat-icon ${stat.bgColor} group-hover:scale-110 transition-transform duration-200`}>
                      <stat.icon className={`h-6 w-6 ${stat.color}`} />
                    </div>
                    <div>
                      <p className="stat-label">{stat.name}</p>
                      <p className="stat-value">{stat.value}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xs text-gray-400">
                      {index === 0 && '总计'}
                      {index === 1 && '当前'}
                      {index === 2 && '活跃'}
                      {index === 3 && '状态'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Charts and Data */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8 mb-8">
          <div className="xl:col-span-2">
            <div className="chart-container">
              <div className="chart-header">
                <h3 className="chart-title">性能指标趋势</h3>
                <div className="flex space-x-2">
                  {['1小时', '24小时', '7天'].map((range) => (
                    <button
                      key={range}
                      onClick={() => handleTimeRangeChange(range)}
                      className={`text-xs py-1 px-3 transition-all duration-200 ${
                        selectedTimeRange === range
                          ? 'btn-primary'
                          : 'btn-secondary hover:bg-gray-200'
                      }`}
                    >
                      {range}
                    </button>
                  ))}
                </div>
              </div>
              <MetricsChart />
            </div>
          </div>
          <div>
            <div className="chart-container">
              <div className="chart-header">
                <h3 className="chart-title">实时告警</h3>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  系统正常
                </span>
              </div>
              <AlertPanel />
            </div>
          </div>
        </div>

        {/* Database List */}
        <div className="chart-container">
          <div className="chart-header">
            <h3 className="chart-title">数据库实例管理</h3>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">{databases.length} 个实例</span>
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className={`btn-secondary text-sm py-1 px-3 ${isRefreshing ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {isRefreshing ? '刷新中...' : '刷新'}
              </button>
            </div>
          </div>
          <DatabaseList databases={databases} metrics={metrics} />
        </div>
      </main>
    </div>
  );
};
