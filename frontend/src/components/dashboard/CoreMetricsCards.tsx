import React, { useState } from 'react';
import {
  CircleStackIcon,
  LinkIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  MinusIcon,
  InformationCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface CoreMetricsCardsProps {
  databaseStats?: {
    total: number;
    active: number;
    inactive: number;
    by_type: Record<string, number>;
  };
  alertStats?: {
    total_rules: number;
    active_rules: number;
    total_events: number;
    active_events: number;
    resolved_events: number;
    by_severity: Record<string, number>;
    by_status: Record<string, number>;
  };
  performanceStats?: {
    total_connections: number;
    avg_response_time: number;
    connections_trend: 'up' | 'down' | 'stable';
    response_time_trend: 'up' | 'down' | 'stable';
    connections_change: number;
    response_time_change: number;
  };
  loading?: boolean;
}

// 指标详情模态框组件
interface MetricDetailModalProps {
  metric: any;
  isOpen: boolean;
  onClose: () => void;
}

const MetricDetailModal: React.FC<MetricDetailModalProps> = ({ metric, isOpen, onClose }) => {
  if (!isOpen || !metric) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-96 overflow-y-auto">
        {/* 模态框头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${metric.iconBg}`}>
              <metric.icon className={`h-6 w-6 ${metric.iconColor}`} />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">{metric.title}</h3>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <XMarkIcon className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* 模态框内容 */}
        <div className="p-6 space-y-4">
          {/* 主要指标 */}
          <div className="text-center">
            <div className="text-3xl font-bold text-gray-900 mb-2">
              {metric.value.toLocaleString()}
              <span className="text-xl font-normal text-gray-500 ml-1">
                {metric.unit}
              </span>
            </div>
            <div className="text-sm text-gray-600">{metric.subtitle}</div>
          </div>

          {/* 趋势信息 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">趋势变化</span>
              <div className={`flex items-center space-x-1 ${getTrendColor(metric.trend, metric.isGoodWhenUp)}`}>
                {getTrendIcon(metric.trend)}
                <span className="text-sm font-medium">{metric.trendValue}</span>
                <span className="text-xs text-gray-500">({metric.trendLabel})</span>
              </div>
            </div>
          </div>

          {/* 详细信息 */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-700">详细信息</h4>
            {getMetricDetails(metric).map((detail, index) => (
              <div key={index} className="flex justify-between text-sm">
                <span className="text-gray-600">{detail.label}</span>
                <span className="font-medium text-gray-900">{detail.value}</span>
              </div>
            ))}
          </div>

          {/* 操作建议 */}
          {getMetricRecommendations(metric).length > 0 && (
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-700">操作建议</h4>
              <div className="space-y-2">
                {getMetricRecommendations(metric).map((rec, index) => (
                  <div key={index} className={`p-3 rounded-lg text-sm ${rec.type === 'warning' ? 'bg-yellow-50 text-yellow-800' : 'bg-blue-50 text-blue-800'}`}>
                    {rec.message}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// 获取指标详细信息
const getMetricDetails = (metric: any) => {
  switch (metric.id) {
    case 'total-databases':
      return [
        { label: '在线数据库', value: `${metric.value - 2} 个` },
        { label: '离线数据库', value: '2 个' },
        { label: '最近添加', value: '3天前' },
        { label: '平均负载', value: '65%' }
      ];
    case 'active-connections':
      return [
        { label: '最大连接数', value: '500' },
        { label: '连接池使用率', value: '31%' },
        { label: '平均连接时长', value: '2.3分钟' },
        { label: '空闲连接', value: '344' }
      ];
    case 'active-alerts':
      return [
        { label: '严重告警', value: '1 个' },
        { label: '警告告警', value: '2 个' },
        { label: '今日已解决', value: '5 个' },
        { label: '平均解决时间', value: '15分钟' }
      ];
    case 'avg-response-time':
      return [
        { label: '最快响应', value: '12ms' },
        { label: '最慢响应', value: '156ms' },
        { label: '95%分位数', value: '78ms' },
        { label: '99%分位数', value: '125ms' }
      ];
    default:
      return [];
  }
};

// 获取指标操作建议
const getMetricRecommendations = (metric: any) => {
  switch (metric.id) {
    case 'total-databases':
      return metric.value > 10 ? [
        { type: 'warning', message: '数据库实例较多，建议定期清理不活跃的实例' }
      ] : [];
    case 'active-connections':
      return metric.value > 400 ? [
        { type: 'warning', message: '连接数较高，建议检查连接池配置' }
      ] : [];
    case 'active-alerts':
      return metric.value > 0 ? [
        { type: 'warning', message: '存在活跃告警，建议及时处理' }
      ] : [
        { type: 'info', message: '系统运行正常，无活跃告警' }
      ];
    case 'avg-response-time':
      return metric.value > 100 ? [
        { type: 'warning', message: '响应时间较慢，建议检查查询性能' }
      ] : [];
    default:
      return [];
  }
};

// 获取趋势图标
const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
  switch (trend) {
    case 'up':
      return <ArrowUpIcon className="h-4 w-4" />;
    case 'down':
      return <ArrowDownIcon className="h-4 w-4" />;
    case 'stable':
      return <MinusIcon className="h-4 w-4" />;
    default:
      return <MinusIcon className="h-4 w-4" />;
  }
};

// 获取趋势颜色
const getTrendColor = (trend: 'up' | 'down' | 'stable', isGoodWhenUp: boolean = true) => {
  if (trend === 'stable') return 'text-gray-500';
  if (trend === 'up') return isGoodWhenUp ? 'text-green-500' : 'text-red-500';
  if (trend === 'down') return isGoodWhenUp ? 'text-red-500' : 'text-green-500';
  return 'text-gray-500';
};

export const CoreMetricsCards: React.FC<CoreMetricsCardsProps> = ({
  databaseStats,
  alertStats,
  performanceStats,
  loading = false
}) => {
  const [selectedMetric, setSelectedMetric] = useState<any>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  // 模拟性能数据（如果没有提供）
  const defaultPerformanceStats = {
    total_connections: 156,
    avg_response_time: 45,
    connections_trend: 'up' as const,
    response_time_trend: 'up' as const,
    connections_change: 12,
    response_time_change: 5
  };

  const perfStats = performanceStats || defaultPerformanceStats;

  // 处理指标点击事件
  const handleMetricClick = (metric: any) => {
    setSelectedMetric(metric);
    setShowDetailModal(true);
  };

  // 关闭详情模态框
  const handleCloseModal = () => {
    setShowDetailModal(false);
    setSelectedMetric(null);
  };

  // 核心指标配置
  const coreMetrics = [
    {
      id: 'total-databases',
      title: '总数据库',
      value: databaseStats?.total || 8,
      unit: '',
      subtitle: `${databaseStats?.active || 6} 个在线`,
      icon: CircleStackIcon,
      iconColor: 'text-blue-600',
      iconBg: 'bg-blue-100',
      trend: (databaseStats?.total && databaseStats.total > 6 ? 'up' : 'stable') as 'up' | 'down' | 'stable',
      trendValue: '+2',
      trendLabel: '本周',
      isGoodWhenUp: true
    },
    {
      id: 'active-connections',
      title: '活跃连接',
      value: perfStats.total_connections,
      unit: '',
      subtitle: `${perfStats.connections_change > 0 ? '+' : ''}${perfStats.connections_change} 今日`,
      icon: LinkIcon,
      iconColor: 'text-green-600',
      iconBg: 'bg-green-100',
      trend: perfStats.connections_trend,
      trendValue: `${perfStats.connections_change > 0 ? '+' : ''}${perfStats.connections_change}`,
      trendLabel: '今日',
      isGoodWhenUp: true
    },
    {
      id: 'active-alerts',
      title: '活跃告警',
      value: alertStats?.active_events || 3,
      unit: '',
      subtitle: `${alertStats?.total_events || 8} 个总告警`,
      icon: ExclamationTriangleIcon,
      iconColor: alertStats?.active_events && alertStats.active_events > 0 ? 'text-red-600' : 'text-green-600',
      iconBg: alertStats?.active_events && alertStats.active_events > 0 ? 'bg-red-100' : 'bg-green-100',
      trend: (alertStats?.active_events && alertStats.active_events > 2 ? 'up' : 'down') as 'up' | 'down' | 'stable',
      trendValue: '-2',
      trendLabel: '今日',
      isGoodWhenUp: false
    },
    {
      id: 'avg-response-time',
      title: '平均响应',
      value: perfStats.avg_response_time,
      unit: 'ms',
      subtitle: `${perfStats.response_time_change > 0 ? '+' : ''}${perfStats.response_time_change}ms 变化`,
      icon: ClockIcon,
      iconColor: perfStats.avg_response_time > 50 ? 'text-yellow-600' : 'text-green-600',
      iconBg: perfStats.avg_response_time > 50 ? 'bg-yellow-100' : 'bg-green-100',
      trend: perfStats.response_time_trend,
      trendValue: `${perfStats.response_time_change > 0 ? '+' : ''}${perfStats.response_time_change}ms`,
      trendLabel: '今日',
      isGoodWhenUp: false
    }
  ];

  if (loading) {
    return (
      <div className="grid grid-cols-4 gap-6">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="bg-white rounded-lg shadow-sm p-4 border border-gray-200 h-24 flex items-center">
            <div className="animate-pulse w-full">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-200 rounded"></div>
                <div className="flex-1">
                  <div className="h-3 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-5 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <>
      <div className="grid grid-cols-4 gap-6">
        {coreMetrics.map((metric) => {
          const Icon = metric.icon;
          const trendColor = getTrendColor(metric.trend, metric.isGoodWhenUp);

          return (
            <div
              key={metric.id}
              className="bg-white rounded-lg shadow-sm p-4 border border-gray-200 hover:shadow-md transition-all duration-200 hover:scale-105 relative group cursor-pointer h-24"
              onClick={() => handleMetricClick(metric)}
            >
              {/* DataDog风格紧凑指标条 */}
              <div className="h-16 flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {/* 图标 - 更小更紧凑 */}
                  <div className={`p-1.5 rounded ${metric.iconBg} transition-transform duration-200 group-hover:scale-110`}>
                    <Icon className={`h-4 w-4 ${metric.iconColor}`} />
                  </div>

                  {/* 指标信息 - DataDog风格紧凑版 */}
                  <div className="flex-1">
                    <p className="text-xs font-medium text-gray-500 mb-0.5">
                      {metric.title}
                    </p>
                    <div className="flex items-baseline space-x-1">
                      <span className="text-xl font-bold text-gray-900">
                        {metric.value.toLocaleString()}
                      </span>
                      {metric.unit && (
                        <span className="text-sm font-normal text-gray-500">
                          {metric.unit}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* 趋势指示器 - 更紧凑 */}
                <div className="flex items-center space-x-1">
                  <div className={`flex items-center space-x-0.5 ${trendColor}`}>
                    {getTrendIcon(metric.trend)}
                    <span className="text-xs font-medium">
                      {metric.trendValue}
                    </span>
                  </div>
                </div>

                {/* 详情按钮 */}
                <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <InformationCircleIcon className="h-3 w-3 text-gray-400" />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* 详情模态框 */}
      <MetricDetailModal
        metric={selectedMetric}
        isOpen={showDetailModal}
        onClose={handleCloseModal}
      />
    </>
  );
};
