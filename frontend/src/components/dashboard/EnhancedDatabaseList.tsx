import React, { useState } from 'react';
import {
  EyeIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ArrowPathIcon,
  PlayIcon,
  WrenchScrewdriverIcon
} from '@heroicons/react/24/outline';

interface DatabaseInstance {
  id: string | number;
  name: string;
  type: string;
  host: string;
  port: number;
  status: string;
  description?: string;
  version?: string;
  cpu?: number;
  memory?: number;
  connections?: number;
  maxConnections?: number;
  lastUpdate?: string;
  uptime?: string;
  size?: string;
}

interface EnhancedDatabaseListProps {
  databases: DatabaseInstance[];
  loading?: boolean;
  onDatabaseSelect?: (database: DatabaseInstance) => void;
  onQuickAction?: (action: string, database: DatabaseInstance) => void;
  onRefresh?: () => void;
}

export const EnhancedDatabaseList: React.FC<EnhancedDatabaseListProps> = ({
  databases,
  loading = false,
  onDatabaseSelect,
  onQuickAction,
  onRefresh
}) => {
  const [selectedDatabase, setSelectedDatabase] = useState<string | number | null>(null);

  // 获取数据库类型图标
  const getTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'postgresql':
        return '🐘';
      case 'mysql':
        return '🐬';
      case 'redis':
        return '🔴';
      case 'mongodb':
        return '🍃';
      case 'oracle':
        return '🔶';
      case 'sqlite':
        return '📦';
      default:
        return '🗄️';
    }
  };

  // 获取状态图标和颜色
  const getStatusInfo = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
      case 'online':
      case 'running':
      case 'normal':
        return {
          icon: CheckCircleIcon,
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          text: '运行中',
          dotColor: 'bg-green-500'
        };
      case 'warning':
        return {
          icon: ExclamationTriangleIcon,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-100',
          text: '警告',
          dotColor: 'bg-yellow-500'
        };
      case 'error':
      case 'offline':
      case 'inactive':
        return {
          icon: XCircleIcon,
          color: 'text-red-600',
          bgColor: 'bg-red-100',
          text: '离线',
          dotColor: 'bg-red-500'
        };
      case 'maintenance':
        return {
          icon: WrenchScrewdriverIcon,
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
          text: '维护中',
          dotColor: 'bg-blue-500'
        };
      default:
        return {
          icon: ClockIcon,
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          text: '未知',
          dotColor: 'bg-gray-500'
        };
    }
  };

  // 获取性能指标颜色
  const getMetricColor = (value: number) => {
    if (value >= 90) return 'text-red-600';
    if (value >= 80) return 'text-red-500';
    if (value >= 70) return 'text-yellow-600';
    if (value >= 60) return 'text-yellow-500';
    return 'text-green-600';
  };

  // 处理数据库选择
  const handleDatabaseClick = (database: DatabaseInstance) => {
    setSelectedDatabase(database.id === selectedDatabase ? null : database.id);
    if (onDatabaseSelect) {
      onDatabaseSelect(database);
    }
  };

  // 处理快速操作
  const handleQuickAction = (action: string, database: DatabaseInstance, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡
    if (onQuickAction) {
      onQuickAction(action, database);
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="animate-pulse">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="w-20 h-8 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (databases.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">🗄️</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">暂无数据库实例</h3>
        <p className="text-gray-500 mb-4">还没有配置任何数据库实例</p>
        <button
          onClick={() => onQuickAction?.('add', {} as DatabaseInstance)}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <PlayIcon className="h-4 w-4 mr-2" />
          添加数据库
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 头部操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <h3 className="text-lg font-semibold text-gray-900">数据库实例</h3>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {databases.length} 个实例
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={onRefresh}
            className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <ArrowPathIcon className="h-4 w-4 mr-1" />
            刷新
          </button>
          <button
            onClick={() => onQuickAction?.('add', {} as DatabaseInstance)}
            className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <PlayIcon className="h-4 w-4 mr-1" />
            添加
          </button>
        </div>
      </div>

      {/* 数据库列表 */}
      <div className="space-y-3">
        {databases.map((database) => {
          const statusInfo = getStatusInfo(database.status);
          const isSelected = selectedDatabase === database.id;

          return (
            <div
              key={database.id}
              className={`
                bg-white rounded-lg border transition-all duration-200 cursor-pointer
                ${isSelected 
                  ? 'border-blue-500 shadow-md bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
                }
              `}
              onClick={() => handleDatabaseClick(database)}
            >
              <div className="p-4">
                {/* 主要信息行 */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    {/* 数据库图标 */}
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center text-lg">
                        {getTypeIcon(database.type)}
                      </div>
                    </div>
                    
                    {/* 基本信息 */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <h4 className="text-sm font-semibold text-gray-900 truncate">
                          {database.name}
                        </h4>
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                          {database.type}
                        </span>
                      </div>
                      <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                        <span>{database.host}:{database.port}</span>
                        {database.version && <span>v{database.version}</span>}
                        {database.uptime && <span>运行 {database.uptime}</span>}
                      </div>
                    </div>
                  </div>

                  {/* 状态和快速操作 */}
                  <div className="flex items-center space-x-3">
                    {/* 状态指示器 */}
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${statusInfo.dotColor} ${
                        statusInfo.text === '运行中' ? 'animate-pulse' : ''
                      }`}></div>
                      <span className={`text-xs font-medium ${statusInfo.color}`}>
                        {statusInfo.text}
                      </span>
                    </div>

                    {/* 快速操作按钮 */}
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={(e) => handleQuickAction('view', database, e)}
                        className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                        title="查看详情"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={(e) => handleQuickAction('monitor', database, e)}
                        className="p-1.5 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded transition-colors"
                        title="性能监控"
                      >
                        <ChartBarIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={(e) => handleQuickAction('settings', database, e)}
                        className="p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded transition-colors"
                        title="设置"
                      >
                        <Cog6ToothIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>

                {/* 性能指标 */}
                {(database.cpu !== undefined || database.memory !== undefined || database.connections !== undefined) && (
                  <div className="grid grid-cols-3 gap-4 pt-3 border-t border-gray-100">
                    {database.cpu !== undefined && (
                      <div className="text-center">
                        <p className="text-xs text-gray-500 mb-1">CPU使用率</p>
                        <p className={`text-sm font-semibold ${getMetricColor(database.cpu)}`}>
                          {database.cpu.toFixed(1)}%
                        </p>
                      </div>
                    )}
                    {database.memory !== undefined && (
                      <div className="text-center">
                        <p className="text-xs text-gray-500 mb-1">内存使用率</p>
                        <p className={`text-sm font-semibold ${getMetricColor(database.memory)}`}>
                          {database.memory.toFixed(1)}%
                        </p>
                      </div>
                    )}
                    {database.connections !== undefined && (
                      <div className="text-center">
                        <p className="text-xs text-gray-500 mb-1">连接数</p>
                        <p className="text-sm font-semibold text-blue-600">
                          {database.connections}
                          {database.maxConnections && (
                            <span className="text-gray-400">/{database.maxConnections}</span>
                          )}
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {/* 描述信息 */}
                {database.description && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <p className="text-xs text-gray-600">{database.description}</p>
                  </div>
                )}

                {/* 选中状态指示 */}
                {isSelected && (
                  <div className="mt-3 pt-3 border-t border-blue-200">
                    <p className="text-xs text-blue-600 text-center font-medium">
                      点击上方按钮进行操作，或查看下方监控图表 ↓
                    </p>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
