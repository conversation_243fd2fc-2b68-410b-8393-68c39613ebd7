import React from 'react';
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  EyeIcon,
  Cog6ToothIcon,
} from '@heroicons/react/24/outline';

interface DatabaseInstance {
  id: string;
  name: string;
  type: 'MySQL' | 'PostgreSQL' | 'MongoDB' | 'Oracle';
  status: 'healthy' | 'warning' | 'error';
  host: string;
  port: number;
  connections: number;
  maxConnections: number;
  cpu: number;
  memory: number;
  lastUpdate: string;
}

// Mock database instances
const mockDatabases: DatabaseInstance[] = [
  {
    id: '1',
    name: 'MySQL-prod',
    type: 'MySQL',
    status: 'warning',
    host: '*************',
    port: 3306,
    connections: 85,
    maxConnections: 100,
    cpu: 75,
    memory: 68,
    lastUpdate: '刚刚',
  },
  {
    id: '2',
    name: 'PostgreSQL-main',
    type: 'PostgreSQL',
    status: 'error',
    host: '*************',
    port: 5432,
    connections: 200,
    maxConnections: 200,
    cpu: 45,
    memory: 82,
    lastUpdate: '1分钟前',
  },
  {
    id: '3',
    name: 'MySQL-test',
    type: 'MySQL',
    status: 'healthy',
    host: '*************',
    port: 3306,
    connections: 12,
    maxConnections: 50,
    cpu: 25,
    memory: 35,
    lastUpdate: '30秒前',
  },
];

const getStatusIcon = (status: DatabaseInstance['status']) => {
  switch (status) {
    case 'healthy':
      return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
    case 'warning':
      return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
    case 'error':
      return <XCircleIcon className="h-5 w-5 text-red-500" />;
  }
};

// const _getStatusText = (status: DatabaseInstance['status']) => {
//   switch (status) {
//     case 'healthy':
//       return '健康';
//     case 'warning':
//       return '警告';
//     case 'error':
//       return '错误';
//   }
// };

// const _getStatusColor = (status: DatabaseInstance['status']) => {
//   switch (status) {
//     case 'healthy':
//       return 'text-green-700 bg-green-100';
//     case 'warning':
//       return 'text-yellow-700 bg-yellow-100';
//     case 'error':
//       return 'text-red-700 bg-red-100';
//   }
// };

interface DatabaseListProps {
  databases?: any[];
  metrics?: Record<string, any>;
}

export const DatabaseList: React.FC<DatabaseListProps> = ({
  databases = mockDatabases,
  metrics = {}
}) => {
  return (
    <div className="table-container">
      <div className="overflow-x-auto">
        <table className="table">
          <thead className="table-header">
            <tr>
              <th className="table-header-cell">
                数据库实例
              </th>
              <th className="table-header-cell">
                状态
              </th>
              <th className="table-header-cell">
                连接数使用率
              </th>
              <th className="table-header-cell">
                资源使用率
              </th>
              <th className="table-header-cell">
                QPS
              </th>
              <th className="table-header-cell">
                最后更新
              </th>
              <th className="table-header-cell text-right">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="table-body">
            {databases.map((db) => {
              const dbMetrics = metrics[db.id];
              const cpu = dbMetrics?.cpu || 0;
              const memory = dbMetrics?.memory || 0;
              const connections = dbMetrics?.connections || 0;
              const qps = dbMetrics?.qps || 0;
              const maxConnections = dbMetrics?.custom?.max_connections || 100;
              const connectionUsage = (connections / maxConnections) * 100;

              return (
              <tr key={db.id} className="table-row">
                <td className="table-cell">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      db.status === 'active' ? 'bg-green-400' : 'bg-gray-400'
                    } animate-pulse`}></div>
                    <div>
                      <div className="text-sm font-semibold text-gray-900">
                        {db.name}
                      </div>
                      <div className="text-xs text-gray-500 flex items-center space-x-2">
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                          {db.type}
                        </span>
                        <span>{db.host}:{db.port}</span>
                      </div>
                    </div>
                  </div>
                </td>
                <td className="table-cell">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon('healthy')}
                    <span className="status-active">
                      运行中
                    </span>
                  </div>
                </td>
                <td className="table-cell">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">{connections} / {maxConnections}</span>
                      <span className="font-medium">{connectionUsage.toFixed(1)}%</span>
                    </div>
                    <div className="progress-bar">
                      <div
                        className={`progress-fill ${
                          connectionUsage > 80
                            ? 'bg-red-500'
                            : connectionUsage > 60
                            ? 'bg-yellow-500'
                            : 'bg-green-500'
                        }`}
                        style={{ width: `${connectionUsage}%` }}
                      ></div>
                    </div>
                  </div>
                </td>
                <td className="table-cell">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">CPU</span>
                      <span className="text-sm font-medium">{cpu.toFixed(1)}%</span>
                    </div>
                    <div className="progress-bar">
                      <div
                        className={`progress-fill ${
                          cpu > 80 ? 'bg-red-500' : cpu > 60 ? 'bg-yellow-500' : 'bg-blue-500'
                        }`}
                        style={{ width: `${cpu}%` }}
                      ></div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">内存</span>
                      <span className="text-sm font-medium">{memory.toFixed(1)}%</span>
                    </div>
                    <div className="progress-bar">
                      <div
                        className={`progress-fill ${
                          memory > 80 ? 'bg-red-500' : memory > 60 ? 'bg-yellow-500' : 'bg-purple-500'
                        }`}
                        style={{ width: `${memory}%` }}
                      ></div>
                    </div>
                  </div>
                </td>
                <td className="table-cell">
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-900">{qps}</div>
                    <div className="text-xs text-gray-500">查询/秒</div>
                  </div>
                </td>
                <td className="table-cell">
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-sm text-gray-500">刚刚</span>
                  </div>
                </td>
                <td className="table-cell">
                  <div className="flex justify-end space-x-2">
                    <button className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors duration-200">
                      <EyeIcon className="h-4 w-4" />
                    </button>
                    <button className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg transition-colors duration-200">
                      <Cog6ToothIcon className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};
