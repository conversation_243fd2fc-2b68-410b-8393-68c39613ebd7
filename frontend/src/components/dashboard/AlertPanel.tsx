import React from 'react';
import {
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';

interface Alert {
  id: string;
  type: 'warning' | 'error' | 'info';
  title: string;
  message: string;
  timestamp: string;
  database: string;
}

// Mock alerts data
const mockAlerts: Alert[] = [
  {
    id: '1',
    type: 'warning',
    title: 'CPU使用率过高',
    message: 'MySQL-prod 实例CPU使用率达到85%，建议检查慢查询',
    timestamp: '2分钟前',
    database: 'MySQL-prod',
  },
  {
    id: '2',
    type: 'error',
    title: '连接数超限',
    message: 'PostgreSQL-main 连接数达到最大值，新连接被拒绝',
    timestamp: '5分钟前',
    database: 'PostgreSQL-main',
  },
  {
    id: '3',
    type: 'info',
    title: '备份完成',
    message: 'MySQL-test 数据库备份已成功完成',
    timestamp: '1小时前',
    database: 'MySQL-test',
  },
];

const getAlertIcon = (type: Alert['type']) => {
  switch (type) {
    case 'warning':
      return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
    case 'error':
      return <XCircleIcon className="h-5 w-5 text-red-500" />;
    case 'info':
      return <InformationCircleIcon className="h-5 w-5 text-blue-500" />;
  }
};

// const _getAlertBgColor = (type: Alert['type']) => {
//   switch (type) {
//     case 'warning':
//       return 'bg-yellow-50 border-yellow-200';
//     case 'error':
//       return 'bg-red-50 border-red-200';
//     case 'info':
//       return 'bg-blue-50 border-blue-200';
//   }
// };

export const AlertPanel: React.FC = () => {
  const [alerts, setAlerts] = React.useState<Alert[]>(mockAlerts);

  // 处理告警操作
  const handleAlertAction = (alertId: string, action: 'resolve' | 'ignore' | 'details') => {
    switch (action) {
      case 'resolve':
        setAlerts(prev => prev.filter(alert => alert.id !== alertId));
        console.log(`告警 ${alertId} 已处理`);
        break;
      case 'ignore':
        setAlerts(prev => prev.filter(alert => alert.id !== alertId));
        console.log(`告警 ${alertId} 已忽略`);
        break;
      case 'details':
        console.log(`查看告警 ${alertId} 详情`);
        alert('将打开告警详情页面');
        break;
    }
  };

  // 查看所有告警
  const handleViewAllAlerts = () => {
    console.log('查看所有告警');
    alert('将跳转到告警管理页面');
  };

  return (
    <div className="space-y-4">
      {alerts.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
            <InformationCircleIcon className="h-8 w-8 text-green-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">系统运行正常</h3>
          <p className="text-gray-500">当前没有需要关注的告警信息</p>
        </div>
      ) : (
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {alerts.map((alert, index) => (
            <div
              key={alert.id}
              className={`relative p-4 rounded-xl border-l-4 bg-white shadow-sm hover:shadow-md transition-shadow duration-200 ${
                alert.type === 'error'
                  ? 'border-l-red-500 bg-red-50/50'
                  : alert.type === 'warning'
                  ? 'border-l-yellow-500 bg-yellow-50/50'
                  : 'border-l-blue-500 bg-blue-50/50'
              }`}
              style={{
                animationDelay: `${index * 100}ms`,
                animation: 'fadeInUp 0.5s ease-out forwards'
              }}
            >
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-0.5">
                  {getAlertIcon(alert.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="text-sm font-semibold text-gray-900 mb-1">
                        {alert.title}
                      </h4>
                      <p className="text-sm text-gray-600 leading-relaxed">
                        {alert.message}
                      </p>
                    </div>
                    <div className="flex flex-col items-end space-y-2 ml-4">
                      <span className="text-xs text-gray-500 whitespace-nowrap">
                        {alert.timestamp}
                      </span>
                      <button
                        onClick={() => handleAlertAction(alert.id, 'resolve')}
                        className="text-xs text-blue-600 hover:text-blue-800 font-medium hover:bg-blue-50 px-2 py-1 rounded transition-colors duration-200"
                      >
                        处理
                      </button>
                    </div>
                  </div>
                  <div className="flex items-center justify-between mt-3">
                    <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                      alert.type === 'error'
                        ? 'bg-red-100 text-red-800'
                        : alert.type === 'warning'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-blue-100 text-blue-800'
                    }`}>
                      {alert.database}
                    </span>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleAlertAction(alert.id, 'ignore')}
                        className="text-xs text-gray-500 hover:text-gray-700 px-2 py-1 rounded hover:bg-gray-100 transition-colors duration-200"
                      >
                        忽略
                      </button>
                      <button
                        onClick={() => handleAlertAction(alert.id, 'details')}
                        className="text-xs text-blue-600 hover:text-blue-800 px-2 py-1 rounded hover:bg-blue-50 transition-colors duration-200"
                      >
                        详情
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {alerts.length > 0 && (
        <div className="border-t border-gray-200 pt-4">
          <button
            onClick={handleViewAllAlerts}
            className="w-full text-sm text-blue-600 hover:text-blue-800 font-medium py-2 px-4 rounded-lg hover:bg-blue-50 transition-colors duration-200"
          >
            查看所有告警 ({alerts.length})
          </button>
        </div>
      )}
    </div>
  );
};
