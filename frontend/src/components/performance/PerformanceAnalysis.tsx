import React, { useState } from 'react';
import {
  ChartBarIcon,
  ClockIcon,
  CpuChipIcon,
  CircleStackIcon,
  MagnifyingGlassIcon,
  ArrowDownIcon,
  ArrowUpIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  LightBulbIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

// 慢查询接口
interface SlowQuery {
  id: string;
  query: string;
  database_name: string;
  execution_time: number;
  timestamp: string;
  rows_examined: number;
  rows_sent: number;
  lock_time: number;
  user: string;
  host: string;
  query_type: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE';
  frequency: number;
  avg_execution_time: number;
}

// 性能指标接口
interface PerformanceMetric {
  id: string;
  database_name: string;
  metric_name: string;
  current_value: number;
  previous_value: number;
  unit: string;
  timestamp: string;
  trend: 'up' | 'down' | 'stable';
  status: 'normal' | 'warning' | 'critical';
}

// 索引建议接口
interface IndexSuggestion {
  id: string;
  database_name: string;
  table_name: string;
  suggested_index: string;
  reason: string;
  estimated_improvement: number;
  query_examples: string[];
  priority: 'high' | 'medium' | 'low';
  impact_score: number;
}

interface PerformanceAnalysisProps {
  onQuerySelect?: (query: SlowQuery) => void;
}

export const PerformanceAnalysis: React.FC<PerformanceAnalysisProps> = ({ onQuerySelect }) => {
  const [currentTab, setCurrentTab] = useState<'slow-queries' | 'metrics' | 'indexes'>('slow-queries');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDatabase, setSelectedDatabase] = useState<string>('all');
  const [timeRange, setTimeRange] = useState<string>('1h');
  const [sortBy, setSortBy] = useState<string>('execution_time');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // 模拟慢查询数据
  const [slowQueries] = useState<SlowQuery[]>([
    {
      id: '1',
      query: 'SELECT u.*, p.profile_data FROM users u LEFT JOIN profiles p ON u.id = p.user_id WHERE u.created_at > \'2024-01-01\' ORDER BY u.created_at DESC',
      database_name: 'PostgreSQL-Main',
      execution_time: 2.45,
      timestamp: '2024-01-15T14:30:00Z',
      rows_examined: 125000,
      rows_sent: 1250,
      lock_time: 0.02,
      user: 'app_user',
      host: '*************',
      query_type: 'SELECT',
      frequency: 45,
      avg_execution_time: 2.12
    },
    {
      id: '2',
      query: 'UPDATE orders SET status = \'completed\', updated_at = NOW() WHERE id IN (SELECT id FROM order_items WHERE product_id = 12345)',
      database_name: 'MySQL-Analytics',
      execution_time: 5.67,
      timestamp: '2024-01-15T14:25:00Z',
      rows_examined: 89000,
      rows_sent: 0,
      lock_time: 0.15,
      user: 'batch_user',
      host: '*************',
      query_type: 'UPDATE',
      frequency: 12,
      avg_execution_time: 4.89
    },
    {
      id: '3',
      query: 'SELECT COUNT(*) FROM logs WHERE created_at BETWEEN \'2024-01-01\' AND \'2024-01-15\' AND level = \'ERROR\'',
      database_name: 'PostgreSQL-Main',
      execution_time: 1.89,
      timestamp: '2024-01-15T14:20:00Z',
      rows_examined: 2500000,
      rows_sent: 1,
      lock_time: 0.01,
      user: 'analytics_user',
      host: '*************',
      query_type: 'SELECT',
      frequency: 78,
      avg_execution_time: 1.95
    }
  ]);

  // 模拟性能指标数据
  const [performanceMetrics] = useState<PerformanceMetric[]>([
    {
      id: '1',
      database_name: 'PostgreSQL-Main',
      metric_name: 'CPU使用率',
      current_value: 78.5,
      previous_value: 65.2,
      unit: '%',
      timestamp: '2024-01-15T14:30:00Z',
      trend: 'up',
      status: 'warning'
    },
    {
      id: '2',
      database_name: 'PostgreSQL-Main',
      metric_name: '内存使用率',
      current_value: 82.3,
      previous_value: 79.1,
      unit: '%',
      timestamp: '2024-01-15T14:30:00Z',
      trend: 'up',
      status: 'warning'
    },
    {
      id: '3',
      database_name: 'PostgreSQL-Main',
      metric_name: '连接数',
      current_value: 145,
      previous_value: 132,
      unit: '个',
      timestamp: '2024-01-15T14:30:00Z',
      trend: 'up',
      status: 'normal'
    },
    {
      id: '4',
      database_name: 'MySQL-Analytics',
      metric_name: '查询响应时间',
      current_value: 125.6,
      previous_value: 98.4,
      unit: 'ms',
      timestamp: '2024-01-15T14:30:00Z',
      trend: 'up',
      status: 'critical'
    }
  ]);

  // 模拟索引建议数据
  const [indexSuggestions] = useState<IndexSuggestion[]>([
    {
      id: '1',
      database_name: 'PostgreSQL-Main',
      table_name: 'users',
      suggested_index: 'CREATE INDEX idx_users_created_at ON users(created_at)',
      reason: '频繁按创建时间排序查询，缺少相应索引',
      estimated_improvement: 65,
      query_examples: [
        'SELECT * FROM users ORDER BY created_at DESC LIMIT 100',
        'SELECT COUNT(*) FROM users WHERE created_at > \'2024-01-01\''
      ],
      priority: 'high',
      impact_score: 8.5
    },
    {
      id: '2',
      database_name: 'MySQL-Analytics',
      table_name: 'orders',
      suggested_index: 'CREATE INDEX idx_orders_status_updated ON orders(status, updated_at)',
      reason: '复合查询条件缺少复合索引',
      estimated_improvement: 45,
      query_examples: [
        'SELECT * FROM orders WHERE status = \'pending\' ORDER BY updated_at',
        'UPDATE orders SET status = \'completed\' WHERE status = \'processing\''
      ],
      priority: 'medium',
      impact_score: 6.8
    },
    {
      id: '3',
      database_name: 'PostgreSQL-Main',
      table_name: 'logs',
      suggested_index: 'CREATE INDEX idx_logs_created_level ON logs(created_at, level)',
      reason: '日志查询频繁使用时间和级别过滤',
      estimated_improvement: 78,
      query_examples: [
        'SELECT COUNT(*) FROM logs WHERE created_at > NOW() - INTERVAL \'1 day\' AND level = \'ERROR\'',
        'SELECT * FROM logs WHERE created_at BETWEEN \'2024-01-01\' AND \'2024-01-15\' AND level IN (\'ERROR\', \'WARN\')'
      ],
      priority: 'high',
      impact_score: 9.2
    }
  ]);

  // 获取数据库列表
  const databases = Array.from(new Set([
    ...slowQueries.map(q => q.database_name),
    ...performanceMetrics.map(m => m.database_name),
    ...indexSuggestions.map(s => s.database_name)
  ]));

  // 过滤慢查询
  const filteredSlowQueries = slowQueries.filter(query => {
    const matchesSearch = query.query.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         query.database_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         query.user.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDatabase = selectedDatabase === 'all' || query.database_name === selectedDatabase;
    return matchesSearch && matchesDatabase;
  }).sort((a, b) => {
    const aValue = a[sortBy as keyof SlowQuery] as number;
    const bValue = b[sortBy as keyof SlowQuery] as number;
    return sortOrder === 'desc' ? bValue - aValue : aValue - bValue;
  });

  // 过滤性能指标
  const filteredMetrics = performanceMetrics.filter(metric => {
    const matchesSearch = metric.metric_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         metric.database_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDatabase = selectedDatabase === 'all' || metric.database_name === selectedDatabase;
    return matchesSearch && matchesDatabase;
  });

  // 过滤索引建议
  const filteredIndexSuggestions = indexSuggestions.filter(suggestion => {
    const matchesSearch = suggestion.table_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         suggestion.database_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         suggestion.reason.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDatabase = selectedDatabase === 'all' || suggestion.database_name === selectedDatabase;
    return matchesSearch && matchesDatabase;
  });

  // 格式化时间
  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString('zh-CN');
  };

  // 格式化查询文本
  const formatQuery = (query: string, maxLength: number = 100) => {
    return query.length > maxLength ? query.substring(0, maxLength) + '...' : query;
  };

  // 获取趋势图标
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <ArrowUpIcon className="h-4 w-4 text-red-500" />;
      case 'down':
        return <ArrowDownIcon className="h-4 w-4 text-green-500" />;
      default:
        return <div className="h-4 w-4 bg-gray-400 rounded-full"></div>;
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'normal':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <ChartBarIcon className="h-8 w-8 text-blue-600" />
            性能分析工具
          </h1>
          <p className="text-gray-600 mt-1">监控数据库性能，分析慢查询，优化索引配置</p>
        </div>

        {/* 搜索和过滤 */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="搜索查询、数据库、用户..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <select
              value={selectedDatabase}
              onChange={(e) => setSelectedDatabase(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">所有数据库</option>
              {databases.map(db => (
                <option key={db} value={db}>{db}</option>
              ))}
            </select>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="1h">最近1小时</option>
              <option value="6h">最近6小时</option>
              <option value="24h">最近24小时</option>
              <option value="7d">最近7天</option>
              <option value="30d">最近30天</option>
            </select>
          </div>
        </div>

        {/* 标签页导航 */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              <button
                onClick={() => setCurrentTab('slow-queries')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  currentTab === 'slow-queries'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <ClockIcon className="h-5 w-5" />
                  慢查询分析 ({filteredSlowQueries.length})
                </div>
              </button>
              <button
                onClick={() => setCurrentTab('metrics')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  currentTab === 'metrics'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <CpuChipIcon className="h-5 w-5" />
                  性能指标 ({filteredMetrics.length})
                </div>
              </button>
              <button
                onClick={() => setCurrentTab('indexes')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  currentTab === 'indexes'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <LightBulbIcon className="h-5 w-5" />
                  索引建议 ({filteredIndexSuggestions.length})
                </div>
              </button>
            </nav>
          </div>
        </div>

        {/* 慢查询分析标签页 */}
        {currentTab === 'slow-queries' && (
          <div className="space-y-6">
            {/* 慢查询统计卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ClockIcon className="h-8 w-8 text-red-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">慢查询总数</p>
                    <p className="text-2xl font-semibold text-gray-900">{filteredSlowQueries.length}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ExclamationTriangleIcon className="h-8 w-8 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">平均执行时间</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {filteredSlowQueries.length > 0
                        ? (filteredSlowQueries.reduce((sum, q) => sum + q.execution_time, 0) / filteredSlowQueries.length).toFixed(2)
                        : '0.00'
                      }s
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CircleStackIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">影响数据库</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {new Set(filteredSlowQueries.map(q => q.database_name)).size}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ChartBarIcon className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">最高频率</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {filteredSlowQueries.length > 0
                        ? Math.max(...filteredSlowQueries.map(q => q.frequency))
                        : 0
                      }次/小时
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* 慢查询列表 */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-gray-900">慢查询列表</h2>
                  <div className="flex items-center space-x-3">
                    <select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value)}
                      className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="execution_time">按执行时间</option>
                      <option value="frequency">按频率</option>
                      <option value="rows_examined">按扫描行数</option>
                      <option value="timestamp">按时间</option>
                    </select>
                    <button
                      onClick={() => setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc')}
                      className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                    >
                      {sortOrder === 'desc' ? '降序' : '升序'}
                    </button>
                  </div>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        查询语句
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        数据库
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        执行时间
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        扫描行数
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        频率
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        时间
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredSlowQueries.map((query) => (
                      <tr key={query.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4">
                          <div className="max-w-md">
                            <div className="text-sm font-medium text-gray-900 mb-1">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full mr-2 ${
                                query.query_type === 'SELECT' ? 'bg-blue-100 text-blue-800' :
                                query.query_type === 'UPDATE' ? 'bg-yellow-100 text-yellow-800' :
                                query.query_type === 'INSERT' ? 'bg-green-100 text-green-800' :
                                'bg-red-100 text-red-800'
                              }`}>
                                {query.query_type}
                              </span>
                            </div>
                            <div className="text-sm text-gray-500 font-mono bg-gray-50 p-2 rounded">
                              {formatQuery(query.query, 80)}
                            </div>
                            <div className="text-xs text-gray-400 mt-1">
                              用户: {query.user} | 主机: {query.host}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{query.database_name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 font-semibold">
                            {query.execution_time.toFixed(2)}s
                          </div>
                          <div className="text-xs text-gray-500">
                            平均: {query.avg_execution_time.toFixed(2)}s
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {query.rows_examined.toLocaleString()}
                          </div>
                          <div className="text-xs text-gray-500">
                            返回: {query.rows_sent.toLocaleString()}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{query.frequency}次/小时</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{formatTime(query.timestamp)}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => onQuerySelect?.(query)}
                            className="text-blue-600 hover:text-blue-900 hover:bg-blue-50 p-2 rounded-lg transition-colors"
                            title="查看详情"
                          >
                            <DocumentTextIcon className="h-4 w-4" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {filteredSlowQueries.length === 0 && (
                <div className="text-center py-8">
                  <CheckCircleIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">暂无慢查询</h3>
                  <p className="mt-1 text-sm text-gray-500">当前时间范围内没有发现慢查询</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 性能指标标签页 */}
        {currentTab === 'metrics' && (
          <div className="space-y-6">
            {/* 性能指标概览 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {filteredMetrics.map((metric) => (
                <div key={metric.id} className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <CpuChipIcon className="h-8 w-8 text-blue-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-500">{metric.metric_name}</p>
                        <p className="text-2xl font-semibold text-gray-900">
                          {metric.current_value.toFixed(1)}{metric.unit}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getTrendIcon(metric.trend)}
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(metric.status)}`}>
                        {metric.status === 'normal' ? '正常' : metric.status === 'warning' ? '警告' : '严重'}
                      </span>
                    </div>
                  </div>
                  <div className="mt-4">
                    <div className="text-sm text-gray-500">
                      数据库: {metric.database_name}
                    </div>
                    <div className="text-sm text-gray-500">
                      变化: {metric.current_value > metric.previous_value ? '+' : ''}{(metric.current_value - metric.previous_value).toFixed(1)}{metric.unit}
                    </div>
                    <div className="text-xs text-gray-400 mt-1">
                      {formatTime(metric.timestamp)}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* 性能趋势图表 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">性能趋势图表</h3>
              <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">图表功能开发中</h3>
                  <p className="mt-1 text-sm text-gray-500">将集成 Chart.js 或 Recharts 来显示性能趋势</p>
                </div>
              </div>
            </div>

            {/* 性能指标详细列表 */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">性能指标详情</h2>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        指标名称
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        数据库
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        当前值
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        变化趋势
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        更新时间
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredMetrics.map((metric) => (
                      <tr key={metric.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <CpuChipIcon className="h-5 w-5 text-gray-400 mr-3" />
                            <div className="text-sm font-medium text-gray-900">{metric.metric_name}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{metric.database_name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 font-semibold">
                            {metric.current_value.toFixed(1)}{metric.unit}
                          </div>
                          <div className="text-xs text-gray-500">
                            上次: {metric.previous_value.toFixed(1)}{metric.unit}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {getTrendIcon(metric.trend)}
                            <span className="ml-2 text-sm text-gray-900">
                              {metric.current_value > metric.previous_value ? '+' : ''}{(metric.current_value - metric.previous_value).toFixed(1)}{metric.unit}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(metric.status)}`}>
                            {metric.status === 'normal' ? '正常' : metric.status === 'warning' ? '警告' : '严重'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{formatTime(metric.timestamp)}</div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {filteredMetrics.length === 0 && (
                <div className="text-center py-8">
                  <CpuChipIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">暂无性能指标</h3>
                  <p className="mt-1 text-sm text-gray-500">当前没有可显示的性能指标数据</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 索引建议标签页 */}
        {currentTab === 'indexes' && (
          <div className="space-y-6">
            {/* 索引建议统计 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <LightBulbIcon className="h-8 w-8 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">建议总数</p>
                    <p className="text-2xl font-semibold text-gray-900">{filteredIndexSuggestions.length}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ExclamationTriangleIcon className="h-8 w-8 text-red-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">高优先级</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {filteredIndexSuggestions.filter(s => s.priority === 'high').length}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ChartBarIcon className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">平均提升</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {filteredIndexSuggestions.length > 0
                        ? (filteredIndexSuggestions.reduce((sum, s) => sum + s.estimated_improvement, 0) / filteredIndexSuggestions.length).toFixed(0)
                        : '0'
                      }%
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* 索引建议列表 */}
            <div className="space-y-4">
              {filteredIndexSuggestions.map((suggestion) => (
                <div key={suggestion.id} className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <LightBulbIcon className="h-6 w-6 text-yellow-500" />
                        <h3 className="text-lg font-semibold text-gray-900">
                          {suggestion.table_name} 表索引优化
                        </h3>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(suggestion.priority)}`}>
                          {suggestion.priority === 'high' ? '高优先级' : suggestion.priority === 'medium' ? '中优先级' : '低优先级'}
                        </span>
                        <div className="flex items-center space-x-1">
                          <span className="text-sm text-gray-500">影响分数:</span>
                          <span className="text-sm font-semibold text-gray-900">{suggestion.impact_score}/10</span>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 mb-2">建议索引</h4>
                          <div className="bg-gray-50 rounded-lg p-3">
                            <code className="text-sm text-gray-800 font-mono">
                              {suggestion.suggested_index}
                            </code>
                          </div>

                          <h4 className="text-sm font-medium text-gray-700 mb-2 mt-4">优化原因</h4>
                          <p className="text-sm text-gray-600">{suggestion.reason}</p>

                          <div className="flex items-center space-x-4 mt-3">
                            <div className="text-sm">
                              <span className="text-gray-500">数据库:</span>
                              <span className="ml-1 font-medium text-gray-900">{suggestion.database_name}</span>
                            </div>
                            <div className="text-sm">
                              <span className="text-gray-500">预计提升:</span>
                              <span className="ml-1 font-medium text-green-600">{suggestion.estimated_improvement}%</span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="text-sm font-medium text-gray-700 mb-2">相关查询示例</h4>
                          <div className="space-y-2">
                            {suggestion.query_examples.map((query, index) => (
                              <div key={index} className="bg-gray-50 rounded-lg p-3">
                                <code className="text-xs text-gray-700 font-mono">
                                  {formatQuery(query, 60)}
                                </code>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-200">
                        <div className="flex items-center space-x-4">
                          <button className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                            应用索引
                          </button>
                          <button className="px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-200 transition-colors">
                            查看详情
                          </button>
                          <button className="px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-200 transition-colors">
                            忽略建议
                          </button>
                        </div>
                        <div className="text-sm text-gray-500">
                          建议ID: {suggestion.id}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredIndexSuggestions.length === 0 && (
              <div className="bg-white rounded-lg shadow-sm p-12">
                <div className="text-center">
                  <CheckCircleIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">暂无索引建议</h3>
                  <p className="mt-1 text-sm text-gray-500">当前数据库索引配置良好，没有优化建议</p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
