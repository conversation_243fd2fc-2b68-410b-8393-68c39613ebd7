import React from 'react';
import {
  XMarkIcon,
  ClockIcon,
  CircleStackIcon,
  UserIcon,
  ComputerDesktopIcon,
  ChartBarIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface SlowQuery {
  id: string;
  query: string;
  database_name: string;
  execution_time: number;
  timestamp: string;
  rows_examined: number;
  rows_sent: number;
  lock_time: number;
  user: string;
  host: string;
  query_type: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE';
  frequency: number;
  avg_execution_time: number;
}

interface SlowQueryModalProps {
  isOpen: boolean;
  onClose: () => void;
  query: SlowQuery | null;
}

export const SlowQueryModal: React.FC<SlowQueryModalProps> = ({ isOpen, onClose, query }) => {
  if (!isOpen || !query) return null;

  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString('zh-CN');
  };

  const formatQuery = (queryText: string) => {
    // 简单的SQL格式化
    return queryText
      .replace(/\s+/g, ' ')
      .replace(/\b(SELECT|FROM|WHERE|JOIN|LEFT|RIGHT|INNER|OUTER|ON|GROUP BY|ORDER BY|HAVING|LIMIT|INSERT|UPDATE|DELETE|SET|VALUES)\b/gi, '\n$1')
      .replace(/,/g, ',\n  ')
      .trim();
  };

  const getQueryTypeColor = (type: string) => {
    switch (type) {
      case 'SELECT':
        return 'bg-blue-100 text-blue-800';
      case 'INSERT':
        return 'bg-green-100 text-green-800';
      case 'UPDATE':
        return 'bg-yellow-100 text-yellow-800';
      case 'DELETE':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPerformanceLevel = (executionTime: number) => {
    if (executionTime < 1) return { level: '良好', color: 'text-green-600', bg: 'bg-green-100' };
    if (executionTime < 3) return { level: '一般', color: 'text-yellow-600', bg: 'bg-yellow-100' };
    return { level: '较差', color: 'text-red-600', bg: 'bg-red-100' };
  };

  const performance = getPerformanceLevel(query.execution_time);

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
        {/* 模态框头部 */}
        <div className="flex items-center justify-between pb-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <ClockIcon className="h-6 w-6 text-red-500" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">慢查询详情</h3>
              <p className="text-sm text-gray-500">查询ID: {query.id}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* 模态框内容 */}
        <div className="mt-6 space-y-6">
          {/* 基本信息卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <ClockIcon className="h-5 w-5 text-red-500" />
                <span className="text-sm font-medium text-gray-700">执行时间</span>
              </div>
              <div className="mt-2">
                <div className="text-2xl font-bold text-gray-900">{query.execution_time.toFixed(2)}s</div>
                <div className={`text-sm ${performance.color}`}>性能: {performance.level}</div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <CircleStackIcon className="h-5 w-5 text-blue-500" />
                <span className="text-sm font-medium text-gray-700">扫描行数</span>
              </div>
              <div className="mt-2">
                <div className="text-2xl font-bold text-gray-900">{query.rows_examined.toLocaleString()}</div>
                <div className="text-sm text-gray-500">返回: {query.rows_sent.toLocaleString()}</div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <ChartBarIcon className="h-5 w-5 text-green-500" />
                <span className="text-sm font-medium text-gray-700">执行频率</span>
              </div>
              <div className="mt-2">
                <div className="text-2xl font-bold text-gray-900">{query.frequency}</div>
                <div className="text-sm text-gray-500">次/小时</div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
                <span className="text-sm font-medium text-gray-700">锁等待时间</span>
              </div>
              <div className="mt-2">
                <div className="text-2xl font-bold text-gray-900">{query.lock_time.toFixed(3)}s</div>
                <div className="text-sm text-gray-500">平均: {query.avg_execution_time.toFixed(2)}s</div>
              </div>
            </div>
          </div>

          {/* 查询信息 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-semibold text-gray-900">SQL查询语句</h4>
                  <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getQueryTypeColor(query.query_type)}`}>
                    {query.query_type}
                  </span>
                </div>
                <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                  <pre className="text-sm text-green-400 font-mono whitespace-pre-wrap">
                    {formatQuery(query.query)}
                  </pre>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              {/* 执行环境 */}
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">执行环境</h4>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <CircleStackIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">数据库</div>
                      <div className="text-sm text-gray-500">{query.database_name}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <UserIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">用户</div>
                      <div className="text-sm text-gray-500">{query.user}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <ComputerDesktopIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">主机</div>
                      <div className="text-sm text-gray-500">{query.host}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <ClockIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">执行时间</div>
                      <div className="text-sm text-gray-500">{formatTime(query.timestamp)}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 优化建议 */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500 mr-2" />
                  优化建议
                </h4>
                <div className="space-y-2 text-sm text-gray-700">
                  <div>• 考虑添加适当的索引来减少扫描行数</div>
                  <div>• 检查WHERE条件是否可以优化</div>
                  <div>• 考虑分页查询减少单次返回数据量</div>
                  <div>• 分析是否可以使用缓存减少查询频率</div>
                </div>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              关闭
            </button>
            <button className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 transition-colors">
              生成优化建议
            </button>
            <button className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 transition-colors">
              添加到监控
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
