import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  DocumentTextIcon,
  PlusIcon,
  EyeIcon,
  TrashIcon,
  PlayIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  DocumentDuplicateIcon,
  MagnifyingGlassIcon,
  ArrowDownTrayIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { apiService } from '../../services/api';
import type { ReportTemplate, ReportExecution } from '../../services/api';

// 报表类型映射
const REPORT_TYPE_MAP = {
  performance: { name: '性能报表', color: 'blue', icon: DocumentTextIcon },
  usage: { name: '使用报表', color: 'green', icon: DocumentDuplicateIcon },
  alert: { name: '告警报表', color: 'red', icon: ExclamationTriangleIcon }
};

// 执行状态映射
const EXECUTION_STATUS_MAP = {
  pending: { name: '等待中', color: 'yellow', icon: ClockIcon },
  running: { name: '执行中', color: 'blue', icon: PlayIcon },
  completed: { name: '已完成', color: 'green', icon: CheckCircleIcon },
  failed: { name: '失败', color: 'red', icon: XCircleIcon }
};

export const ReportGenerator: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'templates' | 'executions'>('templates');
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);
  const [showExecuteModal, setShowExecuteModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [downloadingIds, setDownloadingIds] = useState<Set<number>>(new Set());
  
  const queryClient = useQueryClient();

  // 获取报表模板列表
  const { data: templatesData, isLoading: templatesLoading } = useQuery({
    queryKey: ['report-templates', { search: searchTerm, type: typeFilter }],
    queryFn: () => apiService.getReportTemplates({
      search: searchTerm || undefined,
      type: typeFilter || undefined,
      page: 1,
      page_size: 20
    })
  });

  // 获取报表执行记录
  const { data: executionsData, isLoading: executionsLoading } = useQuery({
    queryKey: ['report-executions'],
    queryFn: () => apiService.getReportExecutions({ page: 1, page_size: 20 }),
    enabled: activeTab === 'executions'
  });

  // 创建报表模板
  const createTemplateMutation = useMutation({
    mutationFn: (data: any) => apiService.createReportTemplate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['report-templates'] });
      setShowCreateModal(false);
    }
  });

  // 删除报表模板
  const deleteTemplateMutation = useMutation({
    mutationFn: (id: number) => apiService.deleteReportTemplate(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['report-templates'] });
    }
  });

  // 执行报表
  const executeReportMutation = useMutation({
    mutationFn: (data: any) => apiService.executeReport(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['report-executions'] });
      setShowExecuteModal(false);
      setSelectedTemplate(null);
    }
  });

  // 处理模板删除
  const handleDeleteTemplate = (template: ReportTemplate) => {
    if (window.confirm(`确定要删除报表模板 "${template.name}" 吗？`)) {
      deleteTemplateMutation.mutate(template.id);
    }
  };

  // 处理创建模板
  const handleCreateTemplate = () => {
    setShowCreateModal(true);
  };

  // 处理查看模板
  const handleViewTemplate = (template: ReportTemplate) => {
    setSelectedTemplate(template);
    setShowViewModal(true);
  };

  // 处理报表执行
  const handleExecuteReport = (template: ReportTemplate) => {
    setSelectedTemplate(template);
    setShowExecuteModal(true);
  };

  // 处理报表下载
  const handleDownloadReport = async (execution: ReportExecution) => {
    if (execution.status !== 'completed') {
      alert('报表还未生成完成，无法下载');
      return;
    }

    setDownloadingIds(prev => new Set(prev).add(execution.id));

    try {
      const blob = await apiService.downloadReport(execution.id);

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // 生成文件名
      const timestamp = new Date(execution.start_time).toISOString().slice(0, 19).replace(/[:-]/g, '');
      const templateName = execution.template?.name || 'report';
      const extension = getFileExtension(execution);
      link.download = `${templateName}_${execution.id}_${timestamp}.${extension}`;

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理URL对象
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('下载失败:', error);
      alert('下载失败，请稍后重试');
    } finally {
      setDownloadingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(execution.id);
        return newSet;
      });
    }
  };

  // 获取文件扩展名
  const getFileExtension = (execution: ReportExecution): string => {
    // 从参数中获取格式信息，或者使用默认值
    try {
      const params = JSON.parse(execution.parameters || '{}');
      const format = params.format || 'csv';
      return format;
    } catch {
      return 'csv';
    }
  };

  // 渲染报表模板卡片
  const renderTemplateCard = (template: ReportTemplate) => {
    const typeInfo = REPORT_TYPE_MAP[template.type];
    const IconComponent = typeInfo.icon;

    return (
      <div key={template.id} className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className={`p-2 rounded-lg bg-${typeInfo.color}-100`}>
              <IconComponent className={`h-6 w-6 text-${typeInfo.color}-600`} />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900">{template.name}</h3>
              <p className="text-sm text-gray-600 mt-1">{template.description}</p>
              <div className="flex items-center space-x-4 mt-3">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${typeInfo.color}-100 text-${typeInfo.color}-800`}>
                  {typeInfo.name}
                </span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  template.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {template.is_active ? '启用' : '禁用'}
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => handleExecuteReport(template)}
              disabled={!template.is_active}
              className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
              title="执行报表"
            >
              <PlayIcon className="h-5 w-5" />
            </button>
            <button
              onClick={() => handleViewTemplate(template)}
              className="p-2 text-gray-600 hover:bg-gray-50 rounded-lg"
              title="查看详情"
            >
              <EyeIcon className="h-5 w-5" />
            </button>
            <button
              onClick={() => handleDeleteTemplate(template)}
              className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
              title="删除模板"
            >
              <TrashIcon className="h-5 w-5" />
            </button>
          </div>
        </div>

        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="flex items-center justify-between text-sm text-gray-500">
            <span>创建者: {template.creator?.name || '未知'}</span>
            <span>创建时间: {new Date(template.created_at).toLocaleDateString()}</span>
          </div>
        </div>
      </div>
    );
  };

  // 渲染执行记录行
  const renderExecutionRow = (execution: ReportExecution) => {
    const statusInfo = EXECUTION_STATUS_MAP[execution.status];
    const StatusIcon = statusInfo.icon;

    return (
      <tr key={execution.id} className="hover:bg-gray-50">
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="text-sm font-medium text-gray-900">{execution.template?.name}</div>
          <div className="text-sm text-gray-500">ID: {execution.id}</div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${statusInfo.color}-100 text-${statusInfo.color}-800`}>
            <StatusIcon className="h-3 w-3 mr-1" />
            {statusInfo.name}
          </span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          {execution.executor?.name || '未知'}
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          {new Date(execution.start_time).toLocaleString()}
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          {execution.end_time ? new Date(execution.end_time).toLocaleString() : '-'}
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div className="flex items-center justify-end space-x-2">
            <button
              className="text-blue-600 hover:text-blue-900"
              title="查看详情"
            >
              查看
            </button>
            {execution.status === 'completed' && (
              <button
                onClick={() => handleDownloadReport(execution)}
                disabled={downloadingIds.has(execution.id)}
                className="inline-flex items-center text-green-600 hover:text-green-900 disabled:text-gray-400 disabled:cursor-not-allowed"
                title="下载报表"
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-1" />
                {downloadingIds.has(execution.id) ? '下载中...' : '下载'}
              </button>
            )}
          </div>
        </td>
      </tr>
    );
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">报表管理</h1>
          <p className="text-gray-600 mt-1">管理报表模板和执行记录</p>
        </div>
        
        <button
          onClick={handleCreateTemplate}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          创建模板
        </button>
      </div>

      {/* 标签页 */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('templates')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'templates'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <DocumentTextIcon className="h-5 w-5 inline mr-2" />
            报表模板
          </button>
          <button
            onClick={() => setActiveTab('executions')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'executions'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <ClockIcon className="h-5 w-5 inline mr-2" />
            执行记录
          </button>
        </nav>
      </div>

      {/* 报表模板标签页 */}
      {activeTab === 'templates' && (
        <div className="space-y-6">
          {/* 搜索和过滤 */}
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="搜索报表模板..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">所有类型</option>
              <option value="performance">性能报表</option>
              <option value="usage">使用报表</option>
              <option value="alert">告警报表</option>
            </select>
          </div>

          {/* 模板列表 */}
          {templatesLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">加载中...</span>
            </div>
          ) : templatesData?.data?.items?.length ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {templatesData.data.items.map(renderTemplateCard)}
            </div>
          ) : (
            <div className="text-center py-12">
              <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">暂无报表模板</p>
            </div>
          )}
        </div>
      )}

      {/* 执行记录标签页 */}
      {activeTab === 'executions' && (
        <div className="bg-white rounded-lg border border-gray-200">
          {executionsLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">加载中...</span>
            </div>
          ) : executionsData?.data?.items?.length ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      报表模板
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      执行者
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      开始时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      结束时间
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {executionsData.data.items.map(renderExecutionRow)}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <ClockIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">暂无执行记录</p>
            </div>
          )}
        </div>
      )}

      {/* 创建模板模态框 */}
      {showCreateModal && (
        <CreateTemplateModal
          onClose={() => setShowCreateModal(false)}
          onCreate={(data) => createTemplateMutation.mutate(data)}
          isLoading={createTemplateMutation.isPending}
        />
      )}

      {/* 查看模板模态框 */}
      {showViewModal && selectedTemplate && (
        <ViewTemplateModal
          template={selectedTemplate}
          onClose={() => {
            setShowViewModal(false);
            setSelectedTemplate(null);
          }}
        />
      )}

      {/* 执行报表模态框 */}
      {showExecuteModal && selectedTemplate && (
        <ExecuteReportModal
          template={selectedTemplate}
          onClose={() => {
            setShowExecuteModal(false);
            setSelectedTemplate(null);
          }}
          onExecute={(data) => executeReportMutation.mutate(data)}
          isLoading={executeReportMutation.isPending}
        />
      )}
    </div>
  );
};

// 执行报表模态框组件
interface ExecuteReportModalProps {
  template: ReportTemplate;
  onClose: () => void;
  onExecute: (data: any) => void;
  isLoading: boolean;
}

const ExecuteReportModal: React.FC<ExecuteReportModalProps> = ({
  template,
  onClose,
  onExecute,
  isLoading
}) => {
  const [format, setFormat] = useState<'pdf' | 'excel' | 'csv'>('pdf');
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');

  // 设置默认时间范围（最近7天）
  React.useEffect(() => {
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    setEndTime(now.toISOString().slice(0, 16));
    setStartTime(weekAgo.toISOString().slice(0, 16));
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    onExecute({
      template_id: template.id,
      format,
      time_range: {
        start_time: new Date(startTime).toISOString(),
        end_time: new Date(endTime).toISOString()
      },
      parameters: {}
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">执行报表</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XCircleIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              报表模板
            </label>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="font-medium text-gray-900">{template.name}</div>
              <div className="text-sm text-gray-600">{template.description}</div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              输出格式
            </label>
            <select
              value={format}
              onChange={(e) => setFormat(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="pdf">PDF</option>
              <option value="excel">Excel</option>
              <option value="csv">CSV</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              开始时间
            </label>
            <input
              type="datetime-local"
              value={startTime}
              onChange={(e) => setStartTime(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              结束时间
            </label>
            <input
              type="datetime-local"
              value={endTime}
              onChange={(e) => setEndTime(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          <div className="flex items-center justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? '执行中...' : '开始执行'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// 创建模板模态框组件
interface CreateTemplateModalProps {
  onClose: () => void;
  onCreate: (data: any) => void;
  isLoading: boolean;
}

const CreateTemplateModal: React.FC<CreateTemplateModalProps> = ({
  onClose,
  onCreate,
  isLoading
}) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [type, setType] = useState<'performance' | 'usage' | 'alert'>('performance');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim()) return;

    onCreate({
      name: name.trim(),
      description: description.trim(),
      type,
      config: {
        chart_type: 'line',
        metrics: ['cpu_usage', 'memory_usage'],
        time_range: '1h'
      }
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-medium text-gray-900 mb-4">创建报表模板</h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              模板名称 *
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入模板名称"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              描述
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入模板描述"
              rows={3}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              模板类型
            </label>
            <select
              value={type}
              onChange={(e) => setType(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="performance">性能报表</option>
              <option value="usage">使用情况报表</option>
              <option value="alert">告警报表</option>
            </select>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={isLoading || !name.trim()}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? '创建中...' : '创建'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// 查看模板模态框组件
interface ViewTemplateModalProps {
  template: ReportTemplate;
  onClose: () => void;
}

const ViewTemplateModal: React.FC<ViewTemplateModalProps> = ({
  template,
  onClose
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">模板详情</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              模板名称
            </label>
            <p className="text-sm text-gray-900">{template.name}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              描述
            </label>
            <p className="text-sm text-gray-900">{template.description || '无描述'}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              类型
            </label>
            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
              template.type === 'performance' ? 'bg-blue-100 text-blue-800' :
              template.type === 'usage' ? 'bg-green-100 text-green-800' :
              template.type === 'alert' ? 'bg-red-100 text-red-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {template.type === 'performance' ? '性能报表' :
               template.type === 'usage' ? '使用情况报表' :
               template.type === 'alert' ? '告警报表' : '自定义报表'}
            </span>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              配置信息
            </label>
            <pre className="text-xs bg-gray-50 p-3 rounded-md overflow-auto">
              {JSON.stringify(template.config, null, 2)}
            </pre>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                创建者
              </label>
              <p className="text-sm text-gray-900">{template.creator?.name || '未知'}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                创建时间
              </label>
              <p className="text-sm text-gray-900">
                {new Date(template.created_at).toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="flex justify-end pt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};
