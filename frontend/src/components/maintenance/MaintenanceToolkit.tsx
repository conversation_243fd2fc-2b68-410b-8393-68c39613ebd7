import React, { useState } from 'react';
import {
  Wrench<PERSON>crewdriver<PERSON>con,
  CloudArrowUpIcon,
  CloudArrowDownIcon,
  TrashIcon,
  CircleStackIcon,
  ChartBarIcon,
  ShieldCheckIcon,
  CogIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  PlayIcon,
  PauseIcon,
  ArrowPathIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import {
  useExecuteBackup,
  useBackupOverview,
  useCreateBackupTask,
  useUpdateBackupTask,
  useDeleteBackupTask,
  useBackupHistory,
  useDatabases
} from '../../queries';
import type { BackupTask as ApiBackupTask } from '../../services/api';

// 维护任务接口
interface MaintenanceTask {
  id: string;
  name: string;
  description: string;
  category: 'backup' | 'cleanup' | 'optimization' | 'security' | 'monitoring';
  priority: 'high' | 'medium' | 'low';
  estimated_duration: string;
  last_run?: string;
  next_scheduled?: string;
  status: 'idle' | 'running' | 'completed' | 'failed';
  auto_schedule: boolean;
  dependencies?: string[];
}

// 备份任务接口（兼容API类型）
interface BackupTask extends Omit<ApiBackupTask, 'id' | 'backup_size'> {
  id: string | number;
  backup_size: string | number;
}

// 清理任务接口
interface CleanupTask {
  id: string;
  task_name: string;
  target_type: 'logs' | 'temp_files' | 'old_backups' | 'unused_indexes' | 'dead_tuples';
  criteria: string;
  auto_run: boolean;
  last_run: string;
  items_cleaned: number;
  space_freed: string;
}

interface MaintenanceToolkitProps {
  onClose?: () => void;
}

export const MaintenanceToolkit: React.FC<MaintenanceToolkitProps> = ({ onClose }) => {
  const [currentTab, setCurrentTab] = useState<'overview' | 'backup' | 'cleanup' | 'optimization' | 'security'>('overview');
  const [isRunningTask, setIsRunningTask] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingTask, setEditingTask] = useState<ApiBackupTask | null>(null);

  // 使用真实的API数据
  const backupOverview = useBackupOverview();
  const executeBackupMutation = useExecuteBackup();
  const createBackupMutation = useCreateBackupTask();
  const updateBackupMutation = useUpdateBackupTask();
  const deleteBackupMutation = useDeleteBackupTask();
  const { data: databases } = useDatabases();
  const { data: backupHistory } = useBackupHistory({ page: 1, page_size: 10 });

  // 从API数据转换为组件需要的格式
  const convertApiBackupTaskToLocal = (apiTask: ApiBackupTask): BackupTask => ({
    ...apiTask,
    id: apiTask.id.toString(),
    backup_size: apiTask.backup_size_formatted || `${(apiTask.backup_size / (1024 * 1024 * 1024)).toFixed(1)} GB`,
  });

  // 获取真实的备份任务数据
  const backupTasks = backupOverview.tasks.data?.data.items?.map(convertApiBackupTaskToLocal) || [];
  const backupTaskStats = backupOverview.taskStats.data?.data;
  const backupStats = backupOverview.stats.data?.data;
  const runningBackups = backupOverview.running.data?.data || [];

  // 模拟维护任务数据（保留非备份相关的任务）
  const [maintenanceTasks] = useState<MaintenanceTask[]>([
    {
      id: 'cleanup_logs',
      name: '日志文件清理',
      description: '清理超过30天的旧日志文件，释放磁盘空间',
      category: 'cleanup',
      priority: 'medium',
      estimated_duration: '10-30分钟',
      last_run: '2025-07-10 03:00:00',
      next_scheduled: '2025-07-17 03:00:00',
      status: 'completed',
      auto_schedule: true,
      dependencies: []
    },
    {
      id: 'optimize_indexes',
      name: '索引优化重建',
      description: '分析并重建碎片化的索引，提升查询性能',
      category: 'optimization',
      priority: 'medium',
      estimated_duration: '1-3小时',
      last_run: '2025-07-08 01:00:00',
      next_scheduled: '2025-07-15 01:00:00',
      status: 'idle',
      auto_schedule: true,
      dependencies: []
    },
    {
      id: 'security_scan',
      name: '安全漏洞扫描',
      description: '扫描数据库配置和权限，检测潜在安全风险',
      category: 'security',
      priority: 'high',
      estimated_duration: '30-60分钟',
      last_run: '2025-07-09 04:00:00',
      next_scheduled: '2025-07-16 04:00:00',
      status: 'idle',
      auto_schedule: true,
      dependencies: []
    },
    {
      id: 'stats_update',
      name: '统计信息更新',
      description: '更新数据库表和索引的统计信息，优化查询计划',
      category: 'optimization',
      priority: 'low',
      estimated_duration: '15-45分钟',
      last_run: '2025-07-11 05:00:00',
      next_scheduled: '2025-07-12 05:00:00',
      status: 'completed',
      auto_schedule: true,
      dependencies: []
    }
  ]);



  // 模拟清理任务数据
  const [cleanupTasks] = useState<CleanupTask[]>([
    {
      id: 'cleanup_1',
      task_name: '清理应用日志',
      target_type: 'logs',
      criteria: '保留最近30天',
      auto_run: true,
      last_run: '2025-07-11 03:00:00',
      items_cleaned: 1247,
      space_freed: '3.2 GB'
    },
    {
      id: 'cleanup_2',
      task_name: '清理临时文件',
      target_type: 'temp_files',
      criteria: '清理超过7天的临时文件',
      auto_run: true,
      last_run: '2025-07-11 03:30:00',
      items_cleaned: 89,
      space_freed: '156 MB'
    },
    {
      id: 'cleanup_3',
      task_name: '清理旧备份',
      target_type: 'old_backups',
      criteria: '保留最近60天的备份',
      auto_run: true,
      last_run: '2025-07-10 04:00:00',
      items_cleaned: 12,
      space_freed: '8.7 GB'
    },
    {
      id: 'cleanup_4',
      task_name: '清理未使用索引',
      target_type: 'unused_indexes',
      criteria: '30天内未使用的索引',
      auto_run: false,
      last_run: '2025-07-08 02:00:00',
      items_cleaned: 5,
      space_freed: '234 MB'
    }
  ]);

  // 执行维护任务
  const runMaintenanceTask = async (taskId: string) => {
    setIsRunningTask(taskId);

    // 模拟任务执行
    setTimeout(() => {
      setIsRunningTask(null);
      // 这里可以更新任务状态
    }, 3000);
  };

  // 执行备份任务
  const executeBackupTask = async (taskId: number) => {
    try {
      await executeBackupMutation.mutateAsync({ id: taskId });
      alert('备份任务已启动！');
    } catch (error) {
      console.error('执行备份失败:', error);
      alert('执行备份失败，请稍后重试');
    }
  };

  // 恢复备份（占位功能）
  const restoreBackup = (taskId: number) => {
    alert(`恢复功能正在开发中，任务ID: ${taskId}`);
  };

  // 设置备份任务
  const configureBackupTask = (taskId: number) => {
    const task = backupOverview.tasks.data?.data?.items?.find(t => t.id === taskId);
    if (task) {
      setEditingTask(task);
    }
  };

  // 删除备份任务
  const deleteBackupTask = async (taskId: number) => {
    if (confirm('确定要删除这个备份任务吗？此操作不可恢复。')) {
      try {
        await deleteBackupMutation.mutateAsync(taskId);
        alert('备份任务删除成功！');
      } catch (error) {
        console.error('删除备份任务失败:', error);
        alert('删除备份任务失败，请稍后重试');
      }
    }
  };

  // 下载备份文件
  const downloadBackupFile = (historyId: number) => {
    // 获取token
    const token = localStorage.getItem('access_token');
    if (!token) {
      alert('请先登录');
      return;
    }

    // 创建下载链接
    const downloadUrl = `http://localhost:8080/api/v1/maintenance/backup/history/${historyId}/download`;

    // 创建一个临时的a标签来触发下载
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.style.display = 'none';

    // 设置Authorization头部（通过在URL中添加token参数，因为a标签不支持自定义头部）
    // 或者使用fetch API来下载文件
    fetch(downloadUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error('下载失败');
      }

      // 从响应头获取文件名，或使用默认文件名
      const contentDisposition = response.headers.get('content-disposition');
      let filename = 'backup.sql.gz';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename=(.+)/);
        if (filenameMatch) {
          filename = filenameMatch[1].replace(/"/g, '');
        }
      }

      return response.blob().then(blob => ({ blob, filename }));
    })
    .then(({ blob, filename }) => {
      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    })
    .catch(error => {
      console.error('下载失败:', error);
      alert('下载失败，请稍后重试');
    });
  };

  // 从备份恢复
  const restoreFromBackup = (historyId: number) => {
    if (confirm('确定要从此备份恢复数据库吗？此操作将覆盖当前数据！')) {
      alert(`恢复功能正在开发中。备份历史ID: ${historyId}`);
      // TODO: 实现真实的恢复功能
    }
  };

  // 删除备份历史记录
  const deleteBackupHistory = async (historyId: number) => {
    if (confirm('确定要删除这个备份记录吗？备份文件也将被删除，此操作不可恢复！')) {
      try {
        const token = localStorage.getItem('access_token');
        const response = await fetch(`http://localhost:8080/api/v1/maintenance/backup/history/${historyId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          alert('备份记录删除成功！');
          // 刷新备份历史列表
          window.location.reload();
        } else {
          throw new Error('删除失败');
        }
      } catch (error) {
        console.error('删除备份记录失败:', error);
        alert('删除备份记录失败，请稍后重试');
      }
    }
  };

  // 清理过期备份
  const cleanupExpiredBackups = async () => {
    if (confirm('确定要清理所有过期的备份文件吗？此操作不可恢复！')) {
      try {
        const token = localStorage.getItem('access_token');
        const response = await fetch('http://localhost:8080/api/v1/maintenance/backup/cleanup', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          const result = await response.json();
          alert(`清理完成！删除了 ${result.data.deleted_count} 个过期备份文件，释放了 ${result.data.freed_space} 的存储空间。`);
          // 刷新备份历史列表
          window.location.reload();
        } else {
          throw new Error('清理失败');
        }
      } catch (error) {
        console.error('清理过期备份失败:', error);
        alert('清理过期备份失败，请稍后重试');
      }
    }
  };

  // 暂停/恢复备份任务
  const toggleBackupTaskStatus = async (taskId: number, currentStatus: string) => {
    const newStatus = currentStatus === 'active' ? 'paused' : 'active';
    const action = newStatus === 'active' ? '恢复' : '暂停';

    if (confirm(`确定要${action}这个备份任务吗？`)) {
      try {
        const token = localStorage.getItem('access_token');
        const response = await fetch(`http://localhost:8080/api/v1/maintenance/backup/tasks/${taskId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ status: newStatus })
        });

        if (response.ok) {
          alert(`备份任务${action}成功！`);
          // 刷新任务列表
          window.location.reload();
        } else {
          throw new Error(`${action}失败`);
        }
      } catch (error) {
        console.error(`${action}备份任务失败:`, error);
        alert(`${action}备份任务失败，请稍后重试`);
      }
    }
  };

  // 新建备份任务
  const createNewBackupTask = () => {
    setShowCreateModal(true);
  };

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600';
      case 'running':
        return 'text-blue-600';
      case 'failed':
        return 'text-red-600';
      case 'active':
        return 'text-green-600';
      case 'paused':
        return 'text-yellow-600';
      default:
        return 'text-gray-600';
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'running':
        return <PlayIcon className="h-5 w-5 text-blue-500" />;
      case 'failed':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'active':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'paused':
        return <PauseIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  // 获取分类图标
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'backup':
        return <CloudArrowUpIcon className="h-6 w-6 text-blue-600" />;
      case 'cleanup':
        return <TrashIcon className="h-6 w-6 text-red-600" />;
      case 'optimization':
        return <ChartBarIcon className="h-6 w-6 text-green-600" />;
      case 'security':
        return <ShieldCheckIcon className="h-6 w-6 text-purple-600" />;
      case 'monitoring':
        return <CogIcon className="h-6 w-6 text-orange-600" />;
      default:
        return <WrenchScrewdriverIcon className="h-6 w-6 text-gray-600" />;
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* 页面头部 */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">数据库维护工具集</h1>
            <p className="mt-2 text-gray-600">自动化数据库维护任务，确保系统稳定运行</p>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              返回
            </button>
          )}
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="mb-6">
        <nav className="flex space-x-8">
          {[
            { key: 'overview', label: '总览', icon: WrenchScrewdriverIcon },
            { key: 'backup', label: '备份管理', icon: CloudArrowUpIcon },
            { key: 'cleanup', label: '清理工具', icon: TrashIcon },
            { key: 'optimization', label: '性能优化', icon: ChartBarIcon },
            { key: 'security', label: '安全维护', icon: ShieldCheckIcon }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.key}
                onClick={() => setCurrentTab(tab.key as any)}
                className={`
                  flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors
                  ${currentTab === tab.key
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                  }
                `}
              >
                <Icon className="h-5 w-5 mr-2" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* 总览标签页 */}
      {currentTab === 'overview' && (
        <div className="space-y-6">
          {/* 维护任务状态概览 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <CheckCircleIcon className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">已完成任务</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {maintenanceTasks.filter(t => t.status === 'completed').length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <PlayIcon className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">运行中任务</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {maintenanceTasks.filter(t => t.status === 'running').length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <ClockIcon className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">待执行任务</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {maintenanceTasks.filter(t => t.status === 'idle').length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <ExclamationTriangleIcon className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">失败任务</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {maintenanceTasks.filter(t => t.status === 'failed').length}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 维护任务列表 */}
          <div className="bg-white rounded-lg shadow-sm">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">维护任务列表</h3>
            </div>
            <div className="divide-y divide-gray-200">
              {maintenanceTasks.map((task) => (
                <div key={task.id} className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      {getCategoryIcon(task.category)}
                      <div>
                        <h4 className="text-lg font-medium text-gray-900">{task.name}</h4>
                        <p className="text-sm text-gray-600">{task.description}</p>
                        <div className="flex items-center space-x-4 mt-2">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(task.priority)}`}>
                            {task.priority === 'high' ? '高优先级' : task.priority === 'medium' ? '中优先级' : '低优先级'}
                          </span>
                          <span className="text-xs text-gray-500">预计耗时: {task.estimated_duration}</span>
                          {task.auto_schedule && (
                            <span className="text-xs text-blue-600">自动调度</span>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(task.status)}
                          <span className={`text-sm font-medium ${getStatusColor(task.status)}`}>
                            {task.status === 'completed' ? '已完成' :
                             task.status === 'running' ? '运行中' :
                             task.status === 'failed' ? '失败' : '待执行'}
                          </span>
                        </div>
                        {task.last_run && (
                          <p className="text-xs text-gray-500 mt-1">
                            上次运行: {task.last_run}
                          </p>
                        )}
                        {task.next_scheduled && (
                          <p className="text-xs text-gray-500">
                            下次运行: {task.next_scheduled}
                          </p>
                        )}
                      </div>

                      <button
                        onClick={() => runMaintenanceTask(task.id)}
                        disabled={isRunningTask === task.id || task.status === 'running'}
                        className="inline-flex items-center px-3 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isRunningTask === task.id ? (
                          <>
                            <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                            执行中...
                          </>
                        ) : (
                          <>
                            <PlayIcon className="h-4 w-4 mr-2" />
                            立即执行
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 备份管理标签页 */}
      {currentTab === 'backup' && (
        <div className="space-y-6">
          {/* 备份概览统计 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <CloudArrowUpIcon className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">活跃备份任务</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {backupOverview.isLoading ? '...' : (backupTaskStats?.active_tasks || 0)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <CircleStackIcon className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">总备份大小</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {backupOverview.isLoading ? '...' : (backupStats?.total_size_formatted || '0 B')}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <ClockIcon className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">运行中备份</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {backupOverview.isLoading ? '...' : runningBackups.length}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 备份任务列表 */}
          <div className="bg-white rounded-lg shadow-sm">
            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">备份任务管理</h3>
              <div className="flex space-x-3">
                <button
                  onClick={cleanupExpiredBackups}
                  className="inline-flex items-center px-4 py-2 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700"
                >
                  <TrashIcon className="h-4 w-4 mr-2" />
                  清理过期备份
                </button>
                <button
                  onClick={createNewBackupTask}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700"
                >
                  <CloudArrowUpIcon className="h-4 w-4 mr-2" />
                  新建备份任务
                </button>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      数据库
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      备份类型
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      调度
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      最近备份
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      大小
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {backupOverview.isLoading ? (
                    <tr>
                      <td colSpan={7} className="px-6 py-8 text-center">
                        <ArrowPathIcon className="h-8 w-8 animate-spin mx-auto text-gray-400" />
                        <p className="mt-2 text-sm text-gray-500">加载备份任务...</p>
                      </td>
                    </tr>
                  ) : backupTasks.length === 0 ? (
                    <tr>
                      <td colSpan={7} className="px-6 py-8 text-center">
                        <CloudArrowUpIcon className="h-12 w-12 mx-auto text-gray-400" />
                        <p className="mt-2 text-sm text-gray-500">暂无备份任务</p>
                        <p className="text-xs text-gray-400">创建第一个备份任务来保护您的数据</p>
                      </td>
                    </tr>
                  ) : (
                    backupTasks.map((backup) => (
                    <tr key={backup.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <CircleStackIcon className="h-5 w-5 text-gray-400 mr-3" />
                          <div>
                            <div className="text-sm font-medium text-gray-900">{backup.database_name}</div>
                            <div className="text-sm text-gray-500">保留 {backup.retention_days} 天</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {backup.backup_type === 'full' ? '完整备份' :
                           backup.backup_type === 'incremental' ? '增量备份' : '差异备份'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {backup.compression && '压缩'} {backup.encryption && '加密'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {backup.schedule}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getStatusIcon(backup.status)}
                          <span className={`ml-2 text-sm font-medium ${getStatusColor(backup.status)}`}>
                            {backup.status === 'active' ? '活跃' :
                             backup.status === 'paused' ? '暂停' : '失败'}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{backup.last_backup}</div>
                        <div className="text-sm text-gray-500">下次: {backup.next_backup}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {backup.backup_size}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => executeBackupTask(Number(backup.id))}
                            disabled={executeBackupMutation.isPending || backup.status !== 'active'}
                            className="text-blue-600 hover:text-blue-900 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {executeBackupMutation.isPending ? '执行中...' : '立即备份'}
                          </button>
                          <button
                            onClick={() => restoreBackup(Number(backup.id))}
                            className="text-green-600 hover:text-green-900"
                          >
                            恢复
                          </button>
                          <button
                            onClick={() => configureBackupTask(Number(backup.id))}
                            className="text-gray-600 hover:text-gray-900"
                          >
                            设置
                          </button>
                          <button
                            onClick={() => toggleBackupTaskStatus(Number(backup.id), backup.status)}
                            className={`${backup.status === 'active' ? 'text-orange-600 hover:text-orange-900' : 'text-green-600 hover:text-green-900'}`}
                          >
                            {backup.status === 'active' ? '暂停' : '恢复'}
                          </button>
                          <button
                            onClick={() => deleteBackupTask(Number(backup.id))}
                            disabled={deleteBackupMutation.isPending}
                            className="text-red-600 hover:text-red-900 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            删除
                          </button>
                        </div>
                      </td>
                    </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* 备份历史和恢复点 */}
          <div className="bg-white rounded-lg shadow-sm">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">备份历史记录</h3>
            </div>
            <div className="overflow-x-auto">
              {backupHistory?.data?.items && backupHistory.data.items.length > 0 ? (
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        任务名称
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        开始时间
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        耗时
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        文件大小
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {backupHistory.data.items.map((history: any) => (
                      <tr key={history.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {history.task_name || `任务 ${history.task_id}`}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            history.status === 'success'
                              ? 'bg-green-100 text-green-800'
                              : history.status === 'failed'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {history.status === 'success' ? '成功' :
                             history.status === 'failed' ? '失败' : '运行中'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(history.start_time).toLocaleString('zh-CN')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {history.duration_formatted || `${history.duration}秒`}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {history.backup_size_formatted || `${(history.backup_size / 1024 / 1024).toFixed(2)} MB`}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            {history.status === 'success' && history.file_path && (
                              <button
                                onClick={() => downloadBackupFile(history.id)}
                                className="text-blue-600 hover:text-blue-900"
                              >
                                下载
                              </button>
                            )}
                            {history.status === 'success' && (
                              <button
                                onClick={() => restoreFromBackup(history.id)}
                                className="text-green-600 hover:text-green-900"
                              >
                                恢复
                              </button>
                            )}
                            <button
                              onClick={() => deleteBackupHistory(history.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              删除
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <div className="p-6">
                  <div className="text-center py-8">
                    <CloudArrowDownIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">暂无备份历史</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      执行备份任务后，历史记录将显示在这里
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 清理工具标签页 */}
      {currentTab === 'cleanup' && (
        <div className="space-y-6">
          {/* 清理统计概览 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <TrashIcon className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">今日清理项目</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {cleanupTasks.filter(t => t.last_run.includes('2025-07-11')).reduce((sum, t) => sum + t.items_cleaned, 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <CircleStackIcon className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">释放空间</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {cleanupTasks.filter(t => t.last_run.includes('2025-07-11')).reduce((sum, t) => {
                      const size = parseFloat(t.space_freed);
                      return sum + (t.space_freed.includes('GB') ? size : size / 1024);
                    }, 0).toFixed(1)} GB
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <CogIcon className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">自动清理任务</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {cleanupTasks.filter(t => t.auto_run).length}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 清理任务列表 */}
          <div className="bg-white rounded-lg shadow-sm">
            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">清理任务管理</h3>
              <button className="inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700">
                <TrashIcon className="h-4 w-4 mr-2" />
                新建清理任务
              </button>
            </div>
            <div className="divide-y divide-gray-200">
              {cleanupTasks.map((cleanup) => (
                <div key={cleanup.id} className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="p-3 bg-red-100 rounded-lg">
                        <TrashIcon className="h-6 w-6 text-red-600" />
                      </div>
                      <div>
                        <h4 className="text-lg font-medium text-gray-900">{cleanup.task_name}</h4>
                        <p className="text-sm text-gray-600">目标: {
                          cleanup.target_type === 'logs' ? '日志文件' :
                          cleanup.target_type === 'temp_files' ? '临时文件' :
                          cleanup.target_type === 'old_backups' ? '旧备份文件' :
                          cleanup.target_type === 'unused_indexes' ? '未使用索引' : '死元组'
                        }</p>
                        <p className="text-sm text-gray-500">清理条件: {cleanup.criteria}</p>
                        <div className="flex items-center space-x-4 mt-2">
                          {cleanup.auto_run && (
                            <span className="inline-flex px-2 py-1 text-xs font-semibold bg-green-100 text-green-800 rounded-full">
                              自动运行
                            </span>
                          )}
                          <span className="text-xs text-gray-500">
                            上次清理: {cleanup.items_cleaned.toLocaleString()} 项，释放 {cleanup.space_freed}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">
                          最后运行: {cleanup.last_run}
                        </p>
                        <p className="text-sm text-gray-500">
                          清理 {cleanup.items_cleaned.toLocaleString()} 项
                        </p>
                      </div>

                      <button className="inline-flex items-center px-3 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700">
                        <PlayIcon className="h-4 w-4 mr-2" />
                        立即清理
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 性能优化标签页 */}
      {currentTab === 'optimization' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">数据库性能优化工具</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <ChartBarIcon className="h-6 w-6 text-blue-600" />
                  <h3 className="ml-2 text-lg font-medium text-gray-900">索引优化</h3>
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  分析和重建碎片化索引，优化查询性能
                </p>
                <button className="w-full px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700">
                  开始索引优化
                </button>
              </div>

              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <CircleStackIcon className="h-6 w-6 text-green-600" />
                  <h3 className="ml-2 text-lg font-medium text-gray-900">统计信息更新</h3>
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  更新表和索引统计信息，优化查询计划
                </p>
                <button className="w-full px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700">
                  更新统计信息
                </button>
              </div>

              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <CogIcon className="h-6 w-6 text-purple-600" />
                  <h3 className="ml-2 text-lg font-medium text-gray-900">配置优化</h3>
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  分析并优化数据库配置参数
                </p>
                <button className="w-full px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-lg hover:bg-purple-700">
                  配置分析
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 安全维护标签页 */}
      {currentTab === 'security' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">数据库安全维护</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <ShieldCheckIcon className="h-6 w-6 text-red-600" />
                  <h3 className="ml-2 text-lg font-medium text-gray-900">安全扫描</h3>
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  扫描数据库配置和权限，检测安全风险
                </p>
                <button className="w-full px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700">
                  开始安全扫描
                </button>
              </div>

              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <CogIcon className="h-6 w-6 text-orange-600" />
                  <h3 className="ml-2 text-lg font-medium text-gray-900">权限审计</h3>
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  审计用户权限和访问控制设置
                </p>
                <button className="w-full px-4 py-2 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700">
                  权限审计
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 新建备份任务模态框 */}
      {showCreateModal && (
        <CreateBackupTaskModal
          databases={databases?.data?.items || []}
          onClose={() => setShowCreateModal(false)}
          onSubmit={createBackupMutation.mutateAsync}
          isLoading={createBackupMutation.isPending}
        />
      )}

      {/* 编辑备份任务模态框 */}
      {editingTask && (
        <EditBackupTaskModal
          task={editingTask}
          databases={databases?.data?.items || []}
          onClose={() => setEditingTask(null)}
          onSubmit={updateBackupMutation.mutateAsync}
          isLoading={updateBackupMutation.isPending}
        />
      )}
    </div>
  );
};

// 新建备份任务模态框组件
interface CreateBackupTaskModalProps {
  databases: Array<{ id: number; name: string; type: string }>;
  onClose: () => void;
  onSubmit: (data: any) => Promise<any>;
  isLoading: boolean;
}

const CreateBackupTaskModal: React.FC<CreateBackupTaskModalProps> = ({
  databases,
  onClose,
  onSubmit,
  isLoading
}) => {
  const [formData, setFormData] = useState({
    database_id: '',
    task_name: '',
    backup_type: 'full',
    schedule: '',
    retention_days: 30,
    compression: true,
    encryption: false
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 基本验证
    const newErrors: Record<string, string> = {};
    if (!formData.database_id) newErrors.database_id = '请选择数据库';
    if (!formData.task_name.trim()) newErrors.task_name = '请输入任务名称';
    if (formData.task_name.length < 2) newErrors.task_name = '任务名称至少2个字符';
    if (formData.retention_days < 1 || formData.retention_days > 365) {
      newErrors.retention_days = '保留天数必须在1-365之间';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    try {
      await onSubmit({
        database_id: parseInt(formData.database_id),
        task_name: formData.task_name.trim(),
        backup_type: formData.backup_type,
        schedule: formData.schedule.trim() || undefined,
        retention_days: formData.retention_days,
        compression: formData.compression,
        encryption: formData.encryption
      });

      alert('备份任务创建成功！');
      onClose();
    } catch (error) {
      console.error('创建备份任务失败:', error);
      alert('创建备份任务失败，请稍后重试');
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">新建备份任务</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="px-6 py-4 space-y-4">
          {/* 数据库选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              数据库 <span className="text-red-500">*</span>
            </label>
            <select
              value={formData.database_id}
              onChange={(e) => handleInputChange('database_id', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.database_id ? 'border-red-500' : 'border-gray-300'
              }`}
            >
              <option value="">请选择数据库</option>
              {databases.map((db) => (
                <option key={db.id} value={db.id}>
                  {db.name} ({db.type})
                </option>
              ))}
            </select>
            {errors.database_id && (
              <p className="mt-1 text-sm text-red-600">{errors.database_id}</p>
            )}
          </div>

          {/* 任务名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              任务名称 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.task_name}
              onChange={(e) => handleInputChange('task_name', e.target.value)}
              placeholder="例如：每日全量备份"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.task_name ? 'border-red-500' : 'border-gray-300'
              }`}
            />
            {errors.task_name && (
              <p className="mt-1 text-sm text-red-600">{errors.task_name}</p>
            )}
          </div>

          {/* 备份类型 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              备份类型
            </label>
            <select
              value={formData.backup_type}
              onChange={(e) => handleInputChange('backup_type', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="full">全量备份</option>
              <option value="incremental">增量备份</option>
              <option value="differential">差异备份</option>
            </select>
          </div>

          {/* 调度设置 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              调度设置 (Cron表达式)
            </label>
            <input
              type="text"
              value={formData.schedule}
              onChange={(e) => handleInputChange('schedule', e.target.value)}
              placeholder="例如：0 2 * * * (每日凌晨2点)"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="mt-1 text-xs text-gray-500">
              留空表示手动执行。格式：分 时 日 月 周
            </p>
          </div>

          {/* 保留天数 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              保留天数
            </label>
            <input
              type="number"
              min="1"
              max="365"
              value={formData.retention_days}
              onChange={(e) => handleInputChange('retention_days', parseInt(e.target.value) || 30)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.retention_days ? 'border-red-500' : 'border-gray-300'
              }`}
            />
            {errors.retention_days && (
              <p className="mt-1 text-sm text-red-600">{errors.retention_days}</p>
            )}
          </div>

          {/* 压缩和加密选项 */}
          <div className="space-y-3">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="compression"
                checked={formData.compression}
                onChange={(e) => handleInputChange('compression', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="compression" className="ml-2 text-sm text-gray-700">
                启用压缩
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="encryption"
                checked={formData.encryption}
                onChange={(e) => handleInputChange('encryption', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="encryption" className="ml-2 text-sm text-gray-700">
                启用加密
              </label>
            </div>
          </div>
        </form>

        <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            type="button"
            onClick={onClose}
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 disabled:opacity-50"
          >
            取消
          </button>
          <button
            type="submit"
            onClick={handleSubmit}
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? '创建中...' : '创建任务'}
          </button>
        </div>
      </div>
    </div>
  );
};

// 编辑备份任务模态框组件
interface EditBackupTaskModalProps {
  task: ApiBackupTask;
  databases: Array<{ id: number; name: string; type: string }>;
  onClose: () => void;
  onSubmit: (data: { id: number; data: any }) => Promise<any>;
  isLoading: boolean;
}

const EditBackupTaskModal: React.FC<EditBackupTaskModalProps> = ({
  task,
  databases,
  onClose,
  onSubmit,
  isLoading
}) => {
  const [formData, setFormData] = useState({
    database_id: task.database_id.toString(),
    task_name: task.task_name,
    backup_type: task.backup_type,
    schedule: task.schedule || '',
    retention_days: task.retention_days,
    compression: task.compression,
    encryption: task.encryption
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 基本验证
    const newErrors: Record<string, string> = {};
    if (!formData.database_id) newErrors.database_id = '请选择数据库';
    if (!formData.task_name.trim()) newErrors.task_name = '请输入任务名称';
    if (formData.task_name.length < 2) newErrors.task_name = '任务名称至少2个字符';
    if (formData.retention_days < 1 || formData.retention_days > 365) {
      newErrors.retention_days = '保留天数必须在1-365之间';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    try {
      await onSubmit({
        id: task.id,
        data: {
          database_id: parseInt(formData.database_id),
          task_name: formData.task_name.trim(),
          backup_type: formData.backup_type,
          schedule: formData.schedule.trim() || undefined,
          retention_days: formData.retention_days,
          compression: formData.compression,
          encryption: formData.encryption
        }
      });

      alert('备份任务更新成功！');
      onClose();
    } catch (error) {
      console.error('更新备份任务失败:', error);
      alert('更新备份任务失败，请稍后重试');
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">编辑备份任务</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="px-6 py-4 space-y-4">
          {/* 数据库选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              数据库 <span className="text-red-500">*</span>
            </label>
            <select
              value={formData.database_id}
              onChange={(e) => handleInputChange('database_id', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.database_id ? 'border-red-500' : 'border-gray-300'
              }`}
            >
              <option value="">请选择数据库</option>
              {databases.map((db) => (
                <option key={db.id} value={db.id}>
                  {db.name} ({db.type})
                </option>
              ))}
            </select>
            {errors.database_id && (
              <p className="mt-1 text-sm text-red-600">{errors.database_id}</p>
            )}
          </div>

          {/* 任务名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              任务名称 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.task_name}
              onChange={(e) => handleInputChange('task_name', e.target.value)}
              placeholder="例如：每日全量备份"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.task_name ? 'border-red-500' : 'border-gray-300'
              }`}
            />
            {errors.task_name && (
              <p className="mt-1 text-sm text-red-600">{errors.task_name}</p>
            )}
          </div>

          {/* 备份类型 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              备份类型
            </label>
            <select
              value={formData.backup_type}
              onChange={(e) => handleInputChange('backup_type', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="full">全量备份</option>
              <option value="incremental">增量备份</option>
              <option value="differential">差异备份</option>
            </select>
          </div>

          {/* 调度设置 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              调度设置 (Cron表达式)
            </label>
            <input
              type="text"
              value={formData.schedule}
              onChange={(e) => handleInputChange('schedule', e.target.value)}
              placeholder="例如：0 2 * * * (每日凌晨2点)"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="mt-1 text-xs text-gray-500">
              留空表示手动执行。格式：分 时 日 月 周
            </p>
          </div>

          {/* 保留天数 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              保留天数
            </label>
            <input
              type="number"
              min="1"
              max="365"
              value={formData.retention_days}
              onChange={(e) => handleInputChange('retention_days', parseInt(e.target.value) || 30)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.retention_days ? 'border-red-500' : 'border-gray-300'
              }`}
            />
            {errors.retention_days && (
              <p className="mt-1 text-sm text-red-600">{errors.retention_days}</p>
            )}
          </div>

          {/* 压缩和加密选项 */}
          <div className="space-y-3">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="edit-compression"
                checked={formData.compression}
                onChange={(e) => handleInputChange('compression', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="edit-compression" className="ml-2 text-sm text-gray-700">
                启用压缩
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="edit-encryption"
                checked={formData.encryption}
                onChange={(e) => handleInputChange('encryption', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="edit-encryption" className="ml-2 text-sm text-gray-700">
                启用加密
              </label>
            </div>
          </div>
        </form>

        <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            type="button"
            onClick={onClose}
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 disabled:opacity-50"
          >
            取消
          </button>
          <button
            type="submit"
            onClick={handleSubmit}
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? '更新中...' : '更新任务'}
          </button>
        </div>
      </div>
    </div>
  );
};
