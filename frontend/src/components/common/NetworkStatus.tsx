import React from 'react';
import { 
  WifiIcon, 
  ExclamationTriangleIcon,
  XCircleIcon 
} from '@heroicons/react/24/outline';
import { useNetworkStatus } from '../../hooks';

interface NetworkStatusProps {
  className?: string;
  showText?: boolean;
}

export const NetworkStatus: React.FC<NetworkStatusProps> = ({ 
  className = '', 
  showText = true 
}) => {
  const { isOnline, isBackendReachable, isConnected } = useNetworkStatus();

  // 获取状态信息
  const getStatusInfo = () => {
    if (!isOnline) {
      return {
        icon: XCircleIcon,
        text: '网络断开',
        color: 'text-red-500',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200'
      };
    }
    
    if (!isBackendReachable) {
      return {
        icon: ExclamationTriangleIcon,
        text: '服务器连接失败',
        color: 'text-orange-500',
        bgColor: 'bg-orange-50',
        borderColor: 'border-orange-200'
      };
    }
    
    return {
      icon: WifiIcon,
      text: '连接正常',
      color: 'text-green-500',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    };
  };

  const statusInfo = getStatusInfo();
  const Icon = statusInfo.icon;

  // 如果连接正常且不显示文本，则不渲染组件
  if (isConnected && !showText) {
    return null;
  }

  return (
    <div className={`
      inline-flex items-center gap-2 px-3 py-1.5 rounded-lg border
      ${statusInfo.bgColor} ${statusInfo.borderColor} ${className}
    `}>
      <Icon className={`h-4 w-4 ${statusInfo.color}`} />
      {showText && (
        <span className={`text-sm font-medium ${statusInfo.color}`}>
          {statusInfo.text}
        </span>
      )}
    </div>
  );
};

// 网络状态横幅组件（用于页面顶部显示）
export const NetworkStatusBanner: React.FC = () => {
  const { isConnected, isOnline, isBackendReachable } = useNetworkStatus();

  // 连接正常时不显示横幅
  if (isConnected) {
    return null;
  }

  const getMessage = () => {
    if (!isOnline) {
      return {
        title: '网络连接断开',
        message: '请检查您的网络连接',
        bgColor: 'bg-red-50',
        textColor: 'text-red-800',
        borderColor: 'border-red-200'
      };
    }
    
    if (!isBackendReachable) {
      return {
        title: '服务器连接失败',
        message: '正在尝试重新连接，部分功能可能不可用',
        bgColor: 'bg-orange-50',
        textColor: 'text-orange-800',
        borderColor: 'border-orange-200'
      };
    }
    
    return null;
  };

  const messageInfo = getMessage();
  
  if (!messageInfo) {
    return null;
  }

  return (
    <div className={`
      border-l-4 p-4 ${messageInfo.bgColor} ${messageInfo.borderColor}
    `}>
      <div className="flex">
        <div className="flex-shrink-0">
          <ExclamationTriangleIcon 
            className={`h-5 w-5 ${messageInfo.textColor}`} 
            aria-hidden="true" 
          />
        </div>
        <div className="ml-3">
          <p className={`text-sm font-medium ${messageInfo.textColor}`}>
            {messageInfo.title}
          </p>
          <p className={`mt-1 text-sm ${messageInfo.textColor} opacity-75`}>
            {messageInfo.message}
          </p>
        </div>
      </div>
    </div>
  );
};

// 简单的连接状态点指示器
export const ConnectionDot: React.FC<{ className?: string }> = ({ 
  className = '' 
}) => {
  const { isConnected } = useNetworkStatus();
  
  return (
    <div className={`
      w-2 h-2 rounded-full
      ${isConnected ? 'bg-green-500' : 'bg-red-500'}
      ${className}
    `} />
  );
};
