import React, { useState, useEffect } from 'react';
import {
  ChevronDownIcon,
  ChevronRightIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

interface CollapsibleSectionProps {
  title: string;
  subtitle?: string;
  icon?: React.ComponentType<{ className?: string }>;
  children: React.ReactNode;
  defaultExpanded?: boolean;
  persistKey?: string; // 用于状态持久化的唯一键
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  showIndicator?: boolean;
  disabled?: boolean;
  onToggle?: (expanded: boolean) => void;
}

/**
 * 可折叠区域组件
 * 支持状态持久化、自定义样式、禁用状态等
 */
export const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  title,
  subtitle,
  icon: Icon,
  children,
  defaultExpanded = false,
  persistKey,
  className = '',
  headerClassName = '',
  contentClassName = '',
  showIndicator = true,
  disabled = false,
  onToggle
}) => {
  // 状态管理 - 支持持久化
  const [isExpanded, setIsExpanded] = useState(() => {
    if (persistKey && typeof window !== 'undefined') {
      const saved = localStorage.getItem(`collapsible-${persistKey}`);
      return saved ? JSON.parse(saved) : defaultExpanded;
    }
    return defaultExpanded;
  });

  // 状态持久化
  useEffect(() => {
    if (persistKey && typeof window !== 'undefined') {
      localStorage.setItem(`collapsible-${persistKey}`, JSON.stringify(isExpanded));
    }
  }, [isExpanded, persistKey]);

  const handleToggle = () => {
    if (disabled) return;
    
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);
    onToggle?.(newExpanded);
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* 可点击的标题栏 */}
      <button
        onClick={handleToggle}
        disabled={disabled}
        className={`
          w-full px-4 py-3 flex items-center justify-between
          text-left transition-colors duration-200
          ${disabled 
            ? 'cursor-not-allowed opacity-50' 
            : 'hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset'
          }
          ${headerClassName}
        `}
      >
        <div className="flex items-center space-x-3 flex-1">
          {/* 图标 */}
          {Icon && (
            <Icon className="h-5 w-5 text-gray-400 flex-shrink-0" />
          )}
          
          {/* 标题和副标题 */}
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-medium text-gray-900 truncate">
              {title}
            </h3>
            {subtitle && (
              <p className="text-xs text-gray-500 mt-0.5 truncate">
                {subtitle}
              </p>
            )}
          </div>
          
          {/* 状态指示器 */}
          {showIndicator && (
            <div className="flex items-center space-x-2">
              {isExpanded ? (
                <ChevronDownIcon className="h-4 w-4 text-gray-400 transition-transform duration-200" />
              ) : (
                <ChevronRightIcon className="h-4 w-4 text-gray-400 transition-transform duration-200" />
              )}
            </div>
          )}
        </div>
      </button>

      {/* 可折叠的内容区域 */}
      <div
        className={`
          overflow-hidden transition-all duration-300 ease-in-out
          ${isExpanded ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'}
        `}
      >
        <div className={`px-4 pb-4 ${contentClassName}`}>
          {children}
        </div>
      </div>
    </div>
  );
};

/**
 * 简化版可折叠区域 - 用于简单的展开/折叠需求
 */
export const SimpleCollapsible: React.FC<{
  title: string;
  children: React.ReactNode;
  defaultExpanded?: boolean;
  className?: string;
}> = ({ title, children, defaultExpanded = false, className = '' }) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  return (
    <div className={className}>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center space-x-2 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
      >
        {isExpanded ? (
          <ChevronDownIcon className="h-4 w-4" />
        ) : (
          <ChevronRightIcon className="h-4 w-4" />
        )}
        <span>{title}</span>
      </button>
      
      {isExpanded && (
        <div className="mt-2 pl-6">
          {children}
        </div>
      )}
    </div>
  );
};

/**
 * 信息提示可折叠区域 - 用于帮助信息、说明等
 */
export const InfoCollapsible: React.FC<{
  title: string;
  children: React.ReactNode;
  type?: 'info' | 'warning' | 'success' | 'error';
  className?: string;
}> = ({ title, children, type = 'info', className = '' }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const typeStyles = {
    info: 'bg-blue-50 border-blue-200 text-blue-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    success: 'bg-green-50 border-green-200 text-green-800',
    error: 'bg-red-50 border-red-200 text-red-800'
  };

  return (
    <div className={`border rounded-lg ${typeStyles[type]} ${className}`}>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full px-3 py-2 flex items-center justify-between text-left hover:bg-opacity-80 transition-colors"
      >
        <div className="flex items-center space-x-2">
          <InformationCircleIcon className="h-4 w-4" />
          <span className="text-sm font-medium">{title}</span>
        </div>
        {isExpanded ? (
          <ChevronDownIcon className="h-4 w-4" />
        ) : (
          <ChevronRightIcon className="h-4 w-4" />
        )}
      </button>
      
      {isExpanded && (
        <div className="px-3 pb-3 text-sm">
          {children}
        </div>
      )}
    </div>
  );
};
