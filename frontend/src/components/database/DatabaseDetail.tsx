import React from 'react';
import { 
  ArrowLeftIcon,
  ServerIcon,
  ChartBarIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  CpuChipIcon,
  CircleStackIcon,
  UsersIcon
} from '@heroicons/react/24/outline';

interface DatabaseDetailProps {
  database: {
    id: string;
    name: string;
    type: string;
    status: string;
    host: string;
    port: number;
    version: string;
  };
  onBack: () => void;
}

export const DatabaseDetail: React.FC<DatabaseDetailProps> = ({ database, onBack }) => {
  // 模拟实时数据
  const [metrics, setMetrics] = React.useState({
    cpu: 45,
    memory: 68,
    connections: 127,
    qps: 1250,
    tps: 89,
    diskUsage: 72
  });

  // 模拟数据更新
  React.useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(prev => ({
        cpu: Math.max(20, Math.min(90, prev.cpu + (Math.random() - 0.5) * 10)),
        memory: Math.max(30, Math.min(95, prev.memory + (Math.random() - 0.5) * 5)),
        connections: Math.max(50, Math.min(200, prev.connections + Math.floor((Math.random() - 0.5) * 20))),
        qps: Math.max(800, Math.min(2000, prev.qps + Math.floor((Math.random() - 0.5) * 200))),
        tps: Math.max(50, Math.min(150, prev.tps + Math.floor((Math.random() - 0.5) * 20))),
        diskUsage: Math.max(50, Math.min(90, prev.diskUsage + (Math.random() - 0.5) * 2))
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getMetricColor = (value: number, threshold: number = 80) => {
    if (value >= threshold) return 'text-red-600';
    if (value >= threshold * 0.7) return 'text-yellow-600';
    return 'text-green-600';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={onBack}
                className="btn-secondary p-2 hover:bg-gray-100"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </button>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <ServerIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">{database.name}</h1>
                  <p className="text-sm text-gray-500">{database.type} • {database.host}:{database.port}</p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(database.status)}`}>
                <CheckCircleIcon className="h-4 w-4 mr-1" />
                {database.status}
              </span>
              <span className="text-sm text-gray-500">版本 {database.version}</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Real-time Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="metric-card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">CPU 使用率</p>
                <p className={`text-2xl font-bold ${getMetricColor(metrics.cpu)}`}>{metrics.cpu}%</p>
              </div>
              <CpuChipIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="mt-3">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-500 ${
                    metrics.cpu >= 80 ? 'bg-red-500' : 
                    metrics.cpu >= 60 ? 'bg-yellow-500' : 'bg-green-500'
                  }`}
                  style={{ width: `${metrics.cpu}%` }}
                ></div>
              </div>
            </div>
          </div>

          <div className="metric-card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">内存使用率</p>
                <p className={`text-2xl font-bold ${getMetricColor(metrics.memory)}`}>{metrics.memory}%</p>
              </div>
              <CircleStackIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="mt-3">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-500 ${
                    metrics.memory >= 80 ? 'bg-red-500' : 
                    metrics.memory >= 60 ? 'bg-yellow-500' : 'bg-green-500'
                  }`}
                  style={{ width: `${metrics.memory}%` }}
                ></div>
              </div>
            </div>
          </div>

          <div className="metric-card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">活跃连接</p>
                <p className="text-2xl font-bold text-gray-900">{metrics.connections}</p>
              </div>
              <UsersIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div className="mt-3">
              <p className="text-xs text-gray-500">最大连接数: 500</p>
            </div>
          </div>

          <div className="metric-card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">查询/秒</p>
                <p className="text-2xl font-bold text-gray-900">{metrics.qps}</p>
              </div>
              <ChartBarIcon className="h-8 w-8 text-indigo-600" />
            </div>
            <div className="mt-3">
              <p className="text-xs text-gray-500">平均响应时间: 12ms</p>
            </div>
          </div>

          <div className="metric-card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">事务/秒</p>
                <p className="text-2xl font-bold text-gray-900">{metrics.tps}</p>
              </div>
              <ClockIcon className="h-8 w-8 text-orange-600" />
            </div>
            <div className="mt-3">
              <p className="text-xs text-gray-500">事务成功率: 99.8%</p>
            </div>
          </div>

          <div className="metric-card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">磁盘使用率</p>
                <p className={`text-2xl font-bold ${getMetricColor(metrics.diskUsage)}`}>{metrics.diskUsage}%</p>
              </div>
              <CircleStackIcon className="h-8 w-8 text-gray-600" />
            </div>
            <div className="mt-3">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-500 ${
                    metrics.diskUsage >= 80 ? 'bg-red-500' : 
                    metrics.diskUsage >= 60 ? 'bg-yellow-500' : 'bg-green-500'
                  }`}
                  style={{ width: `${metrics.diskUsage}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Configuration and Logs */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="chart-container">
            <div className="chart-header">
              <h3 className="chart-title">数据库配置</h3>
            </div>
            <div className="space-y-4">
              <div className="flex justify-between py-2 border-b border-gray-100">
                <span className="text-sm font-medium text-gray-600">主机地址</span>
                <span className="text-sm text-gray-900">{database.host}</span>
              </div>
              <div className="flex justify-between py-2 border-b border-gray-100">
                <span className="text-sm font-medium text-gray-600">端口</span>
                <span className="text-sm text-gray-900">{database.port}</span>
              </div>
              <div className="flex justify-between py-2 border-b border-gray-100">
                <span className="text-sm font-medium text-gray-600">数据库类型</span>
                <span className="text-sm text-gray-900">{database.type}</span>
              </div>
              <div className="flex justify-between py-2 border-b border-gray-100">
                <span className="text-sm font-medium text-gray-600">版本</span>
                <span className="text-sm text-gray-900">{database.version}</span>
              </div>
              <div className="flex justify-between py-2 border-b border-gray-100">
                <span className="text-sm font-medium text-gray-600">字符集</span>
                <span className="text-sm text-gray-900">UTF8MB4</span>
              </div>
              <div className="flex justify-between py-2">
                <span className="text-sm font-medium text-gray-600">时区</span>
                <span className="text-sm text-gray-900">Asia/Shanghai</span>
              </div>
            </div>
          </div>

          <div className="chart-container">
            <div className="chart-header">
              <h3 className="chart-title">最近活动</h3>
            </div>
            <div className="space-y-3">
              <div className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg">
                <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-900">连接成功</p>
                  <p className="text-xs text-gray-500">2分钟前</p>
                </div>
              </div>
              <div className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                <ChartBarIcon className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-900">性能监控已启动</p>
                  <p className="text-xs text-gray-500">5分钟前</p>
                </div>
              </div>
              <div className="flex items-start space-x-3 p-3 bg-yellow-50 rounded-lg">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-900">慢查询检测</p>
                  <p className="text-xs text-gray-500">10分钟前</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};
