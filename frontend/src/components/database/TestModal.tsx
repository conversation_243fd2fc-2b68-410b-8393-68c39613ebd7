import React from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';

interface TestModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const TestModal: React.FC<TestModalProps> = ({ isOpen, onClose }) => {
  console.log('TestModal 渲染:', { isOpen });
  
  if (!isOpen) {
    console.log('TestModal 未打开');
    return null;
  }

  console.log('TestModal 正在显示');

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center"
      style={{ zIndex: 10000 }}
    >
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">测试模态框</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>
        
        <div className="mb-4">
          <p>这是一个测试模态框，用于验证模态框功能是否正常。</p>
        </div>
        
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
          >
            关闭
          </button>
          <button
            onClick={() => {
              alert('测试按钮点击成功！');
              onClose();
            }}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            测试
          </button>
        </div>
      </div>
    </div>
  );
};
