import React, { useState, useEffect } from 'react';
import { 
  XMarkIcon,
  ServerIcon,
  CheckIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface DatabaseFormData {
  name: string;
  type: string;
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  description: string;
  ssl: boolean;
  connectionTimeout: number;
}

interface DatabaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: DatabaseFormData) => void;
  database?: any; // 编辑时传入的数据库信息
  mode: 'add' | 'edit';
}

export const DatabaseModal: React.FC<DatabaseModalProps> = ({
  isOpen,
  onClose,
  onSave,
  database,
  mode
}) => {
  const [formData, setFormData] = useState<DatabaseFormData>({
    name: '',
    type: 'postgresql',
    host: 'localhost',
    port: 5432,
    username: '',
    password: '',
    database: '',
    description: '',
    ssl: false,
    connectionTimeout: 30
  });

  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [connectionResult, setConnectionResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  const [errors, setErrors] = useState<Record<string, string>>({});

  // 数据库类型配置
  const databaseTypes = [
    { value: 'postgresql', label: 'PostgreSQL', defaultPort: 5432 },
    { value: 'mysql', label: 'MySQL', defaultPort: 3306 },
    { value: 'redis', label: 'Redis', defaultPort: 6379 },
    { value: 'mongodb', label: 'MongoDB', defaultPort: 27017 },
    { value: 'sqlserver', label: 'SQL Server', defaultPort: 1433 },
    { value: 'oracle', label: 'Oracle', defaultPort: 1521 }
  ];

  // 初始化表单数据
  useEffect(() => {
    if (mode === 'edit' && database) {
      setFormData({
        name: database.name || '',
        type: database.type || 'postgresql',
        host: database.host || 'localhost',
        port: database.port || 5432,
        username: database.username || '',
        password: '', // 安全考虑，不显示密码
        database: database.database || '',
        description: database.description || '',
        ssl: database.ssl || false,
        connectionTimeout: database.connectionTimeout || 30
      });
    } else {
      // 重置表单
      setFormData({
        name: '',
        type: 'postgresql',
        host: 'localhost',
        port: 5432,
        username: '',
        password: '',
        database: '',
        description: '',
        ssl: false,
        connectionTimeout: 30
      });
    }
    setErrors({});
    setConnectionResult(null);
  }, [mode, database, isOpen]);

  // 处理表单字段变化
  const handleInputChange = (field: keyof DatabaseFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // 处理数据库类型变化
  const handleTypeChange = (type: string) => {
    const dbType = databaseTypes.find(t => t.value === type);
    setFormData(prev => ({
      ...prev,
      type,
      port: dbType?.defaultPort || prev.port
    }));
  };

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = '数据库名称不能为空';
    }
    if (!formData.host.trim()) {
      newErrors.host = '主机地址不能为空';
    }
    if (!formData.port || formData.port < 1 || formData.port > 65535) {
      newErrors.port = '端口号必须在1-65535之间';
    }
    if (!formData.username.trim()) {
      newErrors.username = '用户名不能为空';
    }
    if (mode === 'add' && !formData.password.trim()) {
      newErrors.password = '密码不能为空';
    }
    if (!formData.database.trim()) {
      newErrors.database = '数据库名不能为空';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 测试连接
  const testConnection = async () => {
    if (!validateForm()) {
      return;
    }

    setIsTestingConnection(true);
    setConnectionResult(null);

    try {
      // 这里应该调用真实的连接测试API
      // 暂时使用模拟测试，后续可以替换为真实API调用
      // const testData = {
      //   name: formData.name,
      //   type: formData.type,
      //   host: formData.host,
      //   port: formData.port,
      //   username: formData.username,
      //   password: formData.password,
      //   database_name: formData.database,
      //   description: formData.description
      // };

      await new Promise(resolve => setTimeout(resolve, 2000));

      // 模拟连接结果 - 70% 成功率
      const success = Math.random() > 0.3;

      setConnectionResult({
        success,
        message: success
          ? '连接测试成功！数据库可以正常访问。'
          : '连接测试失败！请检查主机地址、端口号、用户名和密码。'
      });
    } catch (error) {
      setConnectionResult({
        success: false,
        message: '连接测试出现异常，请稍后重试。'
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  // 保存数据库配置
  const handleSave = () => {
    if (!validateForm()) {
      return;
    }

    onSave(formData);
    onClose();
  };

  console.log('DatabaseModal 渲染:', { isOpen, mode, database });

  if (!isOpen) {
    console.log('模态框未打开，返回null');
    return null;
  }

  console.log('模态框正在渲染...');

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center"
      style={{ zIndex: 10000 }}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          console.log('点击外部区域关闭');
          onClose();
        }
      }}
    >
      {/* 模态框 */}
      <div
        className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
          {/* 头部 */}
          <div className="bg-white px-6 py-4 border-b border-gray-200 rounded-t-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <ServerIcon className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">
                  {mode === 'add' ? '添加数据库' : '编辑数据库'}
                </h3>
              </div>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log('点击关闭按钮');
                  onClose();
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* 表单内容 */}
          <div className="px-6 py-4 max-h-[60vh] overflow-y-auto">
            <div className="space-y-4">
              {/* 基本信息 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    数据库名称 *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.name ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="输入数据库名称"
                  />
                  {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    数据库类型 *
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => handleTypeChange(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {databaseTypes.map(type => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* 连接信息 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    主机地址 *
                  </label>
                  <input
                    type="text"
                    value={formData.host}
                    onChange={(e) => handleInputChange('host', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.host ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="localhost 或 IP 地址"
                  />
                  {errors.host && <p className="text-red-500 text-xs mt-1">{errors.host}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    端口 *
                  </label>
                  <input
                    type="number"
                    value={formData.port}
                    onChange={(e) => handleInputChange('port', parseInt(e.target.value) || 0)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.port ? 'border-red-500' : 'border-gray-300'
                    }`}
                    min="1"
                    max="65535"
                  />
                  {errors.port && <p className="text-red-500 text-xs mt-1">{errors.port}</p>}
                </div>
              </div>

              {/* 认证信息 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    用户名 *
                  </label>
                  <input
                    type="text"
                    value={formData.username}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.username ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="数据库用户名"
                  />
                  {errors.username && <p className="text-red-500 text-xs mt-1">{errors.username}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    密码 {mode === 'add' ? '*' : '(留空保持不变)'}
                  </label>
                  <input
                    type="password"
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.password ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="数据库密码"
                  />
                  {errors.password && <p className="text-red-500 text-xs mt-1">{errors.password}</p>}
                </div>
              </div>

              {/* 数据库名和描述 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  数据库名
                </label>
                <input
                  type="text"
                  value={formData.database}
                  onChange={(e) => handleInputChange('database', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="要连接的数据库名称"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  描述
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={2}
                  placeholder="数据库用途描述（可选）"
                />
              </div>

              {/* 高级选项 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="ssl"
                    checked={formData.ssl}
                    onChange={(e) => handleInputChange('ssl', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="ssl" className="ml-2 block text-sm text-gray-700">
                    启用 SSL 连接
                  </label>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    连接超时 (秒)
                  </label>
                  <input
                    type="number"
                    value={formData.connectionTimeout}
                    onChange={(e) => handleInputChange('connectionTimeout', parseInt(e.target.value) || 30)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    min="5"
                    max="300"
                  />
                </div>
              </div>

              {/* 连接测试结果 */}
              {connectionResult && (
                <div className={`p-3 rounded-lg ${
                  connectionResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                }`}>
                  <div className="flex items-center">
                    {connectionResult.success ? (
                      <CheckIcon className="h-5 w-5 text-green-600 mr-2" />
                    ) : (
                      <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mr-2" />
                    )}
                    <span className={`text-sm ${
                      connectionResult.success ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {connectionResult.message}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 底部按钮 */}
          <div className="bg-gray-50 px-6 py-4 flex justify-between rounded-b-lg">
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('点击测试连接按钮');
                testConnection();
              }}
              disabled={isTestingConnection}
              className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isTestingConnection ? '测试中...' : '测试连接'}
            </button>
            
            <div className="flex space-x-3">
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log('点击取消按钮');
                  onClose();
                }}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log('点击保存按钮');
                  handleSave();
                }}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              >
                {mode === 'add' ? '添加' : '保存'}
              </button>
            </div>
          </div>
        </div>
    </div>
  );
};
