import React, { useState } from 'react';
import {
  PlusIcon,
  ServerIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import {
  useDatabases,
  useCreateDatabase,
  useUpdateDatabase,
  useDeleteDatabase,
  useTestConnection
} from '../../queries';
import { DatabaseModal } from './DatabaseModal';
import type { DatabaseInstance } from '../../services/api';

interface DatabaseManagementProps {
  onDatabaseSelect?: (database: DatabaseInstance) => void;
}

export const DatabaseManagement: React.FC<DatabaseManagementProps> = ({ onDatabaseSelect }) => {
  // 使用React Query的数据库查询
  const { data: databasesResponse, isLoading: loading, error, refetch } = useDatabases();
  // 修复数据解析路径：后端返回的是 data.data，而不是 data.items
  const databases = (databasesResponse?.data?.items || []) as DatabaseInstance[];

  // 模态框状态
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingDatabase, setEditingDatabase] = useState<DatabaseInstance | null>(null);

  // 创建数据库的mutation
  const createDatabaseMutation = useCreateDatabase({
    onSuccess: () => {
      setIsModalOpen(false);
      // React Query会自动刷新数据库列表，无需手动调用refetch
      console.log('数据库创建成功');
    },
    onError: (error: any) => {
      console.error('创建数据库失败:', error);

      // 根据错误类型显示不同的提示
      let errorMessage = '创建数据库失败';
      if (error?.response?.status === 409) {
        errorMessage = '数据库连接已存在，请检查主机、端口和数据库名称是否重复';
      } else if (error?.response?.status === 400) {
        errorMessage = '请检查输入的数据库信息是否正确';
      } else if (error?.message) {
        errorMessage = error.message;
      }

      // 这里可以添加Toast通知或其他用户提示
      alert(errorMessage);
    }
  });

  // 更新数据库的mutation
  const updateDatabaseMutation = useUpdateDatabase({
    onSuccess: () => {
      setIsModalOpen(false);
      setEditingDatabase(null);
      console.log('数据库更新成功');
    },
    onError: (error: any) => {
      console.error('更新数据库失败:', error);
      alert('更新数据库失败: ' + (error?.message || '未知错误'));
    }
  });

  // 删除数据库的mutation
  const deleteDatabaseMutation = useDeleteDatabase({
    onSuccess: () => {
      console.log('数据库删除成功');
    },
    onError: (error: any) => {
      console.error('删除数据库失败:', error);
      alert('删除数据库失败: ' + (error?.message || '未知错误'));
    }
  });

  // 测试连接的mutation
  const testConnectionMutation = useTestConnection({
    onSuccess: (result: any) => {
      if (result?.data?.success) {
        alert('连接测试成功！');
      } else {
        alert('连接测试失败: ' + (result?.data?.message || '未知错误'));
      }
    },
    onError: (error: any) => {
      console.error('测试连接失败:', error);
      alert('测试连接失败: ' + (error?.message || '未知错误'));
    }
  });

  // 处理添加数据库
  const handleAddDatabase = () => {
    setEditingDatabase(null);
    setIsModalOpen(true);
  };

  // 处理编辑数据库
  const handleEditDatabase = (database: DatabaseInstance) => {
    setEditingDatabase(database);
    setIsModalOpen(true);
  };

  // 处理删除数据库
  const handleDeleteDatabase = (database: DatabaseInstance) => {
    if (window.confirm(`确定要删除数据库 "${database.name}" 吗？此操作不可恢复。`)) {
      deleteDatabaseMutation.mutate(database.id);
    }
  };

  // 处理测试连接
  const handleTestConnection = (database: DatabaseInstance) => {
    testConnectionMutation.mutate(database.id);
  };

  // 处理保存数据库
  const handleSaveDatabase = (formData: any) => {
    if (editingDatabase) {
      // 编辑模式 - 实现更新逻辑
      const updateData = {
        name: formData.name || '',
        type: formData.type || 'postgresql',
        host: formData.host || '',
        port: formData.port || 5432,
        username: formData.username || '',
        password: formData.password || '',
        database_name: formData.database || formData.name || '',
        description: formData.description || ''
      };

      // 验证必填字段
      if (!updateData.name || !updateData.host || !updateData.username || !updateData.database_name) {
        alert('请填写所有必填字段');
        return;
      }

      console.log('发送更新数据库请求:', updateData);
      updateDatabaseMutation.mutate({
        id: editingDatabase.id,
        data: updateData
      });
    } else {
      // 添加模式 - 确保所有必填字段都有值
      const createData = {
        name: formData.name || '',
        type: formData.type || 'postgresql',
        host: formData.host || '',
        port: formData.port || 5432,
        username: formData.username || '',
        password: formData.password || '',
        database_name: formData.database || formData.name || '', // 如果database为空，使用name作为fallback
        description: formData.description || ''
      };

      // 验证必填字段
      if (!createData.name || !createData.host || !createData.username || !createData.password || !createData.database_name) {
        alert('请填写所有必填字段');
        return;
      }

      console.log('发送创建数据库请求:', createData);
      createDatabaseMutation.mutate(createData);
    }
  };

  // 状态映射函数 - 将后端状态映射到前端显示
  const mapStatus = (status: string): 'running' | 'stopped' | 'error' | 'warning' => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'running';
      case 'inactive':
        return 'stopped';
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      default:
        return 'stopped';
    }
  };

  const getStatusIcon = (status: string) => {
    const mappedStatus = mapStatus(status);
    switch (mappedStatus) {
      case 'running':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      case 'error':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'stopped':
        return <XCircleIcon className="h-5 w-5 text-gray-500" />;
      default:
        return <CheckCircleIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  // 加载状态显示
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">数据库管理</h1>
            <p className="text-gray-600">管理和监控数据库连接</p>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <ArrowPathIcon className="h-8 w-8 text-blue-600 animate-spin" />
          <span className="ml-2 text-gray-600">加载数据库列表...</span>
        </div>
      </div>
    );
  }

  // 错误状态显示
  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">数据库管理</h1>
            <p className="text-gray-600">管理和监控数据库连接</p>
          </div>
          <button
            onClick={() => refetch()}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
          >
            <ArrowPathIcon className="h-5 w-5" />
            重试
          </button>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <XCircleIcon className="h-5 w-5 text-red-500 mr-2" />
            <span className="text-red-700">加载数据库列表失败: {error?.message || '未知错误'}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">数据库管理</h1>
          <p className="text-gray-600">管理和监控数据库连接 ({databases.length} 个数据库)</p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => refetch()}
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center gap-2"
          >
            <ArrowPathIcon className="h-5 w-5" />
            刷新
          </button>
          <button
            onClick={handleAddDatabase}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
          >
            <PlusIcon className="h-5 w-5" />
            添加数据库
          </button>
        </div>
      </div>

      {databases.length === 0 ? (
        <div className="text-center py-12">
          <ServerIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无数据库</h3>
          <p className="text-gray-600 mb-4">开始添加您的第一个数据库连接</p>
          <button
            onClick={handleAddDatabase}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2 mx-auto"
          >
            <PlusIcon className="h-5 w-5" />
            添加数据库
          </button>
        </div>
      ) : (
        <div className="grid gap-6">
          {databases.map((database) => (
            <div key={database.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-4">
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <ServerIcon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="text-lg font-semibold text-gray-900">{database.name}</h3>
                      <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded">
                        {database.type}
                      </span>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(database.status)}
                        <span className="text-sm text-gray-600 capitalize">{mapStatus(database.status)}</span>
                      </div>
                    </div>
                  <p className="text-gray-600 mt-1">{database.description || '暂无描述'}</p>
                  <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                    <span>📍 {database.host}:{database.port}</span>
                    <span>🗄️ {database.database_name || 'N/A'}</span>
                    <span>👤 {database.username || 'N/A'}</span>
                    {database.version && <span>📊 版本: {database.version}</span>}
                  </div>
                  <div className="flex items-center gap-4 mt-1 text-sm text-gray-500">
                    <span>📅 创建: {new Date(database.created_at).toLocaleDateString()}</span>
                    <span>🔄 更新: {new Date(database.updated_at).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4 mt-4 pt-4 border-t border-gray-100">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {database.monitoring_enabled ? '✅' : '❌'}
                </div>
                <div className="text-sm text-gray-500">监控状态</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {database.backup_enabled ? '✅' : '❌'}
                </div>
                <div className="text-sm text-gray-500">备份状态</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {database.connections || 0}
                </div>
                <div className="text-sm text-gray-500">活跃连接</div>
              </div>
            </div>

            <div className="flex gap-2 mt-4 pt-4 border-t border-gray-100">
              <button
                onClick={() => onDatabaseSelect?.(database)}
                className="px-3 py-1 text-sm bg-blue-50 text-blue-600 rounded hover:bg-blue-100"
              >
                查看详情
              </button>
              <button
                onClick={() => handleEditDatabase(database)}
                className="px-3 py-1 text-sm bg-gray-50 text-gray-600 rounded hover:bg-gray-100"
                disabled={updateDatabaseMutation.isPending}
              >
                {updateDatabaseMutation.isPending ? '更新中...' : '编辑'}
              </button>
              <button
                onClick={() => handleTestConnection(database)}
                className="px-3 py-1 text-sm bg-green-50 text-green-600 rounded hover:bg-green-100"
                disabled={testConnectionMutation.isPending}
              >
                {testConnectionMutation.isPending ? '测试中...' : '测试连接'}
              </button>
              <button
                onClick={() => handleDeleteDatabase(database)}
                className="px-3 py-1 text-sm bg-red-50 text-red-600 rounded hover:bg-red-100"
                disabled={deleteDatabaseMutation.isPending}
              >
                {deleteDatabaseMutation.isPending ? '删除中...' : '删除'}
              </button>
            </div>
          </div>
        ))}
        </div>
      )}

      {/* 数据库添加/编辑模态框 */}
      <DatabaseModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={handleSaveDatabase}
        database={editingDatabase}
        mode={editingDatabase ? 'edit' : 'add'}
      />
    </div>
  );
};
