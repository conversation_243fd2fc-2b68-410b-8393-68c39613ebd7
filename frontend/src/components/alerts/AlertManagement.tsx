import React, { useState } from 'react';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  BellIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  PlayIcon,
  PauseIcon,
  XMarkIcon,
  CalendarIcon,
  UserIcon,
  DocumentTextIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';
import { AlertRuleModal } from './AlertRuleModal';

interface AlertRule {
  id: string;
  name: string;
  description: string;
  database_id: string;
  database_name: string;
  metric_type: string;
  operator: string;
  threshold: number;
  severity: 'info' | 'warning' | 'error' | 'critical';
  enabled: boolean;
  created_at: string;
  updated_at: string;
}

interface AlertEvent {
  id: string;
  alert_rule_id: string;
  rule_name: string;
  database_name: string;
  metric_type: string;
  value: number;
  threshold: number;
  operator: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  status: 'active' | 'resolved' | 'suppressed';
  message: string;
  start_time: string;
  end_time?: string;
  duration?: number;
  resolved_by?: string;
  resolved_at?: string;
  resolution_notes?: string;
  action_history?: AlertAction[];
}

interface AlertAction {
  id: string;
  action_type: 'created' | 'acknowledged' | 'resolved' | 'suppressed' | 'escalated' | 'commented';
  user: string;
  timestamp: string;
  notes?: string;
}

interface AlertManagementProps {
  onRuleSelect?: (rule: AlertRule) => void;
  onEventSelect?: (event: AlertEvent) => void;
}

export const AlertManagement: React.FC<AlertManagementProps> = ({ 
  onRuleSelect, 
  onEventSelect 
}) => {
  const [currentTab, setCurrentTab] = useState<'rules' | 'events' | 'stats'>('rules');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSeverity, setSelectedSeverity] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingRule, setEditingRule] = useState<AlertRule | null>(null);
  const [modalMode, setModalMode] = useState<'add' | 'edit'>('add');

  // 告警事件详情模态框状态
  const [showEventDetailModal, setShowEventDetailModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<AlertEvent | null>(null);

  // 批量操作状态
  const [selectedEvents, setSelectedEvents] = useState<string[]>([]);

  // 模拟告警规则数据
  const [alertRules, setAlertRules] = useState<AlertRule[]>([
    {
      id: '1',
      name: 'CPU使用率过高',
      description: '当CPU使用率超过80%时触发告警',
      database_id: '1',
      database_name: 'PostgreSQL-Main',
      metric_type: 'cpu_usage',
      operator: '>',
      threshold: 80,
      severity: 'warning',
      enabled: true,
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-01-15T10:30:00Z'
    },
    {
      id: '2',
      name: '内存使用率告警',
      description: '内存使用率超过90%时触发严重告警',
      database_id: '1',
      database_name: 'PostgreSQL-Main',
      metric_type: 'memory_usage',
      operator: '>',
      threshold: 90,
      severity: 'error',
      enabled: true,
      created_at: '2024-01-15T11:00:00Z',
      updated_at: '2024-01-15T11:00:00Z'
    },
    {
      id: '3',
      name: '连接数过多',
      description: '数据库连接数超过200时触发告警',
      database_id: '2',
      database_name: 'Redis-Cache',
      metric_type: 'connections',
      operator: '>',
      threshold: 200,
      severity: 'warning',
      enabled: false,
      created_at: '2024-01-15T12:00:00Z',
      updated_at: '2024-01-15T12:00:00Z'
    }
  ]);

  // 模拟告警事件数据
  const [alertEvents, setAlertEvents] = useState<AlertEvent[]>([
    {
      id: '1',
      alert_rule_id: '1',
      rule_name: 'CPU使用率过高',
      database_name: 'PostgreSQL-Main',
      metric_type: 'cpu_usage',
      value: 85.5,
      threshold: 80,
      operator: '>',
      severity: 'warning',
      status: 'active',
      message: 'CPU使用率达到85.5%，超过阈值80%',
      start_time: '2024-01-15T14:30:00Z',
      duration: 1800
    },
    {
      id: '2',
      alert_rule_id: '2',
      rule_name: '内存使用率告警',
      database_name: 'PostgreSQL-Main',
      metric_type: 'memory_usage',
      value: 92.3,
      threshold: 90,
      operator: '>',
      severity: 'error',
      status: 'resolved',
      message: '内存使用率达到92.3%，超过阈值90%',
      start_time: '2024-01-15T13:15:00Z',
      end_time: '2024-01-15T13:45:00Z',
      duration: 1800,
      resolved_by: 'admin',
      resolved_at: '2024-01-15T13:45:00Z',
      resolution_notes: '重启了缓存服务，内存使用率恢复正常',
      action_history: [
        {
          id: 'action-1',
          action_type: 'created',
          user: 'system',
          timestamp: '2024-01-15T13:15:00Z',
          notes: '告警自动触发'
        },
        {
          id: 'action-2',
          action_type: 'acknowledged',
          user: 'admin',
          timestamp: '2024-01-15T13:20:00Z',
          notes: '已确认告警，正在调查原因'
        },
        {
          id: 'action-3',
          action_type: 'resolved',
          user: 'admin',
          timestamp: '2024-01-15T13:45:00Z',
          notes: '重启了缓存服务，内存使用率恢复正常'
        }
      ]
    },
    {
      id: '3',
      alert_rule_id: '1',
      rule_name: 'CPU使用率过高',
      database_name: 'PostgreSQL-Main',
      metric_type: 'cpu_usage',
      value: 88.2,
      threshold: 80,
      operator: '>',
      severity: 'warning',
      status: 'suppressed',
      message: 'CPU使用率达到88.2%，超过阈值80%（已抑制）',
      start_time: '2024-01-15T12:00:00Z',
      duration: 3600
    }
  ]);

  // 获取严重程度图标和颜色
  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <XCircleIcon className="h-5 w-5 text-red-600" />;
      case 'error':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      case 'info':
        return <CheckCircleIcon className="h-5 w-5 text-blue-500" />;
      default:
        return <CheckCircleIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'error':
        return 'bg-red-100 text-red-700';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'info':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'resolved':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'suppressed':
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-red-100 text-red-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'suppressed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取操作图标
  const getActionIcon = (actionType: string) => {
    switch (actionType) {
      case 'created':
        return <BellIcon className="h-4 w-4 text-blue-500" />;
      case 'acknowledged':
        return <EyeIcon className="h-4 w-4 text-yellow-500" />;
      case 'resolved':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'suppressed':
        return <XCircleIcon className="h-4 w-4 text-gray-500" />;
      case 'escalated':
        return <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />;
      case 'commented':
        return <ChatBubbleLeftRightIcon className="h-4 w-4 text-blue-500" />;
      default:
        return <ClockIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  // 获取操作标签
  const getActionLabel = (actionType: string) => {
    switch (actionType) {
      case 'created':
        return '告警创建';
      case 'acknowledged':
        return '告警确认';
      case 'resolved':
        return '告警解决';
      case 'suppressed':
        return '告警抑制';
      case 'escalated':
        return '告警升级';
      case 'commented':
        return '添加备注';
      default:
        return '未知操作';
    }
  };

  // 过滤告警规则
  const filteredRules = alertRules.filter(rule => {
    const matchesSearch = rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         rule.database_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         rule.metric_type.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSeverity = selectedSeverity === 'all' || rule.severity === selectedSeverity;
    return matchesSearch && matchesSeverity;
  });

  // 过滤告警事件
  const filteredEvents = alertEvents.filter(event => {
    const matchesSearch = event.rule_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.database_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.message.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSeverity = selectedSeverity === 'all' || event.severity === selectedSeverity;
    const matchesStatus = selectedStatus === 'all' || event.status === selectedStatus;
    return matchesSearch && matchesSeverity && matchesStatus;
  });

  // 处理告警规则操作
  const handleRuleAction = (action: string, rule: AlertRule) => {
    console.log('告警规则操作:', action, rule.name);

    switch (action) {
      case 'view':
        onRuleSelect?.(rule);
        break;
      case 'edit':
        setEditingRule(rule);
        setModalMode('edit');
        setShowAddModal(true);
        break;
      case 'delete':
        if (confirm(`确定要删除告警规则 ${rule.name} 吗？`)) {
          setAlertRules(prev => prev.filter(r => r.id !== rule.id));
        }
        break;
      case 'toggle':
        setAlertRules(prev => prev.map(r =>
          r.id === rule.id ? { ...r, enabled: !r.enabled } : r
        ));
        break;
    }
  };

  // 处理添加告警规则
  const handleAddRule = () => {
    setEditingRule(null);
    setModalMode('add');
    setShowAddModal(true);
  };

  // 处理保存告警规则
  const handleSaveRule = (ruleData: any) => {
    if (modalMode === 'add') {
      const newRule: AlertRule = {
        ...ruleData,
        id: Date.now().toString(),
        database_name: ruleData.database_name || 'Unknown',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      setAlertRules(prev => [...prev, newRule]);
    } else {
      setAlertRules(prev => prev.map(r =>
        r.id === editingRule?.id ? {
          ...ruleData,
          id: r.id,
          database_name: ruleData.database_name || r.database_name,
          created_at: r.created_at,
          updated_at: new Date().toISOString()
        } : r
      ));
    }
  };

  // 处理关闭模态框
  const handleCloseModal = () => {
    setShowAddModal(false);
    setEditingRule(null);
  };

  // 批量操作处理
  const handleBatchAction = (action: string) => {
    console.log('批量操作:', action, selectedEvents);

    switch (action) {
      case 'resolve':
        setAlertEvents(prev => prev.map(e =>
          selectedEvents.includes(e.id) && e.status === 'active' ? {
            ...e,
            status: 'resolved' as const,
            end_time: new Date().toISOString(),
            resolved_by: 'current_user',
            resolved_at: new Date().toISOString(),
            action_history: [
              ...(e.action_history || []),
              {
                id: `action-${Date.now()}`,
                action_type: 'resolved',
                user: 'current_user',
                timestamp: new Date().toISOString(),
                notes: '批量解决告警'
              }
            ]
          } : e
        ));
        break;
      case 'suppress':
        setAlertEvents(prev => prev.map(e =>
          selectedEvents.includes(e.id) && e.status === 'active' ? {
            ...e,
            status: 'suppressed' as const,
            action_history: [
              ...(e.action_history || []),
              {
                id: `action-${Date.now()}`,
                action_type: 'suppressed',
                user: 'current_user',
                timestamp: new Date().toISOString(),
                notes: '批量抑制告警'
              }
            ]
          } : e
        ));
        break;
      case 'acknowledge':
        setAlertEvents(prev => prev.map(e =>
          selectedEvents.includes(e.id) && e.status === 'active' ? {
            ...e,
            action_history: [
              ...(e.action_history || []),
              {
                id: `action-${Date.now()}`,
                action_type: 'acknowledged',
                user: 'current_user',
                timestamp: new Date().toISOString(),
                notes: '批量确认告警'
              }
            ]
          } : e
        ));
        break;
    }

    setSelectedEvents([]);
  };

  // 处理告警事件操作
  const handleEventAction = (action: string, event: AlertEvent) => {
    console.log('告警事件操作:', action, event.id);

    switch (action) {
      case 'view':
        setSelectedEvent(event);
        setShowEventDetailModal(true);
        onEventSelect?.(event);
        break;
      case 'acknowledge':
        if (event.status === 'active') {
          setAlertEvents(prev => prev.map(e =>
            e.id === event.id ? {
              ...e,
              action_history: [
                ...(e.action_history || []),
                {
                  id: `action-${Date.now()}`,
                  action_type: 'acknowledged',
                  user: 'current_user',
                  timestamp: new Date().toISOString(),
                  notes: '手动确认告警'
                }
              ]
            } : e
          ));
        }
        break;
      case 'resolve':
        if (event.status === 'active') {
          setAlertEvents(prev => prev.map(e =>
            e.id === event.id ? {
              ...e,
              status: 'resolved' as const,
              end_time: new Date().toISOString(),
              resolved_by: 'current_user',
              resolved_at: new Date().toISOString(),
              resolution_notes: '手动解决告警',
              action_history: [
                ...(e.action_history || []),
                {
                  id: `action-${Date.now()}`,
                  action_type: 'resolved',
                  user: 'current_user',
                  timestamp: new Date().toISOString(),
                  notes: '手动解决告警'
                }
              ]
            } : e
          ));
        }
        break;
      case 'suppress':
        if (event.status === 'active') {
          setAlertEvents(prev => prev.map(e =>
            e.id === event.id ? {
              ...e,
              status: 'suppressed' as const,
              action_history: [
                ...(e.action_history || []),
                {
                  id: `action-${Date.now()}`,
                  action_type: 'suppressed',
                  user: 'current_user',
                  timestamp: new Date().toISOString(),
                  notes: '手动抑制告警'
                }
              ]
            } : e
          ));
        }
        break;
    }
  };

  // 格式化时间
  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString('zh-CN');
  };

  // 格式化持续时间
  const formatDuration = (seconds?: number) => {
    if (!seconds) return '-';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    }
    return `${minutes}分钟`;
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <BellIcon className="h-8 w-8 text-red-600" />
              告警管理
            </h1>
            <p className="text-gray-600 mt-1">配置和管理数据库监控告警</p>
          </div>
          <button
            onClick={handleAddRule}
            className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors flex items-center gap-2"
          >
            <PlusIcon className="h-5 w-5" />
            添加告警规则
          </button>
        </div>

        {/* 标签页导航 */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setCurrentTab('rules')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  currentTab === 'rules'
                    ? 'border-red-500 text-red-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                告警规则 ({alertRules.length})
              </button>
              <button
                onClick={() => setCurrentTab('events')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  currentTab === 'events'
                    ? 'border-red-500 text-red-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                告警事件 ({alertEvents.filter(e => e.status === 'active').length})
              </button>
              <button
                onClick={() => setCurrentTab('stats')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  currentTab === 'stats'
                    ? 'border-red-500 text-red-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                统计分析
              </button>
            </nav>
          </div>

          {/* 搜索和过滤 */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1 relative">
                <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder={currentTab === 'rules' ? '搜索告警规则...' : '搜索告警事件...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                />
              </div>
              <select
                value={selectedSeverity}
                onChange={(e) => setSelectedSeverity(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
              >
                <option value="all">所有严重程度</option>
                <option value="critical">严重</option>
                <option value="error">错误</option>
                <option value="warning">警告</option>
                <option value="info">信息</option>
              </select>
              {currentTab === 'events' && (
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                >
                  <option value="all">所有状态</option>
                  <option value="active">活跃</option>
                  <option value="resolved">已解决</option>
                  <option value="suppressed">已抑制</option>
                </select>
              )}
            </div>
          </div>
        </div>

        {/* 内容区域 */}
        {currentTab === 'rules' && (
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">告警规则列表</h2>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      规则信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      数据库
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      监控指标
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      条件
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      严重程度
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredRules.map((rule) => (
                    <tr key={rule.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{rule.name}</div>
                          <div className="text-sm text-gray-500">{rule.description}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{rule.database_name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{rule.metric_type}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {rule.operator} {rule.threshold}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getSeverityIcon(rule.severity)}
                          <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(rule.severity)}`}>
                            {rule.severity}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {rule.enabled ? (
                            <CheckCircleIcon className="h-5 w-5 text-green-500" />
                          ) : (
                            <XCircleIcon className="h-5 w-5 text-gray-400" />
                          )}
                          <span className={`ml-2 text-sm ${rule.enabled ? 'text-green-600' : 'text-gray-500'}`}>
                            {rule.enabled ? '启用' : '禁用'}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-1">
                          <button
                            onClick={() => handleRuleAction('view', rule)}
                            className="text-blue-600 hover:text-blue-900 hover:bg-blue-50 p-2 rounded-lg transition-colors"
                            title="查看详情"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleRuleAction('edit', rule)}
                            className="text-gray-600 hover:text-gray-900 hover:bg-gray-50 p-2 rounded-lg transition-colors"
                            title="编辑"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleRuleAction('toggle', rule)}
                            className={`${rule.enabled ? 'text-gray-600 hover:text-gray-900' : 'text-green-600 hover:text-green-900'} hover:bg-gray-50 p-2 rounded-lg transition-colors`}
                            title={rule.enabled ? '禁用' : '启用'}
                          >
                            {rule.enabled ? <PauseIcon className="h-4 w-4" /> : <PlayIcon className="h-4 w-4" />}
                          </button>
                          <button
                            onClick={() => handleRuleAction('delete', rule)}
                            className="text-red-600 hover:text-red-900 hover:bg-red-50 p-2 rounded-lg transition-colors"
                            title="删除"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredRules.length === 0 && (
              <div className="text-center py-12">
                <BellIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">没有找到告警规则</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm || selectedSeverity !== 'all' ? '尝试调整搜索条件' : '开始创建您的第一个告警规则'}
                </p>
              </div>
            )}
          </div>
        )}

        {currentTab === 'events' && (
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900">告警事件列表</h2>
                {selectedEvents.length > 0 && (
                  <div className="flex items-center space-x-3">
                    <span className="text-sm text-gray-500">
                      已选择 {selectedEvents.length} 个告警
                    </span>
                    <button
                      onClick={() => handleBatchAction('acknowledge')}
                      className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                    >
                      批量确认
                    </button>
                    <button
                      onClick={() => handleBatchAction('suppress')}
                      className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                    >
                      批量抑制
                    </button>
                    <button
                      onClick={() => handleBatchAction('resolve')}
                      className="px-3 py-1 text-sm bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors"
                    >
                      批量解决
                    </button>
                  </div>
                )}
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        checked={selectedEvents.length === filteredEvents.length && filteredEvents.length > 0}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedEvents(filteredEvents.map(event => event.id));
                          } else {
                            setSelectedEvents([]);
                          }
                        }}
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      告警信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      数据库
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      指标值
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      严重程度
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      开始时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      持续时间
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredEvents.map((event) => (
                    <tr key={event.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          checked={selectedEvents.includes(event.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedEvents(prev => [...prev, event.id]);
                            } else {
                              setSelectedEvents(prev => prev.filter(id => id !== event.id));
                            }
                          }}
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{event.rule_name}</div>
                          <div className="text-sm text-gray-500">{event.message}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{event.database_name}</div>
                        <div className="text-sm text-gray-500">{event.metric_type}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {event.value} {event.operator} {event.threshold}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getSeverityIcon(event.severity)}
                          <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(event.severity)}`}>
                            {event.severity}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getStatusIcon(event.status)}
                          <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(event.status)}`}>
                            {event.status === 'active' ? '活跃' : event.status === 'resolved' ? '已解决' : '已抑制'}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{formatTime(event.start_time)}</div>
                        {event.end_time && (
                          <div className="text-sm text-gray-500">结束: {formatTime(event.end_time)}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{formatDuration(event.duration)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-1">
                          <button
                            onClick={() => handleEventAction('view', event)}
                            className="text-blue-600 hover:text-blue-900 hover:bg-blue-50 p-2 rounded-lg transition-colors"
                            title="查看详情"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          {event.status === 'active' && (
                            <>
                              <button
                                onClick={() => handleEventAction('acknowledge', event)}
                                className="text-yellow-600 hover:text-yellow-900 hover:bg-yellow-50 p-2 rounded-lg transition-colors"
                                title="确认告警"
                              >
                                <EyeIcon className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleEventAction('resolve', event)}
                                className="text-green-600 hover:text-green-900 hover:bg-green-50 p-2 rounded-lg transition-colors"
                                title="解决告警"
                              >
                                <CheckCircleIcon className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleEventAction('suppress', event)}
                                className="text-gray-600 hover:text-gray-900 hover:bg-gray-50 p-2 rounded-lg transition-colors"
                                title="抑制告警"
                              >
                                <ClockIcon className="h-4 w-4" />
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredEvents.length === 0 && (
              <div className="text-center py-12">
                <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">没有找到告警事件</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm || selectedSeverity !== 'all' || selectedStatus !== 'all' ? '尝试调整搜索条件' : '暂无告警事件'}
                </p>
              </div>
            )}
          </div>
        )}

        {currentTab === 'stats' && (
          <div className="space-y-6">
            {/* 统计卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <BellIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">总告警规则</p>
                    <p className="text-2xl font-semibold text-gray-900">{alertRules.length}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ExclamationTriangleIcon className="h-8 w-8 text-red-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">活跃告警</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {alertEvents.filter(e => e.status === 'active').length}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CheckCircleIcon className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">已解决告警</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {alertEvents.filter(e => e.status === 'resolved').length}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <PlayIcon className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">启用规则</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {alertRules.filter(r => r.enabled).length}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* 严重程度分布 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">告警严重程度分布</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {['critical', 'error', 'warning', 'info'].map(severity => {
                  const count = alertEvents.filter(e => e.severity === severity).length;
                  const percentage = alertEvents.length > 0 ? (count / alertEvents.length * 100).toFixed(1) : '0';
                  return (
                    <div key={severity} className="text-center">
                      <div className="flex items-center justify-center mb-2">
                        {getSeverityIcon(severity)}
                        <span className={`ml-2 px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(severity)}`}>
                          {severity}
                        </span>
                      </div>
                      <div className="text-2xl font-bold text-gray-900">{count}</div>
                      <div className="text-sm text-gray-500">{percentage}%</div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* 告警趋势分析 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 数据库告警分布 */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">数据库告警分布</h3>
                <div className="space-y-3">
                  {Array.from(new Set(alertEvents.map(e => e.database_name))).map(dbName => {
                    const dbEvents = alertEvents.filter(e => e.database_name === dbName);
                    const activeCount = dbEvents.filter(e => e.status === 'active').length;
                    const totalCount = dbEvents.length;
                    const percentage = alertEvents.length > 0 ? (totalCount / alertEvents.length * 100).toFixed(1) : '0';

                    return (
                      <div key={dbName} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                          <span className="text-sm font-medium text-gray-900">{dbName}</span>
                        </div>
                        <div className="flex items-center space-x-4">
                          <span className="text-sm text-gray-500">
                            活跃: {activeCount} / 总计: {totalCount}
                          </span>
                          <span className="text-sm font-semibold text-gray-900">{percentage}%</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* 告警处理效率 */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">告警处理效率</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">平均处理时间</span>
                    <span className="text-sm font-semibold text-gray-900">
                      {(() => {
                        const resolvedEvents = alertEvents.filter(e => e.status === 'resolved' && e.duration);
                        if (resolvedEvents.length === 0) return '暂无数据';
                        const avgDuration = resolvedEvents.reduce((sum, e) => sum + (e.duration || 0), 0) / resolvedEvents.length;
                        return `${Math.round(avgDuration / 60)} 分钟`;
                      })()}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">处理成功率</span>
                    <span className="text-sm font-semibold text-gray-900">
                      {(() => {
                        const totalEvents = alertEvents.length;
                        const resolvedEvents = alertEvents.filter(e => e.status === 'resolved').length;
                        return totalEvents > 0 ? `${((resolvedEvents / totalEvents) * 100).toFixed(1)}%` : '0%';
                      })()}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">待处理告警</span>
                    <span className="text-sm font-semibold text-red-600">
                      {alertEvents.filter(e => e.status === 'active').length}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">抑制告警</span>
                    <span className="text-sm font-semibold text-gray-600">
                      {alertEvents.filter(e => e.status === 'suppressed').length}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* 最近告警事件 */}
            <div className="bg-white rounded-lg shadow-sm">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">最近告警事件</h3>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {alertEvents.slice(0, 5).map((event) => (
                    <div key={event.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                         onClick={() => handleEventAction('view', event)}>
                      <div className="flex items-center space-x-4">
                        {getSeverityIcon(event.severity)}
                        <div>
                          <div className="text-sm font-medium text-gray-900">{event.rule_name}</div>
                          <div className="text-sm text-gray-500">{event.database_name} • {event.message}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(event.status)}`}>
                          {event.status === 'active' ? '活跃' : event.status === 'resolved' ? '已解决' : '已抑制'}
                        </span>
                        <div className="text-sm text-gray-500">{formatTime(event.start_time)}</div>
                        <EyeIcon className="h-4 w-4 text-gray-400" />
                      </div>
                    </div>
                  ))}
                </div>

                {alertEvents.length === 0 && (
                  <div className="text-center py-8">
                    <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">暂无告警事件</h3>
                    <p className="mt-1 text-sm text-gray-500">系统运行正常，没有触发任何告警</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 告警规则配置模态框 */}
        <AlertRuleModal
          isOpen={showAddModal}
          onClose={handleCloseModal}
          onSave={handleSaveRule}
          rule={editingRule}
          mode={modalMode}
        />

        {/* 告警事件详情模态框 */}
        {showEventDetailModal && selectedEvent && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
              {/* 模态框头部 */}
              <div className="flex items-center justify-between pb-4 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  {getSeverityIcon(selectedEvent.severity)}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      告警事件详情
                    </h3>
                    <p className="text-sm text-gray-500">
                      事件ID: {selectedEvent.id}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setShowEventDetailModal(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              {/* 模态框内容 */}
              <div className="mt-6 space-y-6">
                {/* 基本信息 */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        告警规则
                      </label>
                      <p className="text-sm text-gray-900">{selectedEvent.rule_name}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        数据库
                      </label>
                      <p className="text-sm text-gray-900">{selectedEvent.database_name}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        指标类型
                      </label>
                      <p className="text-sm text-gray-900">{selectedEvent.metric_type}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        严重程度
                      </label>
                      <div className="flex items-center space-x-2">
                        {getSeverityIcon(selectedEvent.severity)}
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(selectedEvent.severity)}`}>
                          {selectedEvent.severity}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        当前状态
                      </label>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(selectedEvent.status)}
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(selectedEvent.status)}`}>
                          {selectedEvent.status === 'active' ? '活跃' : selectedEvent.status === 'resolved' ? '已解决' : '已抑制'}
                        </span>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        触发值
                      </label>
                      <p className="text-sm text-gray-900">
                        {selectedEvent.value} {selectedEvent.operator} {selectedEvent.threshold}
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        开始时间
                      </label>
                      <div className="flex items-center space-x-2">
                        <CalendarIcon className="h-4 w-4 text-gray-400" />
                        <p className="text-sm text-gray-900">{formatTime(selectedEvent.start_time)}</p>
                      </div>
                    </div>
                    {selectedEvent.end_time && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          结束时间
                        </label>
                        <div className="flex items-center space-x-2">
                          <CalendarIcon className="h-4 w-4 text-gray-400" />
                          <p className="text-sm text-gray-900">{formatTime(selectedEvent.end_time)}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* 告警消息 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    告警消息
                  </label>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-start space-x-2">
                      <DocumentTextIcon className="h-5 w-5 text-gray-400 mt-0.5" />
                      <p className="text-sm text-gray-900">{selectedEvent.message}</p>
                    </div>
                  </div>
                </div>

                {/* 处理信息 */}
                {(selectedEvent.resolved_by || selectedEvent.resolved_at) && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      处理信息
                    </label>
                    <div className="bg-green-50 rounded-lg p-4">
                      <div className="flex items-start space-x-2">
                        <UserIcon className="h-5 w-5 text-green-400 mt-0.5" />
                        <div>
                          {selectedEvent.resolved_by && (
                            <p className="text-sm text-gray-900">
                              处理人: {selectedEvent.resolved_by}
                            </p>
                          )}
                          {selectedEvent.resolved_at && (
                            <p className="text-sm text-gray-500">
                              处理时间: {formatTime(selectedEvent.resolved_at)}
                            </p>
                          )}
                          {selectedEvent.resolution_notes && (
                            <p className="text-sm text-gray-700 mt-2">
                              处理说明: {selectedEvent.resolution_notes}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* 处理历史 */}
                {selectedEvent.action_history && selectedEvent.action_history.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      处理历史
                    </label>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="space-y-3">
                        {selectedEvent.action_history.map((action) => (
                          <div key={action.id} className="flex items-start space-x-3">
                            <div className="flex-shrink-0">
                              {getActionIcon(action.action_type)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-2">
                                <span className="text-sm font-medium text-gray-900">
                                  {getActionLabel(action.action_type)}
                                </span>
                                <span className="text-xs text-gray-500">
                                  by {action.user}
                                </span>
                              </div>
                              <p className="text-xs text-gray-500 mt-1">
                                {formatTime(action.timestamp)}
                              </p>
                              {action.notes && (
                                <p className="text-sm text-gray-700 mt-1">
                                  {action.notes}
                                </p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* 操作按钮 */}
                {selectedEvent.status === 'active' && (
                  <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                    <button
                      onClick={() => {
                        handleEventAction('suppress', selectedEvent);
                        setShowEventDetailModal(false);
                      }}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                    >
                      抑制告警
                    </button>
                    <button
                      onClick={() => {
                        handleEventAction('resolve', selectedEvent);
                        setShowEventDetailModal(false);
                      }}
                      className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 transition-colors"
                    >
                      解决告警
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AlertManagement;
