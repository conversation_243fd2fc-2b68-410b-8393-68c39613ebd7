import React, { useState, useRef, useEffect } from 'react';
import {
  BellIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  EyeIcon,
  CheckIcon,
  PauseIcon
} from '@heroicons/react/24/outline';
import { BellIcon as BellSolidIcon } from '@heroicons/react/24/solid';
import { useAlertStats, useAlertEvents } from '../../hooks';
import { AlertEventModal } from './AlertEventModal';

interface AlertNotificationBellProps {
  onViewAllAlerts: () => void;
}

export const AlertNotificationBell: React.FC<AlertNotificationBellProps> = ({ onViewAllAlerts }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<any>(null);
  const [showEventModal, setShowEventModal] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  // 获取告警统计数据
  const { data: alertStats, loading: statsLoading } = useAlertStats();

  // 获取最近的告警事件（只显示前5条）
  const { items: alertEvents, loading: eventsLoading } = useAlertEvents({
    page: 1,
    page_size: 5,
    status: 'active' // 只显示活跃告警
  });

  const activeAlertCount = (alertStats as any)?.active_events || 0;

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 获取严重程度颜色
  const getSeverityColor = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case 'critical':
        return 'text-red-600 bg-red-100';
      case 'error':
        return 'text-red-500 bg-red-50';
      case 'warning':
        return 'text-yellow-600 bg-yellow-100';
      case 'info':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // 获取严重程度图标
  const getSeverityIcon = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case 'critical':
        return <XCircleIcon className="h-4 w-4 text-red-600" />;
      case 'error':
        return <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <ExclamationTriangleIcon className="h-4 w-4 text-yellow-600" />;
      case 'info':
        return <CheckCircleIcon className="h-4 w-4 text-blue-600" />;
      default:
        return <ClockIcon className="h-4 w-4 text-gray-600" />;
    }
  };

  // 格式化时间
  const formatTime = (timeString: string) => {
    const date = new Date(timeString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}小时前`;
    return `${Math.floor(diffMins / 1440)}天前`;
  };

  // 获取铃铛颜色（根据最高严重程度）
  const getBellColor = () => {
    if (activeAlertCount === 0) return 'text-gray-400';

    // 检查是否有critical级别的告警
    const hasCritical = alertEvents.some((alert: any) => alert.severity === 'critical');
    if (hasCritical) return 'text-red-600';

    // 检查是否有error级别的告警
    const hasError = alertEvents.some((alert: any) => alert.severity === 'error');
    if (hasError) return 'text-red-500';

    // 检查是否有warning级别的告警
    const hasWarning = alertEvents.some((alert: any) => alert.severity === 'warning');
    if (hasWarning) return 'text-yellow-600';

    return 'text-blue-600';
  };

  // 处理告警事件操作
  const handleEventAction = (action: string, event: any) => {
    console.log('告警操作:', action, event.id);

    switch (action) {
      case 'view':
        setSelectedEvent(event);
        setShowEventModal(true);
        setIsOpen(false);
        break;
      case 'acknowledge':
        // 这里应该调用API确认告警
        console.log('确认告警:', event.id);
        break;
      case 'resolve':
        // 这里应该调用API解决告警
        console.log('解决告警:', event.id);
        break;
      case 'suppress':
        // 这里应该调用API抑制告警
        console.log('抑制告警:', event.id);
        break;
      default:
        break;
    }
  };

  // 处理快速操作
  const handleQuickAction = (action: string, event: any, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡
    handleEventAction(action, event);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* 告警铃铛按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`relative p-2 rounded-lg hover:bg-gray-100 transition-colors ${getBellColor()}`}
        title={`${activeAlertCount} 个活跃告警`}
      >
        {activeAlertCount > 0 ? (
          <BellSolidIcon className="h-6 w-6" />
        ) : (
          <BellIcon className="h-6 w-6" />
        )}
        
        {/* 告警数量徽章 */}
        {activeAlertCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
            {activeAlertCount > 99 ? '99+' : activeAlertCount}
          </span>
        )}
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          {/* 头部 */}
          <div className="px-4 py-3 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">告警通知</h3>
              <span className="text-sm text-gray-500">
                {activeAlertCount} 个活跃告警
              </span>
            </div>
          </div>

          {/* 告警列表 */}
          <div className="max-h-96 overflow-y-auto">
            {statsLoading || eventsLoading ? (
              <div className="p-4 text-center text-gray-500">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 mx-auto"></div>
                <p className="mt-2">加载中...</p>
              </div>
            ) : alertEvents.length === 0 ? (
              <div className="p-6 text-center text-gray-500">
                <CheckCircleIcon className="h-12 w-12 mx-auto mb-2 text-green-500" />
                <p className="text-sm">暂无活跃告警</p>
                <p className="text-xs text-gray-400 mt-1">系统运行正常</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-100">
                {alertEvents.map((alert: any) => (
                  <div key={alert.id} className="p-4 hover:bg-gray-50 transition-colors">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-0.5">
                        {getSeverityIcon(alert.severity)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(alert.severity)}`}>
                            {alert.severity}
                          </span>
                          <span className="text-xs text-gray-500">
                            {formatTime(alert.start_time)}
                          </span>
                        </div>
                        <p
                          className="text-sm text-gray-900 line-clamp-2 mb-2 cursor-pointer hover:text-blue-600"
                          onClick={() => handleEventAction('view', alert)}
                        >
                          {alert.message}
                        </p>
                        {alert.database && (
                          <p className="text-xs text-gray-500 mb-2">
                            {alert.database.name} • {alert.metric_type}
                          </p>
                        )}

                        {/* 快速操作按钮 */}
                        {alert.status === 'active' && (
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={(e) => handleQuickAction('acknowledge', alert, e)}
                              className="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 bg-blue-100 rounded hover:bg-blue-200 transition-colors"
                              title="确认告警"
                            >
                              <CheckIcon className="h-3 w-3 mr-1" />
                              确认
                            </button>
                            <button
                              onClick={(e) => handleQuickAction('resolve', alert, e)}
                              className="inline-flex items-center px-2 py-1 text-xs font-medium text-green-600 bg-green-100 rounded hover:bg-green-200 transition-colors"
                              title="解决告警"
                            >
                              <CheckCircleIcon className="h-3 w-3 mr-1" />
                              解决
                            </button>
                            <button
                              onClick={(e) => handleQuickAction('suppress', alert, e)}
                              className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded hover:bg-gray-200 transition-colors"
                              title="抑制告警"
                            >
                              <PauseIcon className="h-3 w-3 mr-1" />
                              抑制
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 底部操作 */}
          <div className="px-4 py-3 border-t border-gray-200 bg-gray-50 rounded-b-lg">
            <button
              onClick={() => {
                setIsOpen(false);
                onViewAllAlerts();
              }}
              className="w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-indigo-600 hover:text-indigo-500 transition-colors"
            >
              <EyeIcon className="h-4 w-4 mr-2" />
              查看所有告警
            </button>
          </div>
        </div>
      )}

      {/* 告警事件详情模态框 */}
      <AlertEventModal
        isOpen={showEventModal}
        onClose={() => {
          setShowEventModal(false);
          setSelectedEvent(null);
        }}
        event={selectedEvent}
        onAction={handleEventAction}
      />
    </div>
  );
};
