import React from 'react';
import {
  XMarkIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  ServerIcon,
  ChartBarIcon,
  UserIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';

interface AlertEvent {
  id: string;
  alert_rule_id: string;
  rule_name: string;
  database_name: string;
  metric_type: string;
  value: number;
  threshold: number;
  operator: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  status: 'active' | 'resolved' | 'suppressed';
  message: string;
  start_time: string;
  end_time?: string;
  duration?: number;
  resolved_by?: string;
  resolved_at?: string;
  resolution_notes?: string;
  action_history?: Array<{
    id: string;
    action_type: string;
    user: string;
    timestamp: string;
    notes: string;
  }>;
  database?: {
    id: string;
    name: string;
    type: string;
    host: string;
    port: number;
  };
}

interface AlertEventModalProps {
  isOpen: boolean;
  onClose: () => void;
  event: AlertEvent | null;
  onAction?: (action: string, event: AlertEvent) => void;
}

export const AlertEventModal: React.FC<AlertEventModalProps> = ({
  isOpen,
  onClose,
  event,
  onAction
}) => {
  if (!isOpen || !event) return null;

  // 获取严重程度颜色
  const getSeverityColor = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case 'critical':
        return 'text-red-800 bg-red-100 border-red-200';
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'info':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'text-red-600 bg-red-100';
      case 'resolved':
        return 'text-green-600 bg-green-100';
      case 'suppressed':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // 格式化时间
  const formatTime = (timeStr: string) => {
    return new Date(timeStr).toLocaleString('zh-CN');
  };

  // 格式化持续时间
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟${secs}秒`;
    } else {
      return `${secs}秒`;
    }
  };

  // 处理快速操作
  const handleQuickAction = (action: string) => {
    if (onAction) {
      onAction(action, event);
    }
    if (action === 'resolve' || action === 'suppress') {
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
            <h2 className="text-xl font-semibold text-gray-900">告警详情</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* 内容 */}
        <div className="p-6 space-y-6">
          {/* 基本信息 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">告警规则</label>
                <p className="text-sm text-gray-900 font-medium">{event.rule_name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">数据库</label>
                <div className="flex items-center space-x-2">
                  <ServerIcon className="h-4 w-4 text-gray-400" />
                  <p className="text-sm text-gray-900">{event.database_name}</p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">监控指标</label>
                <div className="flex items-center space-x-2">
                  <ChartBarIcon className="h-4 w-4 text-gray-400" />
                  <p className="text-sm text-gray-900">{event.metric_type}</p>
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">严重程度</label>
                <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium border ${getSeverityColor(event.severity)}`}>
                  {event.severity}
                </span>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">状态</label>
                <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(event.status)}`}>
                  {event.status === 'active' ? '活跃' : event.status === 'resolved' ? '已解决' : '已抑制'}
                </span>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">触发时间</label>
                <div className="flex items-center space-x-2">
                  <CalendarIcon className="h-4 w-4 text-gray-400" />
                  <p className="text-sm text-gray-900">{formatTime(event.start_time)}</p>
                </div>
              </div>
            </div>
          </div>

          {/* 告警消息 */}
          <div>
            <label className="text-sm font-medium text-gray-500 block mb-2">告警消息</label>
            <div className="bg-gray-50 rounded-lg p-3">
              <p className="text-sm text-gray-900">{event.message}</p>
            </div>
          </div>

          {/* 指标详情 */}
          <div className="bg-blue-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2">指标详情</h4>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-blue-700">当前值:</span>
                <span className="ml-2 font-medium text-blue-900">{event.value}</span>
              </div>
              <div>
                <span className="text-blue-700">阈值:</span>
                <span className="ml-2 font-medium text-blue-900">{event.operator} {event.threshold}</span>
              </div>
              <div>
                <span className="text-blue-700">持续时间:</span>
                <span className="ml-2 font-medium text-blue-900">
                  {event.duration ? formatDuration(event.duration) : '计算中...'}
                </span>
              </div>
            </div>
          </div>

          {/* 解决信息 */}
          {event.status === 'resolved' && event.resolved_by && (
            <div className="bg-green-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-green-900 mb-2">解决信息</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <UserIcon className="h-4 w-4 text-green-600" />
                  <span className="text-green-700">解决人:</span>
                  <span className="font-medium text-green-900">{event.resolved_by}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <ClockIcon className="h-4 w-4 text-green-600" />
                  <span className="text-green-700">解决时间:</span>
                  <span className="font-medium text-green-900">
                    {event.resolved_at ? formatTime(event.resolved_at) : '未知'}
                  </span>
                </div>
                {event.resolution_notes && (
                  <div>
                    <span className="text-green-700">解决说明:</span>
                    <p className="mt-1 text-green-900">{event.resolution_notes}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 操作历史 */}
          {event.action_history && event.action_history.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">操作历史</h4>
              <div className="space-y-2">
                {event.action_history.map((action) => (
                  <div key={action.id} className="flex items-start space-x-3 text-sm">
                    <div className="flex-shrink-0 w-2 h-2 bg-gray-400 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-gray-900">{action.action_type}</span>
                        <span className="text-gray-500">by {action.user}</span>
                        <span className="text-gray-400">{formatTime(action.timestamp)}</span>
                      </div>
                      {action.notes && (
                        <p className="text-gray-600 mt-1">{action.notes}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 底部操作按钮 */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          {event.status === 'active' && (
            <>
              <button
                onClick={() => handleQuickAction('acknowledge')}
                className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-100 rounded-lg hover:bg-blue-200 transition-colors"
              >
                确认告警
              </button>
              <button
                onClick={() => handleQuickAction('suppress')}
                className="px-4 py-2 text-sm font-medium text-yellow-600 bg-yellow-100 rounded-lg hover:bg-yellow-200 transition-colors"
              >
                抑制告警
              </button>
              <button
                onClick={() => handleQuickAction('resolve')}
                className="px-4 py-2 text-sm font-medium text-green-600 bg-green-100 rounded-lg hover:bg-green-200 transition-colors"
              >
                解决告警
              </button>
            </>
          )}
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};
