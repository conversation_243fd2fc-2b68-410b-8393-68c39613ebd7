import React, { useState, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';

interface Database {
  id: string;
  name: string;
  type: string;
}

interface AlertRule {
  id?: string;
  name: string;
  description: string;
  database_id: string;
  metric_type: string;
  operator: string;
  threshold: number;
  severity: 'info' | 'warning' | 'error' | 'critical';
  enabled: boolean;
  duration?: number;
  notification_channels?: string[];
}

interface AlertRuleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (rule: AlertRule) => void;
  rule?: AlertRule | null;
  mode: 'add' | 'edit';
}

export const AlertRuleModal: React.FC<AlertRuleModalProps> = ({
  isOpen,
  onClose,
  onSave,
  rule,
  mode
}) => {
  const [formData, setFormData] = useState<AlertRule>({
    name: '',
    description: '',
    database_id: '',
    metric_type: '',
    operator: '>',
    threshold: 0,
    severity: 'warning',
    enabled: true,
    duration: 300,
    notification_channels: []
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 模拟数据库列表
  const databases: Database[] = [
    { id: '1', name: 'PostgreSQL-Main', type: 'postgresql' },
    { id: '2', name: 'Redis-Cache', type: 'redis' },
    { id: '3', name: 'MySQL-Analytics', type: 'mysql' }
  ];

  // 监控指标选项
  const metricTypes = [
    { value: 'cpu_usage', label: 'CPU使用率 (%)' },
    { value: 'memory_usage', label: '内存使用率 (%)' },
    { value: 'disk_usage', label: '磁盘使用率 (%)' },
    { value: 'connections', label: '连接数' },
    { value: 'queries_per_second', label: '每秒查询数' },
    { value: 'response_time', label: '响应时间 (ms)' },
    { value: 'error_rate', label: '错误率 (%)' },
    { value: 'replication_lag', label: '复制延迟 (ms)' }
  ];

  // 操作符选项
  const operators = [
    { value: '>', label: '大于 (>)' },
    { value: '>=', label: '大于等于 (>=)' },
    { value: '<', label: '小于 (<)' },
    { value: '<=', label: '小于等于 (<=)' },
    { value: '==', label: '等于 (==)' },
    { value: '!=', label: '不等于 (!=)' }
  ];

  // 严重程度选项
  // const _severityOptions = [
  //   { value: 'info', label: '信息', color: 'text-blue-600' },
  //   { value: 'warning', label: '警告', color: 'text-yellow-600' },
  //   { value: 'error', label: '错误', color: 'text-red-600' },
  //   { value: 'critical', label: '严重', color: 'text-red-800' }
  // ];

  // 通知渠道选项
  // const _notificationChannels = [
  //   { value: 'email', label: '邮件通知' },
  //   { value: 'sms', label: '短信通知' },
  //   { value: 'webhook', label: 'Webhook' },
  //   { value: 'slack', label: 'Slack' },
  //   { value: 'dingtalk', label: '钉钉' }
  // ];

  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && rule) {
        setFormData({
          ...rule,
          notification_channels: rule.notification_channels || []
        });
      } else {
        setFormData({
          name: '',
          description: '',
          database_id: '',
          metric_type: '',
          operator: '>',
          threshold: 0,
          severity: 'warning',
          enabled: true,
          duration: 300,
          notification_channels: []
        });
      }
      setErrors({});
    }
  }, [isOpen, mode, rule]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = '请输入告警规则名称';
    }

    if (!formData.database_id) {
      newErrors.database_id = '请选择数据库';
    }

    if (!formData.metric_type) {
      newErrors.metric_type = '请选择监控指标';
    }

    if (formData.threshold === null || formData.threshold === undefined) {
      newErrors.threshold = '请输入阈值';
    }

    if (formData.duration && formData.duration < 60) {
      newErrors.duration = '持续时间不能少于60秒';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('保存告警规则失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof AlertRule, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // const _handleNotificationChannelChange = (_channel: string, _checked: boolean) => {
  //   const currentChannels = formData.notification_channels || [];
  //   if (_checked) {
  //     handleInputChange('notification_channels', [...currentChannels, _channel]);
  //   } else {
  //     handleInputChange('notification_channels', currentChannels.filter(c => c !== _channel));
  //   }
  // };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center"
      style={{ zIndex: 10000 }}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div 
        className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 */}
        <div className="bg-white px-6 py-4 border-b border-gray-200 rounded-t-lg">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              {mode === 'add' ? '添加告警规则' : '编辑告警规则'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* 表单内容 */}
        <form onSubmit={handleSubmit} className="px-6 py-4">
          <div className="space-y-6">
            {/* 基本信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  规则名称 *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent ${
                    errors.name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="输入告警规则名称"
                />
                {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  数据库 *
                </label>
                <select
                  value={formData.database_id}
                  onChange={(e) => handleInputChange('database_id', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent ${
                    errors.database_id ? 'border-red-300' : 'border-gray-300'
                  }`}
                >
                  <option value="">选择数据库</option>
                  {databases.map(db => (
                    <option key={db.id} value={db.id}>{db.name}</option>
                  ))}
                </select>
                {errors.database_id && <p className="mt-1 text-sm text-red-600">{errors.database_id}</p>}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                描述
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                placeholder="输入告警规则描述"
              />
            </div>

            {/* 告警条件 */}
            <div className="border-t pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">告警条件</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    监控指标 *
                  </label>
                  <select
                    value={formData.metric_type}
                    onChange={(e) => handleInputChange('metric_type', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent ${
                      errors.metric_type ? 'border-red-300' : 'border-gray-300'
                    }`}
                  >
                    <option value="">选择监控指标</option>
                    {metricTypes.map(metric => (
                      <option key={metric.value} value={metric.value}>{metric.label}</option>
                    ))}
                  </select>
                  {errors.metric_type && <p className="mt-1 text-sm text-red-600">{errors.metric_type}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    操作符 *
                  </label>
                  <select
                    value={formData.operator}
                    onChange={(e) => handleInputChange('operator', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  >
                    {operators.map(op => (
                      <option key={op.value} value={op.value}>{op.label}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    阈值 *
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.threshold}
                    onChange={(e) => handleInputChange('threshold', parseFloat(e.target.value) || 0)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent ${
                      errors.threshold ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="输入阈值"
                  />
                  {errors.threshold && <p className="mt-1 text-sm text-red-600">{errors.threshold}</p>}
                </div>
              </div>
            </div>
          </div>
        </form>

        {/* 底部按钮 */}
        <div className="bg-gray-50 px-6 py-4 flex justify-between rounded-b-lg">
          <div className="flex items-center space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.enabled}
                onChange={(e) => handleInputChange('enabled', e.target.checked)}
                className="rounded border-gray-300 text-red-600 focus:ring-red-500"
              />
              <span className="ml-2 text-sm text-gray-700">启用此规则</span>
            </label>
          </div>
          
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >
              取消
            </button>
            <button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50"
            >
              {isSubmitting ? '保存中...' : (mode === 'add' ? '添加' : '保存')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
