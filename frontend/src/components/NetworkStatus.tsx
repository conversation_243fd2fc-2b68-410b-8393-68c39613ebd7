import React, { useState, useEffect } from 'react';
import { healthCheck } from '../services/api';

interface NetworkStatusProps {
  className?: string;
}

export const NetworkStatus: React.FC<NetworkStatusProps> = ({ className = '' }) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [serverStatus, setServerStatus] = useState<'online' | 'offline' | 'checking'>('checking');
  const [lastCheck, setLastCheck] = useState<Date | null>(null);

  // 检查服务器状态
  const checkServerStatus = async () => {
    try {
      setServerStatus('checking');
      await healthCheck();
      setServerStatus('online');
      setLastCheck(new Date());
    } catch (error) {
      setServerStatus('offline');
      setLastCheck(new Date());
    }
  };

  // 监听网络状态变化
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      checkServerStatus();
    };

    const handleOffline = () => {
      setIsOnline(false);
      setServerStatus('offline');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // 初始检查
    checkServerStatus();

    // 定期检查服务器状态（每30秒）
    const interval = setInterval(checkServerStatus, 30000);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      clearInterval(interval);
    };
  }, []);

  const getStatusColor = () => {
    if (!isOnline) return 'bg-red-500';
    if (serverStatus === 'offline') return 'bg-red-500';
    if (serverStatus === 'checking') return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getStatusText = () => {
    if (!isOnline) return 'No Internet';
    if (serverStatus === 'offline') return 'Server Offline';
    if (serverStatus === 'checking') return 'Checking...';
    return 'Online';
  };

  const getStatusIcon = () => {
    if (!isOnline || serverStatus === 'offline') {
      return (
        <svg className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-12.728 12.728m0-12.728l12.728 12.728" />
        </svg>
      );
    }
    
    if (serverStatus === 'checking') {
      return (
        <svg className="h-4 w-4 text-white animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
      );
    }

    return (
      <svg className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
      </svg>
    );
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className={`flex items-center justify-center w-6 h-6 rounded-full ${getStatusColor()}`}>
        {getStatusIcon()}
      </div>
      <span className="text-sm font-medium text-gray-700">
        {getStatusText()}
      </span>
      {lastCheck && (
        <span className="text-xs text-gray-500">
          {lastCheck.toLocaleTimeString()}
        </span>
      )}
    </div>
  );
};

// 网络状态Hook
export const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [serverStatus, setServerStatus] = useState<'online' | 'offline' | 'checking'>('checking');

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const checkServerStatus = async () => {
    try {
      setServerStatus('checking');
      await healthCheck();
      setServerStatus('online');
    } catch (error) {
      setServerStatus('offline');
    }
  };

  return {
    isOnline,
    serverStatus,
    checkServerStatus,
    isConnected: isOnline && serverStatus === 'online',
  };
};
