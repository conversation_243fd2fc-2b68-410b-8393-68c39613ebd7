import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Cog6ToothIcon,
  UserIcon,
  BellIcon,
  PaintBrushIcon,
  ShieldCheckIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  ComputerDesktopIcon
} from '@heroicons/react/24/outline';
import { apiService } from '../../services/api';

// 设置项接口
interface SettingItem {
  value: any;
  type: string;
  description?: string;
  is_public?: boolean;
}

// 验证规则接口
interface ValidationRule {
  type: string;
  required: boolean;
  min?: number;
  max?: number;
  options?: string[];
  pattern?: string;
  description: string;
}

export const SystemSettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'system' | 'preferences'>('preferences');
  const [editingSettings, setEditingSettings] = useState<Record<string, any>>({});
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  
  const queryClient = useQueryClient();

  // 获取设置分类
  const { data: categories } = useQuery({
    queryKey: ['setting-categories'],
    queryFn: () => apiService.getSettingCategories()
  });

  // 获取验证规则
  const { data: validationRules } = useQuery({
    queryKey: ['validation-rules'],
    queryFn: () => apiService.getValidationRules()
  });

  // 获取系统设置
  const { data: systemSettings, isLoading: systemLoading } = useQuery({
    queryKey: ['system-settings'],
    queryFn: () => apiService.getSystemSettings(),
    enabled: activeTab === 'system'
  });

  // 获取用户偏好
  const { data: userPreferences, isLoading: preferencesLoading } = useQuery({
    queryKey: ['user-preferences'],
    queryFn: () => apiService.getUserPreferences(),
    enabled: activeTab === 'preferences'
  });

  // 更新系统设置
  const updateSystemMutation = useMutation({
    mutationFn: (data: any) => apiService.updateSystemSettings(data),
    onSuccess: () => {
      setMessage({ type: 'success', text: '系统设置更新成功' });
      queryClient.invalidateQueries({ queryKey: ['system-settings'] });
      setTimeout(() => setMessage(null), 3000);
    },
    onError: (error: any) => {
      setMessage({ type: 'error', text: error.response?.data?.message || '更新失败' });
      setTimeout(() => setMessage(null), 3000);
    }
  });

  // 更新用户偏好
  const updatePreferencesMutation = useMutation({
    mutationFn: (data: any) => apiService.updateUserPreferences(data),
    onSuccess: () => {
      setMessage({ type: 'success', text: '用户偏好更新成功' });
      queryClient.invalidateQueries({ queryKey: ['user-preferences'] });
      setTimeout(() => setMessage(null), 3000);
    },
    onError: (error: any) => {
      setMessage({ type: 'error', text: error.response?.data?.message || '更新失败' });
      setTimeout(() => setMessage(null), 3000);
    }
  });

  // 初始化用户默认设置
  const initializeDefaultsMutation = useMutation({
    mutationFn: () => apiService.initializeUserDefaults(),
    onSuccess: () => {
      setMessage({ type: 'success', text: '默认设置初始化成功' });
      queryClient.invalidateQueries({ queryKey: ['user-preferences'] });
      setTimeout(() => setMessage(null), 3000);
    },
    onError: (error: any) => {
      setMessage({ type: 'error', text: error.response?.data?.message || '初始化失败' });
      setTimeout(() => setMessage(null), 3000);
    }
  });

  // 处理设置值变更
  const handleSettingChange = (category: string, key: string, value: any, valueType: string) => {
    const settingKey = `${category}.${key}`;
    setEditingSettings(prev => ({
      ...prev,
      [settingKey]: { category, key, value, value_type: valueType }
    }));
  };

  // 保存设置
  const handleSaveSettings = () => {
    const settingsArray = Object.values(editingSettings);
    if (settingsArray.length === 0) return;

    if (activeTab === 'system') {
      updateSystemMutation.mutate({ settings: settingsArray });
    } else {
      updatePreferencesMutation.mutate({ preferences: settingsArray });
    }
    setEditingSettings({});
  };

  // 重置编辑状态
  const handleResetSettings = () => {
    setEditingSettings({});
    setMessage(null);
  };

  // 渲染设置项
  const renderSettingItem = (category: string, key: string, setting: SettingItem, rule?: ValidationRule) => {
    const settingKey = `${category}.${key}`;
    const currentValue = editingSettings[settingKey]?.value ?? setting.value;
    const isEdited = settingKey in editingSettings;

    const handleChange = (value: any) => {
      handleSettingChange(category, key, value, setting.type);
    };

    return (
      <div key={settingKey} className={`p-4 border rounded-lg ${isEdited ? 'border-blue-300 bg-blue-50' : 'border-gray-200'}`}>
        <div className="flex justify-between items-start mb-2">
          <div>
            <h4 className="font-medium text-gray-900">{key}</h4>
            {(setting.description || rule?.description) && (
              <p className="text-sm text-gray-600 mt-1">
                {setting.description || rule?.description}
              </p>
            )}
          </div>
          {isEdited && (
            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
              已修改
            </span>
          )}
        </div>

        <div className="mt-3">
          {setting.type === 'boolean' ? (
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={currentValue}
                onChange={(e) => handleChange(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">启用</span>
            </label>
          ) : setting.type === 'number' ? (
            <input
              type="number"
              value={currentValue}
              onChange={(e) => handleChange(Number(e.target.value))}
              min={rule?.min}
              max={rule?.max}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          ) : rule?.options ? (
            <select
              value={currentValue}
              onChange={(e) => handleChange(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              {rule.options.map(option => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          ) : (
            <input
              type="text"
              value={currentValue}
              onChange={(e) => handleChange(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          )}
        </div>

        {rule && (
          <div className="mt-2 text-xs text-gray-500">
            {rule.required && <span className="text-red-500">* 必填</span>}
            {rule.min !== undefined && rule.max !== undefined && (
              <span className="ml-2">范围: {rule.min} - {rule.max}</span>
            )}
          </div>
        )}
      </div>
    );
  };

  // 获取分类图标
  const getCategoryIcon = (category: string) => {
    const iconMap: Record<string, React.ComponentType<any>> = {
      ui: PaintBrushIcon,
      notification: BellIcon,
      dashboard: ComputerDesktopIcon,
      report: Cog6ToothIcon,
      system: Cog6ToothIcon,
      monitoring: ComputerDesktopIcon,
      alert: BellIcon,
      backup: ShieldCheckIcon,
      security: ShieldCheckIcon
    };
    return iconMap[category] || Cog6ToothIcon;
  };

  const currentData = activeTab === 'system' ? systemSettings?.data : userPreferences?.data;
  const isLoading = activeTab === 'system' ? systemLoading : preferencesLoading;
  const hasChanges = Object.keys(editingSettings).length > 0;

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">系统设置</h1>
          <p className="text-gray-600 mt-1">管理系统配置和用户偏好设置</p>
        </div>
        
        {activeTab === 'preferences' && (
          <button
            onClick={() => initializeDefaultsMutation.mutate()}
            disabled={initializeDefaultsMutation.isPending}
            className="flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50"
          >
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            初始化默认设置
          </button>
        )}
      </div>

      {/* 消息提示 */}
      {message && (
        <div className={`p-4 rounded-lg ${message.type === 'success' ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
          <div className="flex items-center">
            {message.type === 'success' ? (
              <CheckCircleIcon className="h-5 w-5 mr-2" />
            ) : (
              <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
            )}
            {message.text}
          </div>
        </div>
      )}

      {/* 标签页 */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('preferences')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'preferences'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <UserIcon className="h-5 w-5 inline mr-2" />
            用户偏好
          </button>
          <button
            onClick={() => setActiveTab('system')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'system'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Cog6ToothIcon className="h-5 w-5 inline mr-2" />
            系统设置
          </button>
        </nav>
      </div>

      {/* 设置内容 */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">加载中...</span>
        </div>
      ) : currentData?.categories ? (
        <div className="space-y-8">
          {Object.entries(currentData.categories).map(([categoryKey, categorySettings]) => {
            const categoryInfo = activeTab === 'system' 
              ? categories?.data?.system_categories?.[categoryKey]
              : categories?.data?.preference_categories?.[categoryKey];
            
            const IconComponent = getCategoryIcon(categoryKey);

            return (
              <div key={categoryKey} className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="flex items-center mb-4">
                  <IconComponent className="h-6 w-6 text-blue-600 mr-3" />
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {categoryInfo?.name || categoryKey}
                    </h3>
                    {categoryInfo?.description && (
                      <p className="text-sm text-gray-600 mt-1">{categoryInfo.description}</p>
                    )}
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  {Object.entries(categorySettings as Record<string, SettingItem>).map(([key, setting]) => {
                    const rule = validationRules?.data?.[key];
                    return renderSettingItem(categoryKey, key, setting, rule);
                  })}
                </div>
              </div>
            );
          })}

          {/* 操作按钮 */}
          {hasChanges && (
            <div className="flex items-center justify-end space-x-4 p-4 bg-gray-50 rounded-lg">
              <button
                onClick={handleResetSettings}
                className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                重置
              </button>
              <button
                onClick={handleSaveSettings}
                disabled={updateSystemMutation.isPending || updatePreferencesMutation.isPending}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {updateSystemMutation.isPending || updatePreferencesMutation.isPending ? '保存中...' : '保存更改'}
              </button>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500">暂无设置数据</p>
        </div>
      )}
    </div>
  );
};
