import React, { createContext, useContext, useState, useCallback, type ReactNode } from 'react';
import { createPortal } from 'react-dom';

interface LoadingState {
  isLoading: boolean;
  message?: string;
  progress?: number; // 0-100
}

interface LoadingContextType {
  globalLoading: LoadingState;
  setGlobalLoading: (loading: LoadingState) => void;
  showLoading: (message?: string) => void;
  hideLoading: () => void;
  setProgress: (progress: number) => void;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};

// 全局加载遮罩组件
const GlobalLoadingOverlay: React.FC<{ loading: LoadingState }> = ({ loading }) => {
  if (!loading.isLoading) return null;

  const container = document.getElementById('loading-container');
  if (!container) return null;

  return createPortal(
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4 shadow-xl">
        <div className="flex flex-col items-center">
          {/* 加载动画 */}
          <div className="relative">
            <div className="w-12 h-12 border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin"></div>
            {loading.progress !== undefined && (
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-xs font-medium text-blue-600">
                  {Math.round(loading.progress)}%
                </span>
              </div>
            )}
          </div>

          {/* 加载消息 */}
          {loading.message && (
            <p className="mt-4 text-sm text-gray-600 text-center">
              {loading.message}
            </p>
          )}

          {/* 进度条 */}
          {loading.progress !== undefined && (
            <div className="w-full mt-4">
              <div className="bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${loading.progress}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>,
    container
  );
};

// 加载提供者组件
export const LoadingProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [globalLoading, setGlobalLoading] = useState<LoadingState>({
    isLoading: false,
  });

  const showLoading = useCallback((message?: string) => {
    setGlobalLoading({
      isLoading: true,
      message,
    });
  }, []);

  const hideLoading = useCallback(() => {
    setGlobalLoading({
      isLoading: false,
    });
  }, []);

  const setProgress = useCallback((progress: number) => {
    setGlobalLoading(prev => ({
      ...prev,
      progress: Math.max(0, Math.min(100, progress)),
    }));
  }, []);

  // 创建loading容器
  React.useEffect(() => {
    const container = document.getElementById('loading-container');
    if (!container) {
      const newContainer = document.createElement('div');
      newContainer.id = 'loading-container';
      document.body.appendChild(newContainer);
    }
  }, []);

  const value: LoadingContextType = {
    globalLoading,
    setGlobalLoading,
    showLoading,
    hideLoading,
    setProgress,
  };

  return (
    <LoadingContext.Provider value={value}>
      {children}
      <GlobalLoadingOverlay loading={globalLoading} />
    </LoadingContext.Provider>
  );
};

// 局部加载组件
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: 'blue' | 'gray' | 'white';
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'blue',
  className = '',
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  const colorClasses = {
    blue: 'border-blue-200 border-t-blue-500',
    gray: 'border-gray-200 border-t-gray-500',
    white: 'border-white border-opacity-30 border-t-white',
  };

  return (
    <div
      className={`
        ${sizeClasses[size]}
        border-2 ${colorClasses[color]}
        rounded-full animate-spin
        ${className}
      `}
    />
  );
};

// 加载状态包装器
interface LoadingWrapperProps {
  loading: boolean;
  children: ReactNode;
  fallback?: ReactNode;
  className?: string;
}

export const LoadingWrapper: React.FC<LoadingWrapperProps> = ({
  loading,
  children,
  fallback,
  className = '',
}) => {
  if (loading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        {fallback || <LoadingSpinner />}
      </div>
    );
  }

  return <>{children}</>;
};

// 骨架屏组件
interface SkeletonProps {
  className?: string;
  lines?: number;
  avatar?: boolean;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  className = '',
  lines = 3,
  avatar = false,
}) => {
  return (
    <div className={`animate-pulse ${className}`}>
      {avatar && (
        <div className="flex items-center space-x-4 mb-4">
          <div className="rounded-full bg-gray-200 h-10 w-10"></div>
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      )}
      
      <div className="space-y-3">
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={`h-4 bg-gray-200 rounded ${
              index === lines - 1 ? 'w-2/3' : 'w-full'
            }`}
          ></div>
        ))}
      </div>
    </div>
  );
};

// 卡片骨架屏
export const CardSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
      <Skeleton lines={4} />
    </div>
  );
};

// 表格骨架屏
export const TableSkeleton: React.FC<{ rows?: number; columns?: number }> = ({
  rows = 5,
  columns = 4,
}) => {
  return (
    <div className="animate-pulse">
      {/* 表头 */}
      <div className="grid gap-4 mb-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {Array.from({ length: columns }).map((_, index) => (
          <div key={index} className="h-4 bg-gray-200 rounded"></div>
        ))}
      </div>
      
      {/* 表格行 */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div
          key={rowIndex}
          className="grid gap-4 mb-3"
          style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
        >
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div key={colIndex} className="h-3 bg-gray-200 rounded"></div>
          ))}
        </div>
      ))}
    </div>
  );
};

// 页面加载Hook
export const usePageLoading = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const withLoading = useCallback(async <T,>(
    asyncFn: () => Promise<T>,
    _loadingMessage?: string
  ): Promise<T | null> => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await asyncFn();
      return result;
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    isLoading,
    error,
    withLoading,
    setError,
  };
};
