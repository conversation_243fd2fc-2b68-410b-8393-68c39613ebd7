import React from 'react';
import { useResponsive, useSidebarState } from '../../hooks/useResponsive';

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  header?: React.ReactNode;
  className?: string;
}

/**
 * 响应式布局容器组件
 * 基于DataDog UI设计的布局策略
 * 
 * 桌面端: 左侧边栏 + 主内容区
 * 平板端: 顶部导航 + 内容区
 * 移动端: 单列布局
 */
export const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  sidebar,
  header,
  className = ''
}) => {
  const { screenType, isDesktop, isMobileOrTablet } = useResponsive();
  const { sidebarWidth, showSidebar } = useSidebarState();

  // 桌面端布局
  if (isDesktop) {
    return (
      <div className={`min-h-screen bg-gray-50 ${className}`}>
        {/* 顶部Header */}
        {header && (
          <div className="fixed top-0 left-0 right-0 z-30 bg-white border-b border-gray-200">
            {header}
          </div>
        )}
        
        <div className="flex">
          {/* 左侧边栏 */}
          {sidebar && showSidebar && (
            <div
              className={`
                fixed left-0 top-0 h-full bg-white border-r border-gray-200 z-20
                transition-all duration-300 ease-in-out
                ${header ? 'pt-16' : 'pt-0'}
              `}
              style={{ width: sidebarWidth }}
            >
              {sidebar}
            </div>
          )}
          
          {/* 主内容区 */}
          <div
            className={`
              flex-1 transition-all duration-300 ease-in-out
              ${header ? 'pt-16' : 'pt-0'}
            `}
            style={{
              marginLeft: showSidebar ? sidebarWidth : 0,
              minHeight: header ? 'calc(100vh - 4rem)' : '100vh'
            }}
          >
            <div className="p-6">
              {children}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 平板端和移动端布局
  return (
    <div className={`min-h-screen bg-gray-50 ${className}`}>
      {/* 顶部Header */}
      {header && (
        <div className="sticky top-0 z-30 bg-white border-b border-gray-200">
          {header}
        </div>
      )}
      
      {/* 主内容区 */}
      <div className="flex-1">
        <div className={`
          ${screenType === 'mobile' ? 'p-4' : 'p-6'}
        `}>
          {children}
        </div>
      </div>
      
      {/* 移动端侧边栏 (覆盖层) */}
      {sidebar && isMobileOrTablet && (
        <div className="lg:hidden">
          {sidebar}
        </div>
      )}
    </div>
  );
};

/**
 * 响应式网格容器组件
 * 用于自适应不同屏幕尺寸的网格布局
 */
interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  // 不同屏幕尺寸的列数配置
  cols?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  // 间距配置
  gap?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  className = '',
  cols = { mobile: 1, tablet: 2, desktop: 4 },
  gap = { mobile: 4, tablet: 6, desktop: 6 }
}) => {
  const { screenType } = useResponsive();
  
  const currentCols = cols[screenType] || cols.desktop || 4;
  const currentGap = gap[screenType] || gap.desktop || 6;
  
  return (
    <div 
      className={`
        grid
        ${className}
      `}
      style={{
        gridTemplateColumns: `repeat(${currentCols}, 1fr)`,
        gap: `${currentGap * 0.25}rem` // Tailwind gap-6 = 1.5rem = 6 * 0.25rem
      }}
    >
      {children}
    </div>
  );
};

/**
 * 响应式卡片容器组件
 * 统一的卡片样式，支持不同屏幕尺寸的适配
 */
interface ResponsiveCardProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'sm' | 'md' | 'lg';
  shadow?: 'sm' | 'md' | 'lg';
  hover?: boolean;
  onClick?: () => void;
}

export const ResponsiveCard: React.FC<ResponsiveCardProps> = ({
  children,
  className = '',
  padding = 'md',
  shadow = 'sm',
  hover = false,
  onClick
}) => {
  const { screenType } = useResponsive();
  
  // 根据屏幕尺寸调整padding
  const paddingClasses = {
    sm: screenType === 'mobile' ? 'p-3' : 'p-4',
    md: screenType === 'mobile' ? 'p-4' : 'p-6',
    lg: screenType === 'mobile' ? 'p-5' : 'p-8'
  };
  
  const shadowClasses = {
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg'
  };
  
  return (
    <div
      className={`
        bg-white rounded-lg border border-gray-200
        ${paddingClasses[padding]}
        ${shadowClasses[shadow]}
        ${hover ? 'hover:shadow-md transition-shadow duration-200' : ''}
        ${onClick ? 'cursor-pointer' : ''}
        ${className}
      `}
      onClick={onClick}
    >
      {children}
    </div>
  );
};

/**
 * 响应式间距组件
 * 提供统一的间距管理
 */
interface ResponsiveSpacerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  direction?: 'vertical' | 'horizontal' | 'both';
}

export const ResponsiveSpacer: React.FC<ResponsiveSpacerProps> = ({
  size = 'md',
  direction = 'vertical'
}) => {
  const { screenType } = useResponsive();
  
  // 根据屏幕尺寸调整间距
  const sizeMap = {
    xs: screenType === 'mobile' ? 2 : 3,
    sm: screenType === 'mobile' ? 3 : 4,
    md: screenType === 'mobile' ? 4 : 6,
    lg: screenType === 'mobile' ? 6 : 8,
    xl: screenType === 'mobile' ? 8 : 12
  };
  
  const spacing = sizeMap[size];
  
  const getClassName = () => {
    switch (direction) {
      case 'horizontal':
        return `w-${spacing}`;
      case 'both':
        return `w-${spacing} h-${spacing}`;
      default:
        return `h-${spacing}`;
    }
  };
  
  return <div className={getClassName()} />;
};
