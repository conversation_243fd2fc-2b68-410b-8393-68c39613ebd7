import React from 'react';
import {
  CircleStackIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  WrenchScrewdriverIcon,
  DocumentChartBarIcon,
  Cog6ToothIcon,
  EllipsisHorizontalIcon
} from '@heroicons/react/24/outline';

interface MobileActionGridProps {
  onActionSelect: (action: string) => void;
  className?: string;
}

/**
 * 移动端操作网格组件
 * 显示6个主要功能的网格布局
 */
export const MobileActionGrid: React.FC<MobileActionGridProps> = ({
  onActionSelect,
  className = ''
}) => {
  // 移动端主要功能配置 (6个核心功能)
  const mobileActions = [
    {
      id: 'database',
      label: '数据库',
      icon: CircleStackIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      description: '管理数据库实例'
    },
    {
      id: 'alert',
      label: '告警',
      icon: ExclamationTriangleIcon,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      description: '查看和管理告警'
    },
    {
      id: 'performance',
      label: '性能',
      icon: ChartBarIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      description: '性能监控和分析'
    },
    {
      id: 'query-optimizer',
      label: '查询优化',
      icon: MagnifyingGlassIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      description: 'SQL查询优化'
    },
    {
      id: 'maintenance',
      label: '维护',
      icon: WrenchScrewdriverIcon,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      description: '数据库维护工具'
    },
    {
      id: 'more',
      label: '更多',
      icon: EllipsisHorizontalIcon,
      color: 'text-gray-600',
      bgColor: 'bg-gray-50',
      description: '更多功能和设置'
    }
  ];

  const handleActionClick = (actionId: string) => {
    if (actionId === 'more') {
      // 显示更多功能菜单
      onActionSelect('show-more-menu');
    } else {
      onActionSelect(actionId);
    }
  };

  return (
    <div className={`${className}`}>
      {/* 标题 */}
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-1">快速操作</h3>
        <p className="text-sm text-gray-600">选择要执行的操作</p>
      </div>

      {/* 操作网格 */}
      <div className="grid grid-cols-3 gap-4">
        {mobileActions.map((action) => {
          const Icon = action.icon;
          return (
            <button
              key={action.id}
              onClick={() => handleActionClick(action.id)}
              className={`
                flex flex-col items-center p-4 rounded-xl border border-gray-200
                ${action.bgColor} hover:shadow-md transition-all duration-200
                hover:scale-105 active:scale-95
              `}
            >
              <div className={`p-3 rounded-lg bg-white shadow-sm mb-3`}>
                <Icon className={`h-6 w-6 ${action.color}`} />
              </div>
              <span className="text-sm font-medium text-gray-900 text-center">
                {action.label}
              </span>
              <span className="text-xs text-gray-500 text-center mt-1">
                {action.description}
              </span>
            </button>
          );
        })}
      </div>

      {/* 快速操作按钮 */}
      <div className="mt-6">
        <h4 className="text-sm font-medium text-gray-700 mb-3">常用操作</h4>
        <div className="flex space-x-3">
          <button
            onClick={() => onActionSelect('new-alert')}
            className="flex-1 flex items-center justify-center px-4 py-3 bg-orange-50 text-orange-700 rounded-lg hover:bg-orange-100 transition-colors"
          >
            <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
            新建告警
          </button>
          <button
            onClick={() => onActionSelect('health-check')}
            className="flex-1 flex items-center justify-center px-4 py-3 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors"
          >
            <ChartBarIcon className="h-4 w-4 mr-2" />
            健康检查
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * 移动端更多功能菜单组件
 */
interface MobileMoreMenuProps {
  onActionSelect: (action: string) => void;
  onClose: () => void;
  isOpen: boolean;
}

export const MobileMoreMenu: React.FC<MobileMoreMenuProps> = ({
  onActionSelect,
  onClose,
  isOpen
}) => {
  const moreActions = [
    {
      id: 'reports',
      label: '报表系统',
      icon: DocumentChartBarIcon,
      description: '生成和查看报表'
    },
    {
      id: 'settings',
      label: '系统设置',
      icon: Cog6ToothIcon,
      description: '系统配置和设置'
    }
  ];

  const handleActionClick = (actionId: string) => {
    onActionSelect(actionId);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <>
      {/* 背景遮罩 */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={onClose}
      />
      
      {/* 菜单内容 */}
      <div className="fixed bottom-0 left-0 right-0 bg-white rounded-t-xl z-50 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">更多功能</h3>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <EllipsisHorizontalIcon className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        <div className="space-y-3">
          {moreActions.map((action) => {
            const Icon = action.icon;
            return (
              <button
                key={action.id}
                onClick={() => handleActionClick(action.id)}
                className="w-full flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <Icon className="h-6 w-6 text-gray-600 mr-4" />
                <div className="flex-1 text-left">
                  <div className="font-medium text-gray-900">{action.label}</div>
                  <div className="text-sm text-gray-600">{action.description}</div>
                </div>
              </button>
            );
          })}
        </div>

        {/* 安全区域 */}
        <div className="h-6"></div>
      </div>
    </>
  );
};
