import React from 'react';
import {
  HomeIcon,
  CircleStackIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  WrenchScrewdriverIcon,
  DocumentChartBarIcon,
  Cog6ToothIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { useSidebarState } from '../../hooks/useResponsive';

interface MobileMenuProps {
  currentView: string;
  onViewChange: (view: string) => void;
  className?: string;
}

/**
 * 移动端菜单组件
 * 覆盖层式的全屏菜单
 */
export const MobileMenu: React.FC<MobileMenuProps> = ({
  currentView,
  onViewChange,
  className = ''
}) => {
  const { isMobileMenuOpen, closeMobileMenu } = useSidebarState();

  const navigationItems = [
    {
      id: 'dashboard',
      label: '仪表板',
      icon: HomeIcon,
      description: '系统概览和核心指标'
    },
    {
      id: 'database',
      label: '数据库管理',
      icon: CircleStackIcon,
      description: '数据库实例管理'
    },
    {
      id: 'alert',
      label: '告警管理',
      icon: ExclamationTriangleIcon,
      description: '告警规则和事件'
    },
    {
      id: 'performance',
      label: '性能分析',
      icon: ChartBarIcon,
      description: '性能监控和分析'
    },
    {
      id: 'query-optimizer',
      label: '查询优化',
      icon: MagnifyingGlassIcon,
      description: 'SQL查询优化工具'
    },
    {
      id: 'maintenance',
      label: '维护工具',
      icon: WrenchScrewdriverIcon,
      description: '数据库维护工具集'
    },
    {
      id: 'reports',
      label: '报表系统',
      icon: DocumentChartBarIcon,
      description: '报表生成和管理'
    },
    {
      id: 'settings',
      label: '系统设置',
      icon: Cog6ToothIcon,
      description: '系统配置和设置'
    }
  ];

  const handleItemClick = (viewId: string) => {
    onViewChange(viewId);
    closeMobileMenu();
  };

  if (!isMobileMenuOpen) {
    return null;
  }

  return (
    <>
      {/* 背景遮罩 */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={closeMobileMenu}
      />
      
      {/* 菜单内容 */}
      <div className={`
        fixed inset-y-0 left-0 w-80 max-w-full bg-white z-50
        transform transition-transform duration-300 ease-in-out
        ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'}
        ${className}
      `}>
        {/* 菜单头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <CircleStackIcon className="h-8 w-8 text-blue-600" />
            <span className="text-lg font-semibold text-gray-900">
              DB Monitor
            </span>
          </div>
          
          <button
            onClick={closeMobileMenu}
            className="p-2 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <XMarkIcon className="h-6 w-6 text-gray-500" />
          </button>
        </div>

        {/* 导航菜单 */}
        <div className="flex-1 py-4 overflow-y-auto">
          <nav className="px-4 space-y-2">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const isActive = currentView === item.id;
              
              return (
                <button
                  key={item.id}
                  onClick={() => handleItemClick(item.id)}
                  className={`
                    w-full flex items-center px-4 py-3 text-left rounded-lg
                    transition-all duration-200
                    ${isActive 
                      ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-600' 
                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                    }
                  `}
                >
                  <Icon className={`
                    h-6 w-6 mr-4 flex-shrink-0
                    ${isActive ? 'text-blue-600' : 'text-gray-400'}
                  `} />
                  
                  <div className="flex-1">
                    <div className="text-sm font-medium">{item.label}</div>
                    <div className="text-xs text-gray-500 mt-0.5">
                      {item.description}
                    </div>
                  </div>
                  
                  {isActive && (
                    <div className="ml-2">
                      <div className="h-2 w-2 bg-blue-600 rounded-full"></div>
                    </div>
                  )}
                </button>
              );
            })}
          </nav>
        </div>

        {/* 菜单底部 - 快速操作 */}
        <div className="border-t border-gray-200 p-4">
          <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">
            快速操作
          </div>
          
          <div className="space-y-2">
            <button className="w-full flex items-center px-4 py-2 text-sm font-medium rounded-lg bg-orange-50 text-orange-700 hover:bg-orange-100 transition-colors">
              <ExclamationTriangleIcon className="h-4 w-4 mr-3" />
              新建告警
            </button>
            
            <button className="w-full flex items-center px-4 py-2 text-sm font-medium rounded-lg bg-green-50 text-green-700 hover:bg-green-100 transition-colors">
              <ChartBarIcon className="h-4 w-4 mr-3" />
              健康检查
            </button>
          </div>
        </div>

        {/* 系统状态概览 */}
        <div className="border-t border-gray-200 p-4">
          <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">
            系统状态
          </div>
          
          <div className="space-y-3">
            {/* 数据库状态 */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">数据库</span>
              <div className="flex items-center space-x-1">
                <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                <div className="h-2 w-2 bg-yellow-500 rounded-full"></div>
                <span className="text-sm text-gray-900 ml-2">3</span>
              </div>
            </div>

            {/* 告警状态 */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">活跃告警</span>
              <span className="text-sm font-medium text-orange-600">2</span>
            </div>

            {/* 性能状态 */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">系统性能</span>
              <span className="text-sm font-medium text-green-600">良好</span>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
