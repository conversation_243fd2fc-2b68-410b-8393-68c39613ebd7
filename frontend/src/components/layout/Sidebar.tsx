import React from 'react';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  HomeIcon,
  CircleStackIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  WrenchScrewdriverIcon,
  DocumentChartBarIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import { useSidebarState } from '../../hooks/useResponsive';


interface SidebarProps {
  currentView: string;
  onViewChange: (view: string) => void;
  onQuickAction?: (action: string) => void;
  className?: string;
}

/**
 * 桌面端左侧边栏组件
 * 基于DataDog UI设计的侧边栏布局
 */
export const Sidebar: React.FC<SidebarProps> = ({
  currentView,
  onViewChange,
  onQuickAction,
  className = ''
}) => {
  const { isCollapsed, toggleSidebar } = useSidebarState();

  return (
    <div className={`
      h-full bg-white border-r border-gray-200 flex flex-col
      transition-all duration-300 ease-in-out
      ${isCollapsed ? 'w-16' : 'w-64'}
      ${className}
    `}>
      {/* 侧边栏头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        {!isCollapsed && (
          <div className="flex items-center space-x-2">
            <CircleStackIcon className="h-8 w-8 text-blue-600" />
            <span className="text-lg font-semibold text-gray-900">
              DB Monitor
            </span>
          </div>
        )}

        {/* 折叠按钮 */}
        <button
          onClick={toggleSidebar}
          className="p-1.5 rounded-lg hover:bg-gray-100 transition-colors"
          title={isCollapsed ? '展开侧边栏' : '折叠侧边栏'}
        >
          {isCollapsed ? (
            <ChevronRightIcon className="h-5 w-5 text-gray-500" />
          ) : (
            <ChevronLeftIcon className="h-5 w-5 text-gray-500" />
          )}
        </button>
      </div>

      {/* 主导航区域 */}
      <div className="flex-1 py-4">
        <SidebarNavigation
          currentView={currentView}
          onViewChange={onViewChange}
          isCollapsed={isCollapsed}
        />
      </div>

      {/* 快速操作区域 */}
      <div className="border-t border-gray-200">
        <SidebarQuickActions
          isCollapsed={isCollapsed}
          onQuickAction={onQuickAction}
        />
      </div>

      {/* 系统状态区域 */}
      <div className="border-t border-gray-200">
        <SidebarSystemStatus isCollapsed={isCollapsed} />
      </div>
    </div>
  );
};

/**
 * 侧边栏导航组件
 */
interface SidebarNavigationProps {
  currentView: string;
  onViewChange: (view: string) => void;
  isCollapsed: boolean;
}

const SidebarNavigation: React.FC<SidebarNavigationProps> = ({
  currentView,
  onViewChange,
  isCollapsed
}) => {
  const navigationItems = [
    {
      id: 'dashboard',
      label: '仪表板',
      icon: HomeIcon,
      description: '系统概览和核心指标'
    },
    {
      id: 'database',
      label: '数据库管理',
      icon: CircleStackIcon,
      description: '数据库实例管理'
    },
    {
      id: 'alert',
      label: '告警管理',
      icon: ExclamationTriangleIcon,
      description: '告警规则和事件'
    },
    {
      id: 'performance',
      label: '性能分析',
      icon: ChartBarIcon,
      description: '性能监控和分析'
    },
    {
      id: 'query-optimizer',
      label: '查询优化',
      icon: MagnifyingGlassIcon,
      description: 'SQL查询优化工具'
    },
    {
      id: 'maintenance',
      label: '维护工具',
      icon: WrenchScrewdriverIcon,
      description: '数据库维护工具集'
    },
    {
      id: 'reports',
      label: '报表系统',
      icon: DocumentChartBarIcon,
      description: '报表生成和管理'
    },
    {
      id: 'settings',
      label: '系统设置',
      icon: Cog6ToothIcon,
      description: '系统配置和设置'
    }
  ];

  return (
    <nav className="px-2 space-y-1">
      {navigationItems.map((item) => {
        const Icon = item.icon;
        const isActive = currentView === item.id;
        
        return (
          <button
            key={item.id}
            onClick={() => onViewChange(item.id)}
            className={`
              w-full flex items-center px-3 py-2.5 text-sm font-medium rounded-lg
              transition-all duration-200 group
              ${isActive
                ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              }
              ${isCollapsed ? 'justify-center' : 'justify-start'}
            `}
            title={isCollapsed ? item.label : item.description}
          >
            <Icon className={`
              h-5 w-5 flex-shrink-0
              ${isActive ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-500'}
              ${isCollapsed ? '' : 'mr-3'}
            `} />

            {!isCollapsed && (
              <span className="truncate">{item.label}</span>
            )}

            {!isCollapsed && isActive && (
              <div className="ml-auto">
                <div className="h-2 w-2 bg-blue-600 rounded-full"></div>
              </div>
            )}
          </button>
        );
      })}
    </nav>
  );
};

/**
 * 侧边栏快速操作组件
 */
interface SidebarQuickActionsProps {
  isCollapsed: boolean;
  onQuickAction?: (action: string) => void;
}

const SidebarQuickActions: React.FC<SidebarQuickActionsProps> = ({
  isCollapsed,
  onQuickAction
}) => {
  // 一级功能 - 始终可见的核心操作
  const primaryActions = [
    {
      id: 'new-alert',
      label: '新建告警',
      icon: ExclamationTriangleIcon,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50 hover:bg-orange-100',
      description: '创建新的监控告警规则'
    },
    {
      id: 'health-check',
      label: '健康检查',
      icon: ChartBarIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-50 hover:bg-green-100',
      description: '执行系统健康检查'
    },
    {
      id: 'backup-now',
      label: '立即备份',
      icon: CircleStackIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 hover:bg-blue-100',
      description: '执行数据库备份操作'
    }
  ];

  const handleActionClick = (actionId: string) => {
    onQuickAction?.(actionId);
  };

  if (isCollapsed) {
    return (
      <div className="p-2 space-y-2">
        {primaryActions.map((action) => {
          const Icon = action.icon;
          return (
            <button
              key={action.id}
              onClick={() => handleActionClick(action.id)}
              className={`
                w-full p-2 rounded-lg transition-colors
                ${action.bgColor} ${action.color}
              `}
              title={action.label}
            >
              <Icon className="h-5 w-5" />
            </button>
          );
        })}
      </div>
    );
  }

  return (
    <div className="p-4">
      <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">
        快速操作
      </h3>
      <div className="space-y-2">
        {primaryActions.map((action) => {
          const Icon = action.icon;
          return (
            <button
              key={action.id}
              onClick={() => handleActionClick(action.id)}
              className={`
                w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg
                transition-colors ${action.bgColor} ${action.color}
                hover:scale-105 transform duration-200
              `}
              title={action.description}
            >
              <Icon className="h-4 w-4 mr-2" />
              {action.label}
            </button>
          );
        })}
      </div>
    </div>
  );
};

/**
 * 侧边栏系统状态组件
 */
interface SidebarSystemStatusProps {
  isCollapsed: boolean;
}

const SidebarSystemStatus: React.FC<SidebarSystemStatusProps> = ({
  isCollapsed
}) => {
  // 模拟系统状态数据
  const systemStatus = {
    databases: { total: 3, healthy: 2, warning: 1, error: 0 },
    alerts: { active: 2, resolved: 15 },
    performance: { cpu: 45, memory: 67, disk: 23 }
  };

  if (isCollapsed) {
    return (
      <div className="p-2">
        <div className="flex flex-col items-center space-y-1">
          {/* 数据库状态指示器 */}
          <div className="flex space-x-1">
            <div className="h-2 w-2 bg-green-500 rounded-full" title="健康数据库"></div>
            <div className="h-2 w-2 bg-yellow-500 rounded-full" title="警告数据库"></div>
          </div>

          {/* 告警指示器 */}
          <div className="text-xs text-gray-500" title="活跃告警">
            {systemStatus.alerts.active}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">
        系统状态
      </h3>

      <div className="space-y-3">
        {/* 数据库状态 */}
        <div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">数据库</span>
            <span className="text-gray-900 font-medium">
              {systemStatus.databases.total}
            </span>
          </div>
          <div className="flex space-x-1 mt-1">
            {Array.from({ length: systemStatus.databases.healthy }).map((_, i) => (
              <div key={`healthy-${i}`} className="h-1 w-4 bg-green-500 rounded"></div>
            ))}
            {Array.from({ length: systemStatus.databases.warning }).map((_, i) => (
              <div key={`warning-${i}`} className="h-1 w-4 bg-yellow-500 rounded"></div>
            ))}
            {Array.from({ length: systemStatus.databases.error }).map((_, i) => (
              <div key={`error-${i}`} className="h-1 w-4 bg-red-500 rounded"></div>
            ))}
          </div>
        </div>

        {/* 告警状态 */}
        <div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">活跃告警</span>
            <span className={`font-medium ${
              systemStatus.alerts.active > 0 ? 'text-orange-600' : 'text-green-600'
            }`}>
              {systemStatus.alerts.active}
            </span>
          </div>
        </div>

        {/* 性能概览 */}
        <div>
          <div className="text-xs text-gray-500 mb-2">性能概览</div>
          <div className="space-y-1">
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">CPU</span>
              <span className="text-gray-900">{systemStatus.performance.cpu}%</span>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">内存</span>
              <span className="text-gray-900">{systemStatus.performance.memory}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
