import React, { useState } from 'react';
import {
  MagnifyingGlassIcon,
  UserCircleIcon,
  ChevronDownIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  CircleStackIcon,
  HomeIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CodeBracketIcon,
  WrenchScrewdriverIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import { AlertNotificationBell } from '../alerts/AlertNotificationBell';

interface NavigationBarProps {
  currentView: string;
  onViewChange: (view: string) => void;
  currentUser?: {
    name: string;
    email: string;
    avatar?: string;
  };
}

export const NavigationBar: React.FC<NavigationBarProps> = ({
  currentView,
  onViewChange,
  currentUser = { name: 'Admin', email: '<EMAIL>' }
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showUserMenu, setShowUserMenu] = useState(false);

  // 获取当前页面标题和面包屑
  const getPageInfo = () => {
    switch (currentView) {
      case 'dashboard':
        return { title: '概览', breadcrumb: ['数据库监控平台', '概览'] };
      case 'database':
        return { title: '数据库管理', breadcrumb: ['数据库监控平台', '数据库管理'] };
      case 'performance':
        return { title: '性能分析', breadcrumb: ['数据库监控平台', '性能分析'] };
      case 'alert':
        return { title: '告警管理', breadcrumb: ['数据库监控平台', '告警管理'] };
      case 'query-optimizer':
        return { title: '查询优化', breadcrumb: ['数据库监控平台', '查询优化'] };
      case 'maintenance':
        return { title: '维护工具', breadcrumb: ['数据库监控平台', '维护工具'] };
      case 'reports':
        return { title: '报表系统', breadcrumb: ['数据库监控平台', '报表系统'] };
      case 'settings':
        return { title: '系统设置', breadcrumb: ['数据库监控平台', '系统设置'] };
      default:
        return { title: '概览', breadcrumb: ['数据库监控平台', '概览'] };
    }
  };

  const pageInfo = getPageInfo();

  // 主导航菜单项
  const navigationItems = [
    {
      id: 'dashboard',
      label: '概览',
      icon: HomeIcon,
      view: 'dashboard'
    },
    {
      id: 'database',
      label: '数据库',
      icon: CircleStackIcon,
      view: 'database'
    },
    {
      id: 'performance',
      label: '性能分析',
      icon: ChartBarIcon,
      view: 'performance'
    },
    {
      id: 'alert',
      label: '告警管理',
      icon: ExclamationTriangleIcon,
      view: 'alert'
    },
    {
      id: 'query-optimizer',
      label: '查询优化',
      icon: CodeBracketIcon,
      view: 'query-optimizer'
    },
    {
      id: 'maintenance',
      label: '维护工具',
      icon: WrenchScrewdriverIcon,
      view: 'maintenance'
    },
    {
      id: 'reports',
      label: '报表系统',
      icon: DocumentTextIcon,
      view: 'reports'
    },
    {
      id: 'settings',
      label: '系统设置',
      icon: Cog6ToothIcon,
      view: 'settings'
    }
  ];

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('搜索:', searchQuery);
    // TODO: 实现搜索功能
  };

  const handleUserMenuToggle = () => {
    setShowUserMenu(!showUserMenu);
  };

  const handleLogout = () => {
    console.log('用户退出');
    // TODO: 实现退出功能
  };

  return (
    <nav className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* 左侧：Logo + 导航 */}
          <div className="flex items-center">
            {/* Logo和品牌 */}
            <div className="flex-shrink-0 flex items-center">
              <CircleStackIcon className="h-8 w-8 text-blue-600" />
              <span className="ml-2 text-xl font-semibold text-gray-900">
                数据库监控平台
              </span>
            </div>

            {/* 面包屑导航 */}
            <div className="hidden md:flex items-center ml-8">
              <nav className="flex" aria-label="Breadcrumb">
                <ol className="flex items-center space-x-2">
                  {pageInfo.breadcrumb.map((item, index) => (
                    <li key={index} className="flex items-center">
                      {index > 0 && (
                        <ChevronDownIcon className="h-4 w-4 text-gray-400 mx-2 rotate-[-90deg]" />
                      )}
                      <span
                        className={`text-sm ${
                          index === pageInfo.breadcrumb.length - 1
                            ? 'text-gray-900 font-medium'
                            : 'text-gray-500 hover:text-gray-700 cursor-pointer'
                        }`}
                      >
                        {item}
                      </span>
                    </li>
                  ))}
                </ol>
              </nav>
            </div>

            {/* 主导航菜单 */}
            <div className="hidden lg:flex items-center ml-8 space-x-1">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                const isActive = currentView === item.view;
                return (
                  <button
                    key={item.id}
                    onClick={() => onViewChange(item.view)}
                    className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      isActive
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="h-4 w-4 mr-2" />
                    {item.label}
                  </button>
                );
              })}
            </div>
          </div>

          {/* 右侧：搜索 + 告警 + 用户菜单 */}
          <div className="flex items-center space-x-4">
            {/* 搜索框 */}
            <form onSubmit={handleSearch} className="hidden md:block">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="搜索数据库、告警..."
                  className="block w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
                />
              </div>
            </form>

            {/* 告警铃铛 */}
            <AlertNotificationBell
              onViewAllAlerts={() => onViewChange('alert')}
            />

            {/* 用户菜单 */}
            <div className="relative">
              <button
                onClick={handleUserMenuToggle}
                className="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <div className="flex items-center space-x-2 px-3 py-2 rounded-md hover:bg-gray-50">
                  <UserCircleIcon className="h-6 w-6 text-gray-400" />
                  <span className="hidden md:block text-gray-700 font-medium">
                    {currentUser.name}
                  </span>
                  <ChevronDownIcon className="h-4 w-4 text-gray-400" />
                </div>
              </button>

              {/* 用户下拉菜单 */}
              {showUserMenu && (
                <div className="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                  <div className="py-1">
                    {/* 用户信息 */}
                    <div className="px-4 py-3 border-b border-gray-100">
                      <p className="text-sm font-medium text-gray-900">{currentUser.name}</p>
                      <p className="text-sm text-gray-500">{currentUser.email}</p>
                    </div>

                    {/* 菜单项 */}
                    <button
                      onClick={() => {
                        setShowUserMenu(false);
                        onViewChange('settings');
                      }}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <Cog6ToothIcon className="h-4 w-4 mr-3" />
                      系统设置
                    </button>

                    <button
                      onClick={() => {
                        setShowUserMenu(false);
                        handleLogout();
                      }}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <ArrowRightOnRectangleIcon className="h-4 w-4 mr-3" />
                      退出登录
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 移动端导航菜单 */}
      <div className="lg:hidden border-t border-gray-200">
        <div className="px-4 py-3">
          <div className="flex space-x-1 overflow-x-auto">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const isActive = currentView === item.view;
              return (
                <button
                  key={item.id}
                  onClick={() => onViewChange(item.view)}
                  className={`flex items-center px-3 py-2 rounded-md text-sm font-medium whitespace-nowrap transition-colors ${
                    isActive
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {item.label}
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* 点击外部关闭用户菜单 */}
      {showUserMenu && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowUserMenu(false)}
        />
      )}
    </nav>
  );
};
