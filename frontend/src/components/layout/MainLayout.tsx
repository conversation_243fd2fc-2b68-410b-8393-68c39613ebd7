import React, { useState } from 'react';
import {
  HomeIcon,
  ServerIcon,
  BellIcon,
  CogIcon,
  ChartBarIcon,
  UserIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { DashboardV2 } from '../dashboard/DashboardV2';
import { DatabaseManagement } from '../database/DatabaseManagement';
import AlertManagement from '../alerts/AlertManagement';
import { AlertNotificationBell } from '../alerts/AlertNotificationBell';

interface MainLayoutProps {
  user: {
    name: string;
    email: string;
  };
  onLogout: () => void;
}

type ViewType = 'dashboard' | 'databases' | 'alerts' | 'analytics' | 'settings';

export const MainLayout: React.FC<MainLayoutProps> = ({ user, onLogout }) => {
  const [currentView, setCurrentView] = useState<ViewType>('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const navigation = [
    { 
      name: '仪表板', 
      icon: HomeIcon, 
      view: 'dashboard' as ViewType,
      description: '系统概览和实时监控'
    },
    { 
      name: '数据库管理', 
      icon: ServerIcon, 
      view: 'databases' as ViewType,
      description: '数据库配置和连接管理'
    },
    { 
      name: '告警管理', 
      icon: BellIcon, 
      view: 'alerts' as ViewType,
      description: '告警规则和事件管理'
    },
    { 
      name: '性能分析', 
      icon: ChartBarIcon, 
      view: 'analytics' as ViewType,
      description: '性能趋势和分析报告'
    },
    { 
      name: '系统设置', 
      icon: CogIcon, 
      view: 'settings' as ViewType,
      description: '系统配置和用户管理'
    }
  ];

  const renderCurrentView = () => {
    switch (currentView) {
      case 'dashboard':
        return <DashboardV2 user={user} onLogout={onLogout} />;
      case 'databases':
        return <DatabaseManagement />;
      case 'alerts':
        return <AlertManagement />;
      case 'analytics':
        return (
          <div className="min-h-screen bg-gray-50 p-6">
            <div className="max-w-7xl mx-auto">
              <div className="text-center py-12">
                <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">性能分析</h3>
                <p className="mt-1 text-sm text-gray-500">此功能正在开发中...</p>
              </div>
            </div>
          </div>
        );
      case 'settings':
        return (
          <div className="min-h-screen bg-gray-50 p-6">
            <div className="max-w-7xl mx-auto">
              <div className="text-center py-12">
                <CogIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">系统设置</h3>
                <p className="mt-1 text-sm text-gray-500">此功能正在开发中...</p>
              </div>
            </div>
          </div>
        );
      default:
        return <DashboardV2 user={user} onLogout={onLogout} />;
    }
  };

  return (
    <div className="h-screen flex overflow-hidden bg-gray-100">
      {/* 移动端侧边栏遮罩 */}
      {sidebarOpen && (
        <div className="fixed inset-0 flex z-40 md:hidden">
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
          <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white">
            <div className="absolute top-0 right-0 -mr-12 pt-2">
              <button
                className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                onClick={() => setSidebarOpen(false)}
              >
                <XMarkIcon className="h-6 w-6 text-white" />
              </button>
            </div>
            <SidebarContent 
              navigation={navigation} 
              currentView={currentView} 
              setCurrentView={setCurrentView}
              user={user}
              onLogout={onLogout}
            />
          </div>
        </div>
      )}

      {/* 桌面端侧边栏 */}
      <div className="hidden md:flex md:flex-shrink-0">
        <div className="flex flex-col w-64">
          <SidebarContent 
            navigation={navigation} 
            currentView={currentView} 
            setCurrentView={setCurrentView}
            user={user}
            onLogout={onLogout}
          />
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        {/* 顶部导航栏 */}
        <div className="relative z-10 flex-shrink-0 flex h-16 bg-white shadow">
          <button
            className="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 md:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Bars3Icon className="h-6 w-6" />
          </button>
          <div className="flex-1 px-4 flex justify-between">
            <div className="flex-1 flex">
              <div className="w-full flex md:ml-0">
                <div className="relative w-full text-gray-400 focus-within:text-gray-600">
                  <div className="flex items-center h-16">
                    <h1 className="text-xl font-semibold text-gray-900">
                      {navigation.find(nav => nav.view === currentView)?.name || '数据库监控平台'}
                    </h1>
                  </div>
                </div>
              </div>
            </div>
            <div className="ml-4 flex items-center md:ml-6">
              <div className="flex items-center space-x-4">
                {/* 告警通知铃铛 */}
                <AlertNotificationBell
                  onViewAllAlerts={() => setCurrentView('alerts')}
                />
                <span className="text-sm text-gray-700">欢迎，{user.name}</span>
                <button
                  onClick={onLogout}
                  className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm transition-colors"
                >
                  退出登录
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 主内容 */}
        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          {renderCurrentView()}
        </main>
      </div>
    </div>
  );
};

// 侧边栏内容组件
interface SidebarContentProps {
  navigation: Array<{
    name: string;
    icon: React.ComponentType<any>;
    view: ViewType;
    description: string;
  }>;
  currentView: ViewType;
  setCurrentView: (view: ViewType) => void;
  user: { name: string; email: string };
  onLogout: () => void;
}

const SidebarContent: React.FC<SidebarContentProps> = ({ 
  navigation, 
  currentView, 
  setCurrentView, 
  user, 
  onLogout: _onLogout
}) => {
  return (
    <div className="flex flex-col h-0 flex-1 border-r border-gray-200 bg-white">
      {/* Logo区域 */}
      <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
        <div className="flex items-center flex-shrink-0 px-4">
          <div className="flex items-center">
            <ServerIcon className="h-8 w-8 text-indigo-600" />
            <div className="ml-3">
              <h1 className="text-lg font-semibold text-gray-900">DB Monitor</h1>
              <p className="text-xs text-gray-500">数据库监控平台</p>
            </div>
          </div>
        </div>
        
        {/* 导航菜单 */}
        <nav className="mt-8 flex-1 px-2 space-y-1">
          {navigation.map((item) => {
            const isActive = currentView === item.view;
            return (
              <button
                key={item.name}
                onClick={() => setCurrentView(item.view)}
                className={`${
                  isActive
                    ? 'bg-indigo-100 text-indigo-900 border-r-2 border-indigo-500'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                } group flex items-center px-2 py-3 text-sm font-medium rounded-md w-full text-left transition-colors`}
              >
                <item.icon
                  className={`${
                    isActive ? 'text-indigo-500' : 'text-gray-400 group-hover:text-gray-500'
                  } mr-3 flex-shrink-0 h-6 w-6`}
                />
                <div className="flex-1">
                  <div className="font-medium">{item.name}</div>
                  <div className="text-xs text-gray-500 mt-0.5">{item.description}</div>
                </div>
              </button>
            );
          })}
        </nav>
      </div>
      
      {/* 用户信息区域 */}
      <div className="flex-shrink-0 flex border-t border-gray-200 p-4">
        <div className="flex items-center w-full">
          <div className="flex-shrink-0">
            <UserIcon className="h-8 w-8 text-gray-400" />
          </div>
          <div className="ml-3 flex-1">
            <p className="text-sm font-medium text-gray-700">{user.name}</p>
            <p className="text-xs text-gray-500">{user.email}</p>
          </div>
        </div>
      </div>
    </div>
  );
};
