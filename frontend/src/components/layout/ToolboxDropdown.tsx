import React, { useState } from 'react';
import {
  ChevronDownIcon,
  WrenchScrewdriverIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  DocumentChartBarIcon,
  Cog6ToothIcon,
  CircleStackIcon
} from '@heroicons/react/24/outline';

interface ToolboxDropdownProps {
  onActionSelect: (action: string) => void;
  className?: string;
}

/**
 * 平板端工具箱下拉菜单组件
 * 将功能按钮组织为分级菜单
 */
export const ToolboxDropdown: React.FC<ToolboxDropdownProps> = ({
  onActionSelect,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // 功能分级配置
  const toolboxItems = {
    primary: [
      {
        id: 'database',
        label: '数据库管理',
        icon: CircleStackIcon,
        description: '管理数据库实例'
      },
      {
        id: 'alert',
        label: '告警管理',
        icon: ExclamationTriangleIcon,
        description: '查看和管理告警'
      },
      {
        id: 'performance',
        label: '性能分析',
        icon: ChartBarIcon,
        description: '性能监控和分析'
      }
    ],
    secondary: [
      {
        id: 'query-optimizer',
        label: '查询优化',
        icon: MagnifyingGlassIcon,
        description: 'SQL查询优化工具'
      },
      {
        id: 'maintenance',
        label: '维护工具',
        icon: WrenchScrewdriverIcon,
        description: '数据库维护工具集'
      },
      {
        id: 'reports',
        label: '报表系统',
        icon: DocumentChartBarIcon,
        description: '报表生成和管理'
      }
    ],
    tertiary: [
      {
        id: 'settings',
        label: '系统设置',
        icon: Cog6ToothIcon,
        description: '系统配置和设置'
      }
    ]
  };

  const handleItemClick = (itemId: string) => {
    onActionSelect(itemId);
    setIsOpen(false);
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className={`relative ${className}`}>
      {/* 下拉按钮 */}
      <button
        onClick={toggleDropdown}
        className="flex items-center space-x-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
      >
        <WrenchScrewdriverIcon className="h-5 w-5 text-gray-600" />
        <span className="text-sm font-medium text-gray-700">工具箱</span>
        <ChevronDownIcon className={`h-4 w-4 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
          {/* 一级功能 */}
          <div className="px-3 py-2">
            <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
              核心功能
            </div>
            <div className="space-y-1">
              {toolboxItems.primary.map((item) => {
                const Icon = item.icon;
                return (
                  <button
                    key={item.id}
                    onClick={() => handleItemClick(item.id)}
                    className="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    <Icon className="h-4 w-4 mr-3 text-gray-500" />
                    <div className="flex-1 text-left">
                      <div className="font-medium">{item.label}</div>
                      <div className="text-xs text-gray-500">{item.description}</div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          <hr className="my-2" />

          {/* 二级功能 */}
          <div className="px-3 py-2">
            <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
              专业工具
            </div>
            <div className="space-y-1">
              {toolboxItems.secondary.map((item) => {
                const Icon = item.icon;
                return (
                  <button
                    key={item.id}
                    onClick={() => handleItemClick(item.id)}
                    className="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    <Icon className="h-4 w-4 mr-3 text-gray-500" />
                    <div className="flex-1 text-left">
                      <div className="font-medium">{item.label}</div>
                      <div className="text-xs text-gray-500">{item.description}</div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          <hr className="my-2" />

          {/* 三级功能 */}
          <div className="px-3 py-2">
            <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
              系统管理
            </div>
            <div className="space-y-1">
              {toolboxItems.tertiary.map((item) => {
                const Icon = item.icon;
                return (
                  <button
                    key={item.id}
                    onClick={() => handleItemClick(item.id)}
                    className="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    <Icon className="h-4 w-4 mr-3 text-gray-500" />
                    <div className="flex-1 text-left">
                      <div className="font-medium">{item.label}</div>
                      <div className="text-xs text-gray-500">{item.description}</div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          {/* 快速操作 */}
          <hr className="my-2" />
          <div className="px-3 py-2">
            <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
              快速操作
            </div>
            <div className="grid grid-cols-3 gap-2">
              <button
                onClick={() => handleItemClick('new-alert')}
                className="flex flex-col items-center p-2 text-xs text-gray-600 hover:bg-orange-50 hover:text-orange-700 rounded-lg transition-colors"
              >
                <ExclamationTriangleIcon className="h-4 w-4 mb-1" />
                新建告警
              </button>
              <button
                onClick={() => handleItemClick('health-check')}
                className="flex flex-col items-center p-2 text-xs text-gray-600 hover:bg-green-50 hover:text-green-700 rounded-lg transition-colors"
              >
                <ChartBarIcon className="h-4 w-4 mb-1" />
                健康检查
              </button>
              <button
                onClick={() => handleItemClick('backup-now')}
                className="flex flex-col items-center p-2 text-xs text-gray-600 hover:bg-blue-50 hover:text-blue-700 rounded-lg transition-colors"
              >
                <CircleStackIcon className="h-4 w-4 mb-1" />
                立即备份
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 点击外部关闭菜单 */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};
