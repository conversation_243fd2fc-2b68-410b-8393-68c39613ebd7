import React, { useState } from 'react';
import {
  MagnifyingGlassIcon,
  UserCircleIcon,
  ChevronDownIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  CircleStackIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { AlertNotificationBell } from '../alerts/AlertNotificationBell';
import { useResponsive, useSidebarState } from '../../hooks/useResponsive';

interface ResponsiveNavigationBarProps {
  currentView: string;
  onViewChange: (view: string) => void;
  currentUser?: {
    name: string;
    email: string;
    avatar?: string;
  };
  className?: string;
}

/**
 * 响应式导航栏组件
 * 桌面端: 简化版顶部导航 (主要功能在侧边栏)
 * 平板/移动端: 完整功能导航栏
 */
export const ResponsiveNavigationBar: React.FC<ResponsiveNavigationBarProps> = ({
  currentView,
  onViewChange,
  currentUser = { name: 'Admin', email: '<EMAIL>' },
  className = ''
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showUserMenu, setShowUserMenu] = useState(false);
  const { isDesktop, isMobileOrTablet } = useResponsive();
  const { toggleMobileMenu, isMobileMenuOpen } = useSidebarState();

  // 获取当前页面标题
  const getPageTitle = () => {
    switch (currentView) {
      case 'dashboard': return '概览';
      case 'database': return '数据库管理';
      case 'performance': return '性能分析';
      case 'alert': return '告警管理';
      case 'query-optimizer': return '查询优化';
      case 'maintenance': return '维护工具';
      case 'reports': return '报表系统';
      case 'settings': return '系统设置';
      default: return '数据库监控平台';
    }
  };

  // 桌面端导航栏 (简化版)
  if (isDesktop) {
    return (
      <div className={`
        h-16 bg-white border-b border-gray-200 px-6
        flex items-center justify-between
        ${className}
      `}>
        {/* 左侧: 页面标题和面包屑 */}
        <div className="flex items-center space-x-4">
          <div>
            <h1 className="text-xl font-semibold text-gray-900">
              {getPageTitle()}
            </h1>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <span>数据库监控平台</span>
              <span>/</span>
              <span>{getPageTitle()}</span>
            </div>
          </div>
        </div>

        {/* 右侧: 搜索、告警、用户菜单 */}
        <div className="flex items-center space-x-4">
          {/* 搜索框 */}
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="搜索..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 w-64 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* 告警铃铛 */}
          <AlertNotificationBell onViewAllAlerts={() => onViewChange('alert')} />

          {/* 用户菜单 */}
          <div className="relative">
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <UserCircleIcon className="h-8 w-8 text-gray-400" />
              <div className="text-left">
                <div className="text-sm font-medium text-gray-900">{currentUser.name}</div>
                <div className="text-xs text-gray-500">{currentUser.email}</div>
              </div>
              <ChevronDownIcon className="h-4 w-4 text-gray-400" />
            </button>

            {/* 用户下拉菜单 */}
            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                <button
                  onClick={() => {
                    onViewChange('settings');
                    setShowUserMenu(false);
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  <Cog6ToothIcon className="h-4 w-4 mr-3" />
                  系统设置
                </button>
                <hr className="my-1" />
                <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                  <ArrowRightOnRectangleIcon className="h-4 w-4 mr-3" />
                  退出登录
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // 平板/移动端导航栏 (完整版)
  return (
    <div className={`
      bg-white border-b border-gray-200
      ${className}
    `}>
      {/* 主导航栏 */}
      <div className="px-4 h-16 flex items-center justify-between">
        {/* 左侧: Logo和菜单按钮 */}
        <div className="flex items-center space-x-3">
          <button
            onClick={toggleMobileMenu}
            className="p-2 rounded-lg hover:bg-gray-50 transition-colors"
          >
            {isMobileMenuOpen ? (
              <XMarkIcon className="h-6 w-6 text-gray-600" />
            ) : (
              <Bars3Icon className="h-6 w-6 text-gray-600" />
            )}
          </button>
          
          <div className="flex items-center space-x-2">
            <CircleStackIcon className="h-8 w-8 text-blue-600" />
            <span className="text-lg font-semibold text-gray-900">
              DB Monitor
            </span>
          </div>
        </div>

        {/* 右侧: 告警和用户菜单 */}
        <div className="flex items-center space-x-3">
          <AlertNotificationBell onViewAllAlerts={() => onViewChange('alert')} />
          
          <button
            onClick={() => setShowUserMenu(!showUserMenu)}
            className="p-2 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <UserCircleIcon className="h-8 w-8 text-gray-400" />
          </button>
        </div>
      </div>

      {/* 搜索栏 (移动端) */}
      {isMobileOrTablet && (
        <div className="px-4 pb-4">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="搜索..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
      )}

      {/* 用户下拉菜单 */}
      {showUserMenu && (
        <div className="absolute right-4 top-16 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
          <div className="px-4 py-2 border-b border-gray-200">
            <div className="text-sm font-medium text-gray-900">{currentUser.name}</div>
            <div className="text-xs text-gray-500">{currentUser.email}</div>
          </div>
          <button
            onClick={() => {
              onViewChange('settings');
              setShowUserMenu(false);
            }}
            className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
          >
            <Cog6ToothIcon className="h-4 w-4 mr-3" />
            系统设置
          </button>
          <hr className="my-1" />
          <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
            <ArrowRightOnRectangleIcon className="h-4 w-4 mr-3" />
            退出登录
          </button>
        </div>
      )}
    </div>
  );
};
