import { QueryClient, QueryCache, MutationCache, type DefaultOptions } from '@tanstack/react-query';
import { handleApiError } from '../services/errorHandler';

// React Query 默认配置
const defaultOptions: DefaultOptions = {
  queries: {
    // 数据保持新鲜的时间（5分钟）
    staleTime: 5 * 60 * 1000,
    
    // 缓存时间（10分钟）
    gcTime: 10 * 60 * 1000,
    
    // 重试配置
    retry: (failureCount, error: any) => {
      // 对于认证错误，不重试
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        return false;
      }
      
      // 对于客户端错误（4xx），不重试
      if (error?.response?.status >= 400 && error?.response?.status < 500) {
        return false;
      }
      
      // 最多重试2次
      return failureCount < 2;
    },
    
    // 重试延迟（指数退避）
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    
    // 窗口聚焦时不自动重新获取
    refetchOnWindowFocus: false,
    
    // 网络重连时重新获取
    refetchOnReconnect: true,
    
    // 组件挂载时不自动重新获取（除非数据过期）
    refetchOnMount: true,
  },
  
  mutations: {
    // 变更重试配置
    retry: (failureCount, error: any) => {
      // 对于客户端错误，不重试
      if (error?.response?.status >= 400 && error?.response?.status < 500) {
        return false;
      }
      
      // 最多重试1次
      return failureCount < 1;
    },
    
    // 变更重试延迟
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
  },
};

// 创建 QueryClient 实例
export const queryClient = new QueryClient({
  defaultOptions,
  
  // 全局错误处理
  mutationCache: new MutationCache({
    onError: (error: any) => {
      handleApiError(error, { showNotification: true });
    },
  }),

  queryCache: new QueryCache({
    onError: (error: any, query: any) => {
      // 对于后台查询，不显示错误通知
      const showError = !query.meta?.background;
      handleApiError(error, { showNotification: showError });
    },
  }),
});

// 查询键工厂函数
export const createQueryKey = (base: string[], ...params: any[]) => {
  return [...base, ...params.filter(p => p !== undefined && p !== null)];
};

// 预定义的查询选项
export const queryOptions = {
  // 实时数据查询选项
  realtime: {
    refetchInterval: 5000,
    staleTime: 0,
    gcTime: 1000 * 60, // 1分钟
  },
  
  // 静态数据查询选项
  static: {
    staleTime: 1000 * 60 * 30, // 30分钟
    gcTime: 1000 * 60 * 60, // 1小时
  },
  
  // 用户数据查询选项
  user: {
    staleTime: 1000 * 60 * 10, // 10分钟
    gcTime: 1000 * 60 * 30, // 30分钟
  },
  
  // 后台查询选项（不显示错误）
  background: {
    meta: { background: true },
    retry: false,
    refetchOnWindowFocus: false,
  },
} as const;

// 查询无效化辅助函数
export const invalidateQueries = {
  // 无效化所有数据库相关查询
  databases: () => {
    queryClient.invalidateQueries({ queryKey: ['databases'] });
  },
  
  // 无效化特定数据库的查询
  database: (id: number) => {
    queryClient.invalidateQueries({ queryKey: ['databases', 'detail', id] });
    queryClient.invalidateQueries({ queryKey: ['metrics', 'list', id] });
    queryClient.invalidateQueries({ queryKey: ['metrics', 'latest', id] });
  },
  
  // 无效化所有指标相关查询
  metrics: () => {
    queryClient.invalidateQueries({ queryKey: ['metrics'] });
  },
  
  // 无效化特定数据库的指标查询
  databaseMetrics: (dbId: number) => {
    queryClient.invalidateQueries({ queryKey: ['metrics', 'list', dbId] });
    queryClient.invalidateQueries({ queryKey: ['metrics', 'latest', dbId] });
    queryClient.invalidateQueries({ queryKey: ['metrics', 'history', dbId] });
  },
  
  // 无效化所有告警相关查询
  alerts: () => {
    queryClient.invalidateQueries({ queryKey: ['alerts'] });
  },
  
  // 无效化用户相关查询
  auth: () => {
    queryClient.invalidateQueries({ queryKey: ['auth'] });
  },
  
  // 清除所有缓存
  all: () => {
    queryClient.clear();
  },
};

// 预取数据辅助函数
export const prefetchQueries = {
  // 预取数据库列表
  databases: async (params?: any) => {
    await queryClient.prefetchQuery({
      queryKey: ['databases', 'list', params],
      queryFn: () => import('../services/api').then(({ apiService }) => apiService.getDatabases(params)),
      staleTime: queryOptions.static.staleTime,
    });
  },
  
  // 预取数据库统计
  databaseStats: async () => {
    await queryClient.prefetchQuery({
      queryKey: ['databases', 'stats'],
      queryFn: () => import('../services/api').then(({ apiService }) => apiService.getDatabaseStats()),
      staleTime: queryOptions.static.staleTime,
    });
  },
  
  // 预取告警统计
  alertStats: async () => {
    await queryClient.prefetchQuery({
      queryKey: ['alerts', 'stats'],
      queryFn: () => import('../services/api').then(({ apiService }) => apiService.getAlertStats()),
      staleTime: queryOptions.static.staleTime,
    });
  },
};

// 乐观更新辅助函数
export const optimisticUpdates = {
  // 乐观更新数据库状态
  updateDatabaseStatus: (id: number, status: string) => {
    queryClient.setQueryData(['databases', 'detail', id], (old: any) => {
      if (!old) return old;
      return {
        ...old,
        data: {
          ...old.data,
          status,
        },
      };
    });
  },
  
  // 乐观更新告警事件状态
  updateAlertEventStatus: (id: number, status: string) => {
    queryClient.setQueryData(['alerts', 'events', 'detail', id], (old: any) => {
      if (!old) return old;
      return {
        ...old,
        data: {
          ...old.data,
          status,
          resolved_at: status === 'resolved' ? new Date().toISOString() : null,
        },
      };
    });
  },
};

export default queryClient;
