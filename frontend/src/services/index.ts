// 统一的服务入口文件

// API相关
export { apiService, tokenManager, healthCheck } from './api';
export type {
  ApiResponse,
  PaginatedResponse,
  User,
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  DatabaseInstance,
  CreateDatabaseRequest,
  Metric,
  AlertRule,
  AlertEvent,
} from './api';

// 认证服务
export { authService } from './auth';

// 错误处理
export { errorHandler, handleApiError, formatErrorMessage } from './errorHandler';
export type { ApiError, ErrorHandlerOptions } from './errorHandler';

// 便捷的API调用函数
import { apiService } from './api';
import { handleApiError } from './errorHandler';

// 包装API调用，自动处理错误
export const safeApiCall = async <T>(
  apiCall: () => Promise<T>,
  options?: { showError?: boolean; fallbackMessage?: string }
): Promise<T | null> => {
  try {
    return await apiCall();
  } catch (error) {
    const { showError = false, fallbackMessage } = options || {}; // 默认不显示错误弹窗
    handleApiError(error, {
      showNotification: showError,
      fallbackMessage,
    });
    return null;
  }
};

// 常用的API调用封装
export const api = {
  // 认证相关
  auth: {
    login: (data: any) => safeApiCall(() => apiService.login(data)),
    register: (data: any) => safeApiCall(() => apiService.register(data)),
    logout: () => safeApiCall(() => apiService.logout()),
    getProfile: () => safeApiCall(() => apiService.getProfile()),
    updateProfile: (data: any) => safeApiCall(() => apiService.updateProfile(data)),
  },

  // 数据库管理
  databases: {
    list: (params?: any) => safeApiCall(() => apiService.getDatabases(params)),
    get: (id: number) => safeApiCall(() => apiService.getDatabase(id)),
    create: (data: any) => safeApiCall(() => apiService.createDatabase(data)),
    update: (id: number, data: any) => safeApiCall(() => apiService.updateDatabase(id, data)),
    delete: (id: number) => safeApiCall(() => apiService.deleteDatabase(id)),
    test: (id: number) => safeApiCall(() => apiService.testConnection(id)),
    search: (params: any) => safeApiCall(() => apiService.searchDatabases(params)),
    stats: () => safeApiCall(() => apiService.getDatabaseStats()),
  },

  // 监控指标
  metrics: {
    list: (dbId: number, params?: any) => safeApiCall(() => apiService.getMetrics(dbId, params)),
    latest: (dbId: number) => safeApiCall(() => apiService.getLatestMetrics(dbId)),
    history: (dbId: number, params: any) => safeApiCall(() => apiService.getMetricHistory(dbId, params)),
    types: (dbId: number) => safeApiCall(() => apiService.getMetricTypes(dbId)),
    stats: (dbId: number, params: any) => safeApiCall(() => apiService.getMetricStats(dbId, params)),
    realtime: () => safeApiCall(() => apiService.getRealtimeMetrics()),
    aggregate: (data: any) => safeApiCall(() => apiService.getAggregatedMetrics(data)),
    create: (data: any) => safeApiCall(() => apiService.createMetric(data)),
    collect: () => safeApiCall(() => apiService.collectMetrics()),
  },

  // 告警管理
  alerts: {
    rules: {
      list: (params?: any) => safeApiCall(() => apiService.getAlertRules(params)),
      get: (id: number) => safeApiCall(() => apiService.getAlertRule(id)),
      create: (data: any) => safeApiCall(() => apiService.createAlertRule(data)),
      update: (id: number, data: any) => safeApiCall(() => apiService.updateAlertRule(id, data)),
      delete: (id: number) => safeApiCall(() => apiService.deleteAlertRule(id)),
      search: (params: any) => safeApiCall(() => apiService.getAlertRules(params)),
    },
    events: {
      list: (params?: any) => safeApiCall(() => apiService.getAlertEvents(params)),
      get: (id: number) => safeApiCall(() => apiService.getAlertEvent(id)),
      resolve: (id: number) => safeApiCall(() => apiService.resolveAlert(id)),
      acknowledge: (id: number) => safeApiCall(() => apiService.acknowledgeAlert(id)),
    },
    stats: () => safeApiCall(() => apiService.getAlertStats()),
  },

  // 系统统计
  system: {
    stats: () => safeApiCall(() => apiService.getSystemStats()),
    dashboard: () => safeApiCall(() => apiService.getDashboardData()),
  },

  // 健康检查
  health: () => safeApiCall(() => Promise.resolve({ status: 'ok', message: 'Health check passed', version: '1.0.0' })),
};

// 导出默认API实例
export default api;
