// 前端缓存管理系统
import { useEffect, useState } from 'react';

export interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number; // 生存时间（毫秒）
  key: string;
}

export interface CacheOptions {
  ttl?: number; // 默认TTL
  maxSize?: number; // 最大缓存项数
  enablePersistence?: boolean; // 是否持久化到localStorage
}

export class CacheManager {
  private cache: Map<string, CacheItem<any>> = new Map();
  private options: Required<CacheOptions>;
  private cleanupTimer: number | null = null;

  constructor(options: CacheOptions = {}) {
    this.options = {
      ttl: 5 * 60 * 1000, // 默认5分钟
      maxSize: 100, // 默认最大100项
      enablePersistence: false,
      ...options,
    };

    // 启动清理定时器
    this.startCleanupTimer();

    // 从localStorage恢复缓存
    if (this.options.enablePersistence) {
      this.loadFromStorage();
    }
  }

  // 设置缓存
  set<T>(key: string, data: T, ttl?: number): void {
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.options.ttl,
      key,
    };

    // 检查缓存大小限制
    if (this.cache.size >= this.options.maxSize) {
      this.evictOldest();
    }

    this.cache.set(key, item);

    // 持久化到localStorage
    if (this.options.enablePersistence) {
      this.saveToStorage();
    }
  }

  // 获取缓存
  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    // 检查是否过期
    if (this.isExpired(item)) {
      this.cache.delete(key);
      if (this.options.enablePersistence) {
        this.saveToStorage();
      }
      return null;
    }

    return item.data as T;
  }

  // 检查缓存是否存在且未过期
  has(key: string): boolean {
    const item = this.cache.get(key);
    if (!item) return false;
    
    if (this.isExpired(item)) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  // 删除缓存
  delete(key: string): boolean {
    const result = this.cache.delete(key);
    if (result && this.options.enablePersistence) {
      this.saveToStorage();
    }
    return result;
  }

  // 清除所有缓存
  clear(): void {
    this.cache.clear();
    if (this.options.enablePersistence) {
      this.saveToStorage();
    }
  }

  // 清除过期缓存
  cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, item] of this.cache.entries()) {
      if (this.isExpired(item, now)) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));

    if (expiredKeys.length > 0 && this.options.enablePersistence) {
      this.saveToStorage();
    }
  }

  // 获取缓存统计信息
  getStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    memoryUsage: number;
  } {
    const size = this.cache.size;
    const memoryUsage = this.calculateMemoryUsage();
    
    return {
      size,
      maxSize: this.options.maxSize,
      hitRate: this.calculateHitRate(),
      memoryUsage,
    };
  }

  // 获取所有缓存键
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  // 获取缓存项的剩余TTL
  getTTL(key: string): number {
    const item = this.cache.get(key);
    if (!item) return -1;
    
    const remaining = item.ttl - (Date.now() - item.timestamp);
    return Math.max(0, remaining);
  }

  // 更新缓存TTL
  updateTTL(key: string, ttl: number): boolean {
    const item = this.cache.get(key);
    if (!item) return false;
    
    item.ttl = ttl;
    item.timestamp = Date.now();
    
    if (this.options.enablePersistence) {
      this.saveToStorage();
    }
    
    return true;
  }

  // 私有方法：检查是否过期
  private isExpired(item: CacheItem<any>, now: number = Date.now()): boolean {
    return (now - item.timestamp) > item.ttl;
  }

  // 私有方法：淘汰最旧的缓存项
  private evictOldest(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, item] of this.cache.entries()) {
      if (item.timestamp < oldestTime) {
        oldestTime = item.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  // 私有方法：启动清理定时器
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, 60000); // 每分钟清理一次
  }

  // 私有方法：停止清理定时器
  private stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }

  // 私有方法：计算内存使用量（估算）
  private calculateMemoryUsage(): number {
    let size = 0;
    for (const item of this.cache.values()) {
      size += JSON.stringify(item).length * 2; // 粗略估算
    }
    return size;
  }

  // 私有方法：计算命中率
  private calculateHitRate(): number {
    // 这里需要实际的统计数据，暂时返回0
    return 0;
  }

  // 私有方法：保存到localStorage
  private saveToStorage(): void {
    try {
      const data = Array.from(this.cache.entries());
      localStorage.setItem('cache_manager_data', JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save cache to localStorage:', error);
    }
  }

  // 私有方法：从localStorage加载
  private loadFromStorage(): void {
    try {
      const data = localStorage.getItem('cache_manager_data');
      if (data) {
        const entries = JSON.parse(data);
        this.cache = new Map(entries);
        // 清理过期项
        this.cleanup();
      }
    } catch (error) {
      console.warn('Failed to load cache from localStorage:', error);
    }
  }

  // 销毁缓存管理器
  destroy(): void {
    this.stopCleanupTimer();
    this.clear();
  }
}

// 预定义的缓存实例
export const cacheManager = new CacheManager({
  ttl: 5 * 60 * 1000, // 5分钟
  maxSize: 200,
  enablePersistence: true,
});

// 专用缓存实例
export const realtimeCache = new CacheManager({
  ttl: 30 * 1000, // 30秒
  maxSize: 50,
  enablePersistence: false,
});

export const staticCache = new CacheManager({
  ttl: 30 * 60 * 1000, // 30分钟
  maxSize: 100,
  enablePersistence: true,
});

// 缓存装饰器
export function cached<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  options: {
    keyGenerator?: (...args: Parameters<T>) => string;
    ttl?: number;
    cache?: CacheManager;
  } = {}
): T {
  const {
    keyGenerator = (...args) => JSON.stringify(args),
    ttl,
    cache = cacheManager,
  } = options;

  return (async (...args: Parameters<T>) => {
    const key = keyGenerator(...args);
    
    // 尝试从缓存获取
    const cached = cache.get(key);
    if (cached !== null) {
      return cached;
    }

    // 执行原函数
    const result = await fn(...args);
    
    // 缓存结果
    cache.set(key, result, ttl);
    
    return result;
  }) as T;
}



export function useCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: {
    ttl?: number;
    cache?: CacheManager;
    enabled?: boolean;
  } = {}
) {
  const {
    ttl,
    cache = cacheManager,
    enabled = true,
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = async () => {
    if (!enabled) return;

    // 检查缓存
    const cached = cache.get<T>(key);
    if (cached !== null) {
      setData(cached);
      return;
    }

    // 从网络获取
    setLoading(true);
    setError(null);
    
    try {
      const result = await fetcher();
      cache.set(key, result, ttl);
      setData(result);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [key, enabled]);

  const refresh = () => {
    cache.delete(key);
    fetchData();
  };

  const invalidate = () => {
    cache.delete(key);
    setData(null);
  };

  return {
    data,
    loading,
    error,
    refresh,
    invalidate,
    isCached: cache.has(key),
  };
}
