import axios from 'axios';

// API配置 - 直接连接后端服务器
const API_CONFIG = {
  baseURL: 'http://localhost:8080/api/v1',
  timeout: 5000,
};

// Token管理
export const tokenManager = {
  getToken: (): string | null => localStorage.getItem('access_token'),
  setToken: (token: string): void => {
    localStorage.setItem('access_token', token);
  },
  removeToken: (): void => {
    localStorage.removeItem('access_token');
  },
  isAuthenticated: (): boolean => !!localStorage.getItem('access_token'),
};

// 创建axios实例
const api = axios.create(API_CONFIG);

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加token到请求头
    const token = tokenManager.getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 添加Content-Type
    if (!config.headers['Content-Type']) {
      config.headers['Content-Type'] = 'application/json';
    }

    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url} - ${response.status}`);
    return response;
  },
  async (error) => {
    console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url} - ${error.response?.status || 'Network Error'}`);

    if (error.response?.status === 401) {
      // 处理未授权错误
      tokenManager.removeToken();
      window.location.href = '/login';
    }

    return Promise.reject(error);
  }
);

// ===== 类型定义 =====

export interface User {
  id: number;
  email: string;
  name: string;
  role: string;
  avatar_url?: string;
  is_active: boolean;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

export interface Token {
  access_token: string;
  token_type: string;
  expires_in: number;
  expires_at: string;
}

export interface AuthResponse {
  user: User;
  token: Token;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  name: string;
}

export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

// 数据库相关类型
export interface CreateDatabaseRequest {
  name: string;
  type: string;
  host: string;
  port: number;
  username: string;
  password: string;
  database_name: string;
  description?: string;
}

// 监控指标类型
export interface Metric {
  id: number;
  database_id: number;
  metric_type: string;
  value: number;
  unit: string;
  labels: string;
  timestamp: string;
  created_at: string;
}

// 告警规则类型
export interface AlertRule {
  id: number;
  name: string;
  description: string;
  database_id: number;
  metric_type: string;
  condition: string;
  threshold: number;
  severity: string;
  enabled: boolean;
  created_at: string;
  updated_at: string;
}

// 告警事件类型
export interface AlertEvent {
  id: number;
  rule_id: number;
  database_id: number;
  severity: string;
  status: string;
  message: string;
  value: number;
  threshold: number;
  triggered_at: string;
  resolved_at?: string;
  acknowledged_at?: string;
  action_history?: any[];
}

export interface DatabaseInstance {
  id: number;
  name: string;
  type: string;
  host: string;
  port: number;
  database_name: string;
  username: string;
  description?: string;
  status: string;
  version?: string;
  size?: string;
  connections?: number;
  last_backup?: string;
  backup_enabled: boolean;
  monitoring_enabled: boolean;
  is_monitored: boolean;
  created_by: number;
  created_at: string;
  updated_at: string;
}

export interface PaginatedResponse<T> {
  page: number;
  page_size: number;
  total: number;
  total_pages: number;
  items: T[];
}

// 备份管理相关类型
export interface BackupTask {
  id: number;
  database_id: number;
  database_name?: string;
  database_type?: string;
  task_name: string;
  backup_type: 'full' | 'incremental' | 'differential';
  schedule: string;
  retention_days: number;
  compression: boolean;
  encryption: boolean;
  status: 'active' | 'paused' | 'failed';
  last_backup?: string;
  next_backup?: string;
  backup_size: number;
  backup_size_formatted?: string;
  created_by: number;
  created_by_name?: string;
  created_at: string;
  updated_at: string;
}

export interface BackupHistory {
  id: number;
  task_id: number;
  task_name?: string;
  backup_type?: string;
  status: 'success' | 'failed' | 'running';
  start_time: string;
  end_time?: string;
  backup_size: number;
  backup_size_formatted: string;
  file_path: string;
  error_message: string;
  duration: number;
  duration_formatted: string;
  created_at: string;
  updated_at: string;
}

export interface BackupStats {
  total_tasks: number;
  active_tasks: number;
  total_backups: number;
  successful_backups: number;
  failed_backups: number;
  success_rate: number;
  total_size: number;
  total_size_formatted: string;
  last_backup_time?: string;
  next_backup_time?: string;
}

export interface BackupTaskStats {
  total_tasks: number;
  active_tasks: number;
  paused_tasks: number;
  failed_tasks: number;
  total_size: number;
}

export interface CreateBackupTaskRequest {
  database_id: number;
  task_name: string;
  backup_type: 'full' | 'incremental' | 'differential';
  schedule?: string;
  retention_days: number;
  compression?: boolean;
  encryption?: boolean;
}

export interface UpdateBackupTaskRequest {
  task_name?: string;
  backup_type?: 'full' | 'incremental' | 'differential';
  schedule?: string;
  retention_days?: number;
  compression?: boolean;
  encryption?: boolean;
  status?: 'active' | 'paused' | 'failed';
}

export interface BackupExecuteResponse {
  history_id: number;
  task_id: number;
  status: string;
  message: string;
  start_time: string;
}

// ===== 报表相关类型 =====

export interface ReportTemplate {
  id: number;
  name: string;
  description: string;
  type: 'performance' | 'usage' | 'alert';
  config: string;
  is_active: boolean;
  created_by: number;
  created_at: string;
  updated_at: string;
  creator?: {
    id: number;
    name: string;
    email: string;
  };
}

export interface CreateReportTemplateRequest {
  name: string;
  description: string;
  type: 'performance' | 'usage' | 'alert';
  config: Record<string, any>;
}

export interface UpdateReportTemplateRequest {
  name?: string;
  description?: string;
  type?: 'performance' | 'usage' | 'alert';
  config?: Record<string, any>;
  is_active?: boolean;
}

export interface ReportExecution {
  id: number;
  template_id: number;
  executed_by: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  parameters: string;
  file_path?: string;
  file_size?: number;
  error_message?: string;
  start_time: string;
  end_time?: string;
  duration?: number;
  created_at: string;
  template?: ReportTemplate;
  executor?: {
    id: number;
    name: string;
    email: string;
  };
  executed_by_user?: {
    id: number;
    name: string;
    email: string;
  };
}

export interface ExecuteReportRequest {
  template_id: number;
  format: 'pdf' | 'excel' | 'csv' | 'json';
  time_range: {
    start_time: string;
    end_time: string;
  };
  parameters: Record<string, any>;
  database_ids?: number[];
}

// ===== 系统设置相关类型 =====

export interface SystemSetting {
  id: number;
  category: string;
  key: string;
  value: string;
  value_type: 'string' | 'number' | 'boolean' | 'json';
  description: string;
  is_public: boolean;
  updated_by: number;
  updated_at: string;
  updater?: {
    id: number;
    name: string;
    email: string;
  };
}

export interface UserPreference {
  id: number;
  user_id: number;
  category: string;
  key: string;
  value: string;
  value_type: 'string' | 'number' | 'boolean' | 'json';
  created_at: string;
  updated_at: string;
}

export interface SystemSettingsResponse {
  categories: Record<string, Record<string, {
    value: any;
    type: string;
    description: string;
    is_public: boolean;
  }>>;
}

export interface UserPreferencesResponse {
  categories: Record<string, Record<string, {
    value: any;
    type: string;
  }>>;
}

export interface UpdateSystemSettingsRequest {
  settings: Array<{
    category: string;
    key: string;
    value: any;
    value_type?: string;
    description?: string;
    is_public?: boolean;
  }>;
}

export interface UpdateUserPreferencesRequest {
  preferences: Array<{
    category: string;
    key: string;
    value: any;
    value_type?: string;
  }>;
}

export interface SettingCategoriesResponse {
  system_categories: Record<string, {
    name: string;
    description: string;
    icon: string;
    order: number;
  }>;
  preference_categories: Record<string, {
    name: string;
    description: string;
    icon: string;
    order: number;
  }>;
}

export interface ValidationRulesResponse {
  [key: string]: {
    type: string;
    required: boolean;
    min?: number;
    max?: number;
    options?: string[];
    pattern?: string;
    description: string;
  };
}

// ===== API服务类 =====

class ApiService {
  // ===== 认证相关 =====

  async register(data: RegisterRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await api.post('/auth/register', data);
    return response.data;
  }

  async login(data: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await api.post('/auth/login', data);
    return response.data;
  }

  async logout(): Promise<ApiResponse<null>> {
    const response = await api.post('/auth/logout');
    return response.data;
  }

  async refreshToken(): Promise<ApiResponse<AuthResponse>> {
    const response = await api.post('/auth/refresh');
    return response.data;
  }

  async getProfile(): Promise<ApiResponse<User>> {
    const response = await api.get('/auth/profile');
    return response.data;
  }

  async updateProfile(data: Partial<User>): Promise<ApiResponse<User>> {
    const response = await api.put('/auth/profile', data);
    return response.data;
  }

  async changePassword(data: { old_password: string; new_password: string }): Promise<ApiResponse<null>> {
    const response = await api.put('/auth/password', data);
    return response.data;
  }

  // ===== 数据库管理 =====

  async getDatabases(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    type?: string;
    status?: string;
  }): Promise<ApiResponse<PaginatedResponse<DatabaseInstance>>> {
    const response = await api.get('/databases', { params });
    return response.data;
  }

  async getDatabase(id: number): Promise<ApiResponse<DatabaseInstance>> {
    const response = await api.get(`/databases/${id}`);
    return response.data;
  }

  async createDatabase(data: any): Promise<ApiResponse<DatabaseInstance>> {
    const response = await api.post('/databases', data);
    return response.data;
  }

  async updateDatabase(id: number, data: any): Promise<ApiResponse<DatabaseInstance>> {
    const response = await api.put(`/databases/${id}`, data);
    return response.data;
  }

  async deleteDatabase(id: number): Promise<ApiResponse<null>> {
    const response = await api.delete(`/databases/${id}`);
    return response.data;
  }

  async testConnection(id: number): Promise<ApiResponse<{ status: string; message: string }>> {
    const response = await api.post(`/databases/${id}/test`);
    return response.data;
  }

  // ===== 监控指标 =====

  async getMetrics(dbId: number, params?: any): Promise<ApiResponse<any[]>> {
    const response = await api.get(`/metrics/${dbId}`, { params });
    return response.data;
  }

  async getLatestMetrics(dbId: number): Promise<ApiResponse<any[]>> {
    const response = await api.get(`/metrics/${dbId}/latest`);
    return response.data;
  }

  async getHistoryMetrics(dbId: number, params: any): Promise<ApiResponse<any[]>> {
    const response = await api.get(`/metrics/${dbId}/history`, { params });
    return response.data;
  }

  // ===== 告警管理 =====

  async getAlertRules(params?: any): Promise<ApiResponse<PaginatedResponse<any>>> {
    const response = await api.get('/alerts/rules', { params });
    return response.data;
  }

  async getAlertRule(id: number): Promise<ApiResponse<any>> {
    const response = await api.get(`/alerts/rules/${id}`);
    return response.data;
  }

  async createAlertRule(data: any): Promise<ApiResponse<any>> {
    const response = await api.post('/alerts/rules', data);
    return response.data;
  }

  async updateAlertRule(id: number, data: any): Promise<ApiResponse<any>> {
    const response = await api.put(`/alerts/rules/${id}`, data);
    return response.data;
  }

  async deleteAlertRule(id: number): Promise<ApiResponse<null>> {
    const response = await api.delete(`/alerts/rules/${id}`);
    return response.data;
  }

  async getAlertEvents(params?: any): Promise<ApiResponse<PaginatedResponse<any>>> {
    const response = await api.get('/alerts/events', { params });
    return response.data;
  }

  async getAlertEvent(id: number): Promise<ApiResponse<any>> {
    const response = await api.get(`/alerts/events/${id}`);
    return response.data;
  }

  async acknowledgeAlert(id: number): Promise<ApiResponse<null>> {
    const response = await api.post(`/alerts/events/${id}/acknowledge`);
    return response.data;
  }

  async resolveAlert(id: number): Promise<ApiResponse<null>> {
    const response = await api.post(`/alerts/events/${id}/resolve`);
    return response.data;
  }

  async getAlertStats(): Promise<ApiResponse<any>> {
    const response = await api.get('/alerts/stats');
    return response.data;
  }

  // ===== 系统统计 =====

  async getSystemStats(): Promise<ApiResponse<any>> {
    const response = await api.get('/stats');
    return response.data;
  }

  async getDashboardData(): Promise<ApiResponse<any>> {
    const response = await api.get('/dashboard');
    return response.data;
  }

  // ===== 缺失的方法 =====

  async getDatabaseStats(): Promise<ApiResponse<any>> {
    const response = await api.get('/databases/stats');
    return response.data;
  }

  async searchDatabases(params: any): Promise<ApiResponse<PaginatedResponse<any>>> {
    const response = await api.get('/databases/search', { params });
    return response.data;
  }

  async getMetricHistory(dbId: number, params: any): Promise<ApiResponse<any[]>> {
    const response = await api.get(`/metrics/${dbId}/history`, { params });
    return response.data;
  }

  async getMetricTypes(dbId: number): Promise<ApiResponse<any[]>> {
    const response = await api.get(`/metrics/${dbId}/types`);
    return response.data;
  }

  async getMetricStats(dbId: number, params: any): Promise<ApiResponse<any>> {
    const response = await api.get(`/metrics/${dbId}/stats`, { params });
    return response.data;
  }

  async getRealtimeMetrics(): Promise<ApiResponse<any[]>> {
    const response = await api.get('/metrics/realtime');
    return response.data;
  }

  async getAggregatedMetrics(data: any): Promise<ApiResponse<any[]>> {
    const response = await api.post('/metrics/aggregate', data);
    return response.data;
  }

  async createMetric(data: any): Promise<ApiResponse<any>> {
    const response = await api.post('/metrics', data);
    return response.data;
  }

  async collectMetrics(): Promise<ApiResponse<any>> {
    const response = await api.post('/metrics/collect');
    return response.data;
  }

  // ===== 查询优化工具相关 =====

  async analyzeQuery(data: any): Promise<ApiResponse<any>> {
    const response = await api.post('/query-optimizer/analyze', data);
    return response.data;
  }

  async getAnalysis(id: number): Promise<ApiResponse<any>> {
    const response = await api.get(`/query-optimizer/analyze/${id}`);
    return response.data;
  }

  async getAnalysisHistory(params?: any): Promise<ApiResponse<any>> {
    const response = await api.get('/query-optimizer/analyze', { params });
    return response.data;
  }

  async explainQuery(data: any): Promise<ApiResponse<any>> {
    const response = await api.post('/query-optimizer/explain', data);
    return response.data;
  }

  async getExecutionPlan(id: number): Promise<ApiResponse<any>> {
    const response = await api.get(`/query-optimizer/explain/${id}`);
    return response.data;
  }

  async getOptimizationSuggestions(analysisId: number): Promise<ApiResponse<any>> {
    const response = await api.get(`/query-optimizer/suggestions/${analysisId}`);
    return response.data;
  }

  async getIndexSuggestions(data: any): Promise<ApiResponse<any>> {
    const response = await api.post('/query-optimizer/index-suggestions', data);
    return response.data;
  }

  async getIndexRecommendations(analysisId: number): Promise<ApiResponse<any>> {
    const response = await api.get(`/query-optimizer/index-suggestions/${analysisId}`);
    return response.data;
  }

  async getSupportedDatabases(): Promise<ApiResponse<any>> {
    const response = await api.get('/query-optimizer/databases');
    return response.data;
  }

  async getQueryOptimizerStats(): Promise<ApiResponse<any>> {
    const response = await api.get('/query-optimizer/stats');
    return response.data;
  }

  // ===== 备份管理 =====

  // 备份任务管理
  async getBackupTasks(params?: {
    page?: number;
    page_size?: number;
    database_id?: number;
    status?: string;
    backup_type?: string;
    search?: string;
  }): Promise<ApiResponse<PaginatedResponse<BackupTask>>> {
    const response = await api.get('/maintenance/backup/tasks', { params });
    return response.data;
  }

  async getBackupTask(id: number): Promise<ApiResponse<BackupTask>> {
    const response = await api.get(`/maintenance/backup/tasks/${id}`);
    return response.data;
  }

  async createBackupTask(data: CreateBackupTaskRequest): Promise<ApiResponse<BackupTask>> {
    const response = await api.post('/maintenance/backup/tasks', data);
    return response.data;
  }

  async updateBackupTask(id: number, data: UpdateBackupTaskRequest): Promise<ApiResponse<BackupTask>> {
    const response = await api.put(`/maintenance/backup/tasks/${id}`, data);
    return response.data;
  }

  async deleteBackupTask(id: number): Promise<ApiResponse<null>> {
    const response = await api.delete(`/maintenance/backup/tasks/${id}`);
    return response.data;
  }

  // 备份执行
  async executeBackup(id: number, force?: boolean): Promise<ApiResponse<BackupExecuteResponse>> {
    const response = await api.post(`/maintenance/backup/tasks/${id}/execute`, {}, {
      params: { force: force || false }
    });
    return response.data;
  }

  async getRunningBackups(): Promise<ApiResponse<BackupHistory[]>> {
    const response = await api.get('/maintenance/backup/running');
    return response.data;
  }

  async cancelBackup(historyId: number): Promise<ApiResponse<null>> {
    const response = await api.post(`/maintenance/backup/history/${historyId}/cancel`);
    return response.data;
  }

  // 备份历史
  async getBackupHistory(params?: {
    page?: number;
    page_size?: number;
    task_id?: number;
    database_id?: number;
    status?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<ApiResponse<PaginatedResponse<BackupHistory>>> {
    const response = await api.get('/maintenance/backup/history', { params });
    return response.data;
  }

  async getBackupHistoryById(id: number): Promise<ApiResponse<BackupHistory>> {
    const response = await api.get(`/maintenance/backup/history/${id}`);
    return response.data;
  }

  async deleteBackupHistory(id: number): Promise<ApiResponse<null>> {
    const response = await api.delete(`/maintenance/backup/history/${id}`);
    return response.data;
  }

  // 备份统计
  async getBackupStats(params?: {
    database_id?: number;
    start_date?: string;
    end_date?: string;
    period?: string;
  }): Promise<ApiResponse<BackupStats>> {
    const response = await api.get('/maintenance/backup/stats', { params });
    return response.data;
  }

  async getBackupTaskStats(): Promise<ApiResponse<BackupTaskStats>> {
    const response = await api.get('/maintenance/backup/tasks/stats');
    return response.data;
  }

  async getBackupSuccessRate(params?: {
    task_id?: number;
    days?: number;
  }): Promise<ApiResponse<{ success_rate: number; period_days: number }>> {
    const response = await api.get('/maintenance/backup/success-rate', { params });
    return response.data;
  }

  // ===== 报表管理相关 =====

  // 报表模板管理
  async getReportTemplates(params?: {
    page?: number;
    page_size?: number;
    type?: string;
    search?: string;
    is_active?: boolean;
  }): Promise<ApiResponse<PaginatedResponse<ReportTemplate>>> {
    const response = await api.get('/reports/templates', { params });
    return response.data;
  }

  async createReportTemplate(data: CreateReportTemplateRequest): Promise<ApiResponse<ReportTemplate>> {
    const response = await api.post('/reports/templates', data);
    return response.data;
  }

  async getReportTemplate(id: number): Promise<ApiResponse<ReportTemplate>> {
    const response = await api.get(`/reports/templates/${id}`);
    return response.data;
  }

  async updateReportTemplate(id: number, data: UpdateReportTemplateRequest): Promise<ApiResponse<null>> {
    const response = await api.put(`/reports/templates/${id}`, data);
    return response.data;
  }

  async deleteReportTemplate(id: number): Promise<ApiResponse<null>> {
    const response = await api.delete(`/reports/templates/${id}`);
    return response.data;
  }

  // 报表执行管理
  async executeReport(data: ExecuteReportRequest): Promise<ApiResponse<ReportExecution>> {
    const response = await api.post('/reports/execute', data);
    return response.data;
  }

  async getReportExecutions(params?: {
    page?: number;
    page_size?: number;
    template_id?: number;
    status?: string;
    executed_by?: number;
  }): Promise<ApiResponse<PaginatedResponse<ReportExecution>>> {
    const response = await api.get('/reports/executions', { params });
    return response.data;
  }

  async getReportExecution(id: number): Promise<ApiResponse<ReportExecution>> {
    const response = await api.get(`/reports/executions/${id}`);
    return response.data;
  }

  async downloadReport(id: number): Promise<Blob> {
    const response = await api.get(`/reports/executions/${id}/download`, {
      responseType: 'blob'
    });
    return response.data;
  }

  // ===== 系统设置相关 =====

  // 系统设置管理
  async getSystemSettings(params?: {
    category?: string;
    public?: boolean;
    search?: string;
  }): Promise<ApiResponse<SystemSettingsResponse>> {
    const response = await api.get('/settings/system', { params });
    return response.data;
  }

  async updateSystemSettings(data: UpdateSystemSettingsRequest): Promise<ApiResponse<null>> {
    const response = await api.put('/settings/system', data);
    return response.data;
  }

  // 用户偏好管理
  async getUserPreferences(params?: {
    category?: string;
    search?: string;
  }): Promise<ApiResponse<UserPreferencesResponse>> {
    const response = await api.get('/settings/preferences', { params });
    return response.data;
  }

  async updateUserPreferences(data: UpdateUserPreferencesRequest): Promise<ApiResponse<null>> {
    const response = await api.put('/settings/preferences', data);
    return response.data;
  }

  async initializeUserDefaults(): Promise<ApiResponse<null>> {
    const response = await api.post('/settings/preferences/initialize');
    return response.data;
  }

  // 设置元数据
  async getSettingCategories(): Promise<ApiResponse<SettingCategoriesResponse>> {
    const response = await api.get('/settings/categories');
    return response.data;
  }

  async getValidationRules(): Promise<ApiResponse<ValidationRulesResponse>> {
    const response = await api.get('/settings/validation-rules');
    return response.data;
  }
}

// 创建单例实例
export const apiService = new ApiService();

// 健康检查函数
export const healthCheck = async (): Promise<{ status: string; message: string; version: string }> => {
  try {
    const response = await fetch('http://localhost:8080/health');
    if (response.ok) {
      const data = await response.json();
      return data;
    } else {
      throw new Error('Health check failed');
    }
  } catch (error) {
    throw new Error('Backend unavailable');
  }
};

export default api;
