import { apiService, tokenManager } from './api';
import type { LoginRequest, RegisterRequest, AuthResponse, User } from './api';

// 认证状态管理
class AuthService {
  private currentUser: User | null = null;
  private authListeners: Array<(user: User | null) => void> = [];

  constructor() {
    // 初始化时检查本地存储的token
    this.initializeAuth();
  }

  private async initializeAuth() {
    if (tokenManager.isAuthenticated()) {
      try {
        await this.getCurrentUser();
      } catch (error) {
        console.error('Failed to initialize auth:', error);
        // 在演示模式下不要清除认证状态
        if (!this.currentUser) {
          this.logout();
        }
      }
    }
  }

  // 添加认证状态监听器
  addAuthListener(listener: (user: User | null) => void) {
    this.authListeners.push(listener);
    // 立即调用一次，传递当前状态
    listener(this.currentUser);
  }

  // 移除认证状态监听器
  removeAuthListener(listener: (user: User | null) => void) {
    const index = this.authListeners.indexOf(listener);
    if (index > -1) {
      this.authListeners.splice(index, 1);
    }
  }

  // 通知所有监听器
  private notifyAuthListeners() {
    this.authListeners.forEach(listener => listener(this.currentUser));
  }

  // 用户注册
  async register(data: RegisterRequest): Promise<AuthResponse> {
    try {
      const response = await apiService.register(data);
      if (response.code === 200) {
        const authData = response.data;
        this.setAuthData(authData);
        return authData;
      } else {
        throw new Error(response.message || 'Registration failed');
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      throw new Error(error.response?.data?.message || error.message || 'Registration failed');
    }
  }

  // 用户登录
  async login(data: LoginRequest): Promise<AuthResponse> {
    try {
      const response = await apiService.login(data);
      console.log('🔍 Login response:', response);
      if (response.code === 200) {
        const authData = response.data;
        console.log('🔍 Auth data:', authData);
        console.log('🔍 Token:', authData.token);
        this.setAuthData(authData);
        return authData;
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      throw new Error(error.response?.data?.message || error.message || 'Login failed');
    }
  }

  // 用户登出
  async logout(): Promise<void> {
    try {
      if (tokenManager.isAuthenticated()) {
        await apiService.logout();
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.clearAuthData();
    }
  }

  // 刷新Token
  async refreshToken(): Promise<AuthResponse | null> {
    try {
      const response = await apiService.refreshToken();
      if (response.code === 200) {
        const authData = response.data;
        this.setAuthData(authData);
        return authData;
      }
      return null;
    } catch (error) {
      console.error('Token refresh error:', error);
      this.logout();
      return null;
    }
  }

  // 获取当前用户信息
  async getCurrentUser(): Promise<User | null> {
    try {
      // 如果已经有用户信息，直接返回（演示模式）
      if (this.currentUser) {
        return this.currentUser;
      }

      const response = await apiService.getProfile();
      if (response.code === 200) {
        this.currentUser = response.data;
        this.notifyAuthListeners();
        return this.currentUser;
      }
      return null;
    } catch (error) {
      console.error('Get current user error:', error);
      // 在演示模式下不要自动登出
      if (this.currentUser) {
        console.log('🎭 演示模式：保持用户登录状态');
        return this.currentUser;
      }
      this.logout();
      return null;
    }
  }

  // 更新用户信息
  async updateProfile(data: Partial<User>): Promise<User | null> {
    try {
      const response = await apiService.updateProfile(data);
      if (response.code === 200) {
        this.currentUser = response.data;
        this.notifyAuthListeners();
        return this.currentUser;
      }
      return null;
    } catch (error: any) {
      console.error('Update profile error:', error);
      throw new Error(error.response?.data?.message || error.message || 'Update failed');
    }
  }

  // 修改密码
  async changePassword(oldPassword: string, newPassword: string): Promise<void> {
    try {
      const response = await apiService.changePassword({
        old_password: oldPassword,
        new_password: newPassword,
      });
      if (response.code !== 200) {
        throw new Error(response.message || 'Password change failed');
      }
    } catch (error: any) {
      console.error('Change password error:', error);
      throw new Error(error.response?.data?.message || error.message || 'Password change failed');
    }
  }

  // 设置认证数据
  private setAuthData(authData: AuthResponse) {
    console.log('🔍 Setting auth data:', authData);
    console.log('🔍 Token to save:', authData.token.access_token);
    tokenManager.setToken(authData.token.access_token);
    console.log('🔍 Token saved to localStorage:', localStorage.getItem('access_token'));
    this.currentUser = authData.user;
    this.notifyAuthListeners();
  }

  // 清除认证数据
  private clearAuthData() {
    tokenManager.removeToken();
    this.currentUser = null;
    this.notifyAuthListeners();
  }

  // 检查是否已认证
  isAuthenticated(): boolean {
    return tokenManager.isAuthenticated();
  }

  // 获取当前用户（同步）
  getUser(): User | null {
    return this.currentUser;
  }

  // 检查用户权限
  hasRole(role: string): boolean {
    return this.currentUser?.role === role;
  }

  // 检查是否为管理员
  isAdmin(): boolean {
    return this.hasRole('admin');
  }

  // 检查是否为普通用户
  isUser(): boolean {
    return this.hasRole('user');
  }
}

// 创建单例实例
export const authService = new AuthService();

// 导出类型
export type { LoginRequest, RegisterRequest, AuthResponse, User };
