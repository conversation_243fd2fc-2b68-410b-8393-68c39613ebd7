// 演示数据服务
// 当后端不可用时提供模拟数据

export const demoData = {
  // 数据库列表
  databases: [
    {
      id: 1,
      name: "生产数据库",
      type: "mysql",
      host: "prod-mysql-01.example.com",
      port: 3306,
      database_name: "production",
      username: "app_user",
      status: "connected",
      version: "8.0.35",
      size: "2.5 GB",
      connections: 45,
      max_connections: 200,
      uptime: "15天 8小时",
      last_backup: "2025-01-05T02:00:00Z",
      description: "主要生产环境数据库",
      tags: "production,mysql,critical",
      is_monitored: true,
      created_by: 1,
      created_at: "2024-12-01T10:00:00Z",
      updated_at: "2025-01-05T14:30:00Z"
    },
    {
      id: 2,
      name: "开发数据库",
      type: "postgresql",
      host: "dev-postgres-01.example.com",
      port: 5432,
      database_name: "development",
      username: "dev_user",
      status: "connected",
      version: "15.5",
      size: "856 MB",
      connections: 12,
      max_connections: 100,
      uptime: "7天 12小时",
      last_backup: "2025-01-05T01:00:00Z",
      description: "开发环境数据库",
      tags: "development,postgresql",
      is_monitored: true,
      created_by: 1,
      created_at: "2024-12-15T09:00:00Z",
      updated_at: "2025-01-05T14:25:00Z"
    },
    {
      id: 3,
      name: "测试数据库",
      type: "mysql",
      host: "test-mysql-01.example.com",
      port: 3306,
      database_name: "testing",
      username: "test_user",
      status: "warning",
      version: "8.0.33",
      size: "1.2 GB",
      connections: 8,
      max_connections: 50,
      uptime: "3天 4小时",
      last_backup: "2025-01-04T23:00:00Z",
      description: "测试环境数据库",
      tags: "testing,mysql",
      is_monitored: true,
      created_by: 1,
      created_at: "2024-12-20T11:00:00Z",
      updated_at: "2025-01-05T14:20:00Z"
    }
  ],

  // 实时指标
  metrics: {
    realtime: {
      cpu_usage: 65.5,
      memory_usage: 78.2,
      disk_usage: 45.8,
      network_io: 1250,
      active_connections: 65,
      queries_per_second: 145,
      response_time: 45,
      error_rate: 0.02
    },
    history: {
      cpu: Array.from({ length: 24 }, (_, i) => ({
        time: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString(),
        value: Math.random() * 40 + 30 + Math.sin(i / 4) * 20
      })),
      memory: Array.from({ length: 24 }, (_, i) => ({
        time: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString(),
        value: Math.random() * 30 + 50 + Math.cos(i / 3) * 15
      })),
      connections: Array.from({ length: 24 }, (_, i) => ({
        time: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString(),
        value: Math.floor(Math.random() * 50 + 20 + Math.sin(i / 2) * 30)
      })),
      response_time: Array.from({ length: 24 }, (_, i) => ({
        time: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString(),
        value: Math.random() * 30 + 20 + Math.sin(i / 5) * 10
      }))
    }
  },

  // 告警规则
  alertRules: [
    {
      id: 1,
      name: "CPU使用率过高",
      database_id: 1,
      metric_type: "cpu",
      operator: ">",
      threshold: 80,
      duration: 300,
      severity: "critical",
      enabled: true,
      notification_channels: "email,slack",
      description: "当CPU使用率超过80%时触发告警",
      created_by: 1,
      created_at: "2024-12-01T10:00:00Z",
      updated_at: "2025-01-05T14:00:00Z"
    },
    {
      id: 2,
      name: "内存使用率告警",
      database_id: 1,
      metric_type: "memory",
      operator: ">",
      threshold: 85,
      duration: 300,
      severity: "warning",
      enabled: true,
      notification_channels: "email",
      description: "当内存使用率超过85%时触发告警",
      created_by: 1,
      created_at: "2024-12-01T10:00:00Z",
      updated_at: "2025-01-05T14:00:00Z"
    },
    {
      id: 3,
      name: "连接数过多",
      database_id: 2,
      metric_type: "connections",
      operator: ">",
      threshold: 80,
      duration: 180,
      severity: "warning",
      enabled: true,
      notification_channels: "slack",
      description: "当活跃连接数超过80时触发告警",
      created_by: 1,
      created_at: "2024-12-15T09:00:00Z",
      updated_at: "2025-01-05T14:00:00Z"
    }
  ],

  // 告警事件
  alerts: [
    {
      id: 1,
      alert_rule_id: 1,
      rule_id: 1,
      database_id: 1,
      database_name: "生产数据库",
      rule_name: "CPU使用率过高",
      metric_type: "cpu",
      value: 85.2,
      threshold: 80,
      operator: ">",
      severity: "critical",
      status: "active",
      message: "CPU使用率达到85.2%，超过阈值80%",
      start_time: "2025-01-05T14:25:00Z",
      end_time: null,
      duration: 0,
      triggered_at: "2025-01-05T14:25:00Z",
      acknowledged_at: null,
      resolved_at: null,
      acknowledged_by: null,
      resolved_by: null,
      created_at: "2025-01-05T14:25:00Z",
      updated_at: "2025-01-05T14:25:00Z"
    },
    {
      id: 2,
      alert_rule_id: 3,
      rule_id: 3,
      database_id: 2,
      database_name: "开发数据库",
      rule_name: "连接数过多",
      metric_type: "connections",
      value: 85,
      threshold: 80,
      operator: ">",
      severity: "warning",
      status: "acknowledged",
      message: "活跃连接数达到85，超过阈值80",
      start_time: "2025-01-05T13:45:00Z",
      end_time: null,
      duration: 900, // 15分钟
      triggered_at: "2025-01-05T13:45:00Z",
      acknowledged_at: "2025-01-05T14:00:00Z",
      resolved_at: null,
      acknowledged_by: "<EMAIL>",
      resolved_by: null,
      created_at: "2025-01-05T13:45:00Z",
      updated_at: "2025-01-05T14:00:00Z"
    },
    {
      id: 3,
      alert_rule_id: 2,
      rule_id: 2,
      database_id: 1,
      database_name: "生产数据库",
      rule_name: "内存使用率告警",
      metric_type: "memory",
      value: 87.5,
      threshold: 85,
      operator: ">",
      severity: "warning",
      status: "resolved",
      message: "内存使用率达到87.5%，超过阈值85%",
      start_time: "2025-01-05T12:30:00Z",
      end_time: "2025-01-05T13:15:00Z",
      duration: 2700, // 45分钟
      triggered_at: "2025-01-05T12:30:00Z",
      acknowledged_at: "2025-01-05T12:35:00Z",
      resolved_at: "2025-01-05T13:15:00Z",
      acknowledged_by: "<EMAIL>",
      resolved_by: 1,
      created_at: "2025-01-05T12:30:00Z",
      updated_at: "2025-01-05T13:15:00Z"
    }
  ],

  // 系统统计
  stats: {
    total_databases: 3,
    active_databases: 2,
    warning_databases: 1,
    total_alerts: 3,
    active_alerts: 1,
    acknowledged_alerts: 1,
    resolved_alerts: 1,
    avg_response_time: 45,
    total_connections: 65,
    uptime_percentage: 99.8
  }
};

// 模拟API响应格式
export const createDemoResponse = <T>(data: T, message = 'Success') => ({
  code: 200,
  message,
  data,
  timestamp: new Date().toISOString()
});

// 模拟延迟
export const simulateDelay = (ms: number = 300) => 
  new Promise(resolve => setTimeout(resolve, ms));
