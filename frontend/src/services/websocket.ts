// WebSocket客户端服务

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
}

export interface MetricUpdate {
  database_id: number;
  metrics: any[];
}

export interface AlertUpdate {
  event: any;
}

export type WebSocketEventHandler = (message: WebSocketMessage) => void;

export class WebSocketService {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 1000; // 1秒
  private eventHandlers: Map<string, WebSocketEventHandler[]> = new Map();
  private isConnecting = false;
  private shouldReconnect = true;

  constructor(url: string = 'ws://localhost:8080/ws') {
    this.url = url;
  }

  // 连接WebSocket
  connect(token?: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        reject(new Error('Already connecting'));
        return;
      }

      this.isConnecting = true;
      
      // 构建连接URL
      let wsUrl = this.url;
      if (token) {
        wsUrl += `?token=${token}`;
      }

      try {
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.emit('connected', null);
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.isConnecting = false;
          this.ws = null;
          this.emit('disconnected', { code: event.code, reason: event.reason });

          // 自动重连
          if (this.shouldReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.isConnecting = false;
          this.emit('error', error);
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  // 断开连接
  disconnect(): void {
    this.shouldReconnect = false;
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  // 发送消息
  send(type: string, data: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message: WebSocketMessage = {
        type,
        data,
        timestamp: new Date().toISOString(),
      };
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
    }
  }

  // 添加事件监听器
  on(eventType: string, handler: WebSocketEventHandler): void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType)!.push(handler);
  }

  // 移除事件监听器
  off(eventType: string, handler: WebSocketEventHandler): void {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  // 移除所有事件监听器
  removeAllListeners(eventType?: string): void {
    if (eventType) {
      this.eventHandlers.delete(eventType);
    } else {
      this.eventHandlers.clear();
    }
  }

  // 获取连接状态
  getReadyState(): number {
    return this.ws ? this.ws.readyState : WebSocket.CLOSED;
  }

  // 是否已连接
  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  // 处理消息
  private handleMessage(message: WebSocketMessage): void {
    console.log('Received WebSocket message:', message);
    this.emit(message.type, message.data);
  }

  // 触发事件
  private emit(eventType: string, data: any): void {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler({ type: eventType, data, timestamp: new Date().toISOString() });
        } catch (error) {
          console.error('Error in WebSocket event handler:', error);
        }
      });
    }
  }

  // 计划重连
  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1); // 指数退避
    
    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      if (this.shouldReconnect) {
        this.connect().catch(error => {
          console.error('Reconnect failed:', error);
        });
      }
    }, delay);
  }
}

// 全局WebSocket实例
export const wsService = new WebSocketService();

// WebSocket Hook
import { useEffect, useRef, useState } from 'react';

export interface UseWebSocketOptions {
  autoConnect?: boolean;
  token?: string;
}

export function useWebSocket(options: UseWebSocketOptions = {}) {
  const { autoConnect = false, token } = options;
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const wsRef = useRef<WebSocketService>(wsService);

  useEffect(() => {
    const ws = wsRef.current;

    // 连接状态监听器
    const handleConnected = () => {
      setIsConnected(true);
      setConnectionError(null);
    };

    const handleDisconnected = () => {
      setIsConnected(false);
    };

    const handleError = (message: WebSocketMessage) => {
      setConnectionError(message.data?.message || 'WebSocket connection error');
    };

    // 添加监听器
    ws.on('connected', handleConnected);
    ws.on('disconnected', handleDisconnected);
    ws.on('error', handleError);

    // 自动连接
    if (autoConnect && !ws.isConnected()) {
      ws.connect(token).catch(error => {
        setConnectionError(error.message);
      });
    }

    // 清理函数
    return () => {
      ws.off('connected', handleConnected);
      ws.off('disconnected', handleDisconnected);
      ws.off('error', handleError);
    };
  }, [autoConnect, token]);

  const connect = async () => {
    try {
      await wsRef.current.connect(token);
    } catch (error: any) {
      setConnectionError(error.message);
      throw error;
    }
  };

  const disconnect = () => {
    wsRef.current.disconnect();
  };

  const subscribe = (eventType: string, handler: WebSocketEventHandler) => {
    wsRef.current.on(eventType, handler);
  };

  const unsubscribe = (eventType: string, handler: WebSocketEventHandler) => {
    wsRef.current.off(eventType, handler);
  };

  const send = (type: string, data: any) => {
    wsRef.current.send(type, data);
  };

  return {
    isConnected,
    connectionError,
    connect,
    disconnect,
    subscribe,
    unsubscribe,
    send,
    ws: wsRef.current,
  };
}

// 指标更新Hook
export function useMetricUpdates(databaseId?: number) {
  const [latestMetrics, setLatestMetrics] = useState<any[]>([]);
  const { subscribe, unsubscribe, isConnected } = useWebSocket({ autoConnect: true });

  useEffect(() => {
    const handleMetricUpdate = (message: WebSocketMessage) => {
      const update: MetricUpdate = message.data;
      if (!databaseId || update.database_id === databaseId) {
        setLatestMetrics(update.metrics);
      }
    };

    subscribe('metric_update', handleMetricUpdate);

    return () => {
      unsubscribe('metric_update', handleMetricUpdate);
    };
  }, [databaseId, subscribe, unsubscribe]);

  return {
    latestMetrics,
    isConnected,
  };
}

// 告警更新Hook
export function useAlertUpdates() {
  const [latestAlert, setLatestAlert] = useState<any | null>(null);
  const { subscribe, unsubscribe, isConnected } = useWebSocket({ autoConnect: true });

  useEffect(() => {
    const handleAlertUpdate = (message: WebSocketMessage) => {
      const update: AlertUpdate = message.data;
      setLatestAlert(update.event);
    };

    subscribe('alert_update', handleAlertUpdate);

    return () => {
      unsubscribe('alert_update', handleAlertUpdate);
    };
  }, [subscribe, unsubscribe]);

  return {
    latestAlert,
    isConnected,
  };
}
