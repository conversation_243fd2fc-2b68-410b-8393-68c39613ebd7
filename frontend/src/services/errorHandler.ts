import { type AxiosError } from 'axios';

// 错误类型定义
export interface ApiError {
  code: number;
  message: string;
  details?: any;
  timestamp?: string;
}

export interface ErrorHandlerOptions {
  showNotification?: boolean;
  logError?: boolean;
  fallbackMessage?: string;
}

// 错误处理类
class ErrorHandler {
  private errorListeners: Array<(error: ApiError) => void> = [];

  // 添加错误监听器
  addErrorListener(listener: (error: ApiError) => void) {
    this.errorListeners.push(listener);
  }

  // 移除错误监听器
  removeErrorListener(listener: (error: ApiError) => void) {
    const index = this.errorListeners.indexOf(listener);
    if (index > -1) {
      this.errorListeners.splice(index, 1);
    }
  }

  // 通知所有错误监听器
  private notifyErrorListeners(error: ApiError) {
    this.errorListeners.forEach(listener => {
      try {
        listener(error);
      } catch (e) {
        console.error('Error in error listener:', e);
      }
    });
  }

  // 获取友好的错误消息
  private getFriendlyErrorMessage(error: any): string {
    // 网络错误
    if (!error.response) {
      return '网络连接失败，请检查网络连接或稍后重试';
    }

    const status = error.response?.status;
    const message = error.response?.data?.message;

    // 根据状态码返回友好消息
    switch (status) {
      case 401:
        return '登录已过期，请重新登录';
      case 403:
        return '权限不足，无法执行此操作';
      case 404:
        return '请求的资源不存在';
      case 422:
        return message || '请求参数有误，请检查输入内容';
      case 429:
        return '请求过于频繁，请稍后重试';
      case 500:
        return '服务器内部错误，请稍后重试';
      case 502:
      case 503:
      case 504:
        return '服务暂时不可用，请稍后重试';
      default:
        return message || `请求失败 (${status})`;
    }
  }

  // 处理API错误
  handleApiError(
    error: any,
    options: ErrorHandlerOptions = {}
  ): ApiError {
    const {
      showNotification = false, // 默认不显示通知
      logError = true,
      fallbackMessage = 'An unexpected error occurred'
    } = options;

    let apiError: ApiError;

    if (this.isAxiosError(error)) {
      apiError = this.parseAxiosError(error, fallbackMessage);
    } else if (error instanceof Error) {
      apiError = {
        code: 0,
        message: error.message || fallbackMessage,
        timestamp: new Date().toISOString(),
      };
    } else {
      apiError = {
        code: 0,
        message: fallbackMessage,
        timestamp: new Date().toISOString(),
      };
    }

    if (logError) {
      this.logError(apiError, error);
    }

    if (showNotification) {
      this.notifyErrorListeners(apiError);
    }

    return apiError;
  }

  // 检查是否为Axios错误
  private isAxiosError(error: any): error is AxiosError {
    return error && error.isAxiosError === true;
  }

  // 解析Axios错误
  private parseAxiosError(error: AxiosError, fallbackMessage: string): ApiError {
    const response = error.response;

    if (response) {
      // 服务器返回了错误响应
      const data = response.data as any;

      return {
        code: data?.code || response.status,
        message: this.getFriendlyErrorMessage(error), // 使用友好错误消息
        details: data?.details || data,
        timestamp: new Date().toISOString(),
      };
    } else if (error.request) {
      // 请求已发出但没有收到响应
      return {
        code: 0,
        message: '网络连接失败，请检查网络连接或稍后重试',
        details: { type: 'network_error' },
        timestamp: new Date().toISOString(),
      };
    } else {
      // 请求配置出错
      return {
        code: 0,
        message: error.message || fallbackMessage,
        details: { type: 'request_error' },
        timestamp: new Date().toISOString(),
      };
    }
  }



  // 记录错误
  private logError(apiError: ApiError, originalError: any) {
    console.group(`🚨 API Error [${apiError.code}]`);
    console.error('Message:', apiError.message);
    console.error('Timestamp:', apiError.timestamp);
    if (apiError.details) {
      console.error('Details:', apiError.details);
    }
    console.error('Original Error:', originalError);
    console.groupEnd();
  }

  // 格式化错误消息用于显示
  formatErrorMessage(error: ApiError): string {
    // 根据错误类型返回用户友好的消息
    if (error.code === 401) {
      return 'Your session has expired. Please log in again.';
    }

    if (error.code === 403) {
      return 'You do not have permission to perform this action.';
    }

    if (error.code === 404) {
      return 'The requested resource was not found.';
    }

    if (error.code === 409) {
      return 'This resource already exists. Please use a different configuration.';
    }

    if (error.code === 422) {
      return 'Please check your input and try again.';
    }

    if (error.code === 429) {
      return 'Too many requests. Please wait a moment and try again.';
    }

    if (error.code >= 500) {
      return 'Server error. Please try again later.';
    }

    if (error.details?.type === 'network_error') {
      return 'Unable to connect to server. Please check your internet connection.';
    }

    return error.message;
  }

  // 检查错误是否为特定类型
  isNetworkError(error: ApiError): boolean {
    return error.details?.type === 'network_error';
  }

  isAuthError(error: ApiError): boolean {
    return error.code === 401 || error.code === 403;
  }

  isValidationError(error: ApiError): boolean {
    return error.code === 422 || error.code === 400;
  }

  isServerError(error: ApiError): boolean {
    return error.code >= 500;
  }

  isRateLimitError(error: ApiError): boolean {
    return error.code === 429;
  }
}

// 创建单例实例
export const errorHandler = new ErrorHandler();

// 便捷函数
export const handleApiError = (error: any, options?: ErrorHandlerOptions) => {
  return errorHandler.handleApiError(error, options);
};

export const formatErrorMessage = (error: ApiError) => {
  return errorHandler.formatErrorMessage(error);
};

// 导出类型已在上面定义
