import { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Sidebar } from './components/layout/Sidebar';
import { CoreMetricsCards } from './components/dashboard/CoreMetricsCards';
import { RealtimeMonitoringCharts } from './components/dashboard/RealtimeMonitoringCharts';
import { DatabaseManagement } from './components/database/DatabaseManagement';
import { DatabaseDetail } from './components/database/DatabaseDetail';
import AlertManagement from './components/alerts/AlertManagement';
import { PerformanceAnalysis } from './components/performance/PerformanceAnalysis';
import { QueryOptimizer } from './components/query-optimizer/QueryOptimizer';
import { MaintenanceToolkit } from './components/maintenance/MaintenanceToolkit';
import { ReportGenerator } from './components/reports/ReportGenerator';
import { SystemSettings } from './components/settings/SystemSettings';
import { LoginForm } from './components/auth/LoginForm';
import { NetworkStatusBanner } from './components/common/NetworkStatus';
import { useAuth } from './hooks/useAuth';
import './index.css';

// 创建QueryClient实例
const queryClient = new QueryClient();

// 页面类型定义
type ViewType = 'dashboard' | 'database' | 'database-detail' | 'alert' | 'performance' | 'query-optimizer' | 'maintenance' | 'reports' | 'settings';

function App() {
  const [currentView, setCurrentView] = useState<ViewType>('dashboard');
  const [selectedDatabase, setSelectedDatabase] = useState<any>(null);

  const { isAuthenticated, isLoading, error, login } = useAuth();

  // 处理视图切换
  const handleViewChange = (view: string) => {
    setCurrentView(view as ViewType);
  };
  // 模拟数据库数据
  const mockDatabases = [
    { id: 'pg-main', name: 'PostgreSQL-Main', type: 'PostgreSQL', host: 'localhost:5432', status: 'healthy', icon: 'PG', color: 'blue' },
    { id: 'redis-cache', name: 'Redis-Cache', type: 'Redis', host: 'localhost:6379', status: 'healthy', icon: 'RD', color: 'red' },
    { id: 'mysql-backup', name: 'MySQL-Backup', type: 'MySQL', host: 'localhost:3306', status: 'warning', icon: 'MY', color: 'orange' },
    { id: 'mongo-logs', name: 'MongoDB-Logs', type: 'MongoDB', host: 'localhost:27017', status: 'healthy', icon: 'MG', color: 'green' }
  ];

  // 渲染Dashboard内容 - DataDog风格桌面端布局
  const renderDashboard = () => (
    <div className="space-y-6">
      {/* 核心指标条 - DataDog风格 */}
      <div className="h-32">
        <CoreMetricsCards />
      </div>

      {/* 主要内容区域 - 固定桌面端布局 */}
      <div className="grid grid-cols-3 gap-6">
        {/* 左侧: 数据库实例状态总览 */}
        <div className="col-span-1">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">数据库实例</h3>
              <button
                onClick={() => setCurrentView('database')}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                查看全部
              </button>
            </div>
            <div className="space-y-3">
              {mockDatabases.map((db) => {
                const getStatusColor = (status: string) => {
                  switch (status) {
                    case 'healthy': return { dot: 'bg-green-500', text: 'text-green-600', label: '运行中' };
                    case 'warning': return { dot: 'bg-yellow-500', text: 'text-yellow-600', label: '维护中' };
                    case 'error': return { dot: 'bg-red-500', text: 'text-red-600', label: '离线' };
                    default: return { dot: 'bg-gray-500', text: 'text-gray-600', label: '未知' };
                  }
                };

                const getIconColor = (color: string) => {
                  switch (color) {
                    case 'blue': return { bg: 'bg-blue-100', text: 'text-blue-600' };
                    case 'red': return { bg: 'bg-red-100', text: 'text-red-600' };
                    case 'orange': return { bg: 'bg-orange-100', text: 'text-orange-600' };
                    case 'green': return { bg: 'bg-green-100', text: 'text-green-600' };
                    default: return { bg: 'bg-gray-100', text: 'text-gray-600' };
                  }
                };

                const statusStyle = getStatusColor(db.status);
                const iconStyle = getIconColor(db.color);

                return (
                  <div key={db.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 cursor-pointer transition-colors">
                    <div className="flex items-center space-x-3">
                      <div className={`w-8 h-8 ${iconStyle.bg} rounded-lg flex items-center justify-center`}>
                        <span className={`${iconStyle.text} font-semibold text-sm`}>{db.icon}</span>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{db.name}</div>
                        <div className="text-sm text-gray-500">{db.host}</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 ${statusStyle.dot} rounded-full`}></div>
                      <span className={`text-xs ${statusStyle.text}`}>{statusStyle.label}</span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* 右侧: 监控图表区域 - DataDog风格主导展示 */}
        <div className="col-span-2 space-y-6">
          {/* 实时监控图表 - 主要展示空间 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">实时监控</h3>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-gray-600">实时更新</span>
              </div>
            </div>
            <div className="h-[575px]">
              <RealtimeMonitoringCharts />
            </div>
          </div>

          {/* 关键监控指标 - 数据展示区域 */}
          <div className="grid grid-cols-2 gap-6">
            {/* 数据库状态概览 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">数据库状态</h3>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-xs text-gray-600">全部正常</span>
                </div>
              </div>
              <div className="space-y-3">
                {mockDatabases.map((db, index) => (
                  <div key={db.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 ${db.status === 'healthy' ? 'bg-green-500' : db.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'} rounded-full`}></div>
                      <span className="text-sm text-gray-700">{db.name}</span>
                    </div>
                    <div className="text-xs text-gray-500">
                      {index === 0 ? '45ms' : index === 1 ? '12ms' : index === 2 ? '78ms' : '23ms'}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 关键性能指标 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">性能指标</h3>
                <div className="text-xs text-gray-500">实时数据</div>
              </div>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">CPU使用率</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-500 h-2 rounded-full" style={{width: '45%'}}></div>
                    </div>
                    <span className="text-sm font-medium">45%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">内存使用率</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-green-500 h-2 rounded-full" style={{width: '62%'}}></div>
                    </div>
                    <span className="text-sm font-medium">62%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">活跃连接</span>
                  <span className="text-sm font-medium">156/200</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">I/O吞吐</span>
                  <span className="text-sm font-medium">2.3MB/s</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // 渲染其他页面内容
  const renderContent = () => {
    switch (currentView) {
      case 'dashboard':
        return renderDashboard();
      case 'database':
        return <DatabaseManagement onDatabaseSelect={(db) => {
          setSelectedDatabase(db);
          setCurrentView('database-detail');
        }} />;
      case 'database-detail':
        return <DatabaseDetail database={selectedDatabase} onBack={() => setCurrentView('database')} />;
      case 'alert':
        return <AlertManagement />;
      case 'performance':
        return <PerformanceAnalysis onQuerySelect={(query) => {
          console.log('Selected query:', query);
          setCurrentView('query-optimizer');
        }} />;
      case 'query-optimizer':
        return <QueryOptimizer />;
      case 'maintenance':
        return <MaintenanceToolkit />;
      case 'reports':
        return <ReportGenerator />;
      case 'settings':
        return <SystemSettings />;
      default:
        return renderDashboard();
    }
  };

  // 处理登录
  const handleLogin = async (credentials: { email: string; password: string }) => {
    await login(credentials);
  };

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">正在加载...</p>
        </div>
      </div>
    );
  }

  // 如果未登录，显示登录页面
  if (!isAuthenticated) {
    return (
      <QueryClientProvider client={queryClient}>
        <LoginForm onLogin={handleLogin} loading={isLoading} error={error || undefined} />
      </QueryClientProvider>
    );
  }

  return (
    <QueryClientProvider client={queryClient}>
      <div className="min-h-screen bg-gray-50 flex">
        {/* DataDog风格固定侧边栏 */}
        <Sidebar
          currentView={currentView}
          onViewChange={handleViewChange}
        />

        {/* DataDog风格主内容区域 */}
        <main className="flex-1 min-w-0">
          {/* 网络状态横幅 */}
          <NetworkStatusBanner />

          <div className="p-6">
            {renderContent()}
          </div>
        </main>
      </div>
    </QueryClientProvider>
  );
}

export default App;
