import React, { useState, useEffect, useCallback } from 'react';
import { handleApiError, type ApiError } from '../services/errorHandler';

// API状态类型
export interface ApiState<T> {
  data: T | null;
  loading: boolean;
  error: ApiError | null;
  lastUpdated: Date | null;
}

// API Hook选项
export interface UseApiOptions {
  immediate?: boolean; // 是否立即执行
  onSuccess?: (data: any) => void;
  onError?: (error: ApiError) => void;
  showError?: boolean; // 是否显示错误通知
}

// 通用API Hook
export function useApi<T>(
  apiCall: () => Promise<any>,
  options: UseApiOptions = {}
) {
  const {
    immediate = false,
    onSuccess,
    onError,
    showError = true,
  } = options;

  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null,
  });

  // 执行API调用
  const execute = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await apiCall();
      const data = response?.data || response;
      
      setState({
        data,
        loading: false,
        error: null,
        lastUpdated: new Date(),
      });

      onSuccess?.(data);
      return data;
    } catch (error) {
      const apiError = handleApiError(error, { showNotification: showError });
      
      setState(prev => ({
        ...prev,
        loading: false,
        error: apiError,
      }));

      onError?.(apiError);
      throw apiError;
    }
  }, [apiCall, onSuccess, onError, showError]);

  // 重置状态
  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      lastUpdated: null,
    });
  }, []);

  // 立即执行 - 移除execute依赖，避免无限循环
  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [immediate]); // 只依赖immediate

  return {
    ...state,
    execute,
    reset,
    isSuccess: !state.loading && !state.error && state.data !== null,
    isError: !state.loading && state.error !== null,
    isIdle: !state.loading && state.error === null && state.data === null,
  };
}

// 分页API Hook
export function usePaginatedApi<T>(
  apiCall: (params: any) => Promise<any>,
  initialParams: any = {},
  options: UseApiOptions = {}
) {
  const [params, setParams] = useState(initialParams);
  
  const api = useApi<{
    data: T[];
    page: number;
    page_size: number;
    total: number;
    total_pages: number;
  }>(() => apiCall(params), options);

  const setPage = useCallback((page: number) => {
    setParams((prev: any) => ({ ...prev, page }));
  }, []);

  const setPageSize = useCallback((pageSize: number) => {
    setParams((prev: any) => ({ ...prev, page_size: pageSize, page: 1 }));
  }, []);

  const setFilters = useCallback((filters: any) => {
    setParams((prev: any) => ({ ...prev, ...filters, page: 1 }));
  }, []);

  const refresh = useCallback(() => {
    return api.execute();
  }, [api]);

  // 当参数变化时重新执行 - 使用useRef避免无限循环
  const prevParamsRef = React.useRef(params);

  useEffect(() => {
    // 深度比较参数是否真的发生了变化
    const hasChanged = JSON.stringify(prevParamsRef.current) !== JSON.stringify(params);

    if (hasChanged && options.immediate !== false) {
      prevParamsRef.current = params;
      api.execute();
    }
  }, [params, options.immediate]);

  return {
    ...api,
    params,
    setPage,
    setPageSize,
    setFilters,
    refresh,
    items: api.data?.data || [],
    pagination: api.data ? {
      page: api.data.page,
      pageSize: api.data.page_size,
      total: api.data.total,
      totalPages: api.data.total_pages,
    } : null,
  };
}

// 实时数据Hook
export function useRealtimeApi<T>(
  apiCall: () => Promise<any>,
  interval: number = 5000, // 5秒刷新间隔
  options: UseApiOptions = {}
) {
  const api = useApi<T>(apiCall, { ...options, immediate: true });
  const [isRealtime, setIsRealtime] = useState(false);

  useEffect(() => {
    // 暂时禁用实时轮询，避免API错误弹窗
    return;

    if (!isRealtime) return;

    const timer = setInterval(() => {
      if (!api.loading) {
        api.execute();
      }
    }, interval);

    return () => clearInterval(timer);
  }, [isRealtime, interval, api]);

  const startRealtime = useCallback(() => {
    setIsRealtime(true);
  }, []);

  const stopRealtime = useCallback(() => {
    setIsRealtime(false);
  }, []);

  const toggleRealtime = useCallback(() => {
    setIsRealtime(prev => !prev);
  }, []);

  return {
    ...api,
    isRealtime,
    startRealtime,
    stopRealtime,
    toggleRealtime,
  };
}

// 缓存API Hook
export function useCachedApi<T>(
  key: string,
  apiCall: () => Promise<any>,
  cacheTime: number = 5 * 60 * 1000, // 5分钟缓存
  options: UseApiOptions = {}
) {
  const [cache, setCache] = useState<Map<string, {
    data: any;
    timestamp: number;
  }>>(new Map());

  const api = useApi<T>(
    async () => {
      const cached = cache.get(key);
      const now = Date.now();
      
      // 如果有缓存且未过期，返回缓存数据
      if (cached && (now - cached.timestamp) < cacheTime) {
        return cached.data;
      }
      
      // 否则调用API并缓存结果
      const result = await apiCall();
      setCache(prev => new Map(prev).set(key, {
        data: result,
        timestamp: now,
      }));
      
      return result;
    },
    options
  );

  const clearCache = useCallback((cacheKey?: string) => {
    if (cacheKey) {
      setCache(prev => {
        const newCache = new Map(prev);
        newCache.delete(cacheKey);
        return newCache;
      });
    } else {
      setCache(new Map());
    }
  }, []);

  const isCached = cache.has(key);
  const cacheAge = cache.get(key) ? Date.now() - cache.get(key)!.timestamp : 0;

  return {
    ...api,
    isCached,
    cacheAge,
    clearCache,
  };
}
