import { useState, useEffect } from 'react';

// 断点定义 (参考DataDog设计)
export const breakpoints = {
  mobile: '(max-width: 767px)',
  tablet: '(min-width: 768px) and (max-width: 1199px)',
  desktop: '(min-width: 1200px)'
} as const;

export type ScreenType = 'mobile' | 'tablet' | 'desktop';

/**
 * 响应式断点检测Hook
 * 基于DataDog UI设计的断点策略
 */
export const useResponsive = () => {
  const [screenType, setScreenType] = useState<ScreenType>('desktop');
  const [screenWidth, setScreenWidth] = useState<number>(
    typeof window !== 'undefined' ? window.innerWidth : 1200
  );

  useEffect(() => {
    // 检测当前屏幕类型
    const detectScreenType = (): ScreenType => {
      const width = window.innerWidth;
      if (width < 768) return 'mobile';
      if (width < 1200) return 'tablet';
      return 'desktop';
    };

    // 更新屏幕状态
    const updateScreenState = () => {
      const width = window.innerWidth;
      const type = detectScreenType();
      
      setScreenWidth(width);
      setScreenType(type);
    };

    // 初始化
    updateScreenState();

    // 添加resize监听器
    const handleResize = () => {
      updateScreenState();
    };

    window.addEventListener('resize', handleResize);

    // 清理监听器
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 便捷的布局检测方法
  const isMobile = screenType === 'mobile';
  const isTablet = screenType === 'tablet';
  const isDesktop = screenType === 'desktop';
  const isMobileOrTablet = isMobile || isTablet;

  return {
    screenType,
    screenWidth,
    isMobile,
    isTablet,
    isDesktop,
    isMobileOrTablet,
    // 断点检测方法
    isScreenSize: (type: ScreenType) => screenType === type,
    // 最小宽度检测
    isMinWidth: (width: number) => screenWidth >= width,
    // 最大宽度检测
    isMaxWidth: (width: number) => screenWidth <= width
  };
};

/**
 * 媒体查询Hook - 用于更精确的断点控制
 */
export const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState<boolean>(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia(query);
    
    // 设置初始值
    setMatches(mediaQuery.matches);

    // 监听变化
    const handleChange = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    // 添加监听器 (兼容旧版本浏览器)
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
    } else {
      // 兼容旧版本
      mediaQuery.addListener(handleChange);
    }

    // 清理监听器
    return () => {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', handleChange);
      } else {
        // 兼容旧版本
        mediaQuery.removeListener(handleChange);
      }
    };
  }, [query]);

  return matches;
};

/**
 * 预定义的媒体查询Hooks
 */
export const useIsMobile = () => useMediaQuery(breakpoints.mobile);
export const useIsTablet = () => useMediaQuery(breakpoints.tablet);
export const useIsDesktop = () => useMediaQuery(breakpoints.desktop);

/**
 * 响应式值Hook - 根据屏幕类型返回不同的值
 */
export const useResponsiveValue = <T>(values: {
  mobile: T;
  tablet: T;
  desktop: T;
}): T => {
  const { screenType } = useResponsive();
  return values[screenType];
};

/**
 * 侧边栏状态管理Hook
 */
export const useSidebarState = () => {
  const { isDesktop } = useResponsive();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // 桌面端侧边栏切换
  const toggleSidebar = () => {
    if (isDesktop) {
      setIsCollapsed(!isCollapsed);
    }
  };

  // 移动端菜单切换
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  // 关闭移动端菜单
  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  // 当屏幕尺寸变化时，自动关闭移动端菜单
  useEffect(() => {
    if (isDesktop) {
      setIsMobileMenuOpen(false);
    }
  }, [isDesktop]);

  return {
    isCollapsed,
    isMobileMenuOpen,
    toggleSidebar,
    toggleMobileMenu,
    closeMobileMenu,
    // 计算的状态
    sidebarWidth: isDesktop ? (isCollapsed ? 64 : 288) : 0, // 288px = w-72
    showSidebar: isDesktop,
    showMobileMenu: !isDesktop && isMobileMenuOpen
  };
};
