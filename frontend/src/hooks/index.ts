// 统一的Hooks入口文件
import { useState, useEffect } from 'react';

// API相关Hooks
export {
  useApi,
  usePaginatedApi,
  useRealtimeApi,
  useCachedApi,
  type ApiState,
  type UseApiOptions,
} from './useApi';

// 认证相关Hooks
export {
  useAuth,
  useRequireAuth,
  useRequireRole,
  useRequireAdmin,
  type AuthState,
} from './useAuth';

// 业务相关Hooks (基于API Hooks的封装)
import { useApi, usePaginatedApi, useRealtimeApi } from './useApi';
import { api } from '../services';

// 数据库相关Hooks
export const useDatabases = (params?: any) => {
  return usePaginatedApi(
    (p) => api.databases.list(p),
    { page: 1, page_size: 20, ...params },
    { immediate: true }
  );
};

export const useDatabase = (id: number) => {
  return useApi(
    () => api.databases.get(id),
    { immediate: !!id }
  );
};

export const useDatabaseStats = () => {
  return useApi(
    () => api.databases.stats(),
    { immediate: true }
  );
};

// 监控指标相关Hooks
export const useMetrics = (dbId: number, params?: any) => {
  return useApi(
    () => api.metrics.list(dbId, params),
    { immediate: !!dbId }
  );
};

export const useLatestMetrics = (dbId: number) => {
  return useRealtimeApi(
    () => api.metrics.latest(dbId),
    5000, // 5秒刷新
    { immediate: !!dbId }
  );
};

export const useMetricHistory = (dbId: number, params: any) => {
  return useApi(
    () => api.metrics.history(dbId, params),
    { immediate: !!dbId && !!params.start_time && !!params.end_time }
  );
};

export const useRealtimeMetrics = () => {
  return useRealtimeApi(
    () => api.metrics.realtime(),
    3000 // 3秒刷新
  );
};

// 告警相关Hooks
export const useAlertRules = (params?: any) => {
  return usePaginatedApi(
    (p) => api.alerts.rules.list(p),
    { page: 1, page_size: 20, ...params },
    { immediate: true }
  );
};

export const useAlertRule = (id: number) => {
  return useApi(
    () => api.alerts.rules.get(id),
    { immediate: !!id }
  );
};

export const useAlertEvents = (params?: any) => {
  return usePaginatedApi(
    (p) => api.alerts.events.list(p),
    { page: 1, page_size: 20, ...params },
    { immediate: true }
  );
};

export const useAlertStats = () => {
  return useApi(
    () => api.alerts.stats(),
    { immediate: true } // 重新启用告警统计
  );
};

// 健康检查Hook
export const useHealthCheck = () => {
  return useRealtimeApi(
    () => api.health(),
    30000 // 30秒检查一次
  );
};

// 网络状态检测Hook
export const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isBackendReachable, setIsBackendReachable] = useState(true);

  // 监听浏览器网络状态
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // 定期检查后端连接状态
  useEffect(() => {
    const checkBackendHealth = async () => {
      try {
        await api.health();
        setIsBackendReachable(true);
      } catch (error) {
        setIsBackendReachable(false);
      }
    };

    // 立即检查一次
    checkBackendHealth();

    // 每30秒检查一次
    const interval = setInterval(checkBackendHealth, 30000);

    return () => clearInterval(interval);
  }, []);

  return {
    isOnline,
    isBackendReachable,
    isConnected: isOnline && isBackendReachable
  };
};

// 响应式布局相关Hooks
export {
  useResponsive,
  useMediaQuery,
  useIsMobile,
  useIsTablet,
  useIsDesktop,
  useResponsiveValue,
  useSidebarState,
  breakpoints,
  type ScreenType
} from './useResponsive';
