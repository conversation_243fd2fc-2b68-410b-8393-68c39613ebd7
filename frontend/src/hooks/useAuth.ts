import { useState, useEffect, useCallback } from 'react';
import { authService } from '../services/auth';
import type { User, LoginRequest, RegisterRequest } from '../services/auth';

// 认证状态类型
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// 认证Hook
export function useAuth() {
  const [state, setState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
  });

  // 更新认证状态
  const updateAuthState = useCallback((user: User | null) => {
    setState({
      user,
      isAuthenticated: !!user,
      isLoading: false,
      error: null,
    });
  }, []);

  // 设置错误状态
  const setError = useCallback((error: string) => {
    setState(prev => ({
      ...prev,
      isLoading: false,
      error,
    }));
  }, []);

  // 设置加载状态
  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({
      ...prev,
      isLoading: loading,
    }));
  }, []);

  // 用户登录
  const login = useCallback(async (credentials: LoginRequest) => {
    setLoading(true);
    try {
      const authData = await authService.login(credentials);
      updateAuthState(authData.user);
      return authData;
    } catch (error: any) {
      setError(error.message);
      throw error;
    }
  }, [updateAuthState, setError, setLoading]);

  // 用户注册
  const register = useCallback(async (userData: RegisterRequest) => {
    setLoading(true);
    try {
      const authData = await authService.register(userData);
      updateAuthState(authData.user);
      return authData;
    } catch (error: any) {
      setError(error.message);
      throw error;
    }
  }, [updateAuthState, setError, setLoading]);

  // 用户登出
  const logout = useCallback(async () => {
    setLoading(true);
    try {
      await authService.logout();
      updateAuthState(null);
    } catch (error: any) {
      // 即使登出失败，也要清除本地状态
      updateAuthState(null);
    }
  }, [updateAuthState, setLoading]);

  // 刷新用户信息
  const refreshUser = useCallback(async () => {
    setLoading(true);
    try {
      const user = await authService.getCurrentUser();
      updateAuthState(user);
      return user;
    } catch (error: any) {
      setError(error.message);
      return null;
    }
  }, [updateAuthState, setError, setLoading]);

  // 更新用户信息
  const updateProfile = useCallback(async (userData: Partial<User>) => {
    setLoading(true);
    try {
      const user = await authService.updateProfile(userData);
      updateAuthState(user);
      return user;
    } catch (error: any) {
      setError(error.message);
      throw error;
    }
  }, [updateAuthState, setError, setLoading]);

  // 修改密码
  const changePassword = useCallback(async (oldPassword: string, newPassword: string) => {
    setLoading(true);
    try {
      await authService.changePassword(oldPassword, newPassword);
      setState(prev => ({ ...prev, isLoading: false, error: null }));
    } catch (error: any) {
      setError(error.message);
      throw error;
    }
  }, [setError, setLoading]);

  // 清除错误
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // 检查权限
  const hasRole = useCallback((role: string) => {
    return authService.hasRole(role);
  }, []);

  const isAdmin = useCallback(() => {
    return authService.isAdmin();
  }, []);

  const isUser = useCallback(() => {
    return authService.isUser();
  }, []);

  // 初始化认证状态
  useEffect(() => {
    const handleAuthChange = (user: User | null) => {
      updateAuthState(user);
    };

    // 添加认证状态监听器
    authService.addAuthListener(handleAuthChange);

    // 如果已经有token，尝试获取用户信息
    if (authService.isAuthenticated()) {
      refreshUser();
    } else {
      setState(prev => ({ ...prev, isLoading: false }));
    }

    // 清理函数
    return () => {
      authService.removeAuthListener(handleAuthChange);
    };
  }, [updateAuthState, refreshUser]);

  return {
    // 状态
    ...state,
    
    // 操作方法
    login,
    register,
    logout,
    refreshUser,
    updateProfile,
    changePassword,
    clearError,
    
    // 权限检查
    hasRole,
    isAdmin,
    isUser,
    
    // 便捷属性
    isGuest: !state.isAuthenticated,
    userName: state.user?.name || '',
    userEmail: state.user?.email || '',
    userRole: state.user?.role || '',
  };
}

// 权限保护Hook
export function useRequireAuth(redirectTo: string = '/login') {
  const auth = useAuth();

  useEffect(() => {
    if (!auth.isLoading && !auth.isAuthenticated) {
      window.location.href = redirectTo;
    }
  }, [auth.isLoading, auth.isAuthenticated, redirectTo]);

  return auth;
}

// 角色保护Hook
export function useRequireRole(requiredRole: string, redirectTo: string = '/') {
  const auth = useAuth();

  useEffect(() => {
    if (!auth.isLoading && auth.isAuthenticated && !auth.hasRole(requiredRole)) {
      window.location.href = redirectTo;
    }
  }, [auth.isLoading, auth.isAuthenticated, auth.hasRole, requiredRole, redirectTo]);

  return auth;
}

// 管理员保护Hook
export function useRequireAdmin(redirectTo: string = '/') {
  return useRequireRole('admin', redirectTo);
}
