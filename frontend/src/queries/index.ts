// React Query 查询定义

import { useQuery, useMutation, useQueryClient, type UseQueryOptions, type UseMutationOptions } from '@tanstack/react-query';
import { apiService } from '../services/api';
import type { 
  DatabaseInstance, 
  CreateDatabaseRequest, 
  Metric, 
  AlertRule, 
  AlertEvent,
  User,
  LoginRequest,
  RegisterRequest,
  PaginatedResponse,
  ApiResponse
} from '../services/api';

// ===== 查询键定义 =====
export const queryKeys = {
  // 认证相关
  auth: {
    profile: ['auth', 'profile'] as const,
  },
  
  // 数据库相关
  databases: {
    all: ['databases'] as const,
    list: (params?: any) => ['databases', 'list', params] as const,
    detail: (id: number) => ['databases', 'detail', id] as const,
    stats: ['databases', 'stats'] as const,
    search: (query: string) => ['databases', 'search', query] as const,
  },
  
  // 监控指标相关
  metrics: {
    all: ['metrics'] as const,
    list: (dbId: number, params?: any) => ['metrics', 'list', dbId, params] as const,
    latest: (dbId: number) => ['metrics', 'latest', dbId] as const,
    history: (dbId: number, params: any) => ['metrics', 'history', dbId, params] as const,
    types: (dbId: number) => ['metrics', 'types', dbId] as const,
    stats: (dbId: number, params: any) => ['metrics', 'stats', dbId, params] as const,
    realtime: ['metrics', 'realtime'] as const,
  },
  
  // 告警相关
  alerts: {
    all: ['alerts'] as const,
    rules: {
      all: ['alerts', 'rules'] as const,
      list: (params?: any) => ['alerts', 'rules', 'list', params] as const,
      detail: (id: number) => ['alerts', 'rules', 'detail', id] as const,
    },
    events: {
      all: ['alerts', 'events'] as const,
      list: (params?: any) => ['alerts', 'events', 'list', params] as const,
      detail: (id: number) => ['alerts', 'events', 'detail', id] as const,
    },
    stats: ['alerts', 'stats'] as const,
  },
  
  // 健康检查
  health: ['health'] as const,

  // 查询优化工具
  queryOptimizer: {
    all: ['query-optimizer'] as const,
    analyze: {
      all: ['query-optimizer', 'analyze'] as const,
      detail: (id: number) => ['query-optimizer', 'analyze', 'detail', id] as const,
      history: (params?: any) => ['query-optimizer', 'analyze', 'history', params] as const,
    },
    explain: {
      all: ['query-optimizer', 'explain'] as const,
      detail: (id: number) => ['query-optimizer', 'explain', 'detail', id] as const,
    },
    suggestions: {
      all: ['query-optimizer', 'suggestions'] as const,
      detail: (analysisId: number) => ['query-optimizer', 'suggestions', 'detail', analysisId] as const,
    },
    indexRecommendations: {
      all: ['query-optimizer', 'index-recommendations'] as const,
      detail: (analysisId: number) => ['query-optimizer', 'index-recommendations', 'detail', analysisId] as const,
    },
    databases: ['query-optimizer', 'databases'] as const,
    stats: ['query-optimizer', 'stats'] as const,
  },
} as const;

// ===== 认证相关查询 =====

export const useProfile = (options?: UseQueryOptions<ApiResponse<User>>) => {
  return useQuery({
    queryKey: queryKeys.auth.profile,
    queryFn: () => apiService.getProfile(),
    ...options,
  });
};

// ===== 数据库相关查询 =====

export const useDatabases = (
  params?: any,
  options?: UseQueryOptions<ApiResponse<PaginatedResponse<DatabaseInstance>>>
) => {
  return useQuery({
    queryKey: queryKeys.databases.list(params),
    queryFn: () => apiService.getDatabases(params),
    ...options,
  });
};

export const useDatabase = (
  id: number,
  options?: UseQueryOptions<ApiResponse<DatabaseInstance>>
) => {
  return useQuery({
    queryKey: queryKeys.databases.detail(id),
    queryFn: () => apiService.getDatabase(id),
    enabled: !!id,
    ...options,
  });
};

export const useDatabaseStats = (
  options?: UseQueryOptions<ApiResponse<any>>
) => {
  return useQuery({
    queryKey: queryKeys.databases.stats,
    queryFn: () => apiService.getDatabaseStats(),
    ...options,
  });
};

export const useSearchDatabases = (
  query: string,
  options?: UseQueryOptions<ApiResponse<PaginatedResponse<DatabaseInstance>>>
) => {
  return useQuery({
    queryKey: queryKeys.databases.search(query),
    queryFn: () => apiService.searchDatabases({ q: query }),
    enabled: !!query,
    ...options,
  });
};

// ===== 监控指标相关查询 =====

export const useMetrics = (
  dbId: number,
  params?: any,
  options?: UseQueryOptions<ApiResponse<Metric[]>>
) => {
  return useQuery({
    queryKey: queryKeys.metrics.list(dbId, params),
    queryFn: () => apiService.getMetrics(dbId, params),
    enabled: !!dbId,
    ...options,
  });
};

export const useLatestMetrics = (
  dbId: number,
  options?: UseQueryOptions<ApiResponse<Metric[]>>
) => {
  return useQuery({
    queryKey: queryKeys.metrics.latest(dbId),
    queryFn: () => apiService.getLatestMetrics(dbId),
    enabled: !!dbId,
    refetchInterval: 5000, // 5秒自动刷新
    ...options,
  });
};

export const useMetricHistory = (
  dbId: number,
  params: any,
  options?: UseQueryOptions<ApiResponse<Metric[]>>
) => {
  return useQuery({
    queryKey: queryKeys.metrics.history(dbId, params),
    queryFn: () => apiService.getMetricHistory(dbId, params),
    enabled: !!dbId && !!params.start_time && !!params.end_time,
    ...options,
  });
};

export const useRealtimeMetrics = (
  refreshInterval: number | false = 5000, // 默认5秒，false表示禁用
  options?: UseQueryOptions<ApiResponse<any[]>>
) => {
  return useQuery({
    queryKey: queryKeys.metrics.realtime,
    queryFn: () => apiService.getRealtimeMetrics(),
    refetchInterval: refreshInterval, // 可控制的刷新间隔
    ...options,
  });
};

// ===== 告警相关查询 =====

export const useAlertRules = (
  params?: any,
  options?: UseQueryOptions<ApiResponse<PaginatedResponse<AlertRule>>>
) => {
  return useQuery({
    queryKey: queryKeys.alerts.rules.list(params),
    queryFn: () => apiService.getAlertRules(params),
    ...options,
  });
};

export const useAlertRule = (
  id: number,
  options?: UseQueryOptions<ApiResponse<AlertRule>>
) => {
  return useQuery({
    queryKey: queryKeys.alerts.rules.detail(id),
    queryFn: () => apiService.getAlertRule(id),
    enabled: !!id,
    ...options,
  });
};

export const useAlertEvents = (
  params?: any,
  options?: UseQueryOptions<ApiResponse<PaginatedResponse<AlertEvent>>>
) => {
  return useQuery({
    queryKey: queryKeys.alerts.events.list(params),
    queryFn: () => apiService.getAlertEvents(params),
    ...options,
  });
};

export const useAlertStats = (
  options?: UseQueryOptions<ApiResponse<any>>
) => {
  return useQuery({
    queryKey: queryKeys.alerts.stats,
    queryFn: () => apiService.getAlertStats(),
    enabled: false, // 暂时禁用，避免SQL错误
    ...options,
  });
};

// ===== 健康检查查询 =====

export const useHealthCheck = (
  options?: UseQueryOptions<any>
) => {
  return useQuery({
    queryKey: queryKeys.health,
    queryFn: async () => {
      const response = await fetch('http://localhost:8080/health');
      return response.json();
    },
    refetchInterval: false, // 暂时禁用自动刷新
    ...options,
  });
};

// ===== 查询优化工具相关查询 =====

export const useAnalysisHistory = (
  params?: any,
  options?: UseQueryOptions<ApiResponse<any>>
) => {
  return useQuery({
    queryKey: queryKeys.queryOptimizer.analyze.history(params),
    queryFn: () => apiService.getAnalysisHistory(params),
    ...options,
  });
};

export const useAnalysis = (
  id: number,
  options?: UseQueryOptions<ApiResponse<any>>
) => {
  return useQuery({
    queryKey: queryKeys.queryOptimizer.analyze.detail(id),
    queryFn: () => apiService.getAnalysis(id),
    enabled: !!id,
    ...options,
  });
};

export const useExecutionPlan = (
  id: number,
  options?: UseQueryOptions<ApiResponse<any>>
) => {
  return useQuery({
    queryKey: queryKeys.queryOptimizer.explain.detail(id),
    queryFn: () => apiService.getExecutionPlan(id),
    enabled: !!id,
    ...options,
  });
};

export const useOptimizationSuggestions = (
  analysisId: number,
  options?: UseQueryOptions<ApiResponse<any>>
) => {
  return useQuery({
    queryKey: queryKeys.queryOptimizer.suggestions.detail(analysisId),
    queryFn: () => apiService.getOptimizationSuggestions(analysisId),
    enabled: !!analysisId,
    ...options,
  });
};

export const useIndexRecommendations = (
  analysisId: number,
  options?: UseQueryOptions<ApiResponse<any>>
) => {
  return useQuery({
    queryKey: queryKeys.queryOptimizer.indexRecommendations.detail(analysisId),
    queryFn: () => apiService.getIndexRecommendations(analysisId),
    enabled: !!analysisId,
    ...options,
  });
};

export const useSupportedDatabases = (
  options?: UseQueryOptions<ApiResponse<any>>
) => {
  return useQuery({
    queryKey: queryKeys.queryOptimizer.databases,
    queryFn: () => apiService.getSupportedDatabases(),
    ...options,
  });
};

export const useQueryOptimizerStats = (
  options?: UseQueryOptions<ApiResponse<any>>
) => {
  return useQuery({
    queryKey: queryKeys.queryOptimizer.stats,
    queryFn: () => apiService.getQueryOptimizerStats(),
    ...options,
  });
};

// ===== 变更操作 (Mutations) =====

// 认证相关变更
export const useLogin = (options?: UseMutationOptions<any, Error, LoginRequest>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: LoginRequest) => apiService.login(data),
    onSuccess: () => {
      // 登录成功后刷新用户信息
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.profile });
    },
    ...options,
  });
};

export const useRegister = (options?: UseMutationOptions<any, Error, RegisterRequest>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: RegisterRequest) => apiService.register(data),
    onSuccess: () => {
      // 注册成功后刷新用户信息
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.profile });
    },
    ...options,
  });
};

export const useLogout = (options?: UseMutationOptions<any, Error, void>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => apiService.logout(),
    onSuccess: () => {
      // 登出后清除所有缓存
      queryClient.clear();
    },
    ...options,
  });
};

export const useUpdateProfile = (options?: UseMutationOptions<any, Error, Partial<User>>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Partial<User>) => apiService.updateProfile(data),
    onSuccess: () => {
      // 更新成功后刷新用户信息
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.profile });
    },
    ...options,
  });
};

// 数据库相关变更
export const useCreateDatabase = (options?: UseMutationOptions<any, Error, CreateDatabaseRequest>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateDatabaseRequest) => apiService.createDatabase(data),
    onSuccess: () => {
      // 创建成功后刷新数据库列表和统计
      queryClient.invalidateQueries({ queryKey: queryKeys.databases.all });
      queryClient.invalidateQueries({ queryKey: queryKeys.databases.stats });
    },
    ...options,
  });
};

export const useUpdateDatabase = (options?: UseMutationOptions<any, Error, { id: number; data: Partial<CreateDatabaseRequest> }>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }) => apiService.updateDatabase(id, data),
    onSuccess: (_, { id }) => {
      // 更新成功后刷新相关缓存
      queryClient.invalidateQueries({ queryKey: queryKeys.databases.all });
      queryClient.invalidateQueries({ queryKey: queryKeys.databases.detail(id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.databases.stats });
    },
    ...options,
  });
};

export const useDeleteDatabase = (options?: UseMutationOptions<any, Error, number>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => apiService.deleteDatabase(id),
    onSuccess: (_, id) => {
      // 删除成功后刷新相关缓存
      queryClient.invalidateQueries({ queryKey: queryKeys.databases.all });
      queryClient.removeQueries({ queryKey: queryKeys.databases.detail(id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.databases.stats });
      // 同时清除相关的指标和告警缓存
      queryClient.removeQueries({ queryKey: queryKeys.metrics.list(id) });
      queryClient.removeQueries({ queryKey: queryKeys.metrics.latest(id) });
    },
    ...options,
  });
};

export const useTestConnection = (options?: UseMutationOptions<any, Error, number>) => {
  return useMutation({
    mutationFn: (id: number) => apiService.testConnection(id),
    ...options,
  });
};

// 告警规则相关变更
export const useCreateAlertRule = (options?: UseMutationOptions<any, Error, any>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => apiService.createAlertRule(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.alerts.rules.all });
      queryClient.invalidateQueries({ queryKey: queryKeys.alerts.stats });
    },
    ...options,
  });
};

export const useUpdateAlertRule = (options?: UseMutationOptions<any, Error, { id: number; data: any }>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }) => apiService.updateAlertRule(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.alerts.rules.all });
      queryClient.invalidateQueries({ queryKey: queryKeys.alerts.rules.detail(id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.alerts.stats });
    },
    ...options,
  });
};

export const useDeleteAlertRule = (options?: UseMutationOptions<any, Error, number>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => apiService.deleteAlertRule(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.alerts.rules.all });
      queryClient.removeQueries({ queryKey: queryKeys.alerts.rules.detail(id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.alerts.stats });
    },
    ...options,
  });
};

export const useResolveAlertEvent = (options?: UseMutationOptions<any, Error, { id: number; data?: any }>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id }) => apiService.resolveAlert(id),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.alerts.events.all });
      queryClient.invalidateQueries({ queryKey: queryKeys.alerts.events.detail(id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.alerts.stats });
    },
    ...options,
  });
};

// ===== 查询优化工具相关变更 =====

export const useAnalyzeQuery = (options?: UseMutationOptions<any, Error, any>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => apiService.analyzeQuery(data),
    onSuccess: () => {
      // 分析成功后刷新分析历史
      queryClient.invalidateQueries({ queryKey: queryKeys.queryOptimizer.analyze.all });
    },
    ...options,
  });
};

export const useExplainQuery = (options?: UseMutationOptions<any, Error, any>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => apiService.explainQuery(data),
    onSuccess: () => {
      // 执行计划分析成功后刷新相关数据
      queryClient.invalidateQueries({ queryKey: queryKeys.queryOptimizer.explain.all });
    },
    ...options,
  });
};

export const useGetIndexSuggestions = (options?: UseMutationOptions<any, Error, any>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => apiService.getIndexSuggestions(data),
    onSuccess: () => {
      // 索引建议生成成功后刷新相关数据
      queryClient.invalidateQueries({ queryKey: queryKeys.queryOptimizer.indexRecommendations.all });
    },
    ...options,
  });
};

// ===== 备份管理相关查询 =====
// 从 backup.ts 导出所有备份相关的 hooks
export * from './backup';
