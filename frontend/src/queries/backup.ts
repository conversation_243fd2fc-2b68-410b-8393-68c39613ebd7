import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiService } from '../services/api';
import type {
  CreateBackupTaskRequest,
  UpdateBackupTaskRequest
} from '../services/api';

// Query Keys
export const backupKeys = {
  all: ['backup'] as const,
  tasks: () => [...backupKeys.all, 'tasks'] as const,
  task: (id: number) => [...backupKeys.tasks(), id] as const,
  tasksList: (params?: any) => [...backupKeys.tasks(), 'list', params] as const,
  taskStats: () => [...backupKeys.tasks(), 'stats'] as const,
  history: () => [...backupKeys.all, 'history'] as const,
  historyList: (params?: any) => [...backupKeys.history(), 'list', params] as const,
  historyItem: (id: number) => [...backupKeys.history(), id] as const,
  running: () => [...backupKeys.all, 'running'] as const,
  stats: () => [...backupKeys.all, 'stats'] as const,
  successRate: (params?: any) => [...backupKeys.all, 'success-rate', params] as const,
};

// ===== 备份任务相关 Hooks =====

// 获取备份任务列表
export const useBackupTasks = (params?: {
  page?: number;
  page_size?: number;
  database_id?: number;
  status?: string;
  backup_type?: string;
  search?: string;
}) => {
  return useQuery({
    queryKey: backupKeys.tasksList(params),
    queryFn: () => apiService.getBackupTasks(params),
    staleTime: 30 * 1000, // 30秒
    gcTime: 5 * 60 * 1000, // 5分钟
  });
};

// 获取单个备份任务
export const useBackupTask = (id: number) => {
  return useQuery({
    queryKey: backupKeys.task(id),
    queryFn: () => apiService.getBackupTask(id),
    enabled: !!id,
    staleTime: 30 * 1000,
  });
};

// 获取备份任务统计
export const useBackupTaskStats = () => {
  return useQuery({
    queryKey: backupKeys.taskStats(),
    queryFn: () => apiService.getBackupTaskStats(),
    staleTime: 60 * 1000, // 1分钟
  });
};

// 创建备份任务
export const useCreateBackupTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateBackupTaskRequest) => apiService.createBackupTask(data),
    onSuccess: () => {
      // 刷新备份任务列表
      queryClient.invalidateQueries({ queryKey: backupKeys.tasks() });
      queryClient.invalidateQueries({ queryKey: backupKeys.taskStats() });
    },
  });
};

// 更新备份任务
export const useUpdateBackupTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateBackupTaskRequest }) =>
      apiService.updateBackupTask(id, data),
    onSuccess: (_, { id }) => {
      // 刷新相关查询
      queryClient.invalidateQueries({ queryKey: backupKeys.task(id) });
      queryClient.invalidateQueries({ queryKey: backupKeys.tasks() });
      queryClient.invalidateQueries({ queryKey: backupKeys.taskStats() });
    },
  });
};

// 删除备份任务
export const useDeleteBackupTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => apiService.deleteBackupTask(id),
    onSuccess: () => {
      // 刷新备份任务列表
      queryClient.invalidateQueries({ queryKey: backupKeys.tasks() });
      queryClient.invalidateQueries({ queryKey: backupKeys.taskStats() });
    },
  });
};

// ===== 备份执行相关 Hooks =====

// 执行备份
export const useExecuteBackup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, force }: { id: number; force?: boolean }) =>
      apiService.executeBackup(id, force),
    onSuccess: () => {
      // 刷新相关查询
      queryClient.invalidateQueries({ queryKey: backupKeys.running() });
      queryClient.invalidateQueries({ queryKey: backupKeys.history() });
      queryClient.invalidateQueries({ queryKey: backupKeys.tasks() });
    },
  });
};

// 获取正在运行的备份
export const useRunningBackups = () => {
  return useQuery({
    queryKey: backupKeys.running(),
    queryFn: () => apiService.getRunningBackups(),
    refetchInterval: 5 * 1000, // 每5秒刷新一次
    staleTime: 0, // 立即过期，确保数据实时性
  });
};

// 取消备份
export const useCancelBackup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (historyId: number) => apiService.cancelBackup(historyId),
    onSuccess: () => {
      // 刷新相关查询
      queryClient.invalidateQueries({ queryKey: backupKeys.running() });
      queryClient.invalidateQueries({ queryKey: backupKeys.history() });
    },
  });
};

// ===== 备份历史相关 Hooks =====

// 获取备份历史列表
export const useBackupHistory = (params?: {
  page?: number;
  page_size?: number;
  task_id?: number;
  database_id?: number;
  status?: string;
  start_date?: string;
  end_date?: string;
}) => {
  return useQuery({
    queryKey: backupKeys.historyList(params),
    queryFn: () => apiService.getBackupHistory(params),
    staleTime: 30 * 1000,
  });
};

// 获取单个备份历史
export const useBackupHistoryById = (id: number) => {
  return useQuery({
    queryKey: backupKeys.historyItem(id),
    queryFn: () => apiService.getBackupHistoryById(id),
    enabled: !!id,
    staleTime: 60 * 1000,
  });
};

// 删除备份历史
export const useDeleteBackupHistory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => apiService.deleteBackupHistory(id),
    onSuccess: () => {
      // 刷新备份历史列表
      queryClient.invalidateQueries({ queryKey: backupKeys.history() });
      queryClient.invalidateQueries({ queryKey: backupKeys.stats() });
    },
  });
};

// ===== 备份统计相关 Hooks =====

// 获取备份统计
export const useBackupStats = (params?: {
  database_id?: number;
  start_date?: string;
  end_date?: string;
  period?: string;
}) => {
  return useQuery({
    queryKey: backupKeys.stats(),
    queryFn: () => apiService.getBackupStats(params),
    staleTime: 60 * 1000, // 1分钟
  });
};

// 获取备份成功率
export const useBackupSuccessRate = (params?: {
  task_id?: number;
  days?: number;
}) => {
  return useQuery({
    queryKey: backupKeys.successRate(params),
    queryFn: () => apiService.getBackupSuccessRate(params),
    staleTime: 60 * 1000,
  });
};

// ===== 组合 Hooks =====

// 获取备份概览数据（组合多个查询）
export const useBackupOverview = () => {
  const tasksQuery = useBackupTasks({ page: 1, page_size: 10 });
  const statsQuery = useBackupStats();
  const taskStatsQuery = useBackupTaskStats();
  const runningQuery = useRunningBackups();

  return {
    tasks: tasksQuery,
    stats: statsQuery,
    taskStats: taskStatsQuery,
    running: runningQuery,
    isLoading: tasksQuery.isLoading || statsQuery.isLoading || taskStatsQuery.isLoading,
    isError: tasksQuery.isError || statsQuery.isError || taskStatsQuery.isError,
    error: tasksQuery.error || statsQuery.error || taskStatsQuery.error,
  };
};

// 刷新所有备份相关数据
export const useRefreshBackupData = () => {
  const queryClient = useQueryClient();

  return () => {
    queryClient.invalidateQueries({ queryKey: backupKeys.all });
  };
};
