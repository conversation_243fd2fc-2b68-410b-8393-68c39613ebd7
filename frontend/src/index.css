@import 'tailwindcss';

/* 自定义样式 */
@layer base {
  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  }
}

@layer components {
  /* 按钮组件 */
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
  }

  /* 输入框组件 */
  .input-field {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200;
  }

  /* 卡片组件 */
  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200;
  }

  .card-header {
    @apply border-b border-gray-200 pb-4 mb-4;
  }

  /* 状态指示器 */
  .status-active {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800;
  }

  .status-warning {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800;
  }

  .status-error {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800;
  }

  .status-inactive {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800;
  }

  /* 进度条 */
  .progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2;
  }

  .progress-fill {
    @apply h-2 rounded-full transition-all duration-300;
  }

  /* 表格样式 */
  .table-container {
    @apply overflow-hidden shadow ring-1 ring-black/5 rounded-lg;
  }

  /* 指标卡片样式 */
  .metric-card {
    @apply bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all duration-200;
  }

  .table {
    @apply min-w-full divide-y divide-gray-300;
  }

  .table-header {
    @apply bg-gray-50;
  }

  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }

  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }

  .table-row {
    @apply hover:bg-gray-50 transition-colors duration-150;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }

  /* 统计卡片 */
  .stat-card {
    @apply bg-white overflow-hidden shadow-sm rounded-xl border border-gray-200 hover:shadow-md transition-shadow duration-200;
  }

  .stat-card-content {
    @apply p-6;
  }

  .stat-icon {
    @apply flex-shrink-0 p-3 rounded-lg;
  }

  .stat-value {
    @apply text-2xl font-bold text-gray-900;
  }

  .stat-label {
    @apply text-sm font-medium text-gray-500;
  }

  /* 导航样式 */
  .nav-header {
    @apply bg-white shadow-sm border-b border-gray-200;
  }

  .nav-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .nav-content {
    @apply flex justify-between items-center py-4;
  }

  /* 图表容器 */
  .chart-container {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 p-6;
  }

  .chart-header {
    @apply flex items-center justify-between mb-4;
  }

  .chart-title {
    @apply text-lg font-semibold text-gray-900;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
