# 服务管理脚本使用说明

## 📋 脚本概览

为了解决Docker、后端、前端服务的启动顺序问题，我们提供了多个管理脚本：

### 🚀 快速启动/停止脚本

#### Linux/macOS 用户
```bash
# 启动所有服务（推荐）
./start-dev.sh

# 停止所有服务
./stop-dev.sh
```

#### Windows 用户
```cmd
# 启动所有服务（推荐）
start-dev.bat

# 停止所有服务
stop-dev.bat
```

### 🔧 高级管理脚本

#### 完整服务管理器（Linux/macOS）
```bash
# 查看所有可用命令
./service-manager.sh help

# 启动所有服务
./service-manager.sh start

# 停止所有服务
./service-manager.sh stop

# 重启所有服务
./service-manager.sh restart

# 查看服务状态
./service-manager.sh status

# 单独管理服务
./service-manager.sh start-docker    # 仅启动Docker
./service-manager.sh start-backend   # 仅启动后端
./service-manager.sh start-frontend  # 仅启动前端
```

## 🔄 服务启动顺序

脚本会按照正确的顺序启动服务，解决依赖问题：

1. **Docker服务** (PostgreSQL, Redis, MySQL)
   - 等待5秒确保数据库完全启动
2. **后端服务** (Go API)
   - 等待3秒确保API服务就绪
3. **前端服务** (React + Vite)

## 📍 服务地址

启动完成后，可以访问：

- **前端应用**: http://localhost:5173
- **后端API**: http://localhost:8080
- **API文档**: http://localhost:8080/swagger/index.html

## 📝 日志文件

服务运行日志保存在：

- **后端日志**: `backend.log`
- **前端日志**: `frontend.log`

## 🛠️ 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用情况
   ./service-manager.sh status
   
   # 强制停止所有服务
   ./stop-dev.sh
   ```

2. **数据库连接失败**
   - 确保Docker服务先启动
   - 等待数据库完全启动后再启动后端
   - 使用 `./service-manager.sh restart` 重启所有服务

3. **前端页面显示错误**
   - 检查后端服务是否正常运行
   - 查看 `frontend.log` 和 `backend.log` 日志

### 手动启动顺序

如果脚本有问题，可以手动按顺序启动：

```bash
# 1. 启动Docker
docker-compose up -d

# 2. 等待数据库启动（重要！）
sleep 5

# 3. 启动后端
cd backend/go-backend
go run cmd/server/main.go

# 4. 启动前端（新终端）
cd frontend
npm run dev
```

## 💡 最佳实践

1. **总是使用脚本启动** - 避免手动启动导致的顺序问题
2. **检查服务状态** - 启动后使用 `status` 命令确认所有服务正常
3. **查看日志** - 遇到问题时先查看日志文件
4. **完全重启** - 遇到奇怪问题时使用 `restart` 命令

## 🔧 脚本特性

- ✅ **自动依赖检查** - 检查Docker、Go、Node.js环境
- ✅ **端口冲突检测** - 自动检测端口占用情况
- ✅ **等待机制** - 确保服务完全启动后再启动下一个
- ✅ **错误处理** - 提供详细的错误信息和建议
- ✅ **跨平台支持** - Linux、macOS、Windows都有对应脚本
- ✅ **日志管理** - 自动保存服务日志到文件

## 📞 技术支持

如果脚本有问题或需要改进，请：

1. 查看日志文件了解具体错误
2. 使用 `./service-manager.sh status` 检查服务状态
3. 尝试 `./service-manager.sh restart` 重启所有服务
