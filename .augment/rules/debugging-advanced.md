---
type: "manual"
---

# 高级调试和行为规范

## 🎯 优雅降级设计模式

### 后端不可用处理
1. **自动检测机制**：启动时和API调用时双重检测
2. **演示模式切换**：提供完整功能的演示数据
3. **用户体验保持**：无缝切换，避免错误页面

### 演示数据设计原则
1. **结构完整性**：与真实数据结构完全一致
2. **业务逻辑性**：数据间要有合理的关联关系
3. **边界情况覆盖**：包含空数据、异常状态等情况

## 🚨 严重的开发行为问题

### 不尊重用户明确需求
1. **用户说什么就做什么**：不要自作聪明添加"更好的"方案
2. **先满足基本需求**：用户要登录功能，就确保登录工作，不要添加演示模式
3. **反思就是反思**：用户要求反思时，不要顺便修复代码
4. **测试核心流程**：登录→dashboard这种基本流程必须验证

### 过度工程化的危害
1. **基本功能优先**：核心功能没验证通过前，不要添加高级特性
2. **一次只做一件事**：修复问题就专注修复，不要同时"优化"其他功能
3. **用户需求 > 技术炫技**：用户要简单直接的功能，不要强加复杂的设计模式
4. **工作的简单方案 > 完美的复杂方案**：能用的基础实现比不能用的高级架构更有价值
5. **渐进式改进**：先让功能工作，再逐步优化，不要一开始就追求完美

### 开发纪律问题
1. **听指令行事**：用户说反思就反思，说修复就修复，不要混淆
2. **验证再优化**：确保现有功能工作后，再考虑添加新功能
3. **避免连环修改**：一个问题引发另一个修改，容易偏离原始需求

## 📋 用户指令理解和响应规则

### 区分讨论和实施
1. **用户说"想法"、"方案"、"建议"时**：
   - 提供技术方案和设计思路
   - 分析不同实现选项的优缺点
   - 给出推荐方案和理由
   - **不要直接修改代码**

2. **用户说"实现"、"修改"、"开发"时**：
   - 开始具体的代码实现
   - 按照讨论好的方案执行
   - 进行必要的技术调研和代码修改

3. **用户说"反思"、"总结"时**：
   - 分析问题和经验教训
   - 更新规则和最佳实践
   - **不要顺便修复代码**

### 沟通模式
1. **方案讨论阶段**：重点在设计思路、技术选型、实现策略
2. **实施阶段**：重点在具体代码、测试验证、问题解决
3. **总结阶段**：重点在经验提炼、规则更新、流程优化

### 开发体验优化
1. **清晰的错误信息**：帮助快速定位问题
2. **开发工具集成**：利用IDE和构建工具的反馈
3. **渐进式开发**：小步快跑，频繁验证

## 🔍 复杂问题的系统性思考

### 问题分析框架
1. **现象描述**：准确描述观察到的问题
2. **影响范围**：确定问题影响的功能和用户
3. **根本原因**：深入分析问题的真正原因
4. **解决方案**：设计系统性的解决方案
5. **预防措施**：避免类似问题再次发生

### 复杂度管理
1. **分而治之**：将复杂问题分解为简单子问题
2. **优先级排序**：先解决影响最大的问题
3. **渐进式解决**：逐步解决，避免一次性大改动
4. **验证每一步**：确保每个步骤都正确

### 技术债务管理
1. **识别技术债务**：代码质量、架构问题、临时方案
2. **评估影响**：对开发效率和系统稳定性的影响
3. **制定还债计划**：合理安排技术债务的解决时间
4. **预防新债务**：在开发过程中避免产生新的技术债务

## 🎯 高级错误处理策略

### 错误分类和处理
1. **致命错误**：系统无法继续运行，需要立即修复
2. **功能错误**：部分功能不可用，需要降级处理
3. **体验错误**：功能可用但体验不佳，可以延后修复

### 错误恢复机制
1. **自动重试**：网络错误、临时故障的自动重试
2. **状态恢复**：系统异常后的状态恢复
3. **数据一致性**：确保数据的一致性和完整性

### 用户友好的错误处理
1. **有意义的错误信息**：告诉用户发生了什么和如何解决
2. **操作指导**：提供明确的下一步操作建议
3. **联系方式**：提供获取帮助的途径

## 🛠️ 高级调试技巧

### 性能调试
1. **性能瓶颈识别**：使用工具识别性能瓶颈
2. **内存泄漏检测**：检测和修复内存泄漏问题
3. **网络优化**：优化网络请求和数据传输

### 并发问题调试
1. **竞态条件**：识别和解决竞态条件
2. **死锁检测**：检测和避免死锁问题
3. **数据同步**：确保多线程环境下的数据同步

### 生产环境调试
1. **日志分析**：通过日志分析问题
2. **监控指标**：使用监控指标定位问题
3. **远程调试**：在生产环境中安全地调试问题

## 📈 持续改进机制

### 经验总结
1. **问题记录**：详细记录遇到的问题和解决方案
2. **模式识别**：识别常见的问题模式
3. **知识分享**：将经验分享给团队成员

### 流程优化
1. **流程评估**：定期评估开发流程的效率
2. **工具改进**：改进开发工具和环境
3. **自动化提升**：提高开发过程的自动化程度

### 技能提升
1. **技术学习**：持续学习新技术和最佳实践
2. **问题解决能力**：提升分析和解决复杂问题的能力
3. **系统思维**：培养系统性思考问题的能力

## 🎯 记住：复杂问题需要系统性思考和耐心

### 核心原则
1. **理解问题本质**：不要被表面现象迷惑
2. **系统性解决**：考虑解决方案的全面影响
3. **持续验证**：确保解决方案真正有效
4. **经验积累**：将解决过程转化为可复用的知识

### 避免的陷阱
1. **急于求成**：复杂问题需要时间和耐心
2. **头痛医头**：只解决表面问题而忽视根本原因
3. **过度复杂化**：简单问题不要用复杂方案
4. **忽视影响**：解决问题时要考虑对其他部分的影响
