---
type: "manual"
---

# 开发调试规则

## 🚨 强制执行：编译优先原则

### 代码修改后的必执行流程
1. **立即编译检查**：`npm run build` 或 `tsc --noEmit`
2. **修复所有编译错误**（语法、类型错误）
3. **检查运行时错误**（浏览器控制台）
4. **测试功能逻辑**

### 调试优先级
- 🥇 **编译错误**：语法、导入导出、类型错误
- 🥈 **运行时错误**：控制台报错、网络请求失败
- 🥉 **功能错误**：UI显示、交互逻辑

### 禁止行为
- ❌ 在编译错误存在时调试功能问题
- ❌ 依赖开发服务器热重载发现语法错误
- ❌ 多次修改同一文件而不编译检查

### 工具使用
- 使用 `npm run build` 作为第一检查工具
- 配置 IDE TypeScript 实时检查
- 浏览器开发者工具控制台监控

### 目标
- 编译阶段解决 90% 的问题
- 避免在错误方向上浪费时间

## 🔧 页面开发测试规则

### 自主验证原则
- **主动检查**：开发页面功能时，必须使用 `web-fetch` 工具主动检查页面状态
- **独立验证**：不依赖用户截图或描述来了解页面问题
- **实时监控**：在修改代码后立即验证页面是否正常加载

### 页面测试流程
1. **代码修改后**：立即使用 `web-fetch` 检查页面
2. **发现问题时**：分析返回内容，判断是否为默认页面或错误页面
3. **调试过程中**：持续使用 `web-fetch` 验证修复效果
4. **功能完成后**：最终验证页面完整性

### 必须使用的工具
- `web-fetch` - 获取页面实际内容
- `open-browser` - 在关键节点打开浏览器供用户查看
- `read-process` - 检查开发服务器状态和错误信息

### 问题诊断策略
- **默认页面**：检查 React 应用是否正确渲染
- **空白页面**：检查 JavaScript 错误和组件导入
- **编译错误**：检查开发服务器控制台输出
- **样式问题**：验证 CSS 加载和 Tailwind 配置

### 禁止行为
- ❌ 依赖用户反馈来了解页面状态
- ❌ 修改代码后不验证页面效果
- ❌ 假设页面正常而不进行检查

## 🔍 系统性问题诊断原则

### 1. 从根本原因开始分析
- **不要急于修复症状**：表面错误往往不是真正的问题
- **追溯错误链**：API错误 → 网络错误 → 服务不可用 → 需要降级方案
- **验证基础设施**：在调试应用逻辑前，先确认服务、网络、依赖是否正常

### 2. 错误信息分类处理
- **网络错误**：`ERR_NETWORK`, `ECONNREFUSED` → 检查服务状态
- **语法错误**：JSX结构、TypeScript类型 → 检查代码结构
- **运行时错误**：逻辑错误、状态管理 → 检查业务逻辑

## 🛠️ JSX调试专项指南

### 常见JSX结构问题
1. **标签不匹配**：开放标签与闭合标签数量不一致
2. **Fragment使用错误**：`<>` 与 `<div>` 混用导致结构混乱
3. **缩进不一致**：影响代码可读性和错误定位
4. **JSX中的非法表达式**：如直接使用 `console.log()`

### JSX调试策略
1. **简化-测试-恢复**：遇到复杂JSX错误时，先简化到最小可工作版本
2. **标签计数验证**：使用工具统计开放和闭合标签数量
3. **逐步构建**：从简单结构开始，逐步添加复杂内容

## 📝 TypeScript类型调试

### 类型错误处理原则
1. **严格遵循接口定义**：演示数据必须完全匹配真实API接口
2. **null vs undefined**：明确区分可选字段和必需字段
3. **联合类型处理**：正确处理 `string | null` 等联合类型

## 🛠️ 调试工具使用技巧

### 命令行工具
- `grep -n "<div" file.tsx | wc -l` - 统计标签数量
- `npm run build` - 快速发现语法和类型错误
- 开发服务器热重载 - 实时反馈代码变更

### 错误信息解读
1. **编译时错误**：语法、类型问题，必须修复
2. **运行时错误**：逻辑问题，可能需要降级处理
3. **网络错误**：基础设施问题，需要检查服务状态

## 🔄 问题解决流程

### 1. 问题分类
- 确定是前端问题还是后端问题
- 区分是代码错误还是环境问题

### 2. 最小复现
- 创建最简单的能复现问题的版本
- 排除无关因素的干扰

### 3. 系统性修复
- 修复根本原因，不只是症状
- 考虑防止类似问题再次发生
- 提升整体用户体验

### 4. 验证和测试
- 确保修复不引入新问题
- 测试边界情况和异常场景

## 🎯 用户体验优先原则

### 错误处理策略
1. **优雅降级**：服务不可用时提供替代方案
2. **有意义的反馈**：给用户清晰的状态提示
3. **快速恢复**：自动重试和状态恢复机制

### API错误处理的用户体验
1. **避免错误弹窗轰炸**：连续的API错误不应该持续弹窗
2. **开发阶段静默处理**：开发时API不稳定，应该静默记录而非弹窗
3. **轮询调用要谨慎**：未实现的API端点不应该持续轮询

## 🚨 调试过程中的常见陷阱

### 文件引用错误
1. **修改引用前先确认文件存在**：避免引用不存在的文件
2. **重构后及时更新所有引用**：防止遗漏导致的引用错误
3. **立即验证修改效果**：每次关键修改后立即检查页面是否正常

### 错误优先级判断
1. **阻塞性错误优先**：页面无法加载 > 功能异常 > 体验问题
2. **基础功能优先**：页面显示 > API调用 > 交互优化
3. **错误信息要仔细读**：编译错误通常比运行时错误更重要
