---
type: "manual"
---

# 项目管理规则

## 🚨 强制执行：任务完成后的状态同步

### 任务完成标准流程
当完成任何开发任务时，必须按以下顺序执行：

#### 1. 功能验证 (必须)
```bash
# 验证功能正常工作
web-fetch http://localhost:5173
open-browser http://localhost:5173

# 检查是否有编译错误
npm run build
```

#### 2. 更新任务状态 (必须)
```bash
# 更新任务清单中的任务状态
update_tasks [{"task_id": "任务ID", "state": "COMPLETE"}]

# 如果有新的子任务，添加到清单
add_tasks [{"name": "新任务名", "description": "任务描述"}]
```

#### 3. 同步项目状态文档 (必须)
```bash
# 更新主要状态文档
str-replace-editor PROJECT_STATUS.md
str-replace-editor project-status.json
str-replace-editor quick-status-check.md
```

#### 4. 记录成果和影响 (推荐)
```bash
# 在PROJECT_STATUS.md中记录：
- 完成的功能
- 用户价值/影响
- 技术实现要点
- 下一步计划
```

### 自动化提醒系统

#### 任务完成检查清单
完成任务时，AI助手应该主动提醒：

- [ ] ✅ 功能是否验证通过？
- [ ] 📋 任务状态是否已更新？
- [ ] 📄 项目文档是否已同步？
- [ ] 🎯 下一步任务是否已明确？
- [ ] 🔧 是否有新的技术债务？

#### 强制更新的触发条件
以下情况必须更新状态文档：

1. **功能完成**：新功能开发完成并验证
2. **Bug修复**：重要问题解决
3. **里程碑达成**：阶段性目标完成
4. **架构变更**：技术架构调整
5. **环境变更**：开发环境配置修改

### 状态文档更新模板

#### PROJECT_STATUS.md 更新要点
```markdown
## 🚀 最新功能成果

### 🔔 [功能名称] (完成日期)
**位置**: [功能位置]
**功能**: 
- [功能点1]
- [功能点2]

**技术实现**:
- [技术要点1]
- [技术要点2]

**用户价值**: [对用户的价值]
```

#### project-status.json 更新要点
```json
{
  "recentAchievements": [
    {
      "date": "2025-01-XX",
      "achievement": "功能名称",
      "impact": "用户价值描述"
    }
  ],
  "currentTasks": {
    "completed": [
      {
        "id": "task-id",
        "name": "任务名称", 
        "completedDate": "2025-01-XX"
      }
    ]
  }
}
```

## 🔄 新Thread启动协议

### 强制执行：状态同步检查
当开始新的开发会话时，必须按以下顺序执行：

1. **项目状态确认**
   ```bash
   view PROJECT_STATUS.md
   view project-status.json
   view NEW_THREAD_GUIDE.md
   ```

2. **任务清单同步**
   ```bash
   view_tasklist
   # 如果任务清单为空或过时，从文档恢复
   ```

3. **环境状态验证**
   ```bash
   web-fetch http://localhost:5173  # 前端状态
   web-fetch http://localhost:8080/health  # 后端状态
   read-terminal  # 检查开发服务器状态
   ```

4. **功能完整性检查**
   ```bash
   # 验证最新功能是否正常
   open-browser http://localhost:5173
   ```

### 项目状态文档维护

#### 必须更新的文档
- `PROJECT_STATUS.md` - 详细的项目状态和进度
- `project-status.json` - 机器可读的状态数据
- `NEW_THREAD_GUIDE.md` - 新Thread启动指南

#### 更新时机
- 完成重要功能时
- 阶段性里程碑达成时
- 技术架构变更时
- 开发环境配置变更时

#### 更新内容
- 当前开发阶段和进度
- 最新完成的功能
- 下一步开发重点
- 环境配置状态
- 关键文件路径
- 已知问题和技术债务

### 任务管理持久化

#### 任务状态同步策略
1. **会话开始时**：从文档恢复任务清单
2. **开发过程中**：实时更新任务状态
3. **会话结束前**：将任务状态写入文档

#### 任务文档格式
```markdown
## 当前活跃任务
- [/] 告警管理系统 (70%)
  - [x] 告警铃铛组件
  - [x] 告警通知下拉菜单
  - [/] 告警历史查看功能
  - [ ] 告警处理工作流

## 下一步任务
- [ ] 性能分析工具
- [ ] 查询优化工具
```

### 代码状态管理

#### 关键文件监控
必须跟踪以下文件的状态：
- `frontend/src/main.tsx` - 应用入口
- `frontend/src/TempApp.tsx` - 主应用组件
- `frontend/src/components/alerts/` - 告警相关组件
- `backend/go-backend/main.go` - 后端入口

#### 配置文件状态
- `package.json` - 前端依赖
- `go.mod` - 后端依赖  
- `docker-compose.yml` - 服务配置
- `.env` - 环境变量

### 开发环境一致性

#### 服务启动检查
```bash
# 前端开发服务器
cd frontend && npm run dev

# 后端API服务
cd backend/go-backend && go run main.go

# 数据库服务
docker-compose up -d postgres redis
```

#### 端口占用管理
- 前端: http://localhost:5173
- 后端: http://localhost:8080
- PostgreSQL: localhost:5432
- Redis: localhost:6379

### 问题诊断和恢复

#### 常见问题检查清单
- [ ] 开发服务器是否正常启动
- [ ] 页面是否显示正确内容（非默认Vite页面）
- [ ] API接口是否正常响应
- [ ] 数据库连接是否正常
- [ ] 最新功能是否正常工作

#### 快速恢复流程
1. **环境问题**：重启开发服务器
2. **依赖问题**：重新安装依赖
3. **代码问题**：检查最近的修改
4. **数据库问题**：重启Docker服务

### 文档同步规则

#### 自动更新触发条件
- 完成新功能开发
- 修复重要bug
- 环境配置变更
- 架构调整

#### 手动更新要求
- 每个开发会话结束前
- 重要里程碑达成时
- 发现文档过时时

### 质量保证

#### 新Thread启动验证
新Thread必须能够：
- ✅ 快速了解项目当前状态
- ✅ 确认开发环境正常
- ✅ 验证最新功能可用
- ✅ 明确下一步开发重点
- ✅ 无缝继续开发工作

#### 成功标准
- 5分钟内完成状态同步
- 所有服务正常运行
- 最新功能验证通过
- 开发任务清晰明确

### 禁止行为
- ❌ 不检查项目状态直接开始开发
- ❌ 不验证环境状态就修改代码
- ❌ 不更新文档就结束会话
- ❌ 依赖记忆而不查看文档
- ❌ 完成功能后不验证就标记完成
- ❌ 不更新文档就开始下一个任务
- ❌ 不记录技术债务和问题
- ❌ 不明确下一步计划就结束会话
