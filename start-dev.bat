@echo off
REM Windows批处理版本的开发环境启动脚本

echo 🚀 启动数据库监控平台开发环境...

REM 1. 启动Docker服务
echo 📦 启动Docker服务...
docker-compose up -d

REM 等待数据库启动
echo ⏳ 等待数据库启动...
timeout /t 5 /nobreak >nul

REM 2. 启动后端服务
echo 🔧 启动后端服务...
cd backend\go-backend
start /b go run cmd\server\main.go > ..\..\backend.log 2>&1
cd ..\..

REM 等待后端启动
echo ⏳ 等待后端启动...
timeout /t 3 /nobreak >nul

REM 3. 启动前端服务
echo 🎨 启动前端服务...
cd frontend
start /b npm run dev > ..\frontend.log 2>&1
cd ..

echo.
echo ✅ 所有服务启动完成！
echo.
echo 📍 服务地址：
echo    前端应用: http://localhost:5173
echo    后端API: http://localhost:8080
echo    API文档: http://localhost:8080/swagger/index.html
echo.
echo 📋 管理命令：
echo    停止服务: stop-dev.bat
echo.
echo 📝 日志文件：
echo    后端日志: backend.log
echo    前端日志: frontend.log

pause
