version: '3.8'

services:
  # PostgreSQL - 主数据库
  postgres:
    image: postgres:15
    container_name: db-monitor-postgres
    environment:
      POSTGRES_DB: db_monitor
      POSTGRES_USER: dbmonitor
      POSTGRES_PASSWORD: dbmonitor123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - db-monitor-network

  # Redis - 缓存
  redis:
    image: redis:7-alpine
    container_name: db-monitor-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - db-monitor-network

  # ClickHouse - 时序数据库 (暂时禁用)
  # clickhouse:
  #   image: clickhouse/clickhouse-server:latest
  #   container_name: db-monitor-clickhouse
  #   ports:
  #     - "8123:8123"
  #     - "9001:9000"
  #   volumes:
  #     - clickhouse_data:/var/lib/clickhouse
  #   networks:
  #     - db-monitor-network

  # Kafka - 消息队列 (暂时禁用)
  # zookeeper:
  #   image: confluentinc/cp-zookeeper:latest
  #   container_name: db-monitor-zookeeper
  #   environment:
  #     ZOOKEEPER_CLIENT_PORT: 2181
  #     ZOOKEEPER_TICK_TIME: 2000
  #   networks:
  #     - db-monitor-network

  # kafka:
  #   image: confluentinc/cp-kafka:latest
  #   container_name: db-monitor-kafka
  #   depends_on:
  #     - zookeeper
  #   ports:
  #     - "9092:9092"
  #   environment:
  #     KAFKA_BROKER_ID: 1
  #     KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
  #     KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
  #     KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
  #   networks:
  #     - db-monitor-network

  # 测试用的MySQL数据库
  mysql-test:
    image: mysql:8.0
    container_name: db-monitor-mysql-test
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: testdb
      MYSQL_USER: testuser
      MYSQL_PASSWORD: testpass
    ports:
      - "3306:3306"
    volumes:
      - mysql_test_data:/var/lib/mysql
    networks:
      - db-monitor-network

volumes:
  postgres_data:
  redis_data:
  clickhouse_data:
  mysql_test_data:

networks:
  db-monitor-network:
    driver: bridge
